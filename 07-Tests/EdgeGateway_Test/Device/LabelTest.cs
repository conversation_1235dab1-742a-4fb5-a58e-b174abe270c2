using Microsoft.Extensions.DependencyInjection;

namespace EdgeGateway_Test;

public class LabelTest : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public LabelTest(WebApplicationFactory<Program> factory)
    {
        _factory = factory;

        // _factory = factory.WithWebHostBuilder(builder =>
        // {
        //     builder.UseEnvironment("Stage");   // 设置环境
        // });

        // 初始化 Furion
        Serve.RunNative(services =>
        {
            // 注册远程服务
            services.AddHttpRemote();
        });
    }

    // [Fact]
    // public async Task 测试请求百度()
    // {
    //     var rep = await "https://www.baidu.com".GetAsync();
    //     Assert.True(rep.IsSuccessStatusCode);
    // }

    // [Theory]
    // [InlineData(1, 2)]
    // [InlineData(3, 4)]
    // [InlineData(5, 7)]
    // public void 带参数测试(int i, int j)
    // {
    //     Assert.NotEqual(0, (i + j) % 2);
    // }

    [Fact]
    public async Task LabelPageTest()
    {
        using var client = _factory.CreateClient();
        using var response = await client.GetAsync("/api/device/page");
        response.EnsureSuccessStatusCode();
        // Assert.True(response.IsSuccessStatusCode);
        var result = await response.Content.ReadFromJsonAsync<SqlSugarPagedList<Device>>();
        Assert.NotNull(result);
    }
    
    [Theory]
    [InlineData("{\"DeviceId\":616977359691845,\"Identifier\":\"BacodeIn\",\"Name\":\"条码校验\",\"TransitionType\":\"String\",\"SendType\":1,\"Length\":30,\"ProtectType\":1,\"DataType\":11,\"Encoding\":0,\"RegisterAddress\":\"D600\",\"ReadLength\":30}")]
    public async Task LabelCreateTest(string data)
    {
        using var client = _factory.CreateClient();
        using var response = await client.PostAsync("/api/device/add", new StringContent(data));
        response.EnsureSuccessStatusCode();
    }
}