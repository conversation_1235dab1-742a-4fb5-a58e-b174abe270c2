using System;
using System.Linq;
using EdgeGateway.Driver.Entity.Model;

namespace OpcUaCustom;

/// <summary>
/// 测试地址浏览配置功能
/// </summary>
public class TestAddressBrowseConfig
{
    /// <summary>
    /// 测试地址浏览配置
    /// </summary>
    public static void TestConfig()
    {
        Console.WriteLine("=== 测试OPC UA地址浏览配置 ===");

        try
        {
            // 创建驱动信息
            var driverInfo = new DriverInfoDto
            {
                DeviceId = 1,
                Identifier = "test-opcua-driver"
            };

            // 创建驱动实例
            var opcuaDriver = new OpcUaCustom(driverInfo);

            // 测试获取地址浏览配置
            var browseConfig = opcuaDriver.GetAddressBrowseConfig();

            Console.WriteLine($"地址输入类型: {browseConfig.InputType}");
            Console.WriteLine($"格式描述: {browseConfig.FormatDescription}");
            Console.WriteLine($"验证模式: {browseConfig.ValidationPattern}");
            Console.WriteLine($"支持搜索: {browseConfig.Searchable}");

            Console.WriteLine($"\n地址模板数量: {browseConfig.AddressTemplates.Count}");
            foreach (var template in browseConfig.AddressTemplates)
            {
                Console.WriteLine($"  - {template.Category}: {template.Pattern}");
                Console.WriteLine($"    示例: {template.Example}");
                Console.WriteLine($"    描述: {template.Description}");
                Console.WriteLine($"    参数数量: {template.Parameters.Count}");
                
                foreach (var param in template.Parameters)
                {
                    Console.WriteLine($"      * {param.Name} ({param.Type}): {param.Description}");
                }
            }

            Console.WriteLine($"\n常用地址数量: {browseConfig.CommonAddresses.Count}");
            var groupedAddresses = browseConfig.CommonAddresses.GroupBy(a => a.Group);
            foreach (var group in groupedAddresses)
            {
                Console.WriteLine($"  [{group.Key}]");
                foreach (var addr in group)
                {
                    Console.WriteLine($"    - {addr.DisplayName}: {addr.Address}");
                    Console.WriteLine($"      数据类型: {addr.DataType}, 可读: {addr.CanRead}, 可写: {addr.CanWrite}");
                    if (!string.IsNullOrEmpty(addr.Description))
                    {
                        Console.WriteLine($"      描述: {addr.Description}");
                    }
                }
            }

            Console.WriteLine("\n=== 地址浏览配置测试完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }

    /// <summary>
    /// 主程序入口
    /// </summary>
    public static void Main(string[] args)
    {
        Console.WriteLine("OPC UA 地址浏览配置测试程序");
        Console.WriteLine("==============================");
        
        TestConfig();
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
