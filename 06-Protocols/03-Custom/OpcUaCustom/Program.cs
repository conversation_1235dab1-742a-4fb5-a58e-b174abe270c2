using System;
using System.Threading.Tasks;

namespace OpcUaCustom;

/// <summary>
/// OPC UA自定义协议测试程序
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("OPC UA Custom Protocol Driver Test");
        Console.WriteLine("==================================");

        try
        {
            // 测试地址解析器
            OpcUaCustomTest.TestAddressParser();

            // 测试基本功能（需要OPC UA服务器）
            Console.WriteLine("\n按任意键继续测试连接功能（需要OPC UA服务器运行在 opc.tcp://localhost:4840）...");
            Console.WriteLine("如果没有OPC UA服务器，请按 Ctrl+C 退出");
            Console.ReadKey();

            await OpcUaCustomTest.TestBasicFunctionality();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }

        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
