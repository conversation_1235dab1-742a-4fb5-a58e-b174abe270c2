<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <RootNamespace>OpcUaCustom</RootNamespace>
        <PublishAot>true</PublishAot>
        <IlcOptimizationPreference>Speed</IlcOptimizationPreference>
        <IlcFoldIdenticalMethodBodies>true</IlcFoldIdenticalMethodBodies>
        <TrimMode>full</TrimMode>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\EdgeGateway.Driver.Interface\EdgeGateway.Driver.Interface.csproj"/>
        <ProjectReference Include="..\..\EdgeGateway.Driver.Entity\EdgeGateway.Driver.Entity.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="OPCFoundation.NetStandard.Opc.Ua.Client" Version="1.5.376.244" />
        <PackageReference Include="OPCFoundation.NetStandard.Opc.Ua.Core" Version="1.5.376.244" />
    </ItemGroup>

</Project>
