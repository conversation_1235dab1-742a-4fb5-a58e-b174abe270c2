<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <StartupObject>OpcUaCustom.TestDynamicBrowse</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\EdgeGateway.Driver.Entity\EdgeGateway.Driver.Entity.csproj" />
    <ProjectReference Include="..\..\EdgeGateway.Driver.Interface\EdgeGateway.Driver.Interface.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="HslCommunication" Version="12.3.2" />
    <PackageReference Include="OPCFoundation.NetStandard.Opc.Ua" Version="1.5.374.54" />
    <PackageReference Include="OPCFoundation.NetStandard.Opc.Ua.Client" Version="1.5.374.54" />
  </ItemGroup>



</Project>
