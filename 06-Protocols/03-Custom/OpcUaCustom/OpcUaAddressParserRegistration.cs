using System;
using System.Collections.Generic;
using System.Linq;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.Driver.Entity.Parsers;
using EdgeGateway.Driver.Interface.DriverBase.AddressParsing;

namespace OpcUaCustom;

/// <summary>
/// OPC UA协议地址解析器注册类
/// </summary>
public static class OpcUaAddressParserRegistration
{
    /// <summary>
    /// 注册OPC UA协议地址解析器
    /// </summary>
    public static void RegisterOpcUaAddressParser()
    {
        // 注册到接口层的解析器工厂
        var interfaceFactory = EdgeGateway.Driver.Interface.DriverBase.AddressParsing.AddressParserFactory.Instance;
        interfaceFactory.RegisterParser("opcua", new OpcUaAddressParser());

        // 注册到实体层的解析器工厂
        var entityFactory = EdgeGateway.Driver.Entity.Services.AddressParserFactory.Instance;
        entityFactory.RegisterParser(new OpcUaAddressParserAdapter());
    }

    /// <summary>
    /// OPC UA协议地址解析器适配器（用于实体层）
    /// </summary>
    private class OpcUaAddressParserAdapter : EdgeGateway.Driver.Entity.Parsers.AddressParserBase
    {
        public override string ProtocolName => "OpcUA";
        public override int Priority => 50;
        public override string[] SupportedFormats => new[]
        {
            "i=2258",                    // 数值型NodeId
            "ns=2;i=2258",              // 带命名空间的数值型NodeId
            "s=MyVariable",             // 字符串型NodeId
            "ns=2;s=MyVariable",        // 带命名空间的字符串型NodeId
            "g=09087e75-8e5e-499b-954f-f2a9603db28a", // GUID型NodeId
            "ns=2;g=09087e75-8e5e-499b-954f-f2a9603db28a", // 带命名空间的GUID型NodeId
            "b=M/RbKBsRVkePCePcx24oRA==", // ByteString型NodeId
            "ns=2;b=M/RbKBsRVkePCePcx24oRA==", // 带命名空间的ByteString型NodeId
            "i=2258[0]",                // 数组访问
            "ns=2;s=MyArray[0:5]"       // 数组范围访问
        };

        public override bool CanParse(string address)
        {
            if (!base.CanParse(address))
                return false;

            // OPC UA地址通常包含等号和标识符类型
            return address.Contains("=") && 
                   System.Text.RegularExpressions.Regex.IsMatch(address, @"^(?:ns=\d+;)?[isgb]=.+$");
        }

        public override UniversalAddress Parse(string address, string dataType = null, int? length = null)
        {
            // 使用OPC UA地址解析器进行解析
            var parser = new EdgeGateway.Driver.Entity.Parsers.OpcUaAddressParser();
            return parser.Parse(address, dataType, length);
        }
    }

    /// <summary>
    /// OPC UA地址解析器（用于接口层）
    /// </summary>
    private class OpcUaAddressParser : EdgeGateway.Driver.Interface.DriverBase.AddressParsing.IAddressParser
    {
        public bool CanParse(string address)
        {
            if (string.IsNullOrWhiteSpace(address))
                return false;

            // 检查是否为有效的OPC UA NodeId格式
            return System.Text.RegularExpressions.Regex.IsMatch(address.Trim(),
                @"^(?:ns=\d+;)?[isgb]=.+$",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }

        public AddressParseResult Parse(string address, string dataType, int? length = null)
        {
            if (!CanParse(address))
            {
                return new AddressParseResult
                {
                    IsValid = false,
                    ErrorMessage = $"不支持的OPC UA地址格式: {address}",
                    OriginalAddress = address
                };
            }

            try
            {
                var parser = new EdgeGateway.Driver.Entity.Parsers.OpcUaAddressParser();
                var result = parser.Parse(address, dataType, length);

                return new AddressParseResult
                {
                    IsValid = result.IsValid,
                    ErrorMessage = result.ErrorMessage,
                    OriginalAddress = address,
                    Prefix = result.AreaType,
                    Number = (decimal)result.BaseAddress,
                    BitPosition = result.BitPosition ?? -1,
                    DataType = dataType ?? "Unknown",
                    Area = result.AreaType
                };
            }
            catch (Exception ex)
            {
                return new AddressParseResult
                {
                    IsValid = false,
                    ErrorMessage = $"解析OPC UA地址时发生错误: {ex.Message}",
                    OriginalAddress = address
                };
            }
        }

        public List<AddressParseResult> ParseBatch(List<(string Address, string DataType, int? Length)> addresses)
        {
            var results = new List<AddressParseResult>();

            foreach (var (address, dataType, len) in addresses)
            {
                results.Add(Parse(address, dataType, len));
            }

            return results;
        }

        public List<List<AddressParseResult>> OptimizeForBatch(List<AddressParseResult> addresses)
        {
            var result = new List<List<AddressParseResult>>();

            // 按命名空间和标识符类型分组
            var groups = addresses
                .Where(a => a.IsValid)
                .GroupBy(a => a.Prefix)
                .Select(g => g.ToList())
                .ToList();

            result.AddRange(groups);
            return result;
        }
    }
}
