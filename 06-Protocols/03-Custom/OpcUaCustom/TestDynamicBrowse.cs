using System;
using System.Threading.Tasks;
using EdgeGateway.Driver.Entity.Model;

namespace OpcUaCustom;

/// <summary>
/// 测试OPC UA动态地址浏览功能
/// </summary>
public class TestDynamicBrowse
{
    /// <summary>
    /// 测试动态地址浏览配置
    /// </summary>
    public static async Task TestConfig()
    {
        Console.WriteLine("=== 测试OPC UA动态地址浏览配置 ===");

        try
        {
            // 创建驱动信息
            var driverInfo = new DriverInfoDto
            {
                DeviceId = 1,
                Identifier = "test-opcua-driver"
            };

            // 创建驱动实例
            var opcuaDriver = new OpcUaCustom(driverInfo);

            // 测试获取地址浏览配置
            var browseConfig = opcuaDriver.GetAddressBrowseConfig();

            Console.WriteLine($"地址输入类型: {browseConfig.InputType}");
            Console.WriteLine($"支持动态浏览: {browseConfig.SupportsDynamicBrowsing}");
            Console.WriteLine($"需要连接: {browseConfig.RequiresConnection}");
            Console.WriteLine($"格式描述: {browseConfig.FormatDescription}");
            Console.WriteLine($"验证模式: {browseConfig.ValidationPattern}");
            Console.WriteLine($"支持搜索: {browseConfig.Searchable}");
            Console.WriteLine($"最大搜索结果: {browseConfig.MaxSearchResults}");
            Console.WriteLine($"最大浏览深度: {browseConfig.MaxBrowseDepth}");

            Console.WriteLine($"\n地址模板数量: {browseConfig.AddressTemplates.Count}");
            foreach (var template in browseConfig.AddressTemplates)
            {
                Console.WriteLine($"  - {template.Category}: {template.Pattern}");
                Console.WriteLine($"    示例: {template.Example}");
                Console.WriteLine($"    描述: {template.Description}");
                Console.WriteLine($"    参数数量: {template.Parameters.Count}");
                
                foreach (var param in template.Parameters)
                {
                    Console.WriteLine($"      * {param.Name} ({param.Type}): {param.Description}");
                    Console.WriteLine($"        默认值: {param.DefaultValue}, 必填: {param.Required}");
                }
            }

            // 测试动态浏览功能（不需要实际连接）
            Console.WriteLine("\n=== 测试动态浏览功能 ===");
            
            // 测试获取根节点（未连接状态）
            Console.WriteLine("\n测试获取根节点（未连接状态）:");
            var rootResult = await opcuaDriver.GetRootNodesAsync();
            Console.WriteLine($"成功: {rootResult.Success}");
            if (!rootResult.Success)
            {
                Console.WriteLine($"错误消息: {rootResult.ErrorMessage}");
            }
            Console.WriteLine($"节点数量: {rootResult.Nodes.Count}");

            // 测试搜索功能（未连接状态）
            Console.WriteLine("\n测试搜索功能（未连接状态）:");
            var searchResult = await opcuaDriver.SearchNodesAsync("Temperature");
            Console.WriteLine($"成功: {searchResult.Success}");
            if (!searchResult.Success)
            {
                Console.WriteLine($"错误消息: {searchResult.ErrorMessage}");
            }
            Console.WriteLine($"节点数量: {searchResult.Nodes.Count}");

            // 测试节点验证功能
            Console.WriteLine("\n测试节点验证功能:");
            var isValid = await opcuaDriver.ValidateNodeAsync("i=2258");
            Console.WriteLine($"节点 'i=2258' 是否有效: {isValid}");

            // 测试获取节点详细信息
            Console.WriteLine("\n测试获取节点详细信息:");
            var nodeDetails = await opcuaDriver.GetNodeDetailsAsync("i=2258");
            if (nodeDetails != null)
            {
                Console.WriteLine($"节点详细信息: {nodeDetails.DisplayName} ({nodeDetails.NodeClass})");
            }
            else
            {
                Console.WriteLine("无法获取节点详细信息（未连接）");
            }

            Console.WriteLine("\n=== 动态地址浏览配置测试完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }

    /// <summary>
    /// 主程序入口
    /// </summary>
    public static async Task Main(string[] args)
    {
        Console.WriteLine("OPC UA 动态地址浏览测试程序");
        Console.WriteLine("===============================");
        
        await TestConfig();
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
