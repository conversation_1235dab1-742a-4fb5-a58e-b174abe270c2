# OPC UA Custom Protocol Driver

## 概述

OPC UA Custom Protocol Driver 是基于 OPC Foundation 官方 .NET Standard 库实现的 OPC UA 协议驱动，完全按照 ModbusTcp 的格式进行统一封装。

## 特性

### 核心功能
- ✅ 基于 OPC Foundation 官方库 (OPCFoundation.NetStandard.Opc.Ua.Client)
- ✅ 支持标准 OPC UA NodeId 地址格式解析
- ✅ 支持批量读取优化
- ✅ 支持多种身份验证方式（匿名、用户名密码）
- ✅ 支持会话保活和自动重连
- ✅ 完整的配置参数支持
- ✅ 集成日志和版本管理

### 支持的地址格式
- `i=2258` - 数值型 NodeId
- `ns=2;i=2258` - 带命名空间的数值型 NodeId
- `s=MyVariable` - 字符串型 NodeId
- `ns=2;s=MyVariable` - 带命名空间的字符串型 NodeId
- `g=09087e75-8e5e-499b-954f-f2a9603db28a` - GUID 型 NodeId
- `b=M/RbKBsRVkePCePcx24oRA==` - ByteString 型 NodeId
- `i=2258[0]` - 数组访问
- `ns=2;s=MyArray[0:5]` - 数组范围访问

### 配置参数

#### 连接配置
- **端点URL**: OPC UA 服务器端点地址
- **安全策略**: 安全策略选择（枚举：无安全策略、Basic128Rsa15、Basic256、Basic256Sha256、Aes128_Sha256_RsaOaep、Aes256_Sha256_RsaPss）
- **消息安全模式**: 消息安全模式（枚举：无安全模式、签名模式、签名并加密模式）
- **身份验证类型**: 用户身份验证类型（枚举：匿名身份验证、用户名密码身份验证、证书身份验证、发行令牌身份验证）
- **用户名**: 用户名（用户名密码验证时）
- **密码**: 密码（用户名密码验证时）
- **会话超时时间**: 会话超时时间（毫秒）
- **操作超时时间**: 操作超时时间（毫秒）

#### 高级配置
- **应用程序名称**: 客户端应用程序名称
- **保活间隔**: 会话保活检测间隔（毫秒）
- **最大保活计数**: 保活失败最大重试次数
- **地址输入类型**: 地址输入方式选择（枚举：文本输入、助手模式、混合模式）

#### 批量读取配置
- **批量读取**: 是否启用批量读取
- **批量读取最大节点数**: 单次批量读取的最大节点数
- **启用自适应分组**: 根据节点类型自动分组优化
- **最大并行度**: 批量读取的最大并行处理数

## 动态地址浏览功能

OPC UA协议支持动态地址浏览，提供更智能的地址选择方式：

### 地址输入方式

1. **文本输入**：传统的手动输入方式，适合熟悉OPC UA NodeId格式的用户
2. **助手模式**：提供地址模板和动态浏览功能，适合新手用户
3. **混合模式**：结合文本输入和助手功能，提供最大的灵活性

### 动态浏览特性

- **实时浏览**：连接到OPC UA服务器后，可以实时浏览服务器的地址空间
- **树形结构**：以树形方式展示节点层级关系
- **节点搜索**：支持按名称搜索节点
- **节点验证**：验证地址是否存在和可访问
- **详细信息**：显示节点的数据类型、访问权限等详细信息

### 地址模板

系统提供5种地址模板，帮助用户快速构建正确的NodeId：

1. **数值型NodeId**: `ns={namespace};i={identifier}`
   - 示例: `ns=2;i=1001`
   - 描述: 使用命名空间索引和数值标识符

2. **字符串型NodeId**: `ns={namespace};s={identifier}`
   - 示例: `ns=2;s=Temperature`
   - 描述: 使用命名空间索引和字符串标识符

3. **GUID型NodeId**: `ns={namespace};g={identifier}`
   - 示例: `ns=2;g=09087e75-8e5e-499b-954f-f2a9603db28a`
   - 描述: 使用命名空间索引和GUID标识符

4. **简化数值型**: `i={identifier}`
   - 示例: `i=2258`
   - 描述: 使用默认命名空间的数值标识符

5. **简化字符串型**: `s={identifier}`
   - 示例: `s=MyVariable`
   - 描述: 使用默认命名空间的字符串标识符

### 使用说明

1. **连接要求**：动态浏览功能需要先连接到OPC UA服务器
2. **权限控制**：只显示当前用户有权限访问的节点
3. **性能优化**：支持懒加载和搜索结果限制
4. **错误处理**：提供详细的错误信息和状态反馈

## 架构设计

### 文件结构
```
06-Protocols/03-Custom/OpcUaCustom/
├── OpcUaCustom.cs                          # 主驱动类
├── OpcUaAddressParserRegistration.cs       # 地址解析器注册
├── OpcUaCustom.csproj                      # 项目配置
└── README.md                               # 说明文档

06-Protocols/EdgeGateway.Driver.Interface/DriverBase/
├── OpcUaBase.cs                            # OPC UA 基础类
└── BatchReading/Core/
    └── OpcUaBatchReadOptimizer.cs          # 批量读取优化器

06-Protocols/EdgeGateway.Driver.Entity/Parsers/
└── OpcUaAddressParser.cs                   # OPC UA 地址解析器
```

### 核心组件

#### 1. OpcUaCustom (主驱动类)
- 继承自 `OpcUaBase`
- 实现 `IDriver` 接口
- 负责 OPC UA 会话管理
- 提供读写操作接口

#### 2. OpcUaBase (基础类)
- 继承自 `PlcProtocolCollector`
- 封装 OPC UA 通用配置参数
- 提供批量读取基础功能

#### 3. OpcUaAddressParser (地址解析器)
- 支持标准 OPC UA NodeId 格式
- 支持数值型、字符串型、GUID 型、ByteString 型标识符
- 支持数组访问语法

#### 4. OpcUaBatchReadOptimizer (批量读取优化器)
- 针对 OPC UA 协议优化批量读取策略
- 支持地址合并和分组
- 提供读取权重计算

## 使用示例

### 基本连接
```csharp
var driverInfo = new DriverInfoDto
{
    // 配置驱动信息
};

var opcuaDriver = new OpcUaCustom(driverInfo)
{
    EndpointUrl = "opc.tcp://localhost:4840",
    UserIdentityType = "Anonymous",
    SessionTimeout = 60000,
    OperationTimeout = 15000
};

var result = opcuaDriver.Connect();
Console.WriteLine(result);
```

### 读取数据
```csharp
var readParams = new List<DriverReadInput>
{
    new DriverReadInput { Address = "ns=2;i=2258", DataType = "int32" },
    new DriverReadInput { Address = "ns=2;s=Temperature", DataType = "float" }
};

var results = await opcuaDriver.Read(readParams);
foreach (var result in results)
{
    Console.WriteLine($"地址: {result.Address}, 值: {result.Value}, 成功: {result.IsSuccess}");
}
```

### 写入数据
```csharp
var writeParams = new List<DriverWriteInput>
{
    new DriverWriteInput { Address = "ns=2;i=2258", Value = 100, DataType = "int32" },
    new DriverWriteInput { Address = "ns=2;s=SetPoint", Value = 25.5, DataType = "float" }
};

var results = await opcuaDriver.Write(writeParams);
foreach (var result in results)
{
    Console.WriteLine($"地址: {result["Address"]}, 成功: {result["IsSuccess"]}");
}
```

## 依赖项

- .NET 9.0
- OPCFoundation.NetStandard.Opc.Ua.Client (1.5.376.235)
- OPCFoundation.NetStandard.Opc.Ua.Core (1.5.376.235)
- EdgeGateway.Driver.Interface

## 版本历史

### v1.0.0 (2025-01-31)
- 实现基于 OPC Foundation 官方库的 OPC UA 客户端功能
- 支持标准 OPC UA NodeId 地址格式解析
- 支持匿名和用户名密码身份验证
- 支持会话保活和自动重连机制
- 支持批量读取优化功能
- 支持多种安全策略和消息安全模式
- 集成地址解析器，支持数值型、字符串型、GUID 型和字节字符串型 NodeId

## 注意事项

1. **证书管理**: 首次运行时会自动创建自签名证书
2. **安全配置**: 默认配置为自动接受不受信任的证书，生产环境请谨慎配置
3. **性能优化**: 建议启用批量读取以提高性能
4. **错误处理**: 所有操作都包含完整的错误处理和日志记录

## 故障排除

### 常见问题

1. **连接失败**
   - 检查端点 URL 是否正确
   - 确认 OPC UA 服务器是否运行
   - 检查网络连接和防火墙设置

2. **身份验证失败**
   - 确认用户名和密码是否正确
   - 检查服务器是否支持所选的身份验证方式

3. **证书问题**
   - 检查证书存储路径权限
   - 确认证书是否已正确生成和信任

4. **读写失败**
   - 验证 NodeId 格式是否正确
   - 检查节点是否存在于服务器中
   - 确认数据类型是否匹配
