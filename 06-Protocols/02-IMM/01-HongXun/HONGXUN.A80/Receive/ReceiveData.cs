using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;

namespace HONGXUN.A80.Receive;

/// <summary>
/// 宏讯注塑机数据接收和解析类
/// 支持网络和串口两种通信方式
/// </summary>
public class ReceiveData : IDisposable
{
    // TCP客户端连接
    private TcpClient _client;
    // 串口连接
    private SerialPort _serialPort;
    // 数据接收缓冲区，8K大小
    private readonly byte[] _receiveBuffer = new byte[8192];
    // 数据处理缓冲区，用于存储待处理的数据
    private readonly List<byte> _processingBuffer = new();
    // 用于数据处理的信号量，确保同时只有一个线程在处理数据
    private readonly SemaphoreSlim _processingSemaphore = new(1, 1);
    // 取消令牌源，用于控制任务的取消
    private readonly CancellationTokenSource _cts = new();
    // 消息通道，用于消息的异步传递
    private readonly Channel<byte[]> _messageChannel;

    // 数据精度
    private const int RoundLength = 1;

    // 连接状态标志
    private bool _isConnected;
    // 连接状态锁对象
    private readonly object _connectionLock = new();
    // 当前通信类型
    private CommunicationType _communicationType;
    // 使用一个统一的字典存储所有类型数据
    private static readonly ConcurrentDictionary<string, object> _sharedData = new();

    /// <summary>
    /// 通信类型枚举
    /// </summary>
    public enum CommunicationType
    {
        None,       // 未连接
        Network,    // 网络连接
        Serial      // 串口连接
    }

    /// <summary>
    /// 连接状态属性
    /// 线程安全的获取和设置连接状态
    /// </summary>
    public bool IsConnected
    {
        get { lock (_connectionLock) { return _isConnected; } }
        private set { lock (_connectionLock) { _isConnected = value; } }
    }

    /// <summary>
    /// 构造函数
    /// 初始化消息通道，设置通道容量和满时的行为
    /// </summary>
    public ReceiveData()
    {
        var options = new BoundedChannelOptions(1000)
        {
            FullMode = BoundedChannelFullMode.Wait // 通道满时等待
        };
        _messageChannel = Channel.CreateBounded<byte[]>(options);
    }

    /// <summary>
    /// 建立网络连接
    /// </summary>
    /// <param name="ip">目标IP地址</param>
    /// <param name="port">目标端口</param>
    public async Task Connect(string ip, int port)
    {
        try
        {
            _client = new TcpClient();
            await _client.ConnectAsync(ip, port);
            _client.ReceiveBufferSize = 8192;
            _client.SendBufferSize = 8192;
            _client.NoDelay = true;

            _communicationType = CommunicationType.Network;
            IsConnected = true;

            // 启动消息处理任务
            _ = Task.Run(ProcessMessagesAsync);
            // 启动数据接收任务
            await ReceiveDataAsync();
        }
        catch (Exception ex)
        {
            IsConnected = false;
            throw new Exception($"网络连接失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 建立串口连接
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="baudRate">波特率</param>
    /// <param name="parity">校验位</param>
    /// <param name="dataBits">数据位</param>
    /// <param name="stopBits">停止位</param>
    public async Task ConnectSerial(string portName, int baudRate = 9600,
        int dataBits = 8, StopBits stopBits = StopBits.One, Parity parity = Parity.None)
    {
        try
        {
            _serialPort = new SerialPort(portName, baudRate, parity, dataBits, stopBits);
            _serialPort.Open();

            _communicationType = CommunicationType.Serial;
            IsConnected = true;

            // 启动消息处理任务
            _ = Task.Run(ProcessMessagesAsync);
            // 启动数据接收任务
            await ReceiveDataAsync();
        }
        catch (Exception ex)
        {
            IsConnected = false;
            throw new Exception($"串口连接失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 数据接收处理
    /// 持续接收数据并将数据添加到处理缓冲区
    /// </summary>
    private async Task ReceiveDataAsync()
    {
        try
        {
            // 循环接收数据，直到取消
            while (!_cts.Token.IsCancellationRequested)
            {
                int bytesRead;
                // 网络连接
                if (_communicationType == CommunicationType.Network)
                {
                    var stream = _client.GetStream();
                    bytesRead = await stream.ReadAsync(_receiveBuffer, 0, _receiveBuffer.Length);
                }
                else // Serial
                {
                    bytesRead = await _serialPort.BaseStream.ReadAsync(_receiveBuffer, 0, _receiveBuffer.Length);
                }

                // 如果未读取到数据，则认为连接已关闭
                if (bytesRead == 0)
                {
                    IsConnected = false;
                    break; // 连接关闭
                }

                // 等待处理缓冲区信号量
                await _processingSemaphore.WaitAsync();
                try
                {
                    // 将接收到的数据添加到处理缓冲区
                    _processingBuffer.AddRange(_receiveBuffer.Take(bytesRead));
                    // 解析消息
                    await ParseMessagesAsync();
                }
                finally
                {
                    // 释放处理缓冲区信号量
                    _processingSemaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            IsConnected = false;
            // 可以在这里添加重连逻辑
        }
    }

    /// <summary>
    /// 解析消息
    /// 从处理缓冲区中提取完整的消息帧
    /// 处理粘包和分包情况
    /// </summary>
    private async Task ParseMessagesAsync()
    {
        // 当前处理位置
        var currentPosition = 0;

        while (currentPosition < _processingBuffer.Count)
        {
            // 至少需要5个字节才能构成一个有效报文头(帧头1 + 长度2 + 命令字2)
            if (_processingBuffer.Count - currentPosition < 5) break;

            // 检查帧头，帧头为0x02
            if (_processingBuffer[currentPosition] != 0x02)
            {
                // 帧头不正确，跳过
                currentPosition++;
                continue;
            }

            // 获取报文长度，规则是：报文长度 = 帧头 + 长度 + 命令字
            int messageLength = BitConverter.ToUInt16(new[] { _processingBuffer[currentPosition + 2], _processingBuffer[currentPosition + 1] }, 0);

            // 检查是否有完整的报文
            if (_processingBuffer.Count - currentPosition < messageLength + 3) // +3是因为帧头和长度字段
                break;

            // 获取命令字，命令字为2个字节，下标为3和4
            byte[] cmdBytes = new[] { _processingBuffer[currentPosition + 3], _processingBuffer[currentPosition + 4] };

            // 提取完整报文，报文长度 = 帧头 + 长度 + 命令字
            var message = _processingBuffer.Skip(currentPosition).Take(messageLength + 3).ToArray();

            // 根据命令字处理报文，命令字合法则添加到消息通道
            if (IsValidCommand(cmdBytes))
            {
                // 添加到消息通道
                await _messageChannel.Writer.WriteAsync(message);
            }

            // 移动到下一个位置，跳过帧头、长度、命令字
            currentPosition += messageLength + 3;
        }

        // 清理已处理的数据
        if (currentPosition > 0)
        {
            // 移除已处理的数据 
            _processingBuffer.RemoveRange(0, currentPosition);
        }
    }

    /// <summary>
    /// 检查命令字是否合法
    /// </summary>
    private bool IsValidCommand(byte[] cmdBytes)
    {
        return (cmdBytes[0], cmdBytes[1]) switch
        {
            (0x33, 0x33) => true, // 设定数据
            (0x45, 0x43) => true, // 品质数据
            (0x43, 0x44) => true, // 实时温度
            _ => false
        };
    }

    /// <summary>
    /// 处理消息
    /// </summary>
    private async Task ProcessMessagesAsync()
    {
        try
        {
            // 循环读取消息通道中的消息
            await foreach (var message in _messageChannel.Reader.ReadAllAsync())
            {
                try
                {
                    // 获取命令字，命令字为2个字节，下标为3和4
                    byte[] cmdBytes = new[] { message[3], message[4] };

                    // 根据命令字处理报文
                    switch ((cmdBytes[0], cmdBytes[1]))
                    {
                        case (0x33, 0x33):
                            ProcessSettingData(message);
                            break;
                        case (0x45, 0x43):
                            ProcessQualityData(message);
                            break;
                        case (0x43, 0x44):
                            ProcessRealTimeTemperature(message);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    // 处理异常但继续处理下一条消息
                }
            }
        }
        catch (Exception ex)
        {
            // 处理消息处理循环异常
        }
    }

    /// <summary>
    /// 处理设定数据
    /// </summary>
    private void ProcessSettingData(byte[] byteData)
    {
        int baseIndex = 5;

        #region 合模设定
        _sharedData[VariableHelper.DataPoints.All["ClosingPressure1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["ClosingPressure2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        baseIndex += 6; // 跳过FF FF
        _sharedData[VariableHelper.DataPoints.All["ClosingPressure3"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["ClosingPressure4"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));

        // 合模速度设定
        _sharedData[VariableHelper.DataPoints.All["ClosingSpeed1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["ClosingSpeed2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["ClosingSpeed3"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["ClosingSpeed4"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        #endregion

        // 跳过中间的FF FF数据块
        baseIndex = 5 + 64; // 调整到开模数据起始位置

        #region 开模设定
        _sharedData[VariableHelper.DataPoints.All["OpeningPressure1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        baseIndex += 4; // 跳过FF FF
        _sharedData[VariableHelper.DataPoints.All["OpeningPressure2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["OpeningPressure3"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        #endregion

        #region 射出设定
        baseIndex = 5 + 360; // 调整到射出数据起始位置

        // 射出压力设定
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure3"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure4"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure5"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure6"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));

        baseIndex += 8; // 跳过FF FF
        // 射出增压设定
        _sharedData[VariableHelper.DataPoints.All["InjectionBoostPressure1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionBoostPressure2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["InjectionBoostPressure3"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionBoostPressure4"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));

        baseIndex += 4; // 调整到射出速度位置
        // 射出速度设定
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed3"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed4"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed5"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed6"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));

        baseIndex += 8; // 调整到射出快速位置
        // 射出快速设定
        _sharedData[VariableHelper.DataPoints.All["InjectionFastSpeed1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionFastSpeed2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["InjectionFastSpeed3"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["InjectionFastSpeed4"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));

        // 射出动作时间（需要乘以0.1）
        const double TIME_FACTOR = 0.1;
        _sharedData[VariableHelper.DataPoints.All["InjectionActionTime"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR, RoundLength);

        baseIndex += 16; // 调整到射出时间位置
        // 射出时间设定（需要乘以0.1）
        _sharedData[VariableHelper.DataPoints.All["InjectionTime1"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["InjectionTime2"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["InjectionTime3"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["InjectionTime4"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2)) * TIME_FACTOR, RoundLength);

        baseIndex += 8; // 调整到射出位置位置
        // 射出位置设定（需要乘以0.1）
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition1"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition2"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition3"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2)) * TIME_FACTOR, RoundLength);
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition4"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition5"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition6"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2)) * TIME_FACTOR, RoundLength);
        #endregion

        #region 温度设定
        baseIndex = 5 + 520; // 调整到温度数据起始位置
        _sharedData[VariableHelper.DataPoints.All["Temperature1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["Temperature2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["Temperature3"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["Temperature4"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["Temperature5"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["Temperature6"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        #endregion

        #region 中子设定
        baseIndex = 5 + 240; // 调整到中子数据起始位置

        // 中子压力设定
        _sharedData[VariableHelper.DataPoints.All["CoreAPressureIn"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["CoreAPressureOut"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["CoreBPressureIn"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["CoreBPressureOut"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["CoreCPressureIn"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["CoreCPressureOut"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));

        baseIndex += 8; // 跳过FF FF
        // 中子速度设定
        _sharedData[VariableHelper.DataPoints.All["CoreASpeedIn"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["CoreASpeedOut"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["CoreBSpeedIn"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["CoreBSpeedOut"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["CoreCSpeedIn"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["CoreCSpeedOut"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));

        // 中子动作时间设定（需要乘以0.1）

        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["CoreATimeIn"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["CoreATimeOut"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["CoreBTimeIn"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["CoreBTimeOut"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2)) * TIME_FACTOR, RoundLength);
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["CoreCTimeIn"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["CoreCTimeOut"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR, RoundLength);
        #endregion

        #region 托模设定
        baseIndex = 5 + 480; // 调整到托模数据起始位置

        // 托模压力设定
        _sharedData[VariableHelper.DataPoints.All["CarriagePressureIn1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["CarriagePressureIn2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["CarriagePressureOut1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));

        // 托模速度设定
        _sharedData[VariableHelper.DataPoints.All["CarriageSpeedIn1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["CarriageSpeedIn2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["CarriageSpeedOut1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));

        // 托模位置设定（需要乘以0.1）
        _sharedData[VariableHelper.DataPoints.All["CarriagePositionIn2"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2)) * TIME_FACTOR, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["CarriagePositionIn1"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2)) * TIME_FACTOR, RoundLength);
        baseIndex += 8;
        _sharedData[VariableHelper.DataPoints.All["CarriagePositionOut1"]] = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2)) * TIME_FACTOR, RoundLength);
        #endregion
    }

    /// <summary>
    /// 处理品质数据
    /// </summary>
    private void ProcessQualityData(byte[] byteData)
    {
        int baseIndex = 5; // 跳过帧头(1)、长度(2)、命令字(2)
        const double TIME_FACTOR = 0.1; // 时间转换因子

        #region 上模相关时间
        // 循环时间
        _sharedData[VariableHelper.DataPoints.All["CycleTime"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2)) * TIME_FACTOR,
            RoundLength
        ); // 096A=2410*0.1

        // 关模时间
        _sharedData[VariableHelper.DataPoints.All["ClampTime"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR,
            RoundLength
        ); // 008B=139*0.1

        // 低压时间
        _sharedData[VariableHelper.DataPoints.All["LowPressureTime"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2)) * TIME_FACTOR,
            RoundLength
        ); // 000B=11*0.1

        // 高压时间
        _sharedData[VariableHelper.DataPoints.All["HighPressureTime"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2)) * TIME_FACTOR,
            RoundLength
        ); // 0011=17*0.1
        #endregion

        baseIndex += 8;

        #region 开模和射出时间
        // 开模时间
        _sharedData[VariableHelper.DataPoints.All["OpenTime"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR,
            RoundLength
        ); // 009D=157*0.1

        // 射出时间
        _sharedData[VariableHelper.DataPoints.All["InjectionTime"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2)) * TIME_FACTOR,
            RoundLength
        ); // 03E8=1000*0.1
        #endregion

        baseIndex += 8;

        #region 其他时间和位置数据
        // 转保时间
        _sharedData[VariableHelper.DataPoints.All["VpSwitchTime"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2)) * TIME_FACTOR,
            RoundLength
        ); // 012C=300*0.1

        // 射出起点
        _sharedData[VariableHelper.DataPoints.All["InjectionStartPosition"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR,
            RoundLength
        ); // 094D=2381*0.1

        // 保压起点
        _sharedData[VariableHelper.DataPoints.All["HoldingStartPosition"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2)) * TIME_FACTOR,
            RoundLength
        ); // 0248=584*0.1
        #endregion

        baseIndex += 8;

        #region 射出监测和储料时间
        // 射出监测
        _sharedData[VariableHelper.DataPoints.All["InjectionMonitor"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2)) * TIME_FACTOR,
            RoundLength
        ); // 01F8=504*0.1

        // 储料时间
        _sharedData[VariableHelper.DataPoints.All["PlastificationTime"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2)) * TIME_FACTOR,
            RoundLength
        ); // 0157=343*0.1
        #endregion
    }

    /// <summary>
    /// 处理实时温度数据
    /// </summary>
    private void ProcessRealTimeTemperature(byte[] byteData)
    {
        int baseIndex = 5; // 跳过帧头(1)、长度(2)、命令字(2)

        #region 实时温度数据
        // 实时温度1-4
        _sharedData[VariableHelper.DataPoints.All["ActualTemperature1"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["ActualTemperature2"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["ActualTemperature3"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["ActualTemperature4"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));

        baseIndex += 8;

        // 实时温度5-8
        _sharedData[VariableHelper.DataPoints.All["ActualTemperature5"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex, 2));
        _sharedData[VariableHelper.DataPoints.All["ActualTemperature6"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 2, 2));
        _sharedData[VariableHelper.DataPoints.All["ActualTemperature7"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 4, 2));
        _sharedData[VariableHelper.DataPoints.All["ActualTemperature8"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, baseIndex + 6, 2));
        #endregion

        baseIndex += 8;

        #region 计数器
        // 注意：累计计数可能需要通过品质数据条数来计算
        _sharedData[VariableHelper.DataPoints.All["TotalCount"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, baseIndex + 2, 4)); // 00000014=20
        #endregion
    }

    /// <summary>
    /// 从字节数组中提取指定范围的数据
    /// </summary>
    /// <param name="byteArray">源字节数组</param>
    /// <param name="startIndex">起始位置</param>
    /// <param name="length">要提取的长度</param>
    /// <returns>提取的字节数组</returns>
    private byte[] GetHexStringFromByteArray(byte[] byteArray, int startIndex, int length)
    {
        var subData = new byte[length];
        Array.Copy(byteArray, startIndex, subData, 0, length);
        return subData;
    }

    /// <summary>
    /// 释放资源
    /// 关闭连接并释放所有使用的资源
    /// </summary>
    public void Dispose()
    {
        // 设置连接状态为false
        IsConnected = false;
        // 取消接收任务
        _cts.Cancel();
        // 释放处理缓冲区信号量
        _processingSemaphore.Dispose();
        // 释放接收任务
        _cts.Dispose();
        // 关闭连接
        if (_communicationType == CommunicationType.Network)
        {
            _client?.Close();
            _client?.Dispose();
        }
        else if (_communicationType == CommunicationType.Serial)
        {
            // 关闭串口 
            _serialPort?.Close();
            // 释放串口
            _serialPort?.Dispose();
        }
    }

    /// <summary>
    /// 批量获取数据
    /// </summary>
    public static Dictionary<string, object> GetValues(params string[] keys)
    {
        return keys.ToDictionary(k => k, k => _sharedData.GetValueOrDefault(k));
    }

    /// <summary>
    /// 更新数据
    /// </summary>
    protected static void UpdateValue(string key, object value)
    {
        _sharedData.AddOrUpdate(key, value, (_, _) => value);
    }
}