using System;
using System.Collections.Concurrent;
using System.Net.Sockets;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Channels;
using System.IO.Ports;
using KEQIANG.T6F3;

namespace T6F3.Receive;

/// <summary>
/// 科强注塑机数据接收和解析类
/// 支持网络和串口两种通信方式
/// </summary>
public class ReceiveData : IDisposable
{
    // TCP客户端连接
    private TcpClient _client;
    // 串口连接
    private SerialPort _serialPort;
    // 数据接收缓冲区，8K大小
    private readonly byte[] _receiveBuffer = new byte[8192];
    // 数据处理缓冲区，用于存储待处理的数据
    private readonly List<byte> _processingBuffer = new();
    // 用于数据处理的信号量，确保同时只有一个线程在处理数据
    private readonly SemaphoreSlim _processingSemaphore = new(1, 1);
    // 取消令牌源，用于控制任务的取消
    private readonly CancellationTokenSource _cts = new();
    // 消息通道，用于消息的异步传递
    private readonly Channel<byte[]> _messageChannel;

    // 数据精度
    private const int RoundLength = 1;
    // 通信帧头标识
    private readonly byte[] _frameHeader = [0xF6, 0x6F];
    // 连接状态标志
    private bool _isConnected;
    // 连接状态锁对象
    private readonly object _connectionLock = new();
    // 当前通信类型
    private CommunicationType _communicationType;
    // 使用一个统一的字典存储所有类型数据
    private static readonly ConcurrentDictionary<string, object> _sharedData = new();

    /// <summary>
    /// 通信类型枚举
    /// </summary>
    public enum CommunicationType
    {
        None,       // 未连接
        Network,    // 网络连接
        Serial      // 串口连接
    }

    /// <summary>
    /// 连接状态属性
    /// 线程安全的获取和设置连接状态
    /// </summary>
    public bool IsConnected
    {
        get { lock (_connectionLock) { return _isConnected; } }
        private set { lock (_connectionLock) { _isConnected = value; } }
    }

    /// <summary>
    /// 构造函数
    /// 初始化消息通道，设置通道容量和满时的行为
    /// </summary>
    public ReceiveData()
    {
        var options = new BoundedChannelOptions(1000)
        {
            FullMode = BoundedChannelFullMode.Wait // 通道满时等待
        };
        _messageChannel = Channel.CreateBounded<byte[]>(options);
    }

    /// <summary>
    /// 建立网络连接
    /// </summary>
    /// <param name="ip">目标IP地址</param>
    /// <param name="port">目标端口</param>
    public async Task Connect(string ip, int port)
    {
        try
        {
            _client = new TcpClient();
            await _client.ConnectAsync(ip, port);
            _client.ReceiveBufferSize = 8192;
            _client.SendBufferSize = 8192;
            _client.NoDelay = true;

            _communicationType = CommunicationType.Network;
            IsConnected = true;

            // 启动消息处理任务
            _ = Task.Run(ProcessMessagesAsync);
            // 启动数据接收任务
            await ReceiveDataAsync();
        }
        catch (Exception ex)
        {
            IsConnected = false;
            throw new Exception($"网络连接失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 建立串口连接
    /// </summary>
    /// <param name="portName">串口名称</param>
    /// <param name="baudRate">波特率</param>
    /// <param name="parity">校验位</param>
    /// <param name="dataBits">数据位</param>
    /// <param name="stopBits">停止位</param>
    public async Task ConnectSerial(string portName, int baudRate = 9600,
        int dataBits = 8, StopBits stopBits = StopBits.One, Parity parity = Parity.None)
    {
        try
        {
            _serialPort = new SerialPort(portName, baudRate, parity, dataBits, stopBits);
            _serialPort.Open();

            _communicationType = CommunicationType.Serial;
            IsConnected = true;

            // 启动消息处理任务
            _ = Task.Run(ProcessMessagesAsync);
            // 启动数据接收任务
            await ReceiveDataAsync();
        }
        catch (Exception ex)
        {
            IsConnected = false;
            throw new Exception($"串口连接失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 数据接收处理
    /// 持续接收数据并将数据添加到处理缓冲区
    /// </summary>
    private async Task ReceiveDataAsync()
    {
        try
        {
            // 循环接收数据，直到取消
            while (!_cts.Token.IsCancellationRequested)
            {
                int bytesRead;
                // 网络连接
                if (_communicationType == CommunicationType.Network)
                {
                    var stream = _client.GetStream();
                    bytesRead = await stream.ReadAsync(_receiveBuffer, 0, _receiveBuffer.Length);
                }
                else // Serial
                {
                    bytesRead = await _serialPort.BaseStream.ReadAsync(_receiveBuffer, 0, _receiveBuffer.Length);
                }

                // 如果未读取到数据，则认为连接已关闭
                if (bytesRead == 0)
                {
                    IsConnected = false;
                    break; // 连接关闭
                }

                // 等待处理缓冲区信号量
                await _processingSemaphore.WaitAsync();
                try
                {
                    // 将接收到的数据添加到处理缓冲区
                    _processingBuffer.AddRange(_receiveBuffer.Take(bytesRead));
                    // 解析消息
                    await ParseMessagesAsync();
                }
                finally
                {
                    // 释放处理缓冲区信号量
                    _processingSemaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            IsConnected = false;
            // 可以在这里添加重连逻辑
        }
    }

    /// <summary>
    /// 解析消息
    /// 从处理缓冲区中提取完整的消息帧
    /// 处理粘包和分包情况
    /// </summary>
    private async Task ParseMessagesAsync()
    {
        var currentPosition = 0;

        while (currentPosition < _processingBuffer.Count - 1)  // 至少需要2字节才能构成帧头
        {
            // 从当前位置开始查找帧头
            var headerIndex = FindFrameHeader(_processingBuffer.ToArray(), currentPosition);
            if (headerIndex == -1)
            {
                // 如果找不到帧头，保留最后一个字节（可能是下一个帧头的开始）
                if (currentPosition > 0)
                {
                    _processingBuffer.RemoveRange(0, currentPosition);
                }
                break;
            }

            // 更新当前位置到帧头
            currentPosition = headerIndex;

            // 寻找下一个帧头
            var nextHeaderIndex = FindFrameHeader(_processingBuffer.ToArray(), currentPosition + _frameHeader.Length);
            if (nextHeaderIndex == -1) break; // 等待更多数据

            // 提取完整消息
            var messageLength = nextHeaderIndex - currentPosition;
            var message = _processingBuffer.Skip(currentPosition).Take(messageLength).ToArray();

            // 发送消息处理通道
            await _messageChannel.Writer.WriteAsync(message);

            // 移动到下一个位置
            currentPosition = nextHeaderIndex;
        }

        // 清理已处理的数据
        if (currentPosition > 0)
        {
            _processingBuffer.RemoveRange(0, currentPosition);
        }
    }

    /// <summary>
    /// 查找帧头位置
    /// </summary>
    /// <param name="buffer">要搜索的数据缓冲区</param>
    /// <param name="startIndex">开始搜索的位置</param>
    /// <returns>帧头位置，未找到返回-1</returns>
    private int FindFrameHeader(byte[] buffer, int startIndex = 0)
    {
        // 搜索帧头 F6 6F
        for (var i = startIndex; i <= buffer.Length - _frameHeader.Length; i++)
            if (buffer[i] == _frameHeader[0] && buffer[i + 1] == _frameHeader[1])
                return i; // 找到帧头，返回索引

        return -1; // 未找到帧头
    }

    /// <summary>
    /// 处理消息
    /// 根据消息类型分发到不同的处理方法
    /// </summary>
    private async Task ProcessMessagesAsync()
    {
        try
        {
            await foreach (var message in _messageChannel.Reader.ReadAllAsync())
            {
                try
                {
                    if (message[2] == 0x02)
                    {
                        ProcessControllerData(message);
                    }
                    else if ((message[9] == 0xFB && message[10] == 0xCA) ||
                             (message[9] == 0xFB && message[10] == 0x29))
                    {
                        if (message.Length >= 1035)
                        {
                            BaseData(message);
                        }
                    }
                    else if (message[3] == 0x05 && message[4] == 0x13 &&
                             message[9] != 0x00 && message[10] != 0x00)
                    {
                        if (message.Length >= 1035)
                        {
                            PowerOnData(message);
                        }
                    }
                    else if (message.Length == 15)
                    {
                        SetValue(message);
                    }
                }
                catch (Exception ex)
                {
                    // 处理单个消息解析异常
                    // 记录日志但继续处理下一条消息
                }
            }
        }
        catch (Exception ex)
        {
            // 处理消息处理循环异常
        }
    }

    /// <summary>
    /// 处理控制器数据
    /// 解析并更新控制器相关的数据字段
    /// </summary>
    /// <param name="byteData">要处理的字节数据</param>
    private void ProcessControllerData(byte[] byteData)
    {
        _sharedData[VariableHelper.DataPoints.All["AnalogToDigital"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 39, 4));
        _sharedData[VariableHelper.DataPoints.All["Pbar"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 67, 2));
        _sharedData[VariableHelper.DataPoints.All["Fbar"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 69, 2));
        _sharedData[VariableHelper.DataPoints.All["BP"]] = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 71, 2));

        _sharedData[VariableHelper.DataPoints.All["CycleTime"]] = Math.Round(
            BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 393, 4)) * 0.1,
            RoundLength
        );

        // ... 其他数据点处理
    }

    /// <summary>
    /// 模座数据
    /// </summary>
    /// <param name="byteData"></param>
    private void BaseData(byte[] byteData)
    {
        #region 合模位置
        _sharedData[VariableHelper.DataPoints.All["ClosingPosition1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 401, 4)) * 0.001, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["ClosingPosition2"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 281, 4)) * 0.001, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["ClosingPosition3"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 293, 4)) * 0.001, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["ClosingPosition4"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 305, 4)) * 0.001, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["ClosingPosition5"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 317, 4)) * 0.001, RoundLength);
        #endregion

        #region 合模压力
        _sharedData[VariableHelper.DataPoints.All["ClosingPressure1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 265, 4));
        _sharedData[VariableHelper.DataPoints.All["ClosingPressure2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 273, 4));
        _sharedData[VariableHelper.DataPoints.All["ClosingPressure3"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 285, 4));
        _sharedData[VariableHelper.DataPoints.All["ClosingPressure4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 297, 4));
        _sharedData[VariableHelper.DataPoints.All["ClosingPressure5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 309, 4));
        #endregion

        #region 合模速度
        _sharedData[VariableHelper.DataPoints.All["ClosingSpeed1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 269, 4));
        _sharedData[VariableHelper.DataPoints.All["ClosingSpeed2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 277, 4));
        _sharedData[VariableHelper.DataPoints.All["ClosingSpeed3"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 289, 4));
        _sharedData[VariableHelper.DataPoints.All["ClosingSpeed4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 301, 4));
        _sharedData[VariableHelper.DataPoints.All["ClosingSpeed5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 313, 4));
        #endregion

        #region 开模压力
        _sharedData[VariableHelper.DataPoints.All["OpeningPressure1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 345, 4));
        _sharedData[VariableHelper.DataPoints.All["OpeningPressure2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 353, 4));
        _sharedData[VariableHelper.DataPoints.All["OpeningPressure3"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 365, 4));
        _sharedData[VariableHelper.DataPoints.All["OpeningPressure4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 377, 4));
        _sharedData[VariableHelper.DataPoints.All["OpeningPressure5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 389, 4));
        #endregion

        #region 开模速度
        _sharedData[VariableHelper.DataPoints.All["OpeningSpeed1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 349, 4));
        _sharedData[VariableHelper.DataPoints.All["OpeningSpeed2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 357, 4));
        _sharedData[VariableHelper.DataPoints.All["OpeningSpeed3"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 369, 4));
        _sharedData[VariableHelper.DataPoints.All["OpeningSpeed4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 381, 4));
        _sharedData[VariableHelper.DataPoints.All["OpeningSpeed5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 393, 4));
        #endregion

        #region 开模位置
        _sharedData[VariableHelper.DataPoints.All["OpeningPosition1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 361, 4)) * 0.001, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["OpeningPosition2"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 373, 4)) * 0.001, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["OpeningPosition3"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 385, 4)) * 0.001, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["OpeningPosition4"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 397, 4)) * 0.001, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["OpeningPosition5"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 401, 4)) * 0.001, RoundLength);
        #endregion

        // 开模行程
        _sharedData[VariableHelper.DataPoints.All["OpeningStroke"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 401, 4));

        #region 注射压力

        _sharedData[VariableHelper.DataPoints.All["InjectionPressure1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 441, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 449, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure3"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 361, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 473, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 485, 5));
        _sharedData[VariableHelper.DataPoints.All["InjectionPressure6"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 497, 4));

        #endregion

        #region 注射速度

        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 445, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 453, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed3"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 465, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 477, 5));
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 489, 5));
        _sharedData[VariableHelper.DataPoints.All["InjectionSpeed6"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 501, 4));

        #endregion

        #region 注射位置

        _sharedData[VariableHelper.DataPoints.All["InjectionPosition1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 457, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 469, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition3"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 481, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 493, 4));
        _sharedData[VariableHelper.DataPoints.All["InjectionPosition5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 505, 4));

        #endregion

        // 射退压力1
        _sharedData[VariableHelper.DataPoints.All["RetractionPressure1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 573, 4));
        // 射退压力2
        _sharedData[VariableHelper.DataPoints.All["RetractionPressure2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 585, 4));
        // 射退速度1
        _sharedData[VariableHelper.DataPoints.All["RetractionSpeed1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 577, 4));
        // 射退速度2
        _sharedData[VariableHelper.DataPoints.All["RetractionSpeed2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 589, 4));
        // 射退时间1
        _sharedData[VariableHelper.DataPoints.All["RetractionTime1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 581, 4));
        // 射退时间2
        _sharedData[VariableHelper.DataPoints.All["RetractionTime2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 593, 4));
        // 储料压力?
        _sharedData[VariableHelper.DataPoints.All["HoldingPressure"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 937, 4));
        // 储料压力4
        _sharedData[VariableHelper.DataPoints.All["HoldingPressure4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 797, 4));
        // 储料压力5
        _sharedData[VariableHelper.DataPoints.All["HoldingPressure5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 781, 4));
        // 储料速度
        _sharedData[VariableHelper.DataPoints.All["HoldingSpeed"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 941, 4));
        // 储料速度4
        _sharedData[VariableHelper.DataPoints.All["HoldingSpeed4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 801, 4));
        // 储料速度5
        _sharedData[VariableHelper.DataPoints.All["HoldingSpeed5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 785, 4));
        _sharedData[VariableHelper.DataPoints.All["HoldingBackPressure5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 789, 4));
        _sharedData[VariableHelper.DataPoints.All["HoldingBackPressure4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 805, 4));
        // 储料位置4
        _sharedData[VariableHelper.DataPoints.All["HoldingPosition4"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 809, 4));
        _sharedData[VariableHelper.DataPoints.All["HoldingPosition5"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 893, 4));


        // 储料距离
        _sharedData[VariableHelper.DataPoints.All["HoldingDistance"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 945, 4));
        _sharedData[VariableHelper.DataPoints.All["TopInPressure1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 949, 4));
        _sharedData[VariableHelper.DataPoints.All["TopInPressure2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1001, 4));

        _sharedData[VariableHelper.DataPoints.All["TopInSpeed1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 997, 4));
        _sharedData[VariableHelper.DataPoints.All["TopInSpeed2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1005, 4));
        // 顶进位置1
        _sharedData[VariableHelper.DataPoints.All["TopInPosition1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1009, 4));
        // 顶进位置2
        _sharedData[VariableHelper.DataPoints.All["TopInPosition2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1013, 4));
        _sharedData[VariableHelper.DataPoints.All["TopOutPressure1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1017, 4));
        _sharedData[VariableHelper.DataPoints.All["TopOutPressure2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1025, 4));
        _sharedData[VariableHelper.DataPoints.All["TopOutSpeed1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1021, 4));
    }

    // 开机的数据 F6 6F 04 05 13
    private void PowerOnData(byte[] byteData)
    {
        _sharedData[VariableHelper.DataPoints.All["TopOutSpeed2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4));
        // 顶退位置1
        _sharedData[VariableHelper.DataPoints.All["TopOutPosition1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 13, 4)) * 0.001, RoundLength);
        // 顶退位置2
        _sharedData[VariableHelper.DataPoints.All["TopOutPosition2"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 17, 4)) * 0.001, RoundLength);
        // 目标产量
        _sharedData[VariableHelper.DataPoints.All["ProductionTarget"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 81, 4));
        _sharedData[VariableHelper.DataPoints.All["BedInPressure1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 109, 4));
        _sharedData[VariableHelper.DataPoints.All["BedInPressure2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 133, 4));
        _sharedData[VariableHelper.DataPoints.All["BedInSpeed1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 113, 4));
        _sharedData[VariableHelper.DataPoints.All["BedInSpeed2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 137, 4));
        _sharedData[VariableHelper.DataPoints.All["BedInTime1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 117, 4));
        _sharedData[VariableHelper.DataPoints.All["BedInTime2"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 129, 4));
        _sharedData[VariableHelper.DataPoints.All["BedInTime3"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 141, 4));
        // entity.BedInPressure3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 133, 4)) ;
        //entity.BedInSpeed3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 137, 4)) ;
        _sharedData[VariableHelper.DataPoints.All["BedOutPressure1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 145, 4));
        _sharedData[VariableHelper.DataPoints.All["BedOutSpeed1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 149, 4));
        _sharedData[VariableHelper.DataPoints.All["BedOutTime1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 169, 4)) * 0.00001, RoundLength);
        _sharedData[VariableHelper.DataPoints.All["MoldOutPressure"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 193, 4));
        _sharedData[VariableHelper.DataPoints.All["MoldOutSpeed"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 197, 4));
        _sharedData[VariableHelper.DataPoints.All["MoldInPressure"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 201, 4));
        _sharedData[VariableHelper.DataPoints.All["MoldInSpeed"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 205, 4));
        _sharedData[VariableHelper.DataPoints.All["MoldInSlowSpeed"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 209, 4));
    }

    // 下写数据
    private void SetValue(byte[] byteData)
    {
        var key = GetHexStringFromByteArray(byteData, 6, 2);


        switch (key[0])
        {
            #region 合模位置

            // 合模位置1   
            case 0x62 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPosition1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;
            // 合模位置2
            case 0x44 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPosition2"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;
            // 合模位置3
            case 0x47 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPosition3"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;
            // 合模位置4
            case 0x4A when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPosition4"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;
            // 合模位置5
            case 0x4D when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPosition5"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;

            #endregion

            #region 合模压力

            // 合模压力1
            case 0x40 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPressure1"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4));
                return;
            // 合模压力2
            case 0x42 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPressure2"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模压力3
            case 0x45 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPressure3"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模压力4
            case 0x48 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPressure4"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模压力5
            case 0x4B when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingPressure5"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            #region 合模速度

            // 合模速度1
            case 0x41 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingSpeed1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模速度2
            case 0x43 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingSpeed2"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模速度3
            case 0x46 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingSpeed3"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模速度4
            case 0x49 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingSpeed4"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模速度5
            case 0x4C when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["ClosingSpeed5"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            #region 开模位置

            // 开模位置4
            case 0x61 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningPosition4"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模位置3
            case 0x5E when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningPosition3"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模位置2
            case 0x5B when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningPosition2"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模位置1
            case 0x58 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningPosition1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            #region 开模压力

            // 开模压力5
            case 0x5F when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningPressure5"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模压力4
            case 0x5C when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningPressure4"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模压力3
            case 0x59 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningPressure3"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模压力2
            case 0x56 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningPressure2"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模压力1
            case 0x54 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningPressure1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            #region 开模速度

            // 开模速度5
            case 0x60 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningSpeed5"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模速度4
            case 0x5D when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningSpeed4"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模速度3
            case 0x5A when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningSpeed3"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模速度2
            case 0x57 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningSpeed2"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模速度1
            case 0x55 when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["OpeningSpeed1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            // 射出一段压力
            case 0x6C when key[1] == 0x00:
                _sharedData[VariableHelper.DataPoints.All["InjectionPressure1"]] = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            // 目标产量
            case 0x12 when key[1] == 0x00:
                {
                    _sharedData[VariableHelper.DataPoints.All["ProductionTarget"]] = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4));
                    return;
                }
        }
    }

    /// <summary>
    /// 从字节数组中提取指定范围的数据
    /// </summary>
    /// <param name="byteArray">源字节数组</param>
    /// <param name="startIndex">起始位置</param>
    /// <param name="length">要提取的长度</param>
    /// <returns>提取的字节数组</returns>
    private byte[] GetHexStringFromByteArray(byte[] byteArray, int startIndex, int length)
    {
        var subData = new byte[length];
        Array.Copy(byteArray, startIndex, subData, 0, length);
        return subData;
    }

    /// <summary>
    /// 释放资源
    /// 关闭连接并释放所有使用的资源
    /// </summary>
    public void Dispose()
    {
        // 设置连接状态为false
        IsConnected = false;
        // 取消接收任务
        _cts.Cancel();
        // 释放处理缓冲区信号量
        _processingSemaphore.Dispose();
        // 释放接收任务
        _cts.Dispose();
        // 关闭连接
        if (_communicationType == CommunicationType.Network)
        {
            _client?.Close();
            _client?.Dispose();
        }
        else if (_communicationType == CommunicationType.Serial)
        {
            // 关闭串口 
            _serialPort?.Close();
            // 释放串口
            _serialPort?.Dispose();
        }
    }

    /// <summary>
    /// 批量获取数据
    /// </summary>
    public static Dictionary<string, object> GetValues(params string[] keys)
    {
        return keys.ToDictionary(k => k, k => _sharedData.GetValueOrDefault(k));
    }

    /// <summary>
    /// 更新数据
    /// </summary>
    protected static void UpdateValue(string key, object value)
    {
        _sharedData.AddOrUpdate(key, value, (_, _) => value);
    }
}