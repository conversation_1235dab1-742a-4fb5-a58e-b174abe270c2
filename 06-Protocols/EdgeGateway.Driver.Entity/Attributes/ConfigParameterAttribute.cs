namespace EdgeGateway.Driver.Entity.Attributes;

/// <summary>
/// </summary>
[AttributeUsage(AttributeTargets.Property)]
public class ConfigParameterAttribute : Attribute
{
    public ConfigParameterAttribute(string configName, string groupName = "连接配置", string description = "", bool display = true,
        bool required = true, string displayExpress = "", short order = 99, string type = "text")
    {
        ConfigName = configName;
        Description = description;
        GroupName = groupName;
        Display = display;
        Required = required;
        DisplayExpress = displayExpress;
        Order = order;
        Type = type;
    }

    /// <summary>
    ///     方法名称
    /// </summary>
    public string ConfigName { get; }

    /// <summary>
    ///     分组名称
    /// </summary>
    public string GroupName { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    ///     是否显示
    /// </summary>
    public bool Display { get; set; }

    /// <summary>
    ///     显示条件表达式
    /// </summary>
    public string DisplayExpress { get; set; }

    /// <summary>
    ///     是否必填
    /// </summary>
    public bool Required { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public short Order { get; set; }

    /// <summary>
    ///     输入框类型
    /// </summary>
    public string Type { get; set; }
}