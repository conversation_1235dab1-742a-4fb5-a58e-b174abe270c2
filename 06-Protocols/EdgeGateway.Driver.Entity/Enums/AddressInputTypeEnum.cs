using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace EdgeGateway.Driver.Entity.Enums;

/// <summary>
/// 地址输入类型枚举
/// </summary>
public enum AddressInputTypeEnum
{
    /// <summary>
    /// 文本输入（传统方式）
    /// </summary>
    [Description("文本输入")]
    [Display(Name = "文本输入")]
    Text = 1,

    /// <summary>
    /// 助手模式（提供地址助手）
    /// </summary>
    [Description("助手模式")]
    [Display(Name = "助手模式")]
    Helper = 2,

    /// <summary>
    /// 混合模式（文本输入 + 助手）
    /// </summary>
    [Description("混合模式")]
    [Display(Name = "混合模式")]
    Hybrid = 3
}
