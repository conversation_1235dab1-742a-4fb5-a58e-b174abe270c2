using EdgeGateway.Driver.Entity.Enums;

namespace EdgeGateway.Driver.Entity.Model;

/// <summary>
/// 地址浏览配置
/// </summary>
public class AddressBrowseConfig
{
    /// <summary>
    /// 地址输入类型
    /// </summary>
    public AddressInputTypeEnum InputType { get; set; } = AddressInputTypeEnum.Text;

    /// <summary>
    /// 是否支持动态浏览
    /// </summary>
    public bool SupportsDynamicBrowsing { get; set; } = false;

    /// <summary>
    /// 是否需要连接后才能浏览
    /// </summary>
    public bool RequiresConnection { get; set; } = true;

    /// <summary>
    /// 地址模板列表（用于输入提示）
    /// </summary>
    public List<AddressTemplate> AddressTemplates { get; set; } = new();

    /// <summary>
    /// 地址验证规则（正则表达式）
    /// </summary>
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// 地址格式说明
    /// </summary>
    public string? FormatDescription { get; set; }

    /// <summary>
    /// 是否支持搜索
    /// </summary>
    public bool Searchable { get; set; } = true;

    /// <summary>
    /// 最大搜索结果数量
    /// </summary>
    public int MaxSearchResults { get; set; } = 100;

    /// <summary>
    /// 最大浏览深度
    /// </summary>
    public int MaxBrowseDepth { get; set; } = 10;
}

/// <summary>
/// 地址模板
/// </summary>
public class AddressTemplate
{
    /// <summary>
    /// 模板分类
    /// </summary>
    public string Category { get; set; } = "";

    /// <summary>
    /// 地址模式
    /// </summary>
    public string Pattern { get; set; } = "";

    /// <summary>
    /// 示例地址
    /// </summary>
    public string Example { get; set; } = "";

    /// <summary>
    /// 描述说明
    /// </summary>
    public string Description { get; set; } = "";

    /// <summary>
    /// 参数说明
    /// </summary>
    public List<TemplateParameter> Parameters { get; set; } = new();
}

/// <summary>
/// 模板参数
/// </summary>
public class TemplateParameter
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; } = "";

    /// <summary>
    /// 参数描述
    /// </summary>
    public string Description { get; set; } = "";

    /// <summary>
    /// 参数类型
    /// </summary>
    public string Type { get; set; } = "string";

    /// <summary>
    /// 默认值
    /// </summary>
    public string? DefaultValue { get; set; }

    /// <summary>
    /// 是否必填
    /// </summary>
    public bool Required { get; set; } = true;
}

/// <summary>
/// 浏览结果
/// </summary>
public class BrowseResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 节点列表
    /// </summary>
    public List<BrowsableNode> Nodes { get; set; } = new();

    /// <summary>
    /// 是否有更多节点
    /// </summary>
    public bool HasMore { get; set; }

    /// <summary>
    /// 总节点数量（用于分页）
    /// </summary>
    public int TotalCount { get; set; }
}

/// <summary>
/// 可浏览的节点信息
/// </summary>
public class BrowsableNode
{
    /// <summary>节点ID</summary>
    public string NodeId { get; set; } = "";

    /// <summary>显示名称</summary>
    public string DisplayName { get; set; } = "";

    /// <summary>描述信息</summary>
    public string? Description { get; set; }

    /// <summary>数据类型</summary>
    public string? DataType { get; set; }

    /// <summary>是否有子节点</summary>
    public bool HasChildren { get; set; }

    /// <summary>节点类别</summary>
    public string NodeClass { get; set; } = ""; // "Variable", "Object", "Method"

    /// <summary>是否可读</summary>
    public bool CanRead { get; set; }

    /// <summary>是否可写</summary>
    public bool CanWrite { get; set; }

    /// <summary>当前值</summary>
    public object? Value { get; set; }

    /// <summary>节点路径（用于显示层级关系）</summary>
    public string? NodePath { get; set; }

    /// <summary>访问级别</summary>
    public byte AccessLevel { get; set; }

    /// <summary>状态码</summary>
    public string? StatusCode { get; set; }
}
