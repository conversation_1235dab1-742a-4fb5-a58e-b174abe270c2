namespace EdgeGateway.Driver.Entity.Model;

/// <summary>
///     协议
/// </summary>
public class PropertyConfiguration
{
    /// <summary>
    /// 
    /// </summary>
    public PropertyConfiguration()
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="code"></param>
    /// <param name="name"></param>
    /// <param name="value"></param>
    /// <param name="defaultValue"></param>
    /// <param name="order"></param>
    /// <param name="type"></param>
    /// <param name="display"></param>
    /// <param name="displayExpress"></param>
    public PropertyConfiguration(string code ,string name,string value,string defaultValue,short order,string type = "select",bool display = true,string displayExpress = "")
    {
        Code = code;
        Name = name;
        Value = value;
        DefaultValue = defaultValue;
        Order = order;
        Type = type;
        Display = display;
        DisplayExpress = displayExpress;
    }

    /// <summary>
    ///     code
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public string Value{ get; set; }

    /// <summary>
    /// 默认值
    /// </summary>
    public string DefaultValue { get; set; }

    /// <summary>
    ///     输入框类型：text;select;
    /// </summary>
    public string Type { get; set; } 

    /// <summary>
    /// 是否显示
    /// </summary>
    public bool Display { get; set; } 

    /// <summary>
    /// 显示条件
    /// </summary>
    public string DisplayExpress { get; set; } 
    
    /// <summary>
    ///     排序
    /// </summary>
    public short Order { get; set; }
}