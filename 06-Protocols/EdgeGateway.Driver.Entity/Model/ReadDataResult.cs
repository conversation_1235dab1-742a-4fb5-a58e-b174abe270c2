using EdgeGateway.Driver.Entity.Const;
using EdgeGateway.Driver.Entity.Enums;

namespace EdgeGateway.Driver.Entity.Model;

/// <summary>
///     读取数据返回
/// </summary>
public class ReadDataResult
{
    /// <summary>
    ///     标识
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    ///     读取时间（毫秒时间戳）
    /// </summary>
    public long Time { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

    /// <summary>
    ///     读取结果类型
    /// </summary>
    public string Status { get; set; } = VariableStatus.VariableStatusGood;

    /// <summary>
    ///     信息
    /// </summary>
    public string? ErrMsg { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public string? DataType { get; set; }
}