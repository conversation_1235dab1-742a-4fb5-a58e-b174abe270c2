namespace EdgeGateway.Driver.Entity.Model;

/// <summary>
/// 智能批量读取诊断信息
/// </summary>
public class SmartBatchDiagnostics
{
  /// <summary>
  /// 总组数
  /// </summary>
  public int TotalGroups { get; set; }

  /// <summary>
  /// 成功组数
  /// </summary>
  public int SuccessfulGroups { get; set; }

  /// <summary>
  /// 失败组数
  /// </summary>
  public int FailedGroups { get; set; }

  /// <summary>
  /// 降级组数
  /// </summary>
  public int DegradedGroups { get; set; }

  /// <summary>
  /// 总分块数
  /// </summary>
  public int TotalChunks { get; set; }

  /// <summary>
  /// 最大分块大小
  /// </summary>
  public int MaxChunkSize { get; set; }

  /// <summary>
  /// 最小分块大小
  /// </summary>
  public int MinChunkSize { get; set; }

  /// <summary>
  /// 平均分块大小
  /// </summary>
  public double AverageChunkSize { get; set; }

  /// <summary>
  /// 总处理时间（毫秒）
  /// </summary>
  public long TotalProcessingTime { get; set; }

  /// <summary>
  /// 按类型统计的错误数
  /// </summary>
  public Dictionary<string, int> ErrorsByType { get; set; } = new Dictionary<string, int>();

  /// <summary>
  /// 性能瓶颈分析
  /// </summary>
  public List<string> IdentifyBottlenecks()
  {
    var bottlenecks = new List<string>();

    if (FailedGroups > SuccessfulGroups * 0.3)
      bottlenecks.Add("高失败率：考虑减小分块大小或增加重试次数");

    if (DegradedGroups > TotalGroups * 0.5)
      bottlenecks.Add("降级读取频繁：设备可能不支持大块读取，考虑减小初始分块大小");

    if (AverageChunkSize < MinChunkSize * 1.5)
      bottlenecks.Add("分块过小：考虑增加初始分块大小以提高效率");

    if (TotalProcessingTime > 1000 && TotalChunks > 100)
      bottlenecks.Add("处理时间过长：考虑增加并行度或优化分组策略");

    return bottlenecks;
  }

  /// <summary>
  /// 重置诊断信息
  /// </summary>
  public void Reset()
  {
    TotalGroups = 0;
    SuccessfulGroups = 0;
    FailedGroups = 0;
    DegradedGroups = 0;
    TotalChunks = 0;
    MaxChunkSize = 0;
    MinChunkSize = int.MaxValue;
    AverageChunkSize = 0;
    TotalProcessingTime = 0;
    ErrorsByType.Clear();
  }
}