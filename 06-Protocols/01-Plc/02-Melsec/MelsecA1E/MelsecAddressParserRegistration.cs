using System;
using System.Collections.Generic;
using EdgeGateway.Driver.Entity.Services;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.Driver.Entity.Interface;
using EdgeGateway.Driver.Interface.DriverBase.AddressParsing;

namespace MelsecA1E;

/// <summary>
/// 三菱PLC地址解析器注册类
/// </summary>
public static class MelsecAddressParserRegistration
{
  /// <summary>
  /// 注册三菱PLC地址解析器
  /// </summary>
  public static void RegisterMelsecAddressParser()
  {
    // 注册到接口层的解析器工厂
    var interfaceFactory = EdgeGateway.Driver.Interface.DriverBase.AddressParsing.AddressParserFactory.Instance;
    interfaceFactory.RegisterParser("melsec", new MelsecAddressParser());

    // 注册到实体层的解析器工厂
    var entityFactory = EdgeGateway.Driver.Entity.Services.AddressParserFactory.Instance;
    entityFactory.RegisterParser(new MelsecAddressParserAdapter());
  }

  /// <summary>
  /// 三菱PLC地址解析器适配器（用于实体层）
  /// </summary>
  private class MelsecAddressParserAdapter : EdgeGateway.Driver.Entity.Parsers.AddressParserBase
  {
    public override string ProtocolName => "Melsec";
    public override int Priority => 70;
    public override string[] SupportedFormats => new[]
    {
            "X0",        // 输入继电器
            "Y0",        // 输出继电器
            "M0",        // 内部继电器
            "L0",        // 锁存继电器
            "S0",        // 步进继电器
            "D0",        // 数据寄存器
            "R0",        // 文件寄存器
            "ZR0",       // 文件寄存器
            "T0",        // 定时器
            "C0"         // 计数器
        };

    public override bool CanParse(string address)
    {
      if (!base.CanParse(address))
        return false;

      // 三菱地址特征
      return (address.StartsWith("X", StringComparison.OrdinalIgnoreCase) && address.Length > 1 && IsHexDigit(address[1])) ||
             (address.StartsWith("Y", StringComparison.OrdinalIgnoreCase) && address.Length > 1 && IsHexDigit(address[1])) ||
             address.StartsWith("M", StringComparison.OrdinalIgnoreCase) ||
             address.StartsWith("L", StringComparison.OrdinalIgnoreCase) ||
             address.StartsWith("S", StringComparison.OrdinalIgnoreCase) ||
             address.StartsWith("D", StringComparison.OrdinalIgnoreCase) ||
             address.StartsWith("R", StringComparison.OrdinalIgnoreCase) ||
             address.StartsWith("ZR", StringComparison.OrdinalIgnoreCase) ||
             address.StartsWith("T", StringComparison.OrdinalIgnoreCase) ||
             address.StartsWith("C", StringComparison.OrdinalIgnoreCase);
    }

    private bool IsHexDigit(char c)
    {
      return (c >= '0' && c <= '9') || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f');
    }

    public override EdgeGateway.Driver.Entity.Model.UniversalAddress Parse(string address, string dataType = null, int? length = null)
    {
      var result = new EdgeGateway.Driver.Entity.Model.UniversalAddress
      {
        RawAddress = address,
        Protocol = "Melsec",
        DataType = dataType,
        Length = length
      };

      try
      {
        // X输入继电器 (例如: X0, X1A)
        if (address.StartsWith("X", StringComparison.OrdinalIgnoreCase))
        {
          string hexValue = address.Substring(1);
          if (int.TryParse(hexValue, System.Globalization.NumberStyles.HexNumber, null, out int number))
          {
            result.AreaType = "X";
            result.BaseAddress = number;
            result.Modifier = hexValue; // 存储十六进制地址值
            result.IsBitAddress = true; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }
        // Y输出继电器 (例如: Y0, Y1A)
        else if (address.StartsWith("Y", StringComparison.OrdinalIgnoreCase))
        {
          string hexValue = address.Substring(1);
          if (int.TryParse(hexValue, System.Globalization.NumberStyles.HexNumber, null, out int number))
          {
            result.AreaType = "Y";
            result.BaseAddress = number;
            result.Modifier = hexValue; // 存储十六进制地址值
            result.IsBitAddress = true; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }
        // M内部继电器 (例如: M0, M100)
        else if (address.StartsWith("M", StringComparison.OrdinalIgnoreCase))
        {
          string numValue = address.Substring(1);
          if (int.TryParse(numValue, out int number))
          {
            result.AreaType = "M";
            result.BaseAddress = number;
            result.IsBitAddress = true; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }
        // L锁存继电器 (例如: L0, L100)
        else if (address.StartsWith("L", StringComparison.OrdinalIgnoreCase))
        {
          string numValue = address.Substring(1);
          if (int.TryParse(numValue, out int number))
          {
            result.AreaType = "L";
            result.BaseAddress = number;
            result.IsBitAddress = true; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }
        // S步进继电器 (例如: S0, S100)
        else if (address.StartsWith("S", StringComparison.OrdinalIgnoreCase))
        {
          string numValue = address.Substring(1);
          if (int.TryParse(numValue, out int number))
          {
            result.AreaType = "S";
            result.BaseAddress = number;
            result.IsBitAddress = true; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }
        // D数据寄存器 (例如: D0, D100)
        else if (address.StartsWith("D", StringComparison.OrdinalIgnoreCase))
        {
          string numValue = address.Substring(1);
          if (int.TryParse(numValue, out int number))
          {
            result.AreaType = "D";
            result.BaseAddress = number;
            result.IsBitAddress = false; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }
        // R文件寄存器 (例如: R0, R100)
        else if (address.StartsWith("R", StringComparison.OrdinalIgnoreCase) && !address.StartsWith("ZR", StringComparison.OrdinalIgnoreCase))
        {
          string numValue = address.Substring(1);
          if (int.TryParse(numValue, out int number))
          {
            result.AreaType = "R";
            result.BaseAddress = number;
            result.IsBitAddress = false; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }
        // ZR文件寄存器 (例如: ZR0, ZR100)
        else if (address.StartsWith("ZR", StringComparison.OrdinalIgnoreCase))
        {
          string numValue = address.Substring(2);
          if (int.TryParse(numValue, out int number))
          {
            result.AreaType = "ZR";
            result.BaseAddress = number;
            result.IsBitAddress = false; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }
        // T定时器 (例如: T0, T100)
        else if (address.StartsWith("T", StringComparison.OrdinalIgnoreCase))
        {
          string numValue = address.Substring(1);
          if (int.TryParse(numValue, out int number))
          {
            result.AreaType = "T";
            result.BaseAddress = number;
            result.IsBitAddress = false; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }
        // C计数器 (例如: C0, C100)
        else if (address.StartsWith("C", StringComparison.OrdinalIgnoreCase))
        {
          string numValue = address.Substring(1);
          if (int.TryParse(numValue, out int number))
          {
            result.AreaType = "C";
            result.BaseAddress = number;
            result.IsBitAddress = false; // 使用IsBitAddress替代CustomProperties
            result.IsValid = true;
            return result;
          }
        }

        result.IsValid = false;
        result.ErrorMessage = $"无效的三菱PLC地址格式: {address}";
        return result;
      }
      catch (Exception ex)
      {
        result.IsValid = false;
        result.ErrorMessage = $"解析三菱PLC地址时发生异常: {ex.Message}";
        return result;
      }
    }

    public override EdgeGateway.Driver.Entity.Interface.AddressValidationResult Validate(EdgeGateway.Driver.Entity.Model.UniversalAddress universalAddress)
    {
      var baseResult = base.Validate(universalAddress);
      if (!baseResult.IsValid)
        return baseResult;

      var warnings = new List<string>();

      // 验证地址范围
      switch (universalAddress.AreaType)
      {
        case "X":
        case "Y":
          if (universalAddress.BaseAddress < 0 || universalAddress.BaseAddress > 0x7FF)
          {
            return EdgeGateway.Driver.Entity.Interface.AddressValidationResult.Failure(
                $"三菱PLC X/Y地址超出范围: {universalAddress.BaseAddress:X} (有效范围: 0-7FF)");
          }
          break;
        case "M":
          if (universalAddress.BaseAddress < 0 || universalAddress.BaseAddress > 8191)
          {
            return EdgeGateway.Driver.Entity.Interface.AddressValidationResult.Failure(
                $"三菱PLC M地址超出范围: {universalAddress.BaseAddress} (有效范围: 0-8191)");
          }
          break;
        case "L":
          if (universalAddress.BaseAddress < 0 || universalAddress.BaseAddress > 8191)
          {
            return EdgeGateway.Driver.Entity.Interface.AddressValidationResult.Failure(
                $"三菱PLC L地址超出范围: {universalAddress.BaseAddress} (有效范围: 0-8191)");
          }
          break;
        case "D":
          if (universalAddress.BaseAddress < 0 || universalAddress.BaseAddress > 8191)
          {
            return EdgeGateway.Driver.Entity.Interface.AddressValidationResult.Failure(
                $"三菱PLC D地址超出范围: {universalAddress.BaseAddress} (有效范围: 0-8191)");
          }
          break;
      }

      return new EdgeGateway.Driver.Entity.Interface.AddressValidationResult
      {
        IsValid = true,
        Warnings = warnings
      };
    }
  }
}