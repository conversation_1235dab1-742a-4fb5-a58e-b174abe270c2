using System.Text.RegularExpressions;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing.Optimizers;

/// <summary>
/// 三菱PLC专用批量读取优化器
/// </summary>
public class MelsecBatchReadOptimizer : DefaultBatchReadOptimizer
{
  /// <summary>
  /// 获取优化器适用的协议名称
  /// </summary>
  public override string ProtocolName => "mitsubishi";

  /// <summary>
  /// 默认分块大小，根据三菱通信规范优化
  /// </summary>
  protected override int DefaultChunkSize => 80;

  /// <summary>
  /// 三菱PLC使用2字节/地址
  /// </summary>
  protected override int DefaultByteMultiplier => 2;

  /// <summary>
  /// 用于匹配位地址的正则表达式
  /// </summary>
  private static readonly Regex BitAddressRegex = new Regex(@"([A-Z]+)(\d+)\.(\d+)", RegexOptions.IgnoreCase | RegexOptions.Compiled);

  /// <summary>
  /// 优化批量读取的地址列表分组
  /// </summary>
  /// <param name="addresses">已解析的地址列表</param>
  /// <returns>优化后的地址分组</returns>
  public override List<List<AddressParseResult>> OptimizeAddressGroups(List<AddressParseResult> addresses)
  {
    if (addresses == null || addresses.Count == 0)
      return new List<List<AddressParseResult>>();

    // 按地址区域分组（如D、W、R等）
    var areaGroups = addresses
        .GroupBy(a => a.Area)
        .Select(g => g.ToList())
        .ToList();

    var result = new List<List<AddressParseResult>>();

    // 对每个区域内的地址进行排序和连续性分组
    foreach (var group in areaGroups)
    {
      if (group.Count == 0)
        continue;

      // 按地址数字排序
      var sortedAddresses = group.OrderBy(a => a.Number).ToList();

      // 连续性分组
      var subGroups = new List<List<AddressParseResult>>();
      var currentGroup = new List<AddressParseResult> { sortedAddresses[0] };

      for (int i = 1; i < sortedAddresses.Count; i++)
      {
        var prevAddress = sortedAddresses[i - 1];
        var currAddress = sortedAddresses[i];

        // 判断是否可以合并到一个批次中
        if (CanCombineAddresses(prevAddress, currAddress))
        {
          currentGroup.Add(currAddress);
        }
        else
        {
          // 不连续，创建新分组
          subGroups.Add(new List<AddressParseResult>(currentGroup));
          currentGroup = new List<AddressParseResult> { currAddress };
        }
      }

      // 添加最后一个分组
      if (currentGroup.Count > 0)
        subGroups.Add(currentGroup);

      result.AddRange(subGroups);
    }

    return result;
  }

  /// <summary>
  /// 计算读取长度，考虑三菱PLC的字节对齐和数据类型要求
  /// </summary>
  /// <param name="minNumber">最小地址数字</param>
  /// <param name="maxNumber">最大地址数字</param>
  /// <param name="dataTypes">数据类型列表</param>
  /// <returns>计算得到的读取长度</returns>
  public override ushort CalculateReadLength(decimal minNumber, decimal maxNumber, List<string> dataTypes)
  {
    // 三菱PLC按字寻址，基础长度就是字数
    var baseDiff = (ushort)Math.Ceiling(maxNumber - minNumber + 1);

    // 如果数据类型列表为空，直接返回基础差值
    if (dataTypes == null || dataTypes.Count == 0)
      return baseDiff;

    // 查找最大地址对应的数据类型
    var maxAddressIndex = dataTypes.Count - 1;
    var maxAddressDataType = dataTypes[maxAddressIndex].ToLower();

    // 根据数据类型调整长度（三菱PLC特定调整）
    switch (maxAddressDataType)
    {
      case "int32":
      case "uint32":
      case "float":
        return (ushort)(baseDiff + 1); // 32位类型需要多读1个字

      case "int64":
      case "uint64":
      case "double":
        return (ushort)(baseDiff + 3); // 64位类型需要多读3个字

      case "string":
        // 三菱字符串通常是按字符数*2计算，再加上字符串长度信息
        return (ushort)(baseDiff + 2);

      default:
        // 其他数据类型不需要特别调整
        return baseDiff;
    }
  }

  /// <summary>
  /// 获取推荐的最大分块大小，根据三菱PLC不同区域特性优化
  /// </summary>
  /// <param name="addressPrefix">地址前缀（如D、W等）</param>
  /// <returns>推荐的分块大小</returns>
  public override int GetRecommendedChunkSize(string addressPrefix)
  {
    if (string.IsNullOrEmpty(addressPrefix))
      return DefaultChunkSize;

    // 数据寄存器区域可以读取较大块
    if (addressPrefix.Equals("D", StringComparison.OrdinalIgnoreCase))
      return 100;

    // 文件寄存器区域可以读取较大块
    if (addressPrefix.Equals("R", StringComparison.OrdinalIgnoreCase))
      return 100;

    // 链接寄存器区域建议较小块
    if (addressPrefix.Equals("W", StringComparison.OrdinalIgnoreCase))
      return 60;

    // 位设备区域建议较小块
    if (addressPrefix.Equals("X", StringComparison.OrdinalIgnoreCase) ||
        addressPrefix.Equals("Y", StringComparison.OrdinalIgnoreCase) ||
        addressPrefix.Equals("M", StringComparison.OrdinalIgnoreCase) ||
        addressPrefix.Equals("B", StringComparison.OrdinalIgnoreCase))
      return 40;

    // 其他区域使用默认值
    return DefaultChunkSize;
  }

  /// <summary>
  /// 获取协议特定的字节倍率
  /// </summary>
  /// <param name="addressPrefix">地址前缀</param>
  /// <returns>字节倍率</returns>
  public override int GetByteMultiplier(string addressPrefix)
  {
    // 三菱PLC按字寻址，每个字地址占用2字节
    return 2;
  }

  /// <summary>
  /// 检查是否支持跨区域批量读取
  /// </summary>
  /// <param name="fromPrefix">起始区域前缀</param>
  /// <param name="toPrefix">目标区域前缀</param>
  /// <returns>是否支持跨区域读取</returns>
  public override bool SupportsCrossAreaReading(string fromPrefix, string toPrefix)
  {
    // 三菱PLC不支持跨区域批量读取
    return string.Equals(fromPrefix, toPrefix, StringComparison.OrdinalIgnoreCase);
  }

  /// <summary>
  /// 判断两个地址是否可以合并成一个批量读取请求
  /// </summary>
  /// <param name="address1">第一个地址</param>
  /// <param name="address2">第二个地址</param>
  /// <param name="maxGap">最大允许的地址间隔</param>
  /// <returns>是否可以合并</returns>
  public override bool CanCombineAddresses(AddressParseResult address1, AddressParseResult address2, int maxGap = 50)
  {
    // 检查前缀是否相同
    if (!string.Equals(address1.Prefix, address2.Prefix, StringComparison.OrdinalIgnoreCase))
      return false;

    // 检查位地址
    if (address1.BitPosition >= 0 || address2.BitPosition >= 0)
    {
      // 如果两者都是位地址，检查是否是同一个字中的不同位
      if (address1.Number == address2.Number)
        return true;
    }

    // 检查地址间隔
    var gap = address2.Number - address1.Number;

    // 三菱PLC对不同区域有不同的最大间隔限制
    var adjustedMaxGap = maxGap;

    // 数据寄存器和文件寄存器可以允许更大的间隔
    if (address1.Prefix.Equals("D", StringComparison.OrdinalIgnoreCase) ||
        address1.Prefix.Equals("R", StringComparison.OrdinalIgnoreCase))
    {
      adjustedMaxGap = 100;
    }

    // 位设备区域建议较小间隔
    if (address1.Prefix.Equals("X", StringComparison.OrdinalIgnoreCase) ||
        address1.Prefix.Equals("Y", StringComparison.OrdinalIgnoreCase) ||
        address1.Prefix.Equals("M", StringComparison.OrdinalIgnoreCase) ||
        address1.Prefix.Equals("B", StringComparison.OrdinalIgnoreCase))
    {
      adjustedMaxGap = 30;
    }

    return gap > 0 && gap <= adjustedMaxGap;
  }
}