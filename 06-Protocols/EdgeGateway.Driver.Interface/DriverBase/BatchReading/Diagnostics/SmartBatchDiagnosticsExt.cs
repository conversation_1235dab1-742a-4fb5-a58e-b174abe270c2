using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.BatchReading.Diagnostics;

/// <summary>
///     智能批量读取诊断信息扩展
/// </summary>
public static class SmartBatchDiagnosticsExt
{
  /// <summary>
  ///     分析性能瓶颈并提供建议
  /// </summary>
  /// <param name="diagnostics">诊断信息</param>
  /// <returns>优化建议列表</returns>
  public static List<string> AnalyzeBottlenecks(this SmartBatchDiagnostics diagnostics)
  {
    var suggestions = new List<string>();

    // 分析失败率
    if (diagnostics.FailedGroups > 0)
    {
      var failureRate = (double)diagnostics.FailedGroups / diagnostics.TotalGroups;
      if (failureRate > 0.3)
        suggestions.Add($"失败率过高 ({failureRate:P2})，建议减小分块大小或增加重试次数");
    }

    // 分析降级率
    if (diagnostics.DegradedGroups > 0)
    {
      var degradationRate = (double)diagnostics.DegradedGroups / diagnostics.TotalGroups;
      if (degradationRate > 0.5)
        suggestions.Add($"降级率过高 ({degradationRate:P2})，建议减小初始分块大小");
    }

    // 分析分块大小
    if (diagnostics.MinChunkSize > 0 && diagnostics.AverageChunkSize > 0)
    {
      var ratio = (double)diagnostics.AverageChunkSize / diagnostics.MinChunkSize;
      if (ratio < 1.5)
        suggestions.Add("平均分块大小接近最小分块大小，建议增加初始分块大小");
    }

    // 分析处理时间
    if (diagnostics.TotalProcessingTime > 1000 && diagnostics.TotalChunks > 100)
      suggestions.Add($"处理时间过长 ({diagnostics.TotalProcessingTime} ms) 且分块数量较多 ({diagnostics.TotalChunks})，建议增加并行度或优化分组策略");

    // 分析错误类型
    if (diagnostics.ErrorsByType?.Count > 0)
    {
      var mostCommonError = diagnostics.ErrorsByType.OrderByDescending(e => e.Value).FirstOrDefault();
      suggestions.Add($"最常见错误类型: {mostCommonError.Key} (出现 {mostCommonError.Value} 次)，建议针对此错误类型优化策略");
    }

    return suggestions;
  }

  /// <summary>
  ///     生成诊断报告
  /// </summary>
  /// <param name="diagnostics">诊断信息</param>
  /// <returns>诊断报告文本</returns>
  public static string GenerateReport(this SmartBatchDiagnostics diagnostics)
  {
    var sb = new StringBuilder();
    sb.AppendLine("智能批量读取诊断报告");
    sb.AppendLine("=======================");
    sb.AppendLine($"总组数: {diagnostics.TotalGroups}");
    sb.AppendLine($"成功组数: {diagnostics.SuccessfulGroups}");
    sb.AppendLine($"失败组数: {diagnostics.FailedGroups}");
    sb.AppendLine($"降级组数: {diagnostics.DegradedGroups}");
    sb.AppendLine($"总分块数: {diagnostics.TotalChunks}");
    sb.AppendLine($"最大分块大小: {diagnostics.MaxChunkSize}");
    sb.AppendLine($"最小分块大小: {diagnostics.MinChunkSize}");
    sb.AppendLine($"平均分块大小: {diagnostics.AverageChunkSize}");
    sb.AppendLine($"总处理时间: {diagnostics.TotalProcessingTime} ms");

    if (diagnostics.ErrorsByType?.Count > 0)
    {
      sb.AppendLine("\n错误类型统计:");
      foreach (var error in diagnostics.ErrorsByType.OrderByDescending(e => e.Value))
      {
        sb.AppendLine($"- {error.Key}: {error.Value} 次");
      }
    }

    var bottlenecks = diagnostics.AnalyzeBottlenecks();
    if (bottlenecks.Count > 0)
    {
      sb.AppendLine("\n性能优化建议:");
      foreach (var suggestion in bottlenecks)
      {
        sb.AppendLine($"- {suggestion}");
      }
    }

    return sb.ToString();
  }
}