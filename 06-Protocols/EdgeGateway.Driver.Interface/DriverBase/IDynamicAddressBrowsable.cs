using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase;

/// <summary>
/// 支持动态地址浏览的驱动接口
/// </summary>
public interface IDynamicAddressBrowsable
{
    /// <summary>
    /// 是否支持动态浏览
    /// </summary>
    bool SupportsDynamicBrowsing { get; }

    /// <summary>
    /// 获取根节点
    /// </summary>
    /// <returns>浏览结果</returns>
    Task<BrowseResult> GetRootNodesAsync();

    /// <summary>
    /// 获取子节点
    /// </summary>
    /// <param name="parentNodeId">父节点ID</param>
    /// <returns>浏览结果</returns>
    Task<BrowseResult> GetChildNodesAsync(string parentNodeId);

    /// <summary>
    /// 搜索节点
    /// </summary>
    /// <param name="searchText">搜索文本</param>
    /// <param name="maxResults">最大结果数量</param>
    /// <returns>浏览结果</returns>
    Task<BrowseResult> SearchNodesAsync(string searchText, int maxResults = 100);

    /// <summary>
    /// 获取节点详细信息
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点详细信息</returns>
    Task<BrowsableNode?> GetNodeDetailsAsync(string nodeId);

    /// <summary>
    /// 验证节点是否存在
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>是否存在</returns>
    Task<bool> ValidateNodeAsync(string nodeId);
}
