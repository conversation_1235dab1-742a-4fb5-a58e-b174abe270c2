using EdgeGateway.Driver.Entity.Attributes;
using EdgeGateway.Driver.Entity.Enums;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.Driver.Interface.DriverBase.AddressParsing.Optimizers;
using HslCommunication;

namespace EdgeGateway.Driver.Interface.DriverBase;

/// <summary>
/// OPC UA协议公共父类，封装通用属性与批量读取接口
/// </summary>
public abstract class OpcUaBase : PlcProtocolCollector, IDynamicAddressBrowsable
{
    #region 连接配置

    /// <summary>
    /// OPC UA服务器端点URL
    /// </summary>
    [ConfigParameter("端点URL", GroupName = "连接配置", Order = 1, Description = "OPC UA服务器的端点地址")]
    public virtual string EndpointUrl { get; set; } = "opc.tcp://localhost:4840";

    /// <summary>
    /// 安全策略
    /// </summary>
    [ConfigParameter("安全策略", GroupName = "连接配置", Order = 2, Type = "select", Description = "OPC UA通信安全策略")]
    public virtual OpcUaSecurityPolicyEnum SecurityPolicy { get; set; } = OpcUaSecurityPolicyEnum.None;

    /// <summary>
    /// 消息安全模式
    /// </summary>
    [ConfigParameter("消息安全模式", GroupName = "连接配置", Order = 3, Type = "select", Description = "消息传输安全模式")]
    public virtual OpcUaMessageSecurityModeEnum MessageSecurityMode { get; set; } = OpcUaMessageSecurityModeEnum.None;

    /// <summary>
    /// 用户身份验证类型
    /// </summary>
    [ConfigParameter("身份验证类型", GroupName = "连接配置", Order = 4, Type = "select", Description = "用户身份验证方式")]
    public virtual OpcUaUserIdentityTypeEnum UserIdentityType { get; set; } = OpcUaUserIdentityTypeEnum.Anonymous;

    /// <summary>
    /// 用户名（当身份验证类型为用户名密码时使用）
    /// </summary>
    [ConfigParameter("用户名", GroupName = "连接配置", Order = 5, Description = "用户名密码验证时的用户名")]
    public virtual string Username { get; set; } = "";

    /// <summary>
    /// 密码（当身份验证类型为用户名密码时使用）
    /// </summary>
    [ConfigParameter("密码", GroupName = "连接配置", Order = 6, Type = "password", Description = "用户名密码验证时的密码")]
    public virtual string Password { get; set; } = "";

    /// <summary>
    /// 会话超时时间（毫秒）
    /// </summary>
    [ConfigParameter("会话超时时间(毫秒)", GroupName = "连接配置", Order = 7, Type = "number", Description = "OPC UA会话超时时间")]
    public virtual uint SessionTimeout { get; set; } = 60000;

    /// <summary>
    /// 操作超时时间（毫秒）
    /// </summary>
    [ConfigParameter("操作超时时间(毫秒)", GroupName = "连接配置", Order = 8, Type = "number", Description = "单次操作超时时间")]
    public virtual int OperationTimeout { get; set; } = 15000;

    #endregion

    #region 高级配置

    /// <summary>
    /// 应用程序名称
    /// </summary>
    [ConfigParameter("应用程序名称", GroupName = "高级配置", Order = 1, Description = "OPC UA客户端应用程序名称")]
    public virtual string ApplicationName { get; set; } = "EdgeGateway OPC UA Client";

    /// <summary>
    /// 保活间隔（毫秒）
    /// </summary>
    [ConfigParameter("保活间隔(毫秒)", GroupName = "高级配置", Order = 2, Type = "number", Description = "会话保活检测间隔")]
    public virtual uint KeepAliveInterval { get; set; } = 5000;

    /// <summary>
    /// 最大保活计数
    /// </summary>
    [ConfigParameter("最大保活计数", GroupName = "高级配置", Order = 3, Type = "number", Description = "保活失败最大重试次数")]
    public virtual uint MaxKeepAliveCount { get; set; } = 3;

    /// <summary>
    /// 地址输入类型
    /// </summary>
    [ConfigParameter("地址输入类型", GroupName = "高级配置", Order = 4, Type = "select", Description = "地址输入方式选择")]
    public virtual AddressInputTypeEnum AddressInputType { get; set; } = AddressInputTypeEnum.Helper;

    #endregion

    #region 动态地址浏览

    /// <summary>
    /// 是否支持动态浏览
    /// </summary>
    public virtual bool SupportsDynamicBrowsing => true;

    #endregion

    #region 批量读取配置

    /// <summary>
    /// 批量读取
    /// </summary>
    [ConfigParameter("批量读取", GroupName = "批量读取配置", Order = 1, Type = "boolean", Description = "启用批量读取以提高性能")]
    public bool CombinedRead { get; set; } = true;

    /// <summary>
    /// 批量读取最大节点数
    /// </summary>
    [ConfigParameter("批量读取最大节点数", GroupName = "批量读取配置", Order = 2, Type = "number", Description = "单次批量读取的最大节点数量")]
    public int MaxNodesPerRead { get; set; } = 1000;

    /// <summary>
    /// 启用自适应分组
    /// </summary>
    [ConfigParameter("启用自适应分组", GroupName = "批量读取配置", Order = 3, Type = "boolean", Description = "根据节点类型自动分组优化")]
    public bool EnableAdaptiveGrouping { get; set; } = true;

    /// <summary>
    /// 最大并行度
    /// </summary>
    [ConfigParameter("最大并行度", GroupName = "批量读取配置", Order = 4, Type = "number", Description = "批量读取的最大并行处理数")]
    public int MaxParallelism { get; set; } = 4;

    #endregion

    /// <summary>
    /// 获取OPC UA批量读取优化器
    /// </summary>
    /// <returns>OPC UA优化器实例</returns>
    protected override IBatchReadOptimizer GetBatchReadOptimizer()
    {
        return new OpcUaBatchReadOptimizer();
    }

    /// <summary>
    /// 批量读取实现
    /// </summary>
    /// <param name="paramList">参数列表</param>
    /// <returns>读取结果</returns>
    public virtual async Task<List<ReadDataResult>> ReadBatchAsync(List<DriverReadInput> paramList)
    {
        // 使用智能批量读取功能
        var options = new SmartBatchOptions
        {
            ProcessingMode = CombinedRead ? EdgeGateway.Driver.Entity.Model.BatchProcessingMode.BatchedParallel : EdgeGateway.Driver.Entity.Model.BatchProcessingMode.Sequential,
            InitialChunkSize = MaxNodesPerRead,
            MaxRetries = 2,
            EnableAdaptiveGrouping = EnableAdaptiveGrouping,
            MaxParallelism = MaxParallelism,
            EnableDegradedReading = true
        };

        // 执行批量读取
        return await ReadWithSmartBatch(paramList, options);
    }

    #region 地址浏览配置

    /// <summary>
    /// 获取地址浏览配置
    /// </summary>
    /// <returns>地址浏览配置</returns>
    public virtual AddressBrowseConfig GetAddressBrowseConfig()
    {
        return new AddressBrowseConfig
        {
            InputType = AddressInputType,
            SupportsDynamicBrowsing = SupportsDynamicBrowsing,
            RequiresConnection = true,
            FormatDescription = "OPC UA NodeId格式：ns=命名空间索引;标识符类型=标识符值",
            ValidationPattern = @"^(ns=\d+;)?[isgb]=.+$",
            AddressTemplates = GetDefaultAddressTemplates(),
            Searchable = true,
            MaxSearchResults = 100,
            MaxBrowseDepth = 10
        };
    }

    /// <summary>
    /// 获取默认地址模板
    /// </summary>
    /// <returns>地址模板列表</returns>
    protected virtual List<AddressTemplate> GetDefaultAddressTemplates()
    {
        return new List<AddressTemplate>
        {
            new AddressTemplate
            {
                Category = "数值型NodeId",
                Pattern = "ns={namespace};i={identifier}",
                Example = "ns=2;i=1001",
                Description = "使用命名空间索引和数值标识符",
                Parameters = new List<TemplateParameter>
                {
                    new TemplateParameter { Name = "namespace", Description = "命名空间索引", Type = "number", DefaultValue = "2" },
                    new TemplateParameter { Name = "identifier", Description = "数值标识符", Type = "number", DefaultValue = "1001" }
                }
            },
            new AddressTemplate
            {
                Category = "字符串型NodeId",
                Pattern = "ns={namespace};s={identifier}",
                Example = "ns=2;s=Temperature",
                Description = "使用命名空间索引和字符串标识符",
                Parameters = new List<TemplateParameter>
                {
                    new TemplateParameter { Name = "namespace", Description = "命名空间索引", Type = "number", DefaultValue = "2" },
                    new TemplateParameter { Name = "identifier", Description = "字符串标识符", Type = "string", DefaultValue = "Temperature" }
                }
            },
            new AddressTemplate
            {
                Category = "GUID型NodeId",
                Pattern = "ns={namespace};g={identifier}",
                Example = "ns=2;g=09087e75-8e5e-499b-954f-f2a9603db28a",
                Description = "使用命名空间索引和GUID标识符",
                Parameters = new List<TemplateParameter>
                {
                    new TemplateParameter { Name = "namespace", Description = "命名空间索引", Type = "number", DefaultValue = "2" },
                    new TemplateParameter { Name = "identifier", Description = "GUID标识符", Type = "string", DefaultValue = "09087e75-8e5e-499b-954f-f2a9603db28a" }
                }
            },
            new AddressTemplate
            {
                Category = "简化数值型",
                Pattern = "i={identifier}",
                Example = "i=2258",
                Description = "使用默认命名空间的数值标识符",
                Parameters = new List<TemplateParameter>
                {
                    new TemplateParameter { Name = "identifier", Description = "数值标识符", Type = "number", DefaultValue = "2258" }
                }
            },
            new AddressTemplate
            {
                Category = "简化字符串型",
                Pattern = "s={identifier}",
                Example = "s=MyVariable",
                Description = "使用默认命名空间的字符串标识符",
                Parameters = new List<TemplateParameter>
                {
                    new TemplateParameter { Name = "identifier", Description = "字符串标识符", Type = "string", DefaultValue = "MyVariable" }
                }
            }
        };
    }

    /// <summary>
    /// 获取根节点 - 子类必须实现
    /// </summary>
    /// <returns>浏览结果</returns>
    public abstract Task<EdgeGateway.Driver.Entity.Model.BrowseResult> GetRootNodesAsync();

    /// <summary>
    /// 获取子节点 - 子类必须实现
    /// </summary>
    /// <param name="parentNodeId">父节点ID</param>
    /// <returns>浏览结果</returns>
    public abstract Task<EdgeGateway.Driver.Entity.Model.BrowseResult> GetChildNodesAsync(string parentNodeId);

    /// <summary>
    /// 搜索节点 - 默认实现
    /// </summary>
    /// <param name="searchText">搜索文本</param>
    /// <param name="maxResults">最大结果数量</param>
    /// <returns>浏览结果</returns>
    public virtual Task<EdgeGateway.Driver.Entity.Model.BrowseResult> SearchNodesAsync(string searchText, int maxResults = 100)
    {
        return Task.FromResult(new EdgeGateway.Driver.Entity.Model.BrowseResult
        {
            Success = false,
            ErrorMessage = "搜索功能未实现"
        });
    }

    /// <summary>
    /// 获取节点详细信息 - 默认实现
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点详细信息</returns>
    public virtual Task<BrowsableNode?> GetNodeDetailsAsync(string nodeId)
    {
        return Task.FromResult<BrowsableNode?>(null);
    }

    /// <summary>
    /// 验证节点是否存在 - 默认实现
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>是否存在</returns>
    public virtual Task<bool> ValidateNodeAsync(string nodeId)
    {
        return Task.FromResult(false);
    }

    #endregion
}
