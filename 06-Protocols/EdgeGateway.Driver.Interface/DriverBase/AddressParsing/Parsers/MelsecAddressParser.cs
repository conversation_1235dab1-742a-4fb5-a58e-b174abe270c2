using System.Text.RegularExpressions;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing;

/// <summary>
/// 三菱PLC地址解析器
/// </summary>
public class MelsecAddressParser : IAddressParser
{
  // 三菱PLC地址格式的正则表达式
  private static readonly Regex XRegex = new(@"^X([0-9A-F]+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex YRegex = new(@"^Y([0-9A-F]+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex MRegex = new(@"^M(\d+)$", RegexOptions.Compiled);
  private static readonly Regex LRegex = new(@"^L(\d+)$", RegexOptions.Compiled);
  private static readonly Regex SRegex = new(@"^S(\d+)$", RegexOptions.Compiled);
  private static readonly Regex DRegex = new(@"^D(\d+)$", RegexOptions.Compiled);
  private static readonly Regex RRegex = new(@"^R(\d+)$", RegexOptions.Compiled);
  private static readonly Regex ZRRegex = new(@"^ZR(\d+)$", RegexOptions.Compiled);
  private static readonly Regex TRegex = new(@"^T(\d+)$", RegexOptions.Compiled);
  private static readonly Regex CRegex = new(@"^C(\d+)$", RegexOptions.Compiled);

  /// <summary>
  /// 解析单个三菱PLC地址
  /// </summary>
  public AddressParseResult Parse(string address, string dataType, int? length = null)
  {
    if (string.IsNullOrEmpty(address))
    {
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = "地址为空"
      };
    }

    try
    {
      // X输入继电器 (例如: X0, X1A)
      var xMatch = XRegex.Match(address);
      if (xMatch.Success)
      {
        string hexValue = xMatch.Groups[1].Value;
        decimal number = Convert.ToInt32(hexValue, 16);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "X",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "X",
          AddressType = "X",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "HexAddress", hexValue },
                        { "IsBitAddress", true }
                    }
        };
      }

      // Y输出继电器 (例如: Y0, Y1A)
      var yMatch = YRegex.Match(address);
      if (yMatch.Success)
      {
        string hexValue = yMatch.Groups[1].Value;
        decimal number = Convert.ToInt32(hexValue, 16);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "Y",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "Y",
          AddressType = "Y",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "HexAddress", hexValue },
                        { "IsBitAddress", true }
                    }
        };
      }

      // M内部继电器 (例如: M0, M100)
      var mMatch = MRegex.Match(address);
      if (mMatch.Success)
      {
        decimal number = decimal.Parse(mMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "M",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "M",
          AddressType = "M",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "IsBitAddress", true }
                    }
        };
      }

      // L锁存继电器 (例如: L0, L100)
      var lMatch = LRegex.Match(address);
      if (lMatch.Success)
      {
        decimal number = decimal.Parse(lMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "L",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "L",
          AddressType = "L",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "IsBitAddress", true }
                    }
        };
      }

      // S步进继电器 (例如: S0, S100)
      var sMatch = SRegex.Match(address);
      if (sMatch.Success)
      {
        decimal number = decimal.Parse(sMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "S",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "S",
          AddressType = "S",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "IsBitAddress", true }
                    }
        };
      }

      // D数据寄存器 (例如: D0, D100)
      var dMatch = DRegex.Match(address);
      if (dMatch.Success)
      {
        decimal number = decimal.Parse(dMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "D",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "D",
          AddressType = "D",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "IsWordAddress", true }
                    }
        };
      }

      // R文件寄存器 (例如: R0, R100)
      var rMatch = RRegex.Match(address);
      if (rMatch.Success)
      {
        decimal number = decimal.Parse(rMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "R",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "R",
          AddressType = "R",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "IsWordAddress", true }
                    }
        };
      }

      // ZR文件寄存器 (例如: ZR0, ZR100)
      var zrMatch = ZRRegex.Match(address);
      if (zrMatch.Success)
      {
        decimal number = decimal.Parse(zrMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "ZR",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "ZR",
          AddressType = "ZR",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "IsWordAddress", true }
                    }
        };
      }

      // T定时器 (例如: T0, T100)
      var tMatch = TRegex.Match(address);
      if (tMatch.Success)
      {
        decimal number = decimal.Parse(tMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "T",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "T",
          AddressType = "T",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "IsWordAddress", true }
                    }
        };
      }

      // C计数器 (例如: C0, C100)
      var cMatch = CRegex.Match(address);
      if (cMatch.Success)
      {
        decimal number = decimal.Parse(cMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "C",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "C",
          AddressType = "C",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "IsWordAddress", true }
                    }
        };
      }

      // 不支持的地址格式
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = $"不支持的三菱PLC地址格式: {address}"
      };
    }
    catch (Exception ex)
    {
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = $"解析三菱PLC地址时发生异常: {ex.Message}"
      };
    }
  }

  /// <summary>
  /// 批量解析三菱PLC地址
  /// </summary>
  public List<AddressParseResult> ParseBatch(List<(string Address, string DataType, int? Length)> addresses)
  {
    var results = new List<AddressParseResult>();
    foreach (var (address, dataType, length) in addresses)
    {
      results.Add(Parse(address, dataType, length));
    }
    return results;
  }

  /// <summary>
  /// 优化批量读取
  /// </summary>
  public List<List<AddressParseResult>> OptimizeForBatch(List<AddressParseResult> addresses)
  {
    var result = new List<List<AddressParseResult>>();

    // 按区域分组
    var groups = addresses
        .Where(a => a.IsValid)
        .GroupBy(a => a.Area)
        .ToList();

    foreach (var group in groups)
    {
      // 按地址排序
      var sortedAddresses = group.OrderBy(a => a.Number).ToList();

      // 创建子组
      var currentBatch = new List<AddressParseResult>();
      decimal lastAddress = -1;
      const int maxGap = 100; // 最大地址间隔

      foreach (var address in sortedAddresses)
      {
        // 如果是第一个地址或者与上一个地址间隔在允许范围内，则加入当前批次
        if (lastAddress == -1 || address.Number - lastAddress <= maxGap)
        {
          currentBatch.Add(address);
        }
        else
        {
          // 否则创建新的批次
          if (currentBatch.Any())
            result.Add(currentBatch);
          currentBatch = new List<AddressParseResult> { address };
        }

        lastAddress = address.Number;
      }

      // 添加最后一个批次
      if (currentBatch.Any())
        result.Add(currentBatch);
    }

    return result;
  }
}