using System.Text.RegularExpressions;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing;

/// <summary>
/// 松下PLC地址解析器
/// </summary>
public class PanasonicAddressParser : IAddressParser
{
  // 松下PLC地址格式的正则表达式
  private static readonly Regex XRegex = new(@"^X(\d+)(?:\.(\d+))?$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex YRegex = new(@"^Y(\d+)(?:\.(\d+))?$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex RRegex = new(@"^R(\d+)(?:\.(\d+))?$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex DRegex = new(@"^D(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex LRegex = new(@"^L(\d+)(?:\.(\d+))?$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex TRegex = new(@"^T(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex CRegex = new(@"^C(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);

  /// <summary>
  /// 解析单个松下PLC地址
  /// </summary>
  public AddressParseResult Parse(string address, string dataType, int? length = null)
  {
    if (string.IsNullOrEmpty(address))
    {
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = "地址为空"
      };
    }

    try
    {
      // X输入继电器
      var xMatch = XRegex.Match(address);
      if (xMatch.Success)
      {
        decimal byteOffset = decimal.Parse(xMatch.Groups[1].Value);
        int bitPosition = xMatch.Groups.Count > 2 && !string.IsNullOrEmpty(xMatch.Groups[2].Value)
            ? int.Parse(xMatch.Groups[2].Value)
            : -1;

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "X",
          Number = byteOffset,
          BitPosition = bitPosition,
          DataType = dataType,
          Length = length,
          Area = "X",
          AddressType = bitPosition >= 0 ? "X" : "B",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "ByteOffset", byteOffset },
                        { "IsBitAddress", bitPosition >= 0 }
                    }
        };
      }

      // Y输出继电器
      var yMatch = YRegex.Match(address);
      if (yMatch.Success)
      {
        decimal byteOffset = decimal.Parse(yMatch.Groups[1].Value);
        int bitPosition = yMatch.Groups.Count > 2 && !string.IsNullOrEmpty(yMatch.Groups[2].Value)
            ? int.Parse(yMatch.Groups[2].Value)
            : -1;

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "Y",
          Number = byteOffset,
          BitPosition = bitPosition,
          DataType = dataType,
          Length = length,
          Area = "Y",
          AddressType = bitPosition >= 0 ? "X" : "B",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "ByteOffset", byteOffset },
                        { "IsBitAddress", bitPosition >= 0 }
                    }
        };
      }

      // R内部继电器
      var rMatch = RRegex.Match(address);
      if (rMatch.Success)
      {
        decimal byteOffset = decimal.Parse(rMatch.Groups[1].Value);
        int bitPosition = rMatch.Groups.Count > 2 && !string.IsNullOrEmpty(rMatch.Groups[2].Value)
            ? int.Parse(rMatch.Groups[2].Value)
            : -1;

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "R",
          Number = byteOffset,
          BitPosition = bitPosition,
          DataType = dataType,
          Length = length,
          Area = "R",
          AddressType = bitPosition >= 0 ? "X" : "B",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "ByteOffset", byteOffset },
                        { "IsBitAddress", bitPosition >= 0 }
                    }
        };
      }

      // D数据寄存器
      var dMatch = DRegex.Match(address);
      if (dMatch.Success)
      {
        decimal number = decimal.Parse(dMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "D",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "D",
          AddressType = "D",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "RegisterOffset", number },
                        { "IsWordAddress", true }
                    }
        };
      }

      // L锁存继电器
      var lMatch = LRegex.Match(address);
      if (lMatch.Success)
      {
        decimal byteOffset = decimal.Parse(lMatch.Groups[1].Value);
        int bitPosition = lMatch.Groups.Count > 2 && !string.IsNullOrEmpty(lMatch.Groups[2].Value)
            ? int.Parse(lMatch.Groups[2].Value)
            : -1;

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "L",
          Number = byteOffset,
          BitPosition = bitPosition,
          DataType = dataType,
          Length = length,
          Area = "L",
          AddressType = bitPosition >= 0 ? "X" : "B",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "ByteOffset", byteOffset },
                        { "IsBitAddress", bitPosition >= 0 }
                    }
        };
      }

      // T定时器
      var tMatch = TRegex.Match(address);
      if (tMatch.Success)
      {
        decimal number = decimal.Parse(tMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "T",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "T",
          AddressType = "T",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "TimerNumber", number }
                    }
        };
      }

      // C计数器
      var cMatch = CRegex.Match(address);
      if (cMatch.Success)
      {
        decimal number = decimal.Parse(cMatch.Groups[1].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "C",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "C",
          AddressType = "C",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "CounterNumber", number }
                    }
        };
      }

      // 不支持的地址格式
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = $"不支持的松下PLC地址格式: {address}"
      };
    }
    catch (Exception ex)
    {
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = $"解析松下PLC地址时发生异常: {ex.Message}"
      };
    }
  }

  /// <summary>
  /// 批量解析松下PLC地址
  /// </summary>
  public List<AddressParseResult> ParseBatch(List<(string Address, string DataType, int? Length)> addresses)
  {
    var results = new List<AddressParseResult>();
    foreach (var (address, dataType, length) in addresses)
    {
      results.Add(Parse(address, dataType, length));
    }
    return results;
  }

  /// <summary>
  /// 优化批量读取
  /// </summary>
  public List<List<AddressParseResult>> OptimizeForBatch(List<AddressParseResult> addresses)
  {
    var result = new List<List<AddressParseResult>>();

    // 按区域分组
    var groups = addresses
        .Where(a => a.IsValid)
        .GroupBy(a => a.Area)
        .ToList();

    foreach (var group in groups)
    {
      // 按地址排序
      var sortedAddresses = group.OrderBy(a => a.Number).ToList();

      // 创建子组
      var currentBatch = new List<AddressParseResult>();
      decimal lastAddress = -1;
      const int maxGap = 10; // 最大地址间隔

      foreach (var address in sortedAddresses)
      {
        // 如果是第一个地址或者与上一个地址间隔在允许范围内，则加入当前批次
        if (lastAddress == -1 || address.Number - lastAddress <= maxGap)
        {
          currentBatch.Add(address);
        }
        else
        {
          // 否则创建新的批次
          if (currentBatch.Any())
            result.Add(currentBatch);
          currentBatch = new List<AddressParseResult> { address };
        }

        lastAddress = address.Number;
      }

      // 添加最后一个批次
      if (currentBatch.Any())
        result.Add(currentBatch);
    }

    return result;
  }
}