using System.Text.RegularExpressions;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing;

/// <summary>
/// 三菱PLC地址解析器
/// </summary>
public class MitsubishiAddressParser : IAddressParser
{
  // 三菱PLC地址格式的正则表达式
  private static readonly Regex DRegex = new(@"^D(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex WRegex = new(@"^W(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex RRegex = new(@"^R(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex ZRRegex = new(@"^ZR(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex MRegex = new(@"^M(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex XRegex = new(@"^X(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
  private static readonly Regex YRegex = new(@"^Y(\d+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);

  /// <summary>
  /// 解析单个三菱PLC地址
  /// </summary>
  public AddressParseResult Parse(string address, string dataType, int? length = null)
  {
    if (string.IsNullOrEmpty(address))
    {
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = "地址为空"
      };
    }

    try
    {
      // 数据寄存器D
      var dMatch = DRegex.Match(address);
      if (dMatch.Success)
      {
        decimal number = decimal.Parse(dMatch.Groups[1].Value);
        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "D",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "D",
          AddressType = "Word"
        };
      }

      // 链接寄存器W
      var wMatch = WRegex.Match(address);
      if (wMatch.Success)
      {
        decimal number = decimal.Parse(wMatch.Groups[1].Value);
        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "W",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "W",
          AddressType = "Word"
        };
      }

      // 文件寄存器R
      var rMatch = RRegex.Match(address);
      if (rMatch.Success)
      {
        decimal number = decimal.Parse(rMatch.Groups[1].Value);
        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "R",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "R",
          AddressType = "Word"
        };
      }

      // 文件寄存器ZR
      var zrMatch = ZRRegex.Match(address);
      if (zrMatch.Success)
      {
        decimal number = decimal.Parse(zrMatch.Groups[1].Value);
        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "ZR",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "ZR",
          AddressType = "Word"
        };
      }

      // 辅助继电器M
      var mMatch = MRegex.Match(address);
      if (mMatch.Success)
      {
        decimal number = decimal.Parse(mMatch.Groups[1].Value);
        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "M",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "M",
          AddressType = "Bit"
        };
      }

      // 输入X
      var xMatch = XRegex.Match(address);
      if (xMatch.Success)
      {
        decimal number = decimal.Parse(xMatch.Groups[1].Value);
        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "X",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "X",
          AddressType = "Bit"
        };
      }

      // 输出Y
      var yMatch = YRegex.Match(address);
      if (yMatch.Success)
      {
        decimal number = decimal.Parse(yMatch.Groups[1].Value);
        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "Y",
          Number = number,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "Y",
          AddressType = "Bit"
        };
      }

      // 尝试通用解析
      var defaultParser = new DefaultAddressParser();
      return defaultParser.Parse(address, dataType, length);
    }
    catch (Exception ex)
    {
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = $"解析三菱地址异常: {ex.Message}"
      };
    }
  }

  /// <summary>
  /// 批量解析三菱PLC地址
  /// </summary>
  public List<AddressParseResult> ParseBatch(List<(string Address, string DataType, int? Length)> addresses)
  {
    var results = new List<AddressParseResult>();

    foreach (var (address, dataType, length) in addresses)
    {
      var result = Parse(address, dataType, length);
      results.Add(result);
    }

    return results;
  }

  /// <summary>
  /// 优化三菱PLC地址分组
  /// </summary>
  public List<List<AddressParseResult>> OptimizeForBatch(List<AddressParseResult> addresses)
  {
    var result = new List<List<AddressParseResult>>();

    // 按区域和地址类型分组
    var groups = addresses
        .Where(a => a.IsValid)
        .GroupBy(a => new { a.Area, a.AddressType })
        .Select(g => g.ToList())
        .ToList();

    foreach (var group in groups)
    {
      if (!group.Any())
        continue;

      // 对每个组内地址进行排序
      var sortedAddresses = group.OrderBy(a => a.Number).ToList();

      // 分批处理
      var currentBatch = new List<AddressParseResult>();
      const int MAX_BATCH_SIZE = 120; // 单次请求最大点数
      var currentBatchSize = 0;

      foreach (var address in sortedAddresses)
      {
        // 获取当前地址的数据类型大小（字/位）
        int addressSize = CalculateAddressSize(address);

        if (currentBatch.Count == 0 || currentBatchSize + addressSize <= MAX_BATCH_SIZE)
        {
          // 可以添加到当前批次
          currentBatch.Add(address);
          currentBatchSize += addressSize;
        }
        else
        {
          // 创建新批次
          result.Add(currentBatch);
          currentBatch = new List<AddressParseResult> { address };
          currentBatchSize = addressSize;
        }
      }

      // 添加最后一个批次
      if (currentBatch.Any())
        result.Add(currentBatch);
    }

    return result;
  }

  /// <summary>
  /// 计算地址大小（以点数计算）
  /// </summary>
  private int CalculateAddressSize(AddressParseResult address)
  {
    if (address.AddressType == "Bit")
      return 1; // 位地址占用1点

    // 字地址根据数据类型计算点数
    return address.DataType?.ToLower() switch
    {
      "int16" or "uint16" => 1,
      "int32" or "uint32" or "float" => 2,
      "int64" or "uint64" or "double" => 4,
      "string" => address.Length.GetValueOrDefault(10),
      _ => 1
    };
  }
}