using EdgeGateway.Alarm.Entity;
using EdgeGateway.Alarm.Entity.Model;

namespace EdgeGateway.Alarm.Services;

/// <summary>
///     报警通知服务接口
/// </summary>
public interface IAlarmNotificationService
{
  /// <summary>
  ///     发送报警通知
  /// </summary>
  /// <param name="alarm">报警记录</param>
  /// <param name="action">报警动作</param>
  /// <returns>是否成功</returns>
  Task<bool> SendNotificationAsync(AlarmRecord alarm, AlarmAction action);

  /// <summary>
  ///     获取支持的通知类型列表
  /// </summary>
  /// <returns>通知类型列表</returns>
  List<string> GetSupportedNotificationTypes();
}