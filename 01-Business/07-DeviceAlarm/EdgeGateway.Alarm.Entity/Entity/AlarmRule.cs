using SqlSugar;
using EdgeGateway.Alarm.Entity.Model;
using EdgeGateway.SqlSugar.Entity;

namespace EdgeGateway.Alarm.Entity;

/// <summary>
///     报警规则实体
/// </summary>
[SugarTable("alarm_rule", "报警规则")]
public class AlarmRule : EntityBaseId
{
  /// <summary>
  ///     规则名称
  /// </summary>
  [SugarColumn(ColumnDescription = "规则名称", Length = 128)]
  public string Name { get; set; }

  /// <summary>
  ///     设备ID
  /// </summary>
  [SugarColumn(ColumnDescription = "设备ID", Length = 64)]
  public string DeviceId { get; set; }

  /// <summary>
  ///     标签标识符列表
  /// </summary>
  [SugarColumn(ColumnDescription = "标签标识符列表", IsJson = true, ColumnDataType = "longtext,text,clob")]
  public List<string> Identifiers { get; set; } = new List<string>();

  /// <summary>
  ///     条件表达式
  /// </summary>
  [SugarColumn(ColumnDescription = "条件表达式", ColumnDataType = "longtext,text,clob")]
  public string Expression { get; set; }

  /// <summary>
  ///     报警级别
  /// </summary>
  [SugarColumn(ColumnDescription = "报警级别", Length = 4)]
  public int Severity { get; set; } = 3; // 默认为一般级别

  /// <summary>
  ///     描述
  /// </summary>
  [SugarColumn(ColumnDescription = "描述", Length = 512)]
  public string Description { get; set; }

  /// <summary>
  ///     是否启用
  /// </summary>
  [SugarColumn(ColumnDescription = "是否启用")]
  public bool IsEnabled { get; set; } = true;

  /// <summary>
  ///     报警动作
  /// </summary>
  [SugarColumn(ColumnDescription = "报警动作", IsJson = true, ColumnDataType = "longtext,text,clob")]
  public List<AlarmAction> Actions { get; set; } = new List<AlarmAction>();

  /// <summary>
  ///     创建时间
  /// </summary>
  [SugarColumn(ColumnDescription = "创建时间")]
  public DateTime CreateTime { get; set; } = DateTime.Now;

  /// <summary>
  ///     更新时间
  /// </summary>
  [SugarColumn(ColumnDescription = "更新时间")]
  public DateTime UpdateTime { get; set; } = DateTime.Now;
}