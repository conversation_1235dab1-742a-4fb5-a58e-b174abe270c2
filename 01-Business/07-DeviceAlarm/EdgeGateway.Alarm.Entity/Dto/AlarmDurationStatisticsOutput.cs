namespace EdgeGateway.Alarm.Entity.Dto;

/// <summary>
///     报警持续时间统计输出
/// </summary>
public class AlarmDurationStatisticsOutput
{
  /// <summary>
  ///     平均报警持续时间（分钟）
  /// </summary>
  public double AverageDuration { get; set; }

  /// <summary>
  ///     最长报警持续时间（分钟）
  /// </summary>
  public double MaxDuration { get; set; }

  /// <summary>
  ///     最短报警持续时间（分钟）
  /// </summary>
  public double MinDuration { get; set; }

  /// <summary>
  ///     按持续时间范围分组的报警数量
  /// </summary>
  public Dictionary<string, int> AlarmsByDuration { get; set; } = new();

  /// <summary>
  ///     开始时间
  /// </summary>
  public DateTime StartTime { get; set; }

  /// <summary>
  ///     结束时间
  /// </summary>
  public DateTime EndTime { get; set; }
}