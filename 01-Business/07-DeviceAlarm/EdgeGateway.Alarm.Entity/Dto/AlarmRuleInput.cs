using EdgeGateway.Alarm.Entity.Model;

namespace EdgeGateway.Alarm.Entity.Dto;

/// <summary>
///     报警规则输入DTO
/// </summary>
public class AlarmRuleInput
{
  /// <summary>
  ///     规则名称
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  ///     设备ID
  /// </summary>
  public string DeviceId { get; set; }

  /// <summary>
  ///     标签标识符列表
  /// </summary>
  public List<string> Identifiers { get; set; } = new List<string>();

  /// <summary>
  ///     条件表达式
  /// </summary>
  public string Expression { get; set; }

  /// <summary>
  ///     报警级别
  /// </summary>
  public int Severity { get; set; } = 3;

  /// <summary>
  ///     描述
  /// </summary>
  public string Description { get; set; }

  /// <summary>
  ///     是否启用
  /// </summary>
  public bool IsEnabled { get; set; } = true;

  /// <summary>
  ///     报警动作
  /// </summary>
  public List<AlarmAction> Actions { get; set; } = new List<AlarmAction>();
}