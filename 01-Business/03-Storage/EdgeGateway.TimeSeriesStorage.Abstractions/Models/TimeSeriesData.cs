namespace EdgeGateway.TimeSeriesStorage.Abstractions.Models;

/// <summary>
///     时序数据
/// </summary>
public class TimeSeriesData
{
  /// <summary>
  ///     设备ID
  /// </summary>
  public string DeviceId { get; set; }

  /// <summary>
  ///     时间戳
  /// </summary>
  public DateTime Timestamp { get; set; }

  /// <summary>
  ///     额外标签数据
  ///     key: 标签名称
  ///     value: 标签值
  /// </summary>
  public Dictionary<string, object> ExtraTags { get; set; } = new();
}