using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace EdgeGateway.Forwarding.Entity;

/// <summary>
/// 转发失败记录
/// </summary>
[SugarTable("forward_failure_record")]
public class ForwardFailureRecord : EntityBase
{
  /// <summary>
  /// 转发配置ID
  /// </summary>
  [SugarColumn(ColumnDescription = "转发配置ID")]
  public long ForwardConfigId { get; set; }

  /// <summary>
  /// 数据内容
  /// </summary>
  [SugarColumn(ColumnDescription = "数据内容", ColumnDataType = "longtext,text,clob")]
  public string Data { get; set; }

  /// <summary>
  /// 失败原因
  /// </summary>
  [SugarColumn(ColumnDescription = "失败原因", ColumnDataType = "longtext,text,clob")]
  public string? FailureReason { get; set; }

  /// <summary>
  /// 重试次数
  /// </summary>
  [SugarColumn(ColumnDescription = "重试次数")]
  public int RetryCount { get; set; }

  /// <summary>
  /// 下次重试时间
  /// </summary>
  [SugarColumn(ColumnDescription = "下次重试时间")]
  public DateTime? NextRetryTime { get; set; }

  /// <summary>
  /// 状态(0:待重试,1:重试中)
  /// </summary>
  [SugarColumn(ColumnDescription = "状态")]
  public ForwardFailureRecordStatus Status { get; set; }

  #region 忽略字段

  /// <summary>
  /// 协议类型
  /// </summary>
  [SugarColumn(IsIgnore = true)]
  public string? ProtocolType => ForwardConfig?.Type.ToString();

  /// <summary>
  /// 转发配置名称
  /// </summary>
  [SugarColumn(IsIgnore = true)]
  public string? ForwardConfigName => ForwardConfig?.Name;

  #endregion

  #region 导航属性

  /// <summary>
  /// 转发配置
  /// </summary>
  [Navigate(NavigateType.OneToOne, nameof(ForwardConfigId))]
  [SugarColumn(IsIgnore = true)]
  [System.Text.Json.Serialization.JsonIgnore]
  [Newtonsoft.Json.JsonIgnore]
  public ForwardConfig? ForwardConfig { get; set; }

  #endregion
}

/// <summary>
/// 转发失败记录状态
/// </summary>
public enum ForwardFailureRecordStatus
{
  /// <summary>
  /// 待重试
  /// </summary>
  Pending = 0,

  /// <summary>
  /// 重试中
  /// </summary>
  Retrying = 1,
}
