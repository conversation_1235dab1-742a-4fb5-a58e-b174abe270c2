using System.Collections.Concurrent;
using System.Diagnostics;
using System.Globalization;
using EdgeGateway.Forwarding.Statistics.Models;
using Microsoft.Extensions.Logging;

namespace EdgeGateway.Forwarding.Statistics.Services;

/// <summary>
/// 转发统计收集器
/// 使用高性能内存数据结构进行实时统计收集
/// </summary>
public class ForwardStatisticsCollector
{
  private readonly ILogger<ForwardStatisticsCollector> _logger;

  /// <summary>
  /// 统计数据存储
  /// Key: ConfigId（0表示全局统计）
  /// </summary>
  private readonly ConcurrentDictionary<long, ForwardStatisticsData> _statisticsData = new();

  /// <summary>
  /// 响应时间样本环形缓冲区
  /// </summary>
  private readonly ConcurrentDictionary<long, CircularBuffer<double>> _latencyBuffers = new();

  /// <summary>
  /// 时间窗口数据
  /// Key: ConfigId_TimeSlot
  /// </summary>
  private readonly ConcurrentDictionary<string, TimeWindowData> _timeWindows = new();

  /// <summary>
  /// 最大响应时间样本数量
  /// </summary>
  private const int MAX_LATENCY_SAMPLES = 1000;

  /// <summary>
  /// 时间窗口大小（秒）
  /// </summary>
  private const int TIME_WINDOW_SECONDS = 60;

  /// <summary>
  /// 构造函数
  /// </summary>
  public ForwardStatisticsCollector(ILogger<ForwardStatisticsCollector> logger)
  {
    _logger = logger;
  }

  /// <summary>
  /// 记录消息发送成功
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小（字节）</param>
  /// <param name="latencyMs">延迟时间（毫秒）</param>
  public void RecordSuccess(long configId, long dataSize, double latencyMs)
  {
    try
    {
      var stats = GetOrCreateStatistics(configId);

      // 原子操作更新基础计数
      Interlocked.Increment(ref stats.TotalMessages);
      Interlocked.Increment(ref stats.SuccessMessages);
      Interlocked.Add(ref stats.TotalBytes, dataSize);

      // 记录延迟
      RecordLatency(configId, latencyMs);

      // 更新时间窗口数据
      UpdateTimeWindow(configId, 1, 0, dataSize, latencyMs);

      _logger.LogDebug("记录成功消息: ConfigId={ConfigId}, Size={Size}, Latency={Latency}ms",
          configId, dataSize, latencyMs);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录成功消息时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 记录消息发送失败
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小（字节）</param>
  /// <param name="errorType">错误类型</param>
  /// <param name="latencyMs">延迟时间（毫秒）</param>
  public void RecordFailure(long configId, long dataSize, ForwardErrorType errorType, double latencyMs = 0)
  {
    try
    {
      var stats = GetOrCreateStatistics(configId);

      // 原子操作更新基础计数
      Interlocked.Increment(ref stats.TotalMessages);
      Interlocked.Increment(ref stats.FailedMessages);
      Interlocked.Add(ref stats.TotalBytes, dataSize);

      // 记录错误类型
      stats.ErrorCounts.AddOrUpdate(errorType, 1, (key, value) => value + 1);

      // 记录延迟（如果有）
      if (latencyMs > 0)
      {
        RecordLatency(configId, latencyMs);
      }

      // 更新时间窗口数据
      UpdateTimeWindow(configId, 0, 1, dataSize, latencyMs);

      _logger.LogDebug("记录失败消息: ConfigId={ConfigId}, ErrorType={ErrorType}, Size={Size}",
          configId, errorType, dataSize);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录失败消息时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 记录重试消息
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小（字节）</param>
  public void RecordRetry(long configId, long dataSize)
  {
    try
    {
      var stats = GetOrCreateStatistics(configId);
      Interlocked.Increment(ref stats.RetryMessages);

      _logger.LogDebug("记录重试消息: ConfigId={ConfigId}, Size={Size}", configId, dataSize);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录重试消息时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 记录完成的转发（成功后删除记录）
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小（字节）</param>
  public void RecordCompletion(long configId, long dataSize)
  {
    try
    {
      var stats = GetOrCreateStatistics(configId);
      Interlocked.Increment(ref stats.CompletedMessages);

      _logger.LogDebug("记录完成消息: ConfigId={ConfigId}, Size={Size}", configId, dataSize);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录完成消息时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 记录离线消息
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="count">离线消息数量</param>
  public void RecordOfflineMessages(long configId, long count)
  {
    try
    {
      var stats = GetOrCreateStatistics(configId);
      Interlocked.Exchange(ref stats.OfflineMessages, count);

      _logger.LogDebug("更新离线消息数: ConfigId={ConfigId}, Count={Count}", configId, count);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录离线消息时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 获取统计数据
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <returns>统计数据</returns>
  public ForwardStatistics GetStatistics(long configId)
  {
    try
    {
      var stats = GetOrCreateStatistics(configId);
      var latencyMetrics = CalculateLatencyMetrics(configId);
      var realTimeMetrics = CalculateRealTimeMetrics(configId);
      var timeSeries = GetTimeSeriesData(configId);

      return new ForwardStatistics
      {
        ConfigId = configId,
        Timestamp = DateTime.Now,
        Metrics = new ForwardMetrics
        {
          TotalMessages = stats.TotalMessages,
          SuccessMessages = stats.SuccessMessages,
          FailedMessages = stats.FailedMessages,
          RetryMessages = stats.RetryMessages,
          CompletedMessages = stats.CompletedMessages,
          OfflineMessages = stats.OfflineMessages,
          TotalBytes = stats.TotalBytes,
          TodayGrowth = CalculateTodayGrowth(configId),
          GrowthRate = CalculateGrowthRate(configId)
        },
        RealTimeMetrics = realTimeMetrics,
        ErrorCounts = new Dictionary<ForwardErrorType, long>(stats.ErrorCounts),
        LatencyMetrics = latencyMetrics,
        TimeSeries = timeSeries
      };
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取统计数据时发生错误: ConfigId={ConfigId}", configId);
      return new ForwardStatistics { ConfigId = configId };
    }
  }

  /// <summary>
  /// 获取全局统计数据
  /// </summary>
  /// <returns>全局统计数据</returns>
  public ForwardStatistics GetGlobalStatistics()
  {
    return GetStatistics(0);
  }

  /// <summary>
  /// 清理过期数据
  /// </summary>
  public void CleanupExpiredData()
  {
    try
    {
      var cutoffTime = DateTime.Now.AddHours(-24);
      var expiredKeys = _timeWindows
          .Where(kvp => ParseTimeSlot(kvp.Key).timestamp < cutoffTime)
          .Select(kvp => kvp.Key)
          .ToList();

      foreach (var key in expiredKeys)
      {
        _timeWindows.TryRemove(key, out _);
      }

      _logger.LogDebug("清理过期时间窗口数据，清理数量: {Count}", expiredKeys.Count);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "清理过期数据时发生错误");
    }
  }

  #region 私有方法

  /// <summary>
  /// 获取或创建统计数据
  /// </summary>
  private ForwardStatisticsData GetOrCreateStatistics(long configId)
  {
    return _statisticsData.GetOrAdd(configId, _ => new ForwardStatisticsData());
  }

  /// <summary>
  /// 记录延迟数据
  /// </summary>
  private void RecordLatency(long configId, double latencyMs)
  {
    var buffer = _latencyBuffers.GetOrAdd(configId, _ => new CircularBuffer<double>(MAX_LATENCY_SAMPLES));
    buffer.Add(latencyMs);
  }

  /// <summary>
  /// 计算延迟指标
  /// </summary>
  private ForwardLatencyMetrics CalculateLatencyMetrics(long configId)
  {
    if (!_latencyBuffers.TryGetValue(configId, out var buffer) || buffer.Count == 0)
    {
      return new ForwardLatencyMetrics();
    }

    var samples = buffer.ToArray();
    Array.Sort(samples);

    return new ForwardLatencyMetrics
    {
      AverageLatency = samples.Average(),
      MinLatency = samples.Min(),
      MaxLatency = samples.Max(),
      P50Latency = GetPercentile(samples, 0.5),
      P90Latency = GetPercentile(samples, 0.9),
      P99Latency = GetPercentile(samples, 0.99)
    };
  }

  /// <summary>
  /// 计算实时指标
  /// </summary>
  private ForwardRealTimeMetrics CalculateRealTimeMetrics(long configId)
  {
    var recentData = GetRecentTimeWindows(configId, 5); // 最近5分钟
    if (!recentData.Any()) return new ForwardRealTimeMetrics();

    var totalMessages = recentData.Sum(d => d.MessageCount);
    var totalBytes = recentData.Sum(d => d.DataBytes);
    var timeSpanMinutes = Math.Max(recentData.Count, 1);

    return new ForwardRealTimeMetrics
    {
      MessagesPerSecond = totalMessages / (timeSpanMinutes * 60.0),
      BytesPerSecond = totalBytes / (timeSpanMinutes * 60.0),
      ActiveConnections = GetActiveConnections(configId),
      QueueLength = GetQueueLength(configId)
    };
  }

  /// <summary>
  /// 更新时间窗口数据
  /// </summary>
  private void UpdateTimeWindow(long configId, int successCount, int failCount, long dataBytes, double latency)
  {
    var timeSlot = GetCurrentTimeSlot();
    var key = $"{configId}_{timeSlot:yyyyMMddHHmm}";

    _timeWindows.AddOrUpdate(key,
        new TimeWindowData
        {
          Timestamp = timeSlot,
          MessageCount = successCount + failCount,
          SuccessCount = successCount,
          FailCount = failCount,
          DataBytes = dataBytes,
          TotalLatency = latency,
          SampleCount = latency > 0 ? 1 : 0
        },
        (_, existing) =>
        {
          existing.MessageCount += successCount + failCount;
          existing.SuccessCount += successCount;
          existing.FailCount += failCount;
          existing.DataBytes += dataBytes;
          if (latency > 0)
          {
            existing.TotalLatency += latency;
            existing.SampleCount++;
          }
          return existing;
        });
  }

  /// <summary>
  /// 获取时间序列数据
  /// </summary>
  private List<ForwardTimeSeriesPoint> GetTimeSeriesData(long configId)
  {
    var recentData = GetRecentTimeWindows(configId, 60); // 最近60分钟
    return recentData.Select(data => new ForwardTimeSeriesPoint
    {
      Timestamp = data.Timestamp,
      MessageCount = data.MessageCount,
      SuccessCount = data.SuccessCount,
      FailCount = data.FailCount,
      AvgLatency = data.SampleCount > 0 ? data.TotalLatency / data.SampleCount : 0,
      DataBytes = data.DataBytes
    }).OrderBy(p => p.Timestamp).ToList();
  }

  /// <summary>
  /// 获取最近的时间窗口数据
  /// </summary>
  private List<TimeWindowData> GetRecentTimeWindows(long configId, int minutes)
  {
    var cutoffTime = DateTime.Now.AddMinutes(-minutes);
    return _timeWindows
        .Where(kvp => kvp.Key.StartsWith($"{configId}_") &&
                     ParseTimeSlot(kvp.Key).timestamp >= cutoffTime)
        .Select(kvp => kvp.Value)
        .ToList();
  }

  /// <summary>
  /// 获取当前时间槽
  /// </summary>
  private DateTime GetCurrentTimeSlot()
  {
    var now = DateTime.Now;
    return new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, 0);
  }

  /// <summary>
  /// 解析时间槽
  /// </summary>
  private (long configId, DateTime timestamp) ParseTimeSlot(string key)
  {
    var parts = key.Split('_');
    if (parts.Length == 2 &&
        long.TryParse(parts[0], out var configId) &&
        DateTime.TryParseExact(parts[1], "yyyyMMddHHmm", null, DateTimeStyles.None, out var timestamp))
    {
      return (configId, timestamp);
    }
    return (0, DateTime.MinValue);
  }

  /// <summary>
  /// 计算百分位数
  /// </summary>
  private double GetPercentile(double[] sortedArray, double percentile)
  {
    if (sortedArray.Length == 0) return 0;

    var index = percentile * (sortedArray.Length - 1);
    var lowerIndex = (int)Math.Floor(index);
    var upperIndex = (int)Math.Ceiling(index);

    if (lowerIndex == upperIndex)
      return sortedArray[lowerIndex];

    var lowerValue = sortedArray[lowerIndex];
    var upperValue = sortedArray[upperIndex];
    var fraction = index - lowerIndex;

    return lowerValue + (upperValue - lowerValue) * fraction;
  }

  /// <summary>
  /// 计算今日增长量
  /// </summary>
  private long CalculateTodayGrowth(long configId)
  {
    var today = DateTime.Today;
    var todayData = _timeWindows
        .Where(kvp => kvp.Key.StartsWith($"{configId}_") &&
                     ParseTimeSlot(kvp.Key).timestamp >= today)
        .Sum(kvp => kvp.Value.MessageCount);
    return todayData;
  }

  /// <summary>
  /// 计算增长率
  /// </summary>
  private double CalculateGrowthRate(long configId)
  {
    var today = DateTime.Today;
    var yesterday = today.AddDays(-1);

    var todayData = _timeWindows
        .Where(kvp => kvp.Key.StartsWith($"{configId}_") &&
                     ParseTimeSlot(kvp.Key).timestamp >= today)
        .Sum(kvp => kvp.Value.MessageCount);

    var yesterdayData = _timeWindows
        .Where(kvp => kvp.Key.StartsWith($"{configId}_") &&
                     ParseTimeSlot(kvp.Key).timestamp >= yesterday &&
                     ParseTimeSlot(kvp.Key).timestamp < today)
        .Sum(kvp => kvp.Value.MessageCount);

    return yesterdayData > 0 ? (double)(todayData - yesterdayData) / yesterdayData * 100 : 0;
  }

  /// <summary>
  /// 获取活跃连接数
  /// </summary>
  private int GetActiveConnections(long configId)
  {
    // 这里可以从转发客户端获取实际连接数
    // 暂时返回模拟数据
    return 1;
  }

  /// <summary>
  /// 获取队列长度
  /// </summary>
  private int GetQueueLength(long configId)
  {
    // 这里可以从队列服务获取实际队列长度
    // 暂时返回模拟数据
    return 0;
  }

  #endregion
}

/// <summary>
/// 内部统计数据结构
/// </summary>
internal class ForwardStatisticsData
{
  /// <summary>
  /// 总消息数
  /// </summary>
  public long TotalMessages;

  /// <summary>
  /// 成功消息数
  /// </summary>
  public long SuccessMessages;

  /// <summary>
  /// 失败消息数  
  /// </summary>
  public long FailedMessages;

  /// <summary>
  /// 重试消息数
  /// </summary>
  public long RetryMessages;

  /// <summary>
  /// 完成消息数
  /// </summary>
  public long CompletedMessages;

  /// <summary>
  /// 离线消息数
  /// </summary>
  public long OfflineMessages;

  /// <summary>
  /// 总字节数
  /// </summary>
  public long TotalBytes;

  /// <summary>
  /// 错误类型计数
  /// </summary>
  public readonly ConcurrentDictionary<ForwardErrorType, long> ErrorCounts = new();
}

/// <summary>
/// 时间窗口数据
/// </summary>
internal class TimeWindowData
{
  /// <summary>
  /// 时间戳
  /// </summary>
  public DateTime Timestamp { get; set; }
  /// <summary>
  /// 消息总数
  /// </summary>
  public long MessageCount { get; set; }
  /// <summary>
  /// 成功消息数
  /// </summary>
  public long SuccessCount { get; set; }
  /// <summary>
  /// 失败消息数
  /// </summary>
  public long FailCount { get; set; }
  /// <summary>
  /// 数据字节数
  /// </summary>
  public long DataBytes { get; set; }
  /// <summary>
  /// 总延迟
  /// </summary>
  public double TotalLatency { get; set; }
  /// <summary>
  /// 样本数量
  /// </summary>
  public int SampleCount { get; set; }
}

/// <summary>
/// 环形缓冲区实现
/// </summary>
public class CircularBuffer<T>
{
  /// <summary>
  /// 缓冲区
  /// </summary>
  private readonly T[] _buffer;
  /// <summary>
  /// 索引
  /// </summary>
  private int _index;
  /// <summary>
  /// 计数
  /// </summary>
  private int _count;
  /// <summary>
  /// 锁
  /// </summary>
  private readonly object _lock = new();

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="capacity">容量</param>
  public CircularBuffer(int capacity)
  {
    _buffer = new T[capacity];
  }

  /// <summary>
  /// 计数
  /// </summary>
  public int Count
  {
    get
    {
      lock (_lock)
      {
        return _count;
      }
    }
  }

  /// <summary>
  /// 添加
  /// </summary>
  /// <param name="item">项</param>
  public void Add(T item)
  {
    lock (_lock)
    {
      _buffer[_index] = item;
      _index = (_index + 1) % _buffer.Length;
      if (_count < _buffer.Length)
        _count++;
    }
  }

  /// <summary>
  /// 转换为数组
  /// </summary>
  /// <returns>数组</returns>
  public T[] ToArray()
  {
    lock (_lock)
    {
      var result = new T[_count];
      if (_count == 0) return result;

      if (_count < _buffer.Length)
      {
        Array.Copy(_buffer, 0, result, 0, _count);
      }
      else
      {
        var splitIndex = _buffer.Length - _index;
        Array.Copy(_buffer, _index, result, 0, splitIndex);
        Array.Copy(_buffer, 0, result, splitIndex, _index);
      }
      return result;
    }
  }
}