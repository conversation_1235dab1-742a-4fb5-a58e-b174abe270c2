namespace EdgeGateway.Device.Entity;

/// <summary>
///     设备事件实体
/// </summary>
[SugarTable("device_event", "设备事件表")]
public class DeviceEvent : EntityBase
{
  /// <summary>
  ///     设备ID
  /// </summary>
  [SugarColumn(ColumnDescription = "设备ID")]
  public long DeviceId { get; set; }

  /// <summary>
  ///     事件名称
  /// </summary>
  [SugarColumn(ColumnDescription = "事件名称", Length = 128)]
  public string EventName { get; set; }

  /// <summary>
  ///     事件描述
  /// </summary>
  [SugarColumn(ColumnDescription = "事件描述", Length = 256, IsNullable = true)]
  public string Description { get; set; }

  /// <summary>
  ///     事件是否启用
  /// </summary>
  [SugarColumn(ColumnDescription = "事件是否启用")]
  public bool Enable { get; set; }

  /// <summary>
  ///     触发事件类型
  /// </summary>
  [SugarColumn(ColumnDescription = "触发事件类型")]
  public TriggerEventTypeEnum TriggerEventType { get; set; }

  /// <summary>
  ///     自定义事件条件列表
  /// </summary>
  [SugarColumn(ColumnDescription = "自定义事件条件列表", IsJson = true)]
  public List<CustomEventWhere> CustomEventWhereList { get; set; } = new();

  /// <summary>
  ///     定时触发配置 - 当TriggerEventType为定时触发或条件定时触发时使用
  /// </summary>
  [SugarColumn(ColumnDescription = "定时触发配置", IsJson = true, IsNullable = true)]
  public EntityTimerTriggerConfig TimerConfig { get; set; }

  /// <summary>
  ///     事件优先级 - 用于控制事件处理的顺序和重要程度
  /// </summary>
  [SugarColumn(ColumnDescription = "事件优先级", DefaultValue = "0")]
  public int Priority { get; set; } = 0;

  /// <summary>
  ///     事件分组 - 用于对事件进行分类管理
  /// </summary>
  [SugarColumn(ColumnDescription = "事件分组", Length = 50, IsNullable = true)]
  public string EventGroup { get; set; }

  /// <summary>
  ///     最后触发时间
  /// </summary>
  [SugarColumn(ColumnDescription = "最后触发时间", IsNullable = true)]
  public DateTime? LastTime { get; set; }

  /// <summary>
  ///     触发次数
  /// </summary>
  [SugarColumn(ColumnDescription = "触发次数", DefaultValue = "0")]
  public int TriggerCount { get; set; } = 0;

  /// <summary>
  ///     事件动作 - 定义事件触发后要执行的动作列表
  /// </summary>
  [SugarColumn(ColumnDescription = "事件动作", IsJson = true)]
  public List<EventAction> Actions { get; set; } = new();

  /// <summary>
  ///     状态缓冲时间(毫秒) - 防止状态频繁变化导致事件重复触发
  /// </summary>
  [SugarColumn(ColumnDescription = "状态缓冲时间(毫秒)", DefaultValue = "0")]
  public int StatusBufferTime { get; set; } = 0;

  /// <summary>
  ///     是否保存触发历史 - 控制是否记录事件触发日志
  /// </summary>
  [SugarColumn(ColumnDescription = "是否保存触发历史", DefaultValue = "true")]
  public bool SaveTriggerHistory { get; set; } = true;

  /// <summary>
  ///     历史记录保留天数 - 事件日志保留的天数，0表示永久保存
  /// </summary>
  [SugarColumn(ColumnDescription = "历史记录保留天数", DefaultValue = "30")]
  public int HistoryRetentionDays { get; set; } = 30;

  #region 导航属性

  /// <summary>
  ///     关联设备
  /// </summary>
  [Navigate(NavigateType.OneToOne, nameof(DeviceId))]
  [SugarColumn(IsIgnore = true)]
  public Device Device { get; set; }

  /// <summary>
  ///     事件日志列表
  /// </summary>
  [Navigate(NavigateType.OneToMany, nameof(DeviceEventLog.DeviceEventId))]
  [SugarColumn(IsIgnore = true)]
  public List<DeviceEventLog> EventLogs { get; set; }

  #endregion
}

/// <summary>
///     定时触发配置
/// </summary>
public class EntityTimerTriggerConfig
{
  /// <summary>
  ///     定时表达式类型
  /// </summary>
  public TimerExpressionTypeEnum ExpressionType { get; set; } = TimerExpressionTypeEnum.Cron;

  /// <summary>
  ///     定时表达式 - Cron表达式或其他类型的时间表达式
  /// </summary>
  public string Expression { get; set; }

  /// <summary>
  ///     开始时间 - 定时任务开始执行的时间，为空表示立即开始
  /// </summary>
  public DateTime? StartTime { get; set; }

  /// <summary>
  ///     结束时间 - 定时任务停止执行的时间，为空表示永不停止
  /// </summary>
  public DateTime? EndTime { get; set; }

  /// <summary>
  ///     最大执行次数 - 定时任务最多执行的次数，为0表示不限制
  /// </summary>
  public int MaxTriggerCount { get; set; } = 0;

  /// <summary>
  ///     是否在错过触发时补偿执行
  /// </summary>
  public bool MissedFireHandling { get; set; } = false;
}

/// <summary>
///     定时表达式类型枚举
/// </summary>
public enum TimerExpressionTypeEnum
{
  /// <summary>
  ///     Cron表达式
  /// </summary>
  Cron = 0,

  /// <summary>
  ///     简单间隔 - 按固定间隔触发，单位为秒
  /// </summary>
  SimpleInterval = 1,

  /// <summary>
  ///     每日特定时间 - 例如每天8:30
  /// </summary>
  DailyTime = 2,

  /// <summary>
  ///     每周特定时间 - 例如每周一8:30
  /// </summary>
  WeeklyTime = 3,

  /// <summary>
  ///     每月特定时间 - 例如每月1日8:30
  /// </summary>
  MonthlyTime = 4
}

/// <summary>
///     自定义事件条件
/// </summary>
public class CustomEventWhere
{
  /// <summary>
  ///     条件ID
  /// </summary>
  public long Id { get; set; }

  /// <summary>
  ///     事件ID
  /// </summary>
  public long EventId { get; set; }

  /// <summary>
  ///     属性标识
  /// </summary>
  public string PropertyIdentifier { get; set; }

  /// <summary>
  ///     操作符
  /// </summary>
  public string Operator { get; set; }

  /// <summary>
  ///     比较值
  /// </summary>
  public string CompareValue { get; set; }

  /// <summary>
  ///     逻辑关系
  /// </summary>
  public string LogicalRelation { get; set; }

  /// <summary>
  ///     排序顺序
  /// </summary>
  public int SortOrder { get; set; }

  /// <summary>
  ///     持续时间(秒) - 条件需要持续满足的时间，用于过滤短暂波动
  /// </summary>
  public int Duration { get; set; } = 0;
}

/// <summary>
///     事件动作
/// </summary>
public class EventAction
{
  /// <summary>
  ///     动作ID
  /// </summary>
  public long Id { get; set; }

  /// <summary>
  ///     动作类型
  /// </summary>
  public string ActionType { get; set; }

  /// <summary>
  ///     动作名称
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  ///     动作参数 - JSON格式存储的动作参数
  /// </summary>
  public string Parameters { get; set; }

  /// <summary>
  ///     执行顺序
  /// </summary>
  public int Order { get; set; } = 0;

  /// <summary>
  ///     是否启用
  /// </summary>
  public bool Enabled { get; set; } = true;
}