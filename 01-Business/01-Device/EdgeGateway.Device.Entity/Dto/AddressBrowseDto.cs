using EdgeGateway.Driver.Entity.Enums;
using System.Collections.Generic;

namespace EdgeGateway.Device.Entity
{
    /// <summary>
    /// 地址浏览配置DTO
    /// </summary>
    public class AddressBrowseConfigDto
    {
        /// <summary>
        /// 地址输入类型
        /// </summary>
        public AddressInputTypeEnum InputType { get; set; }

        /// <summary>
        /// 是否支持动态浏览
        /// </summary>
        public bool SupportsDynamicBrowsing { get; set; }

        /// <summary>
        /// 是否需要连接
        /// </summary>
        public bool RequiresConnection { get; set; }

        /// <summary>
        /// 格式描述
        /// </summary>
        public string FormatDescription { get; set; }

        /// <summary>
        /// 验证模式
        /// </summary>
        public string ValidationPattern { get; set; }

        /// <summary>
        /// 地址模板列表
        /// </summary>
        public List<AddressTemplateDto> AddressTemplates { get; set; } = new List<AddressTemplateDto>();

        /// <summary>
        /// 是否支持搜索
        /// </summary>
        public bool Searchable { get; set; }

        /// <summary>
        /// 最大搜索结果数量
        /// </summary>
        public int MaxSearchResults { get; set; }

        /// <summary>
        /// 最大浏览深度
        /// </summary>
        public int MaxBrowseDepth { get; set; }
    }

    /// <summary>
    /// 地址模板DTO
    /// </summary>
    public class AddressTemplateDto
    {
        /// <summary>
        /// 模板分类
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 地址模式
        /// </summary>
        public string Pattern { get; set; }

        /// <summary>
        /// 示例地址
        /// </summary>
        public string Example { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 模板参数列表
        /// </summary>
        public List<TemplateParameterDto> Parameters { get; set; } = new List<TemplateParameterDto>();
    }

    /// <summary>
    /// 模板参数DTO
    /// </summary>
    public class TemplateParameterDto
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public string DefaultValue { get; set; }

        /// <summary>
        /// 是否必填
        /// </summary>
        public bool Required { get; set; }
    }

    /// <summary>
    /// 浏览结果DTO
    /// </summary>
    public class BrowseResultDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 节点列表
        /// </summary>
        public List<BrowsableNodeDto> Nodes { get; set; } = new List<BrowsableNodeDto>();

        /// <summary>
        /// 是否有更多数据
        /// </summary>
        public bool HasMore { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }
    }

    /// <summary>
    /// 可浏览节点DTO
    /// </summary>
    public class BrowsableNodeDto
    {
        /// <summary>
        /// 节点ID
        /// </summary>
        public string NodeId { get; set; }

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 是否有子节点
        /// </summary>
        public bool HasChildren { get; set; }

        /// <summary>
        /// 节点类别
        /// </summary>
        public string NodeClass { get; set; }

        /// <summary>
        /// 是否可读
        /// </summary>
        public bool CanRead { get; set; }

        /// <summary>
        /// 是否可写
        /// </summary>
        public bool CanWrite { get; set; }

        /// <summary>
        /// 当前值
        /// </summary>
        public object Value { get; set; }

        /// <summary>
        /// 节点路径
        /// </summary>
        public string NodePath { get; set; }

        /// <summary>
        /// 访问级别
        /// </summary>
        public int? AccessLevel { get; set; }

        /// <summary>
        /// 状态码
        /// </summary>
        public string StatusCode { get; set; }
    }
}
