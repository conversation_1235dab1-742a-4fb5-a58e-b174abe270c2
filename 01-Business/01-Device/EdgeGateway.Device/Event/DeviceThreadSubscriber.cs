namespace EdgeGateway.Device.Event;

/// <summary>
///     设备线程事件订阅器
///     负责订阅和处理与设备线程相关的事件
/// </summary>
public class DeviceThreadSubscriber : IEventSubscriber
{
    private readonly ILogger<DeviceThreadSubscriber> _logger;
    private readonly IMessagePushService _messagePushService;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="logger">日志服务</param>
    /// <param name="messagePushService">消息推送服务</param>
    public DeviceThreadSubscriber(
        ILogger<DeviceThreadSubscriber> logger,
        IMessagePushService messagePushService)
    {
        _logger = logger;
        _messagePushService = messagePushService;
    }

    /// <summary>
    ///     处理设备参数变化事件
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <returns>处理任务</returns>
    [EventSubscribe("device_param_changed")]
    public async Task HandleDeviceParamChangedEvent(EventHandlerExecutingContext context)
    {
        try
        {
            // 从事件上下文中获取负载数据
            var payload = context.GetPayload<DeviceParamChangedEvent>();
            if (payload == null)
            {
                _logger.LogWarning("设备参数变化事件的负载为空");
                return;
            }

            _logger.LogInformation(
                "设备参数变化: DeviceId={DeviceId}, ParamId={ParamId}, OldValue={OldValue}, NewValue={NewValue}",
                payload.DeviceId,
                payload.ParamId,
                payload.OldValue,
                payload.NewValue);

            // 推送设备参数变化通知
            await _messagePushService.PushMessageAsync($"device_{payload.DeviceId}_param", new
            {
                type = "param_changed",
                deviceId = payload.DeviceId,
                paramId = payload.ParamId,
                oldValue = payload.OldValue,
                newValue = payload.NewValue,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备参数变化事件时发生错误");
        }
    }

    /// <summary>
    ///     处理设备线程状态变化事件
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <returns>处理任务</returns>
    [EventSubscribe("device_thread_status_changed")]
    public async Task HandleDeviceThreadStatusChangedEvent(EventHandlerExecutingContext context)
    {
        try
        {
            // 从事件上下文中获取负载数据
            var payload = context.GetPayload<DeviceThreadStatusEvent>();
            if (payload == null)
            {
                _logger.LogWarning("设备线程状态变化事件的负载为空");
                return;
            }

            _logger.LogInformation(
                "设备线程状态变化: DeviceId={DeviceId}, Status={Status}, Message={Message}",
                payload.DeviceId,
                payload.IsRunning ? "运行中" : "已停止",
                payload.Message);

            // 推送设备线程状态变化通知
            await _messagePushService.PushMessageAsync($"device_{payload.DeviceId}_thread", new
            {
                type = "thread_status_changed",
                deviceId = payload.DeviceId,
                isRunning = payload.IsRunning,
                message = payload.Message,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            });

            // 更新设备线程运行状态的统计信息
            UpdateDeviceThreadStatistics(payload.DeviceId, payload.IsRunning);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备线程状态变化事件时发生错误");
        }
    }

    /// <summary>
    ///     处理设备线程性能指标事件
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <returns>处理任务</returns>
    [EventSubscribe("device_thread_metrics")]
    public async Task HandleDeviceThreadMetricsEvent(EventHandlerExecutingContext context)
    {
        try
        {
            // 从事件上下文中获取负载数据
            var payload = context.GetPayload<DeviceThreadMetricsEvent>();
            if (payload == null)
            {
                _logger.LogWarning("设备线程性能指标事件的负载为空");
                return;
            }

            // 记录性能指标日志（低频率）
            if (payload.LogLevel <= LogLevel.Information)
                _logger.LogInformation(
                    "设备线程性能指标: DeviceId={DeviceId}, CPU={CpuUsage}%, Memory={MemoryUsageMB}MB, ReadTime={ReadTime}ms, ScriptTime={ScriptTime}ms",
                    payload.DeviceId,
                    payload.CpuUsage,
                    payload.MemoryUsageMB,
                    payload.ReadTime,
                    payload.ScriptTime);

            // 推送设备线程性能指标
            await _messagePushService.PushMessageAsync($"device_{payload.DeviceId}_metrics", new
            {
                type = "thread_metrics",
                deviceId = payload.DeviceId,
                cpuUsage = payload.CpuUsage,
                memoryUsageMB = payload.MemoryUsageMB,
                readTime = payload.ReadTime,
                scriptTime = payload.ScriptTime,
                reconnectCount = payload.ReconnectCount,
                messageCount = payload.MessageCount,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备线程性能指标事件时发生错误");
        }
    }

    /// <summary>
    ///     更新设备线程运行状态的统计信息
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="isRunning">是否运行中</param>
    private void UpdateDeviceThreadStatistics(string deviceId, bool isRunning)
    {
        try
        {
            // 将设备线程状态保存到全局存储
            DataStorage.Instance.SetDeviceThreadStatus(deviceId, isRunning);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备线程统计信息时发生错误: DeviceId={DeviceId}", deviceId);
        }
    }
}

/// <summary>
///     设备参数变化事件数据
/// </summary>
public class DeviceParamChangedEvent
{
    /// <summary>
    ///     设备ID
    /// </summary>
    public string DeviceId { get; set; }

    /// <summary>
    ///     参数ID
    /// </summary>
    public string ParamId { get; set; }

    /// <summary>
    ///     旧值
    /// </summary>
    public object OldValue { get; set; }

    /// <summary>
    ///     新值
    /// </summary>
    public object NewValue { get; set; }
}

/// <summary>
///     设备线程状态事件数据
/// </summary>
public class DeviceThreadStatusEvent
{
    /// <summary>
    ///     设备ID
    /// </summary>
    public string DeviceId { get; set; }

    /// <summary>
    ///     是否运行中
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    ///     状态消息
    /// </summary>
    public string Message { get; set; }
}

/// <summary>
///     设备线程性能指标事件数据
/// </summary>
public class DeviceThreadMetricsEvent
{
    /// <summary>
    ///     设备ID
    /// </summary>
    public string DeviceId { get; set; }

    /// <summary>
    ///     CPU使用率
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    ///     内存使用量(MB)
    /// </summary>
    public double MemoryUsageMB { get; set; }

    /// <summary>
    ///     读取耗时(毫秒)
    /// </summary>
    public long ReadTime { get; set; }

    /// <summary>
    ///     脚本执行耗时(毫秒)
    /// </summary>
    public long ScriptTime { get; set; }

    /// <summary>
    ///     重连次数
    /// </summary>
    public int ReconnectCount { get; set; }

    /// <summary>
    ///     消息数量
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    ///     日志级别
    /// </summary>
    public LogLevel LogLevel { get; set; } = LogLevel.Information;
}