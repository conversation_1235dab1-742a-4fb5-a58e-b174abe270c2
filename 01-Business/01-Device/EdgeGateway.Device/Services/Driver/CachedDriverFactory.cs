using EdgeGateway.Device.Services.Clients;
using EdgeGateway.Driver.Interface;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace EdgeGateway.Device.Services.Driver;

/// <summary>
/// 缓存驱动工厂，装饰DefaultDriverFactory，添加缓存功能
/// </summary>
public class CachedDriverFactory : IDriverFactory
{
  private readonly IDriverFactory _innerFactory;
  private readonly ILoggerFactory _loggerFactory;
  private readonly IOptions<CachedDriverOptions> _options;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="innerFactory">内部工厂</param>
  /// <param name="loggerFactory">日志工厂</param>
  /// <param name="options">缓存配置选项</param>
  public CachedDriverFactory(
      IDriverFactory innerFactory,
      ILoggerFactory loggerFactory,
      IOptions<CachedDriverOptions> options)
  {
    _innerFactory = innerFactory;
    _loggerFactory = loggerFactory;
    _options = options;
  }

  /// <summary>
  /// 创建驱动实例，添加缓存包装
  /// </summary>
  public IDriver CreateDriver(Entity.Device device)
  {
    // 使用内部工厂创建原始驱动
    var originalDriver = _innerFactory.CreateDriver(device);

    // 创建日志记录器
    var logger = _loggerFactory.CreateLogger<CachedDriverWrapper>();

    // 获取缓存配置
    var options = _options.Value;

    // 创建缓存包装器
    var wrapper = new CachedDriverWrapper(
        originalDriver,
        logger,
        options.MaxCacheSize,
        options.CacheExpirationMinutes);

    // 注册缓存包装器，以便后续可以获取统计信息
    if (device.Driver != null)
    {
      wrapper.RegisterCachedWrapper(device.Driver.DriverName);
    }

    return wrapper;
  }

  /// <summary>
  /// 委托内部工厂尝试更新驱动
  /// </summary>
  public Task<bool> TryUpdateDriverAsync(string driverName)
  {
    return _innerFactory.TryUpdateDriverAsync(driverName);
  }
}