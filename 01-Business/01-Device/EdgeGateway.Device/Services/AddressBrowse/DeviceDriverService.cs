using EdgeGateway.Device.Entity;
using EdgeGateway.Device.Services.Clients;
using EdgeGateway.SqlSugar.SqlSugar;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace EdgeGateway.Device.Services.AddressBrowse
{
    /// <summary>
    /// 设备驱动服务实现
    /// </summary>
    public class DeviceDriverService : IDeviceDriverService
    {
        private readonly ILogger<DeviceDriverService> _logger;
        private readonly SqlSugarRepository<EdgeGateway.Device.Entity.Device> _deviceRepository;
        private readonly Lazy<EdgeGateway> _gateway;

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceDriverService(
            ILogger<DeviceDriverService> logger,
            SqlSugarRepository<EdgeGateway.Device.Entity.Device> deviceRepository,
            Lazy<EdgeGateway> gateway)
        {
            _logger = logger;
            _deviceRepository = deviceRepository;
            _gateway = gateway;
        }

        /// <summary>
        /// 网关实例
        /// </summary>
        private EdgeGateway Gateway => _gateway.Value;

        /// <summary>
        /// 获取设备驱动实例
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>驱动实例</returns>
        public async Task<object> GetDeviceDriverAsync(long deviceId)
        {
            try
            {
                _logger.LogDebug("获取设备驱动实例: {DeviceId}", deviceId);

                // 获取设备信息
                var device = await _deviceRepository.GetByIdAsync(deviceId);
                if (device == null)
                {
                    _logger.LogWarning("设备不存在: {DeviceId}", deviceId);
                    return null;
                }

                // 通过网关获取设备线程
                var deviceThread = Gateway.GetDeviceThread(device.Identifier);
                if (deviceThread == null)
                {
                    _logger.LogWarning("设备线程不存在: {DeviceId}, Identifier: {Identifier}", deviceId, device.Identifier);
                    return null;
                }

                // 返回驱动实例
                var driver = deviceThread.Driver;
                _logger.LogDebug("成功获取设备驱动实例: {DeviceId}, DriverType: {DriverType}", 
                    deviceId, driver?.GetType().Name);

                return driver;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备驱动实例失败: {DeviceId}", deviceId);
                return null;
            }
        }

        /// <summary>
        /// 检查设备是否在线
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>是否在线</returns>
        public async Task<bool> IsDeviceOnlineAsync(long deviceId)
        {
            try
            {
                var device = await _deviceRepository.GetByIdAsync(deviceId);
                if (device == null)
                {
                    return false;
                }

                var deviceThread = Gateway.GetDeviceThread(device.Identifier);
                if (deviceThread == null)
                {
                    return false;
                }

                // 检查驱动连接状态
                return deviceThread.Driver?.IsConnected ?? false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查设备在线状态失败: {DeviceId}", deviceId);
                return false;
            }
        }

        /// <summary>
        /// 获取设备连接状态
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>连接状态信息</returns>
        public async Task<DeviceConnectionStatus> GetDeviceConnectionStatusAsync(long deviceId)
        {
            try
            {
                var device = await _deviceRepository.GetByIdAsync(deviceId);
                if (device == null)
                {
                    return new DeviceConnectionStatus
                    {
                        IsConnected = false,
                        ErrorMessage = "设备不存在"
                    };
                }

                var deviceThread = Gateway.GetDeviceThread(device.Identifier);
                if (deviceThread == null)
                {
                    return new DeviceConnectionStatus
                    {
                        IsConnected = false,
                        ErrorMessage = "设备线程不存在",
                        DriverType = device.Driver?.Name
                    };
                }

                var driver = deviceThread.Driver;
                if (driver == null)
                {
                    return new DeviceConnectionStatus
                    {
                        IsConnected = false,
                        ErrorMessage = "驱动实例不存在",
                        DriverType = device.Driver?.Name
                    };
                }

                return new DeviceConnectionStatus
                {
                    IsConnected = driver.IsConnected,
                    ConnectedTime = driver.IsConnected ? deviceThread.StartTime : null,
                    LastCommunicationTime = deviceThread.LastRunTime,
                    DriverType = driver.GetType().Name,
                    DriverVersion = driver.GetType().Assembly.GetName().Version?.ToString(),
                    ErrorMessage = driver.IsConnected ? null : "设备未连接"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备连接状态失败: {DeviceId}", deviceId);
                return new DeviceConnectionStatus
                {
                    IsConnected = false,
                    ErrorMessage = $"获取连接状态失败: {ex.Message}"
                };
            }
        }
    }
}
