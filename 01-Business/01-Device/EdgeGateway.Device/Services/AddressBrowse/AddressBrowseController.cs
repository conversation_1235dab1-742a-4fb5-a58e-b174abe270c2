using EdgeGateway.Device.Entity;
using EdgeGateway.Device.Services.AddressBrowse;
using EdgeGateway.Driver.Interface.DriverBase;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.SqlSugar.SqlSugar;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using Furion.SpecificationDocument;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EdgeGateway.Device.API.Controllers
{
    /// <summary>
    /// 地址浏览控制器
    /// </summary>
    [ApiDescriptionSettings("Device", Name = "AddressBrowse")]
    public class AddressBrowseController : IDynamicApiController
    {
        private readonly ILogger<AddressBrowseController> _logger;
        private readonly SqlSugarRepository<EdgeGateway.Device.Entity.Device> _deviceRepository;
        private readonly IDeviceDriverService _deviceDriverService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public AddressBrowseController(
            ILogger<AddressBrowseController> logger,
            SqlSugarRepository<EdgeGateway.Device.Entity.Device> deviceRepository,
            IDeviceDriverService deviceDriverService)
        {
            _logger = logger;
            _deviceRepository = deviceRepository;
            _deviceDriverService = deviceDriverService;
        }

        /// <summary>
        /// 获取设备的地址浏览配置
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>地址浏览配置</returns>
        [HttpGet("devices/{deviceId}/address-browse/config")]
        public async Task<ActionResult<AddressBrowseConfigDto>> GetAddressBrowseConfig(long deviceId)
        {
            try
            {
                _logger.LogInformation("获取设备地址浏览配置: {DeviceId}", deviceId);

                // 获取设备信息
                var device = await _deviceRepository.GetByIdAsync(deviceId);
                if (device == null)
                {
                    throw Oops.Oh($"设备不存在: {deviceId}");
                }

                // 获取设备驱动实例
                var driver = await GetDeviceDriverAsync(deviceId);
                if (driver == null)
                {
                    throw Oops.Oh($"无法获取设备驱动: {deviceId}");
                }

                // 检查驱动是否支持地址浏览配置
                if (driver is not OpcUaBase opcUaDriver)
                {
                    // 对于非OPC UA驱动，返回基本配置
                    return new AddressBrowseConfigDto
                    {
                        InputType = AddressInputTypeEnum.Text,
                        SupportsDynamicBrowsing = false,
                        RequiresConnection = false,
                        FormatDescription = "请输入设备地址",
                        AddressTemplates = new List<AddressTemplateDto>(),
                        Searchable = false,
                        MaxSearchResults = 0,
                        MaxBrowseDepth = 0
                    };
                }

                // 获取OPC UA驱动的地址浏览配置
                var config = opcUaDriver.GetAddressBrowseConfig();
                
                return new AddressBrowseConfigDto
                {
                    InputType = config.InputType,
                    SupportsDynamicBrowsing = config.SupportsDynamicBrowsing,
                    RequiresConnection = config.RequiresConnection,
                    FormatDescription = config.FormatDescription,
                    ValidationPattern = config.ValidationPattern,
                    AddressTemplates = config.AddressTemplates?.Select(t => new AddressTemplateDto
                    {
                        Category = t.Category,
                        Pattern = t.Pattern,
                        Example = t.Example,
                        Description = t.Description,
                        Parameters = t.Parameters?.Select(p => new TemplateParameterDto
                        {
                            Name = p.Name,
                            Description = p.Description,
                            Type = p.Type,
                            DefaultValue = p.DefaultValue,
                            Required = p.Required
                        }).ToList() ?? new List<TemplateParameterDto>()
                    }).ToList() ?? new List<AddressTemplateDto>(),
                    Searchable = config.Searchable,
                    MaxSearchResults = config.MaxSearchResults,
                    MaxBrowseDepth = config.MaxBrowseDepth
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备地址浏览配置失败: {DeviceId}", deviceId);
                throw Oops.Oh($"获取地址浏览配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取根节点
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>根节点列表</returns>
        [HttpGet("devices/{deviceId}/address-browse/root-nodes")]
        public async Task<ActionResult<BrowseResultDto>> GetRootNodes(long deviceId)
        {
            try
            {
                _logger.LogInformation("获取设备根节点: {DeviceId}", deviceId);

                var driver = await GetDeviceDriverAsync(deviceId);
                if (driver is not IDynamicAddressBrowsable browsableDriver)
                {
                    throw Oops.Oh($"设备驱动不支持动态地址浏览: {deviceId}");
                }

                var result = await browsableDriver.GetRootNodesAsync();
                
                return new BrowseResultDto
                {
                    Success = result.Success,
                    ErrorMessage = result.ErrorMessage,
                    Nodes = result.Nodes?.Select(MapToBrowsableNodeDto).ToList() ?? new List<BrowsableNodeDto>(),
                    HasMore = result.HasMore,
                    TotalCount = result.TotalCount
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备根节点失败: {DeviceId}", deviceId);
                return new BrowseResultDto
                {
                    Success = false,
                    ErrorMessage = $"获取根节点失败: {ex.Message}",
                    Nodes = new List<BrowsableNodeDto>(),
                    HasMore = false,
                    TotalCount = 0
                };
            }
        }

        /// <summary>
        /// 获取子节点
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="parentNodeId">父节点ID</param>
        /// <returns>子节点列表</returns>
        [HttpGet("devices/{deviceId}/address-browse/child-nodes")]
        public async Task<ActionResult<BrowseResultDto>> GetChildNodes(long deviceId, [FromQuery] string parentNodeId)
        {
            try
            {
                _logger.LogInformation("获取设备子节点: {DeviceId}, 父节点: {ParentNodeId}", deviceId, parentNodeId);

                if (string.IsNullOrEmpty(parentNodeId))
                {
                    throw Oops.Oh("父节点ID不能为空");
                }

                var driver = await GetDeviceDriverAsync(deviceId);
                if (driver is not IDynamicAddressBrowsable browsableDriver)
                {
                    throw Oops.Oh($"设备驱动不支持动态地址浏览: {deviceId}");
                }

                var result = await browsableDriver.GetChildNodesAsync(parentNodeId);
                
                return new BrowseResultDto
                {
                    Success = result.Success,
                    ErrorMessage = result.ErrorMessage,
                    Nodes = result.Nodes?.Select(MapToBrowsableNodeDto).ToList() ?? new List<BrowsableNodeDto>(),
                    HasMore = result.HasMore,
                    TotalCount = result.TotalCount
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备子节点失败: {DeviceId}, 父节点: {ParentNodeId}", deviceId, parentNodeId);
                return new BrowseResultDto
                {
                    Success = false,
                    ErrorMessage = $"获取子节点失败: {ex.Message}",
                    Nodes = new List<BrowsableNodeDto>(),
                    HasMore = false,
                    TotalCount = 0
                };
            }
        }

        /// <summary>
        /// 搜索节点
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="searchText">搜索文本</param>
        /// <param name="maxResults">最大结果数量</param>
        /// <returns>搜索结果</returns>
        [HttpGet("devices/{deviceId}/address-browse/search")]
        public async Task<ActionResult<BrowseResultDto>> SearchNodes(long deviceId, [FromQuery] string searchText, [FromQuery] int maxResults = 100)
        {
            try
            {
                _logger.LogInformation("搜索设备节点: {DeviceId}, 搜索文本: {SearchText}", deviceId, searchText);

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    throw Oops.Oh("搜索文本不能为空");
                }

                var driver = await GetDeviceDriverAsync(deviceId);
                if (driver is not IDynamicAddressBrowsable browsableDriver)
                {
                    throw Oops.Oh($"设备驱动不支持动态地址浏览: {deviceId}");
                }

                var result = await browsableDriver.SearchNodesAsync(searchText, maxResults);
                
                return new BrowseResultDto
                {
                    Success = result.Success,
                    ErrorMessage = result.ErrorMessage,
                    Nodes = result.Nodes?.Select(MapToBrowsableNodeDto).ToList() ?? new List<BrowsableNodeDto>(),
                    HasMore = result.HasMore,
                    TotalCount = result.TotalCount
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索设备节点失败: {DeviceId}, 搜索文本: {SearchText}", deviceId, searchText);
                return new BrowseResultDto
                {
                    Success = false,
                    ErrorMessage = $"搜索节点失败: {ex.Message}",
                    Nodes = new List<BrowsableNodeDto>(),
                    HasMore = false,
                    TotalCount = 0
                };
            }
        }

        /// <summary>
        /// 验证节点是否存在
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="nodeId">节点ID</param>
        /// <returns>验证结果</returns>
        [HttpGet("devices/{deviceId}/address-browse/validate")]
        public async Task<ActionResult<bool>> ValidateNode(long deviceId, [FromQuery] string nodeId)
        {
            try
            {
                _logger.LogInformation("验证设备节点: {DeviceId}, 节点ID: {NodeId}", deviceId, nodeId);

                if (string.IsNullOrEmpty(nodeId))
                {
                    throw Oops.Oh("节点ID不能为空");
                }

                var driver = await GetDeviceDriverAsync(deviceId);
                if (driver is not IDynamicAddressBrowsable browsableDriver)
                {
                    // 对于不支持动态浏览的驱动，使用简单的格式验证
                    return !string.IsNullOrWhiteSpace(nodeId);
                }

                var result = await browsableDriver.ValidateNodeAsync(nodeId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证设备节点失败: {DeviceId}, 节点ID: {NodeId}", deviceId, nodeId);
                return false;
            }
        }

        /// <summary>
        /// 获取设备驱动实例
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>驱动实例</returns>
        private async Task<object> GetDeviceDriverAsync(long deviceId)
        {
            // 这里需要根据实际的驱动管理方式来获取驱动实例
            // 可能需要通过设备管理服务或驱动工厂来获取
            
            // 临时实现：返回null，需要根据实际架构调整
            _logger.LogWarning("GetDeviceDriverAsync 需要实现具体的驱动获取逻辑");
            return null;
        }

        /// <summary>
        /// 将BrowsableNode映射为DTO
        /// </summary>
        /// <param name="node">原始节点</param>
        /// <returns>DTO节点</returns>
        private static BrowsableNodeDto MapToBrowsableNodeDto(BrowsableNode node)
        {
            return new BrowsableNodeDto
            {
                NodeId = node.NodeId,
                DisplayName = node.DisplayName,
                Description = node.Description,
                DataType = node.DataType,
                HasChildren = node.HasChildren,
                NodeClass = node.NodeClass,
                CanRead = node.CanRead,
                CanWrite = node.CanWrite,
                Value = node.Value,
                NodePath = node.NodePath,
                AccessLevel = node.AccessLevel,
                StatusCode = node.StatusCode
            };
        }
    }
}
