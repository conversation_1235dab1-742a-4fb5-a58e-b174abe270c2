using EdgeGateway.Alarm.Services;
using EdgeGateway.Base.ResourceManagement;
using EdgeGateway.Device.Services.Events;
using EdgeGateway.Device.Event;
using EdgeGateway.Notification.Events;
using EdgeGateway.Base.Entity.Enums;
using System.Collections.Generic;
using System.Diagnostics;
using System.Dynamic;
using System.IO;
using System.Linq;
using EdgeGateway.Shared.Utils;
using System.Collections.Concurrent;

namespace EdgeGateway.Device.Services.Clients;

/// <summary>
///     采集线程统一入口
/// </summary>
public class DeviceThread : IDisposable
{
    // private readonly Interpreter _interpreter;
    private readonly CancellationTokenSource _tokenSource = new();

    /// <summary>
    ///     队列服务
    /// </summary>
    private readonly QueueService<PayLoad> _queueService;

    /// <summary>
    /// </summary>
    public Entity.Device Device { get; set; }

    /// <summary>
    ///     采集协议
    /// </summary>
    public IDriver Driver { get; set; }

    /// <summary>
    /// </summary>
    private readonly IMessagePushService _messagePushService;

    #region 开放字段

    /// <summary>
    ///     属性源
    /// </summary>
    public Dictionary<string, DeviceLabel> DeviceLabelSource { get; set; } = new();

    /// <summary>
    ///     最后活动时间
    /// </summary>
    public DateTime? LastActiveTime { get; set; }

    /// <summary>
    ///     采集开始时间
    /// </summary>
    public DateTime CollectionStartTime { get; private set; }

    /// <summary>
    ///     获取今日在线时长（分钟）
    /// </summary>
    public double TodayOnlineMinutes => GetTotalOnlineTime().TotalMinutes;

    #endregion

    #region 私有字段

    /// <summary>
    /// </summary>
    private IServiceProvider Services { get; }

    /// <summary>
    /// 服务作用域工厂
    /// </summary>
    private readonly IServiceScopeFactory _serviceScopeFactory;

    /// <summary>
    /// </summary>
    private Task Task { get; set; }

    /// <summary>
    ///     采集推送对象
    /// </summary>
    private PayLoad _payLoad;

    /// <summary>
    ///     数据来源分设备属性集合
    /// </summary>
    private readonly Dictionary<ValueSourceEnum, List<DeviceLabel>> ValueSourceDeviceLabel = new();

    /// <summary>
    ///     标签锁
    /// </summary>
    private readonly object _LabelLock = new();

    /// <summary>
    ///     数据通道
    /// </summary>
    private readonly Channel<ReadDataResult> _dataChannel;

    // 添加性能计数器
    private readonly PerformanceCounter _readCounter;
    private long _readCount; // 添加一个简单的计数器

    /// <summary>
    ///     日志记录器
    /// </summary>
    private readonly ILogger<DeviceThread> _logger;

    /// <summary>
    ///     事件发布器
    /// </summary>
    private readonly IEventPublisher _eventPublisher;

    /// <summary>
    ///     添加设备内部Channel用于处理数据上报策略
    /// </summary>
    private readonly Channel<PayLoad> _reportChannel;

    // 添加统计字段
    private int _reconnectCount; // 重连次数
    private int _connectSuccessCount; // 连接成功次数
    private int _currentMinuteMessageCount; // 当前分钟消息数
    private DateTime _lastMinute = DateTime.Now; // 上一分钟时间
    private readonly Stopwatch _onlineTimeWatch = new(); // 在线时长计时器
    private readonly Stopwatch _readLabelWatch = new(); // 读取点位耗时
    private readonly Stopwatch _runScriptWatch = new(); // 执行脚本耗时
    private readonly Stopwatch _readStaticWatch = new(); // 读取静态数据耗时

    // 添加点位采集日志推送频率控制
    private readonly ConcurrentDictionary<string, long> _lastPointLogTime = new(); // 每个点位的最后推送时间
    // private const long POINT_LOG_INTERVAL_MS = 5000; // 点位日志推送间隔（5秒）

    /// <summary>
    ///     添加当日累计在线时间字段
    /// </summary>
    private double _todayOnlineMinutes;

    // 添加字段
    private readonly JsScriptEnginePool _scriptEnginePool;

    /// <summary>
    ///     时序数据库
    /// </summary>
    private readonly ITimeSeriesStorage _timeSeriesStorage;

    /// <summary>
    ///     资源管理器
    /// </summary>
    private readonly IResourceManager _resourceManager;

    /// <summary>
    ///     设备事件服务
    /// </summary>
    private readonly IDeviceEventService _deviceEventService;

    // 在类中定义委托类型和字段
    private delegate Task<List<ReadDataResult>> ReadDelegate(List<DriverReadInput> paramList);
    private readonly ReadDelegate _cachedReadMethod;

    // 缓存最后使用的参数列表
    private List<DriverReadInput> _cachedReadInputs = new List<DriverReadInput>();
    private Dictionary<string, DriverReadInput> _readInputMap = new Dictionary<string, DriverReadInput>();

    // 为Read方法创建特定的缓存实现
    public class ReadMethodCache
    {
        private readonly IDriver _driver;
        private readonly ConcurrentDictionary<string, DriverReadInput> _inputCache = new ConcurrentDictionary<string, DriverReadInput>();
        private List<DriverReadInput> _currentBatch = new List<DriverReadInput>();

        public ReadMethodCache(IDriver driver)
        {
            _driver = driver;
        }

        public async Task<List<ReadDataResult>> ReadBatch(IEnumerable<DeviceLabel> labels)
        {
            UpdateBatch(labels);
            return await _driver.Read(_currentBatch);
        }

        private void UpdateBatch(IEnumerable<DeviceLabel> labels)
        {
            bool needsUpdate = false;
            var currentIds = new HashSet<string>();

            foreach (var label in labels)
            {
                // 跳过空标签
                if (string.IsNullOrEmpty(label.Identifier))
                    continue;

                currentIds.Add(label.Identifier);

                // 检查是否需要创建或更新
                if (!_inputCache.TryGetValue(label.Identifier, out var cachedInput))
                {
                    // 创建新的参数
                    var input = CreateReadInput(label);
                    if (_inputCache.TryAdd(label.Identifier, input))
                        needsUpdate = true;
                }
                else
                {
                    // 检查参数是否发生变化
                    if (cachedInput.Address != label.RegisterAddress ||
                        cachedInput.Method != label.Method ||
                        cachedInput.Encoding != label.Encoding ||
                        cachedInput.Length != label.ReadLength)
                    {
                        // 更新参数
                        var updatedInput = CreateReadInput(label);
                        _inputCache[label.Identifier] = updatedInput;
                        needsUpdate = true;
                    }
                }
            }

            // 移除不再需要的参数
            var keysToRemove = _inputCache.Keys.Where(k => !currentIds.Contains(k)).ToArray();
            foreach (var key in keysToRemove)
            {
                if (_inputCache.TryRemove(key, out _))
                    needsUpdate = true;
            }

            // 仅在需要更新时重建批处理列表
            if (needsUpdate || _currentBatch.Count == 0)
            {
                _currentBatch = _inputCache.Values.ToList();
            }
        }

        private DriverReadInput CreateReadInput(DeviceLabel label)
        {
            // 创建读取输入参数
            return new DriverReadInput(
                label.Identifier,
                label.RegisterAddress,
                label.DataType,
                label.Method,
                label.ReadLength,
                label.Encoding
            );
        }
    }

    #endregion

    /// <summary>
    ///     添加设备状态变更事件
    /// </summary>
    public event EventHandler<DeviceStatusEventArgs> DeviceStatusChanged;

    // 添加设备状态缓存
    private bool _currentStatus;

    /// <summary>
    ///     当前设备连接状态
    /// </summary>
    public bool CurrentStatus
    {
        get => _currentStatus;
        private set
        {
            if (_currentStatus != value)
            {
                _currentStatus = value;
                OnDeviceStatusChanged(new DeviceStatusEventArgs(value));
            }
        }
    }

    /// <summary>
    ///     状态变更事件触发器
    /// </summary>
    /// <param name="e"></param>
    protected virtual void OnDeviceStatusChanged(DeviceStatusEventArgs e)
    {
        DeviceStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// </summary>
    /// <param name="device">采集设备</param>
    /// <param name="driver">采集协议</param>
    /// <param name="messagePushService">socket服务</param>
    /// <param name="services">作用域</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="eventPublisher">事件发布器</param>
    /// <param name="scriptEnginePool">脚本引擎池</param>
    /// <param name="queueService">队列服务</param>
    /// <param name="timeSeriesStorage">时序数据库</param>
    /// <param name="resourceManager">资源管理器</param>
    /// <param name="deviceEventService">设备事件服务</param>
    /// <param name="serviceScopeFactory">服务作用域工厂</param>
    public DeviceThread(Entity.Device device, IDriver driver, IMessagePushService messagePushService, IServiceProvider services, ILogger<DeviceThread> logger, IEventPublisher eventPublisher,
        JsScriptEnginePool scriptEnginePool, QueueService<PayLoad> queueService, ITimeSeriesStorage timeSeriesStorage, IResourceManager resourceManager, IDeviceEventService deviceEventService = null,
        IServiceScopeFactory serviceScopeFactory = null)
    {
        // 记录采集开始时间
        CollectionStartTime = DateTime.Now;

        // 日志记录器
        _logger = logger;
        // 作用域
        Services = services;
        // 设备
        Device = device;
        // 采集协议
        Driver = driver;
        // 消息推送服务
        _messagePushService = messagePushService;
        // 输出接收事件
        Driver.OutputReceived += DriverOutputReceived!;
        // 队列服务
        _queueService = queueService;
        // _interpreter = new Interpreter();
        // 创建无限容量的Channel
        _dataChannel = Channel.CreateUnbounded<ReadDataResult>(new UnboundedChannelOptions
        {
            SingleReader = true, // 如果只有一个读取器，设置为true可以提高性能
            SingleWriter = false // 如果有多个写入器，设置为false
        });
        // 加载数据
        Load();
        // 启动采集
        StartDataCollectionTask();
        // 启动数据处理任务
        _ = StartDataProcessingTask();

        _logger.LogInformation($"设备 {Device.Identifier} 线程初始化完成");

        // 事件发布器
        _eventPublisher = eventPublisher;

        // 初始化设备内部Channel
        _reportChannel = Channel.CreateUnbounded<PayLoad>(new UnboundedChannelOptions
        {
            SingleReader = true,
            SingleWriter = true
        });

        // 启动数据上报处理任务
        _ = Task.Run(ProcessReportData);

        // 初始化当日在线时间
        _ = InitializeTodayOnlineTime();

        _scriptEnginePool = scriptEnginePool;
        _timeSeriesStorage = timeSeriesStorage;
        _resourceManager = resourceManager;
        _deviceEventService = deviceEventService;
        _serviceScopeFactory = serviceScopeFactory;

        // 订阅资源状态变更事件
        if (_resourceManager != null)
        {
            _resourceManager.ResourceStateChanged += ResourceManager_ResourceStateChanged;
        }

        // 在构造函数中初始化
        _cachedReadMethod = Driver.Read;
    }

    /// <summary>
    ///     资源状态变更事件处理
    /// </summary>
    private void ResourceManager_ResourceStateChanged(object sender, ResourceStateChangedEventArgs e)
    {
        // 记录资源状态变更日志
        if (_resourceManager.ShouldLogMessage(LogLevel.Information))
        {
            _logger.LogInformation(
                "设备 {DeviceId} 接收到资源状态变更: CPU={CpuState}, 存储={StorageState}, 调整因子={Factor}",
                Device.Identifier,
                e.CpuState,
                e.StorageState,
                e.SpeedAdjustmentFactor);
        }
    }

    /// <summary>
    ///     加载数据
    /// </summary>
    private void Load()
    {
        // 初始化PayLoad
        _payLoad = new PayLoad { Labels = new ConcurrentDictionary<string, ParamValue>(), Id = Device.Identifier, Name = Device.Name ?? "" };
        // 变量转为字典
        DeviceLabelSource = Device.DeviceLabel.ToDictionary(k => k.Identifier, v => v);
        ValueSourceDeviceLabel.TryAdd(ValueSourceEnum.Static, DeviceLabelSource.Values.Where(w => w.ValueSource == ValueSourceEnum.Static).OrderBy(o => o.ActionOrder).ToList());
        ValueSourceDeviceLabel.TryAdd(ValueSourceEnum.Read, DeviceLabelSource.Values.Where(w => w.ValueSource == ValueSourceEnum.Read).OrderBy(o => o.ActionOrder).ToList());
        ValueSourceDeviceLabel.TryAdd(ValueSourceEnum.Calculate,
            DeviceLabelSource.Values.Where(w => w.ValueSource == ValueSourceEnum.Calculate && !string.IsNullOrWhiteSpace(w.Content)).OrderBy(o => o.ActionOrder).ToList());
        //  虚拟属性
        ValueSourceDeviceLabel[ValueSourceEnum.Fictitious] = DeviceLabelSource.Values
            .Where(w => w.ValueSource == ValueSourceEnum.Fictitious)
            .ToList();
    }

    /// <summary>
    ///     启动采集
    /// </summary>
    private void StartDataCollectionTask()
    {
        Task = Task.Factory.StartNew(async () =>
        {
            // 初始化最后消息时间
            var _lastMessageTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            // 初始化最后性能指标时间
            var _lastMetricsTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            // 循环采集
            while (!_tokenSource.IsCancellationRequested)
            {
                try
                {
                    LastActiveTime = DateTime.Now; // 更新最后活动时间
                    // 清除历史数据 
                    _payLoad.Labels = new ConcurrentDictionary<string, ParamValue>();
                    // 采集时间戳
                    _payLoad.Ts = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    // 检查设备是否连接成功
                    await DeviceConnect();

                    // 未配置设备采集属性就循环持续等待
                    while (DeviceLabelSource.Count == 0 && !_tokenSource.IsCancellationRequested)
                    {
                        // 增加策略不要一直推送消息
                        if (DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - _lastMessageTime > 1000 * 30)
                        {
                            // 检查存储状态，根据资源状态决定是否记录日志
                            if (_resourceManager == null || _resourceManager.ShouldLogMessage(LogLevel.Warning))
                            {
                                // 推送连接成功消息
                                await PushDeviceLog("warning", $"设备 {Device.Identifier} 未配置标签,等待配置...");
                            }

                            _lastMessageTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                        }

                        await Task.Delay(1000 * 3, _tokenSource.Token);
                    }

                    // 记录静态数据读取耗时
                    _readStaticWatch.Restart();
                    await ReadStatic(_payLoad);
                    _readStaticWatch.Stop();

                    if (Driver.IsConnected)
                    {
                        // 记录点位读取耗时
                        _readLabelWatch.Restart();
                        await ReadLabel(_payLoad);
                        _readLabelWatch.Stop();
                    }

                    // 记录脚本执行耗时
                    _runScriptWatch.Restart();
                    await RunScript(_payLoad);
                    _runScriptWatch.Stop();

                    // 计算和更新整体状态
                    UpdatePayLoadStatus(_payLoad);

                    // 推送性能统计数据
                    // 增加策略不要一直推送消息,_lastMessageTime 1秒推送一次
                    if (DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - _lastMessageTime > 1000 * 1)
                    {
                        // 检查存储状态，根据资源状态决定是否记录性能度量日志
                        if (_resourceManager == null || _resourceManager.ShouldLogMessage(LogLevel.Information))
                        {
                            await _messagePushService.PushMessageAsync(MessageTopics.GetDeviceMetricsTopic(Device.Id), new
                            {
                                deviceId = Device.Identifier,
                                readStaticTime = _readStaticWatch.ElapsedMilliseconds,
                                readLabelTime = _readLabelWatch.ElapsedMilliseconds,
                                runScriptTime = _runScriptWatch.ElapsedMilliseconds,
                                onlineTime = _onlineTimeWatch.Elapsed.TotalMinutes,
                                reconnectCount = _reconnectCount,
                                connectSuccessCount = _connectSuccessCount,
                                currentMinuteMessageCount = _currentMinuteMessageCount,
                                // 添加状态统计信息
                                collectionStatus = _payLoad.Status.ToString(),
                                totalLabels = _payLoad.StatusSummary.TotalCount,
                                successLabels = _payLoad.StatusSummary.SuccessCount,
                                failedLabels = _payLoad.StatusSummary.FailedCount + _payLoad.StatusSummary.ErrorCount,
                                successRate = _payLoad.StatusSummary.SuccessRate
                            });
                        }
                        _lastMessageTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    }

                    // 定期发布性能指标事件（每60秒一次）
                    if (DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - _lastMetricsTime > 1000 * 60)
                    {
                        PublishThreadMetrics();
                        _lastMetricsTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    }

                    // 写入通道
                    await _reportChannel.Writer.WriteAsync(_payLoad);
                }
                catch (Exception ex)
                {
                    // 错误日志始终记录
                    await _messagePushService.PushMessageAsync($"device_{Device.Identifier}_status", new
                    {
                        type = "error",
                        deviceId = Device.Identifier,
                        message = $"设备 {Device.Identifier} 采集异常: {ex.Message}"
                    });
                    _logger.LogError(ex, $"设备 {Device.Identifier} 采集异常");
                }

                // 根据资源状态动态调整采集间隔
                int adjustedInterval = _resourceManager != null
                    ? _resourceManager.GetAdjustedCollectionInterval(Device.DeviceInfo.MinPeriod)
                    : Device.DeviceInfo.MinPeriod;

                // 采集间隔时间
                await Task.Delay(adjustedInterval, _tokenSource.Token);
                // 检查设备是否关机。注：该检查放在最后
                if (!Driver.IsConnected)
                {
                    await Task.Delay(Device.DeviceInfo.ReConnTime, _tokenSource.Token);
                }
            }
        }, TaskCreationOptions.LongRunning);
    }

    /// <summary>
    ///     设备连接
    /// </summary>
    /// <returns></returns>
    public async Task DeviceConnect()
    {
        try
        {
            // 设备已连接
            if (Driver.IsConnected)
            {
                // 更新设备状态
                UpdateDeviceStatus(true);
                return;
            }

            // 关闭设备
            Driver.Close();
            _onlineTimeWatch.Stop(); // 停止在线计时

            // 增加重连次数
            Interlocked.Increment(ref _reconnectCount);
            // 推送重连消息
            await PushDeviceLog("warning", $"设备 {Device.Identifier} 正在尝试第 {_reconnectCount} 次重新连接");

            var message = Driver.Connect();
            // 更新设备状态
            if (Driver.IsConnected)
            {
                // 更新设备状态
                UpdateDeviceStatus(true);
                _onlineTimeWatch.Start(); // 开始在线计时

                // 增加连接成功次数
                Interlocked.Increment(ref _connectSuccessCount);
                await PushDeviceLog("info", $"设备 {Device.Identifier} 连接成功，重连次数: {_reconnectCount}，连接成功次数: {_connectSuccessCount}");

                await Task.Delay(Device.DeviceInfo.WaitTime, _tokenSource.Token);
            }
            else
            {
                UpdateDeviceStatus(false);
                await PushDeviceLog("error", $"设备 {Device.Identifier} 连接失败，原因: {message}，重连次数: {_reconnectCount}");

                await Task.Delay(Device.DeviceInfo.ReConnTime, _tokenSource.Token);
            }
        }
        catch (Exception ex)
        {
            // 更新设备状态
            UpdateDeviceStatus(false);
            _onlineTimeWatch.Stop();

            await PushDeviceLog("error", $"设备 {Device.Identifier} 连接异常: {ex.Message}");

            _logger.LogError(ex, $"设备 {Device.Identifier} 连接异常");
            await Task.Delay(Device.DeviceInfo.ReConnTime, _tokenSource.Token);
        }
    }

    /// <summary>
    ///     读取静态数据
    /// </summary>
    /// <returns></returns>
    public Task ReadStatic(PayLoad payLoad)
    {
        // 检查 ValueSourceEnum.Static 键是否存在于字典中
        if (!ValueSourceDeviceLabel.TryGetValue(ValueSourceEnum.Static, out var staticLabels) || staticLabels == null || !staticLabels.Any())
        {
            // 如果键不存在或值为空，直接返回
            return Task.CompletedTask;
        }

        Parallel.ForEachAsync(staticLabels, async (variable, token) =>
        {
            // 创建读取数据结果
            var ret = CreateReadDataResult(variable);
            // 设置值
            ret.Value = variable.Content;
            // 将有效的结果写入Channel
            await _dataChannel.Writer.WriteAsync(ret, token);

            // 直接写入传入的payLoad而不是_payLoad
            var paramValue = new ParamValue
            {
                Time = ret.Time,
                Value = ret.Value,
                Name = variable.Name,
                TransitionType = variable.TransitionType,
                Status = ret.Status, // 设置状态
                ErrorMessage = ret.ErrMsg // 设置错误信息
            };
            payLoad.Labels.TryAdd(variable.Identifier, paramValue);
        });

        return Task.CompletedTask;
    }

    /// <summary>
    ///     创建读取数据结果
    /// </summary>
    /// <param name="variable"></param>
    /// <returns></returns>
    private ReadDataResult CreateReadDataResult(DeviceLabel variable)
    {
        var ret = new ReadDataResult
        {
            Value = variable.Content ?? "",
            Time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
            Status = VariableStatus.VariableStatusGood,
            Id = variable.Identifier,
            DataType = variable.DataType ?? "string"
        };
        return ret;
    }

    /// <summary>
    ///     读取标签
    /// </summary>
    /// <returns></returns>
    public async Task ReadLabel(PayLoad payLoad)
    {
        if (!Driver.IsConnected) return;

        // 检查 ValueSourceEnum.Read 键是否存在于字典中
        if (!ValueSourceDeviceLabel.TryGetValue(ValueSourceEnum.Read, out var readLabels) || readLabels == null || !readLabels.Any())
        {
            // 如果键不存在或值为空，直接返回
            return;
        }

        // 筛选满足读取条件的标签
        var activeLabels = readLabels.Where(ShouldReadVariable).ToList();
        if (!activeLabels.Any()) return;

        // 记录读取开始时间
        _readLabelWatch.Restart();

        // 更新参数缓存
        UpdateReadInputsCache(activeLabels);

        // 如果缓存的读取输入为空，则说明没有需要读取的参数
        if (_cachedReadInputs.Count == 0) return;

        // 使用缓存的参数列表和委托
        List<ReadDataResult> results;

        try
        {
            // 使用缓存的委托方法
            results = await _cachedReadMethod(_cachedReadInputs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备 {DeviceId} 读取标签失败", Device.Identifier);
            results = new List<ReadDataResult>();
        }

        // 计算读取耗时
        _readLabelWatch.Stop();
        var totalElapsedMs = _readLabelWatch.ElapsedMilliseconds;

        // 如果有结果，则处理
        if (results != null && results.Count > 0)
        {
            // 计算单个点位的平均耗时
            var pointElapsedMs = totalElapsedMs / results.Count;

            // 记录读取性能
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug(
                    "设备 {DeviceId} 读取标签完成: 总数={TotalCount}, 成功数={SuccessCount}, 总耗时={TotalTimeMs}ms, 平均耗时={AverageTimeMs}ms/点",
                    Device.Identifier,
                    _cachedReadInputs.Count,
                    results.Count,
                    totalElapsedMs,
                    pointElapsedMs);
            }

            // 处理结果
            await ProcessReadResults(results, payLoad, pointElapsedMs);
        }
        else
        {
            // 记录无结果的情况
            _logger.LogWarning(
                "设备 {DeviceId} 读取标签无返回结果: 请求数={RequestCount}, 耗时={TotalTimeMs}ms",
                Device.Identifier,
                _cachedReadInputs.Count,
                totalElapsedMs);
        }
    }

    /// <summary>
    /// 处理读取结果
    /// </summary>
    private async Task ProcessReadResults(List<ReadDataResult> results, PayLoad payLoad, long pointElapsedMs)
    {
        foreach (var result in results.Where(r => r != null))
        {
            try
            {
                await _dataChannel.Writer.WriteAsync(result);

                // 确保DeviceLabelSource包含结果ID
                if (!DeviceLabelSource.TryGetValue(result.Id, out var variable))
                {
                    _logger.LogWarning("设备 {DeviceId} 收到未知标签结果: {LabelId}", Device.Identifier, result.Id);
                    continue;
                }

                // 添加写入payLoad的逻辑，包含状态信息
                var paramValue = new ParamValue
                {
                    Time = result.Time,
                    Value = result.Value,
                    Name = variable.Name,
                    TransitionType = variable.TransitionType,
                    Status = result.Status,
                    ErrorMessage = result.ErrMsg
                };
                payLoad.Labels.TryAdd(result.Id, paramValue);

                // 推送点位采集日志（异步执行，不阻塞主流程）
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await PushPointCollectionLog(
                            variable.Identifier,
                            variable.Name,
                            result.Value,
                            result.Value,
                            result.Status,
                            result.ErrMsg,
                            pointElapsedMs,
                            false
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "推送点位采集日志失败: {LabelId}", variable.Identifier);
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理标签结果失败: {LabelId}", result.Id);
            }
        }
    }

    /// <summary>
    ///     是否满足标签的设置读取周期时间
    /// </summary>
    /// <param name="variable"></param>
    /// <returns></returns>
    private bool ShouldReadVariable(DeviceLabel variable)
    {
        if (variable.Period <= 0) return true;

        var lastRead = DataStorage.Instance.Get(Device.Identifier + "." + variable.Identifier);
        if (lastRead == null) return true;

        var timeSinceLastRead = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - lastRead.ReadTime;
        return timeSinceLastRead >= variable.Period;
    }

    /// <summary>
    ///     更新参数缓存
    /// </summary>
    private void UpdateReadInputsCache(IEnumerable<DeviceLabel> activeLabels)
    {
        // 只在参数变化时重建列表
        bool needsUpdate = false;
        var currentKeys = new HashSet<string>();

        foreach (var label in activeLabels)
        {
            currentKeys.Add(label.Identifier);
            if (!_readInputMap.ContainsKey(label.Identifier))
            {
                // 创建新的读取输入参数
                var input = CreateReadInput(label);
                _readInputMap[label.Identifier] = input;
                needsUpdate = true;
            }
            else
            {
                // 检查参数是否需要更新（地址、方法或编码可能变化）
                var existingInput = _readInputMap[label.Identifier];
                if (existingInput.Address != label.RegisterAddress ||
                    existingInput.Method != label.Method ||
                    existingInput.Encoding != label.Encoding ||
                    existingInput.Length != label.ReadLength)
                {
                    // 参数已变化，更新
                    _readInputMap[label.Identifier] = CreateReadInput(label);
                    needsUpdate = true;
                }
            }
        }

        // 删除不再需要的参数
        foreach (var key in _readInputMap.Keys.ToList())
        {
            if (!currentKeys.Contains(key))
            {
                _readInputMap.Remove(key);
                needsUpdate = true;
            }
        }

        // 只在必要时重建列表
        if (needsUpdate || _cachedReadInputs.Count == 0)
        {
            _cachedReadInputs = _readInputMap.Values.ToList();

            // 记录参数更新日志
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug(
                    "设备 {DeviceId} 更新读取参数: 总数={TotalCount}, 是否重建={NeedsUpdate}",
                    Device.Identifier,
                    _cachedReadInputs.Count,
                    needsUpdate);
            }
        }
    }

    /// <summary>
    ///     读取完毕后执行脚本
    /// </summary>
    /// <param name="payLoad">当前设备的数据负载</param>
    /// <returns>异步任务</returns>
    public async Task RunScript(PayLoad payLoad)
    {
        try
        {
            // 创建标签值的安全副本，添加空检查和并发保护
            Dictionary<string, ParamValue> labelsCopy;
            try
            {
                // 创建标签值的深拷贝，避免在脚本执行过程中被修改
                labelsCopy = payLoad?.Labels != null
                    ? new Dictionary<string, ParamValue>(
                        payLoad.Labels.ToDictionary(
                            entry => entry.Key,
                            entry => new ParamValue
                            {
                                Time = entry.Value.Time,
                                Value = entry.Value.Value,
                                Name = entry.Value.Name,
                                TransitionType = entry.Value.TransitionType
                            }
                        ))
                    : new Dictionary<string, ParamValue>();
            }
            catch (Exception)
            {
                // 如果复制过程出现异常，则使用空字典
                labelsCopy = new Dictionary<string, ParamValue>();
            }

            // 检查计算类型的变量集合是否存在
            if (!ValueSourceDeviceLabel.TryGetValue(ValueSourceEnum.Calculate, out var calculateLabels) || calculateLabels == null)
            {
                return; // 如果没有计算类型的标签，直接返回
            }

            // 遍历所有计算类型的标签并执行脚本
            foreach (var variable in calculateLabels)
            {
                try
                {
                    // 检查是否需要根据周期执行脚本
                    // 设置周期时间的情况下进行时间判断 => 防止系统时间被调整导致脚本不执行的问题
                    if (variable.Period > 0)
                    {
                        // 检查当前标签是否已存在于负载中
                        if (payLoad?.Labels != null && payLoad.Labels.TryGetValue(variable.Identifier, out var existingValue))
                        {
                            // 计算时间差：当前时间 - 上次执行时间
                            long timeSinceLastExecution = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - existingValue.Time;

                            // 如果未达到配置的执行周期，则跳过当前标签的脚本执行
                            if (timeSinceLastExecution < variable.Period)
                            {
                                continue;
                            }
                        }
                    }

                    // 创建标准返回对象
                    var resultData = CreateReadDataResult(variable);

                    // 获取脚本内容
                    string scriptContent = variable.Content;

                    // 确保脚本内容不为空再执行
                    if (!string.IsNullOrEmpty(scriptContent))
                    {
                        // 记录脚本执行开始时间
                        var scriptStartTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                        // 创建标签值字典用于脚本执行
                        var scriptVariables = labelsCopy.ToDictionary(
                            k => k.Key,
                            v => (object)(v.Value.Value ?? string.Empty)
                        );

                        // 创建设备对象
                        scriptVariables["dev"] = new
                        {
                            id = Device.Id,
                            identifier = Device.Identifier,
                            name = Device.Name ?? "测试设备",
                            status = Device.Status,
                            lastActiveTime = Device.LastActiveTime,
                            onlineTime = _todayOnlineMinutes,
                            isOnline = Driver.IsConnected
                        };

                        // 添加tags对象，用于引用设备标签值
                        try
                        {
                            // 创建支持点操作符访问的嵌套标签结构
                            scriptVariables["tags"] = TagsHelper.CreateNestedTagsObject(
                                labelsCopy,
                                paramValue => paramValue.Name,
                                paramValue => paramValue.Value,
                                paramValue => paramValue.Time,
                                paramValue => paramValue.TransitionType.ToString()
                            );
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"创建嵌套标签结构失败，将使用平面结构 - 设备:{Device.Identifier}");
                            // 回退到平面结构
                            scriptVariables["tags"] = labelsCopy.ToDictionary(
                                k => k.Key,
                                v => (object)(v.Value.Value ?? string.Empty)
                            );
                        }

                        var originalValue = labelsCopy.ContainsKey(variable.Identifier)
                            ? labelsCopy[variable.Identifier].Value
                            : null;

                        resultData.Value = await _scriptEnginePool.ExecuteScriptAsync(
                            $"calculate_{variable.Identifier}",
                            scriptContent,
                            scriptVariables
                        );

                        // 记录脚本执行结束时间
                        var scriptEndTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                        var scriptElapsedMs = scriptEndTime - scriptStartTime;

                        // 添加写入payLoad的逻辑，包含状态信息
                        var paramValue = new ParamValue
                        {
                            Time = resultData.Time,
                            Value = resultData.Value,
                            Name = variable.Name,
                            TransitionType = variable.TransitionType,
                            Status = resultData.Status, // 设置状态
                            ErrorMessage = resultData.ErrMsg // 设置错误信息
                        };
                        payLoad?.Labels?.TryAdd(variable.Identifier, paramValue);

                        // 推送点位采集日志（异步执行，不阻塞主流程）
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await PushPointCollectionLog(
                                    variable.Identifier,
                                    variable.Name,
                                    originalValue,
                                    resultData.Value,
                                    resultData.Status,
                                    resultData.ErrMsg,
                                    scriptElapsedMs,
                                    true // 这里是表达式处理
                                );
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, $"推送点位采集日志失败: {variable.Identifier}");
                            }
                        });

                        // 将有效的结果写入Channel
                        await _dataChannel.Writer.WriteAsync(resultData);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"执行脚本失败 - 设备:{Device.Identifier} 标签:{variable.Identifier}");

                    // 脚本执行失败时，创建错误状态的参数值
                    var errorParamValue = new ParamValue
                    {
                        Time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                        Value = null,
                        Name = variable.Name,
                        TransitionType = variable.TransitionType,
                        Status = VariableStatus.VariableStatusError,
                        ErrorMessage = ex.Message
                    };
                    payLoad?.Labels?.TryAdd(variable.Identifier, errorParamValue);

                    // 推送脚本执行失败的点位采集日志（异步执行，不阻塞主流程）
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await PushPointCollectionLog(
                                variable.Identifier,
                                variable.Name,
                                null,
                                null,
                                VariableStatus.VariableStatusError,
                                ex.Message,
                                0, // 脚本执行失败，耗时为0
                                true // 这里是表达式处理
                            );
                        }
                        catch (Exception logEx)
                        {
                            _logger.LogWarning(logEx, $"推送点位采集日志失败: {variable.Identifier}");
                        }
                    });
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"RunScript 方法执行失败 - 设备:{Device.Identifier}");
            throw;
        }
    }

    /// <summary>
    ///     根据数据类型和长度处理数据值
    /// </summary>
    private void ProcessVariableValue(DeviceLabel variable, ReadDataResult ret)
    {
        // 处理长度限制
        if (variable.Length > 0)
            ret.Value = variable.TransitionType switch
            {
                VariableStatus.TransitionNumber => ProcessNumberWithLength(ret.Value, variable.Length ?? 2),
                VariableStatus.TransitionInt when double.IsNaN(Convert.ToDouble(ret.Value)) => 0,
                VariableStatus.TransitionString when ret.Value?.ToString()?.Length > variable.Length
                    => ret.Value.ToString()?.Substring(0, variable.Length ?? 2),
                _ => ret.Value
            };

        // 根据转换类型处理数据
        switch (variable.TransitionType)
        {
            case VariableStatus.TransitionInt:
                if (double.IsNaN(Convert.ToDouble(ret.Value))) ret.Value = 0;
                break;

            case VariableStatus.TransitionNumber:
                ret.Value = ProcessNumberWithLength(ret.Value, variable.Length ?? 2);
                break;

            case VariableStatus.TransitionString:
                ret.Value = ProcessString(ret.Value, variable.Length);
                break;
        }
    }

    /// <summary>
    ///     处理数字类型,保留指定小数位
    /// </summary>
    private decimal ProcessNumberWithLength(object value, int length)
    {
        var number = double.IsNaN(Convert.ToDouble(value)) ? 0 : Convert.ToDecimal(value);
        return Math.Round(number, length);
    }

    /// <summary>
    ///     处理字符串类型,去除乱码并限制长度
    /// </summary>
    private string ProcessString(object value, int? maxLength)
    {
        if (value == null) return string.Empty;

        var str = value.ToString()!;

        // 清理乱码
        if (str.Contains("\u0000") || str.Contains("")) str = str.Replace("\u0000", "");

        // 限制长度
        if (maxLength.HasValue && str.Length > maxLength.Value) str = str.Substring(0, maxLength.Value);

        return str;
    }

    /// <summary>
    ///     根据数据类型转换结果值
    /// </summary>
    private void SetVariableValue(DeviceLabel variable, ReadDataResult ret, long elapsedMs = 0)
    {
        var originalValue = ret.Value;

        // 处理数据转换
        ProcessVariableValue(variable, ret);

        var processedValue = ret.Value;

        // 创建参数值对象，包含状态信息
        var paramValue = new ParamValue
        {
            Time = ret.Time,
            Value = ret.Value,
            Name = variable.Name,
            TransitionType = variable.TransitionType,
            Status = ret.Status, // 设置读取状态
            ErrorMessage = ret.ErrMsg // 设置错误信息
        };

        _payLoad.Labels.TryAdd(variable.Identifier, paramValue);

        // socket 实时推送数据
        _messagePushService.PushMessageAsync(MessageTopics.DeviceOnlineData, ret);
        // 读取失败的值不更新
        if (ret.Status == VariableStatus.VariableStatusGood && string.IsNullOrEmpty(ret.ErrMsg))
            DataStorage.Instance.Set(Device.Identifier + "." + variable.Identifier, ret);
    }

    /// <summary>
    ///     计算和更新PayLoad的整体状态
    /// </summary>
    /// <param name="payLoad">要更新状态的PayLoad</param>
    private void UpdatePayLoadStatus(PayLoad payLoad)
    {
        if (payLoad?.Labels == null || !payLoad.Labels.Any())
        {
            payLoad.Status = DeviceCollectionStatus.AllFailed;
            payLoad.StatusSummary = new CollectionStatusSummary();
            return;
        }

        var statusSummary = new CollectionStatusSummary
        {
            TotalCount = payLoad.Labels.Count
        };

        // 统计各种状态的数量
        foreach (var label in payLoad.Labels.Values)
        {
            switch (label.Status)
            {
                case VariableStatus.VariableStatusGood:
                    statusSummary.SuccessCount++;
                    break;
                case VariableStatus.VariableStatusError:
                    statusSummary.ErrorCount++;
                    break;
                case VariableStatus.VariableStatusBad:
                    statusSummary.FailedCount++;
                    break;
            }
        }

        // 更新状态统计
        payLoad.StatusSummary = statusSummary;

        // 计算整体状态
        if (statusSummary.SuccessCount == statusSummary.TotalCount)
        {
            // 全部成功
            payLoad.Status = DeviceCollectionStatus.AllSuccess;
        }
        else if (statusSummary.SuccessCount > 0)
        {
            // 部分成功
            payLoad.Status = DeviceCollectionStatus.PartialSuccess;
        }
        else
        {
            // 全部失败
            payLoad.Status = DeviceCollectionStatus.AllFailed;
        }

        // 记录状态统计日志
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug(
                "设备 {DeviceIdentifier} 采集状态统计: 总数={TotalCount}, 成功={SuccessCount}, 错误={ErrorCount}, 失败={FailedCount}, 成功率={SuccessRate:F2}%, 整体状态={Status}",
                Device.Identifier,
                statusSummary.TotalCount,
                statusSummary.SuccessCount,
                statusSummary.ErrorCount,
                statusSummary.FailedCount,
                statusSummary.SuccessRate,
                payLoad.Status);
        }
    }

    /// <summary>
    ///     停止采集线程
    /// </summary>
    public Task StopThread()
    {
        if (Task == null) return Task.CompletedTask;
        Driver.Close();
        _tokenSource.Cancel();
        return Task.CompletedTask;
    }

    /// <summary>
    ///     释放采集线程
    /// </summary>
    public void Dispose()
    {
        try
        {
            _tokenSource.Cancel();
            Driver.Close();
            Driver.Dispose();

            // 完成Channel写入
            _reportChannel.Writer.Complete();

            // 停止计时并保存最终的在线时间
            _onlineTimeWatch.Stop();
            var finalOnlineTime = GetTotalOnlineTime();

            // 添加重试机制
            var retryCount = 0;
            const int maxRetries = 3;
            while (retryCount < maxRetries)
                try
                {
                    DataStorage.Instance.SetOnlineTime(Device.Identifier, finalOnlineTime).Wait();
                    break;
                }
                catch (IOException)
                {
                    retryCount++;
                    if (retryCount == maxRetries)
                        throw;
                    Task.Delay(100 * retryCount).Wait(); // 递增延迟
                }

            PushDeviceLog("warning", $"设备 {Device.Identifier} 停止采集，在线时间: {finalOnlineTime.TotalMinutes:F2}分钟，重连次数: {_reconnectCount}，连接成功次数: {_connectSuccessCount}，消息数: {_currentMinuteMessageCount}").GetAwaiter().GetResult();

            _logger.LogInformation($"{Device.Identifier} 停止采集，最终在线时间: {finalOnlineTime.TotalMinutes:F2} 分钟");
        }
        catch (Exception ex)
        {
            PushDeviceLog("error", $"设备 {Device.Identifier} 停止采集时发生错误: {ex.Message}").GetAwaiter().GetResult();
            _logger.LogError(ex, $"{Device.Identifier} 停止采集时发生错误");
        }
    }

    /// <summary>
    ///     设备报文回调
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void DriverOutputReceived(object sender, string e)
    {
        // 推送实时报文信息
        _messagePushService.PushMessageAsync(MessageTopics.GetDeviceDebugTopic(Device.Id), e);
    }

    /// <summary>
    ///     更新设备属性
    /// </summary>
    /// <param name="newLabels"></param>
    /// <param name="overwrite"></param>
    public void UpdateDeviceLabel(List<DeviceLabel> newLabels, bool overwrite = true)
    {
        lock (_LabelLock)
        {
            if (overwrite)
            {
                // 完全覆盖模式
                DeviceLabelSource.Clear();
                foreach (var Label in newLabels) DeviceLabelSource[Label.Identifier] = Label;
            }
            else
            {
                // 追加模式 - 只更新已存在的和添加新的
                foreach (var Label in newLabels) DeviceLabelSource[Label.Identifier] = Label;
            }

            // 重新分类属性
            RefreshLabelSourceCategories();
        }
    }

    /// <summary>
    ///     添加单个设备属性
    /// </summary>
    /// <param name="Label"></param>
    public void AddDeviceLabel(DeviceLabel Label)
    {
        lock (_LabelLock)
        {
            DeviceLabelSource[Label.Identifier] = Label;
            RefreshLabelSourceCategories();
        }
    }

    /// <summary>
    ///     删除设备属性
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    public bool RemoveDeviceLabel(string identifier)
    {
        lock (_LabelLock)
        {
            var removed = DeviceLabelSource.Remove(identifier);
            if (removed) RefreshLabelSourceCategories();
            return removed;
        }
    }

    /// <summary>
    ///     批量删除设备属性
    /// </summary>
    /// <param name="identifiers"></param>
    public void RemoveDeviceLabels(IEnumerable<string> identifiers)
    {
        lock (_LabelLock)
        {
            var anyRemoved = false;
            foreach (var identifier in identifiers)
                if (DeviceLabelSource.Remove(identifier))
                    anyRemoved = true;

            if (anyRemoved) RefreshLabelSourceCategories();
        }
    }

    /// <summary>
    ///     清空所有设备属性
    /// </summary>
    public void ClearDeviceLabels()
    {
        lock (_LabelLock)
        {
            DeviceLabelSource.Clear();
            ValueSourceDeviceLabel.Clear();
        }
    }

    /// <summary>
    ///     获取设备属性
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    public DeviceLabel? GetDeviceLabel(string identifier)
    {
        lock (_LabelLock)
        {
            return DeviceLabelSource.TryGetValue(identifier, out var Label) ? Label : null;
        }
    }

    /// <summary>
    ///     重新分类属性的私有方法
    /// </summary>
    private void RefreshLabelSourceCategories()
    {
        ValueSourceDeviceLabel.Clear();

        // 自然数据
        ValueSourceDeviceLabel[ValueSourceEnum.Static] = DeviceLabelSource.Values
            .Where(w => w.ValueSource == ValueSourceEnum.Static)
            .OrderBy(o => o.ActionOrder)
            .ToList();
        // 读取属性
        ValueSourceDeviceLabel[ValueSourceEnum.Read] = DeviceLabelSource.Values
            .Where(w => w.ValueSource == ValueSourceEnum.Read)
            .OrderBy(o => o.ActionOrder)
            .ToList();

        //  计算属性
        ValueSourceDeviceLabel[ValueSourceEnum.Calculate] = DeviceLabelSource.Values
            .Where(w => w.ValueSource == ValueSourceEnum.Calculate && !string.IsNullOrWhiteSpace(w.Content))
            .OrderBy(o => o.ActionOrder)
            .ToList();

        //  虚拟属性
        ValueSourceDeviceLabel[ValueSourceEnum.Fictitious] = DeviceLabelSource.Values
            .Where(w => w.ValueSource == ValueSourceEnum.Fictitious)
            .ToList();
    }

    /// <summary>
    ///     启动数据处理任务
    /// </summary>
    /// <returns></returns>
    private async Task StartDataProcessingTask()
    {
        await foreach (var data in _dataChannel.Reader.ReadAllAsync())
            try
            {
                // 处理数据
                ProcessData(data);

                // 使用原子操作增加计数
                Interlocked.Increment(ref _readCount);

                // 仅在Windows平台上使用PerformanceCounter
                if (OperatingSystem.IsWindows()) _readCounter?.Increment();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理数据失败: {ex.Message}");
            }
    }

    /// <summary>
    ///     创建读取输入
    /// </summary>
    /// <param name="variable"></param>
    /// <returns></returns>
    private DriverReadInput CreateReadInput(DeviceLabel variable)
    {
        // 创建读取输入
        return new DriverReadInput(variable.Identifier, variable?.RegisterAddress, variable?.DataType, variable?.Method, variable?.ReadLength, variable?.Encoding);
    }

    /// <summary>
    ///     添加数据处理方法
    /// </summary>
    /// <param name="ret"></param>
    private void ProcessData(ReadDataResult ret)
    {
        try
        {
            // 获取对应的标签配置
            var variable = DeviceLabelSource[ret.Id];

            // 根据数据类型转换数据值
            SetVariableValue(variable, ret);

            if (!string.IsNullOrEmpty(ret.ErrMsg))
            {
                // 送失败消息,生成模板字符串
                var template = TP.Wrapper("数据采集", "读取失败",
                    $"##标签## {variable.Identifier}",
                    $"##地址## {(variable.RegisterAddress.IsNotEmptyOrNull() ? variable.RegisterAddress : variable.Method)}",
                    $"##采集状态## {ret.Status}",
                    $"##异常信息## {ret.ErrMsg}");
                // 使用统一消息主题发送
                _messagePushService.PushMessageAsync(MessageTopics.GetDeviceDataTopic(Device.Id), template);
            }
            else
            {
                // 推送成功消息,生成模板字符串
                var template = TP.Wrapper("数据采集", "读取成功",
                    $"##标签## {variable.Identifier}",
                    $"##地址## {(variable.RegisterAddress.IsNotEmptyOrNull() ? variable.RegisterAddress : variable.Method)}",
                    $"##采集状态## {ret.Status}",
                    $"##标签值## {ret.Value}");
                // 使用统一消息主题发送
                _messagePushService.PushMessageAsync(MessageTopics.GetDeviceDataTopic(Device.Id), template);
            }
        }
        catch (Exception ex)
        {
            PushDeviceLog("error", $"设备 {Device.Identifier} 数据处理失败: {ex.Message}").GetAwaiter().GetResult();
            ConsoleEx.WriteLine($"处理数据失败: {ex.Message}", "red");
        }
    }

    /// <summary>
    ///     更新设备状态
    /// </summary>
    /// <param name="status"></param>
    private void UpdateDeviceStatus(bool status)
    {
        // 更新全局状态存储
        DataStorage.Instance.SetDeviceStatus(Device.Identifier, status);

        // 状态相同不处理
        if (_currentStatus == status) return;
        // 更新状态
        _currentStatus = status;
        // 触发状态变更事件
        OnDeviceStatusChanged(new DeviceStatusEventArgs(status));
        // 发布设备状态变更事件 - 使用强类型事件对象
        _eventPublisher.PublishAsync("device_status_changed", new DeviceStatusChangedEvent
        {
            DeviceId = Device.Id,
            DeviceIdentifier = Device.Identifier,
            DeviceName = Device.Name ?? Device.Identifier,
            IsOnline = status,
            Status = status ? DeviceStatusTypeEnum.Good : DeviceStatusTypeEnum.Bad,
            EventTime = DateTime.Now,
            ErrorMessage = null,
            Location = null,
            DriverName = Driver?.GetType().Name
        });

        // 发布设备线程状态变更事件
        _eventPublisher.PublishAsync("device_thread_status_changed", new DeviceThreadStatusEvent
        {
            DeviceId = Device.Identifier,
            IsRunning = status,
            Message = status ? "设备已连接" : "设备已断开"
        });

        // 处理设备事件
        if (_deviceEventService != null)
        {
            try
            {
                // 异步处理，不等待结果
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _deviceEventService.ProcessDeviceStatusChangeAsync(Device.Id, Device.Identifier, status);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"设备 {Device.Identifier} 处理状态变更事件失败");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"设备 {Device.Identifier} 提交状态变更事件失败");
            }
        }
    }

    /// <summary>
    ///     添加新的队列处理方法
    /// </summary>
    private async Task ProcessReportData()
    {
        await foreach (var payLoad in _reportChannel.Reader.ReadAllAsync(_tokenSource.Token))
        {
            try
            {
                if (!payLoad.Labels.Any())
                    return;

                // 获取报警引擎服务
                IAlarmEngineService alarmEngine;

                // 如果提供了服务作用域工厂，则使用它创建作用域
                if (_serviceScopeFactory != null)
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    alarmEngine = scope.ServiceProvider.GetRequiredService<IAlarmEngineService>();
                }
                else
                {
                    // 改进的错误处理 - 提供更清晰的诊断信息
                    var errorMessage = "IServiceScopeFactory 未注入，无法解析 Scoped 服务 IAlarmEngineService。请检查 DeviceThreadFactory 的依赖注入配置。";
                    _logger.LogError(errorMessage);
                    throw new InvalidOperationException(errorMessage);
                }

                // 直接触发报警评估
                await alarmEngine.OnPayloadReceived(payLoad);

                // 处理设备事件
                if (_deviceEventService != null)
                {
                    try
                    {
                        // 异步处理，不等待结果
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await _deviceEventService.ProcessDeviceDataAsync(Device.Id, Device.Identifier, payLoad);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, $"设备 {Device.Identifier} 处理数据事件失败");
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"设备 {Device.Identifier} 提交数据事件失败");
                    }
                }

                var shouldReport = false;

                #region 时序数据存储

                // 设备设置存储历史数据
                if (Device.DeviceInfo.StoreHistoryData)
                {
                    // 转换为时序数据
                    var timeSeriesData = new TimeSeriesData
                    {
                        DeviceId = payLoad.Id,
                        Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(payLoad.Ts).UtcDateTime,
                        ExtraTags = payLoad.Labels.ToDictionary(
                            kvp => kvp.Key,
                            kvp => kvp.Value.Value
                        )
                    };

                    // 异步写入时序数据
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await _timeSeriesStorage.WriteAsync(timeSeriesData);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "写入时序数据失败: {DeviceId}", payLoad.Id);
                        }
                    });
                }

                #endregion

                // 根据设备数据上报策略进行处理
                switch (Device.DeviceInfo.ReportType)
                {
                    case DataReportTypeEnum.Always when Driver.IsConnected: // 一直上报
                        shouldReport = true;
                        break;
                    case DataReportTypeEnum.OnValueChanged when Driver.IsConnected: // 值变化上报
                        shouldReport = HasDataChanged(payLoad);
                        break;
                    case DataReportTypeEnum.OnDemand when Driver.IsConnected: // 按照设置上报
                    case DataReportTypeEnum.OnDemandWithPowerOff: // 按照设置上报并断线后上报
                        // 过滤变化上报和从不上报的点位
                        var devicePrefix = Device.Identifier + ".";

                        // 使用HashSet提高查找性能
                        var labelsToRemove = new HashSet<string>();

                        // 一次性获取所有需要处理的标签并缓存结果
                        var changeAndNever = DeviceLabelSource.Values
                            .Where(label => label.SendType is SendTypeEnum.Changed or SendTypeEnum.Never)
                            .ToList();

                        // 批量预加载所有需要的数据存储值
                        var identifiers = changeAndNever.Select(l => devicePrefix + l.Identifier);
                        var lastValues = DataStorage.Instance.GetBatch(identifiers);

                        foreach (var label in changeAndNever)
                        {
                            var identifier = label.Identifier;

                            // 从不上报直接加入移除列表
                            if (label.SendType == SendTypeEnum.Never)
                            {
                                labelsToRemove.Add(identifier);
                                continue;
                            }

                            // 变化上报检查 - 使用预加载的值
                            if (lastValues.TryGetValue(devicePrefix + identifier, out var lastValue))
                                if (lastValue?.CookieValue == lastValue?.Value)
                                    labelsToRemove.Add(identifier);
                        }

                        // 使用批量移除优化性能
                        if (labelsToRemove.Count > 0)
                            foreach (var key in labelsToRemove)
                                payLoad.Labels.TryRemove(key, out _);

                        shouldReport = true;
                        break;
                }

                // 如果需要上报，则写入全局队列
                if (shouldReport)
                {
                    // 写入全局队列用于转发
                    _queueService.Enqueue(payLoad);

                    // 增加一个策略方式，降低这个日志的推送频率，一般15秒一次即可   
                    if (DateTime.Now.Second % 15 == 0)
                        await PushDeviceLog("info", $"设备 {Device.Identifier} 数据上报，标签数: {payLoad.Labels.Count}，消息数: {_currentMinuteMessageCount}，上报类型: {Device.DeviceInfo.ReportType}");

                    // 更新消息计数
                    var currentMinute = DateTime.Now.Minute;
                    if (currentMinute != _lastMinute.Minute)
                    {
                        // 新的一分钟，重置计数
                        _currentMinuteMessageCount = 0;
                        _lastMinute = DateTime.Now;
                    }

                    Interlocked.Increment(ref _currentMinuteMessageCount);
                }

                // 持久化在线时长
                if (Driver.IsConnected)
                {
                    var totalOnlineTime = GetTotalOnlineTime();
                    // 只有当在线时间发生变化时才更新
                    if (Math.Abs(totalOnlineTime.TotalMinutes - _todayOnlineMinutes) >= 1)
                    {
                        _todayOnlineMinutes = totalOnlineTime.TotalMinutes;
                        try
                        {
                            // 添加重试机制
                            var retryCount = 0;
                            const int maxRetries = 3;
                            while (retryCount < maxRetries)
                                try
                                {
                                    await DataStorage.Instance.SetOnlineTime(Device.Identifier, totalOnlineTime);
                                    break;
                                }
                                catch (IOException)
                                {
                                    retryCount++;
                                    if (retryCount == maxRetries)
                                        throw;
                                    await Task.Delay(100 * retryCount); // 递增延迟
                                }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"设备 {Device.Identifier} 更新在线时间失败");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await PushDeviceLog("error", $"设备 {Device.Identifier} 数据上报处理失败: {ex.Message}");
                _logger.LogError(ex, $"设备 {Device.Identifier} 处理数据失败");
            }
        }
    }

    /// <summary>
    ///     添加数据变化检测方法
    /// </summary>
    /// <param name="newPayLoad"></param>
    /// <returns></returns>
    private bool HasDataChanged(PayLoad newPayLoad)
    {
        // 缓存设备标识前缀,避免重复拼接
        var devicePrefix = Device.Identifier + ".";
        // 使用 ValueTuple 避免创建临时对象
        var (hasChanged, _) = newPayLoad.Labels.Select(label =>
        {
            // 获取上次数据
            var lastPayLoad = DataStorage.Instance.Get(devicePrefix + label.Key);
            // 比较数据是否发生变化
            return (lastPayLoad == null || !Equals(lastPayLoad.CookieValue, lastPayLoad.Value), true);
        }).FirstOrDefault(t => t.Item1);
        return hasChanged;
    }

    /// <summary>
    ///     初始化当日在线时间
    /// </summary>
    private async Task InitializeTodayOnlineTime()
    {
        try
        {
            // 获取当日已累计的在线时间
            _todayOnlineMinutes = await DataStorage.Instance.GetOnlineTime(Device.Identifier, DateTime.Today);

            // 如果设备已连接，启动计时器
            if (Driver.IsConnected)
            {
                // 重置计时器
                _onlineTimeWatch.Reset();
                _onlineTimeWatch.Start();
            }

            _logger.LogInformation($"设备 {Device.Identifier} 初始化当日在线时间: {_todayOnlineMinutes:F2} 分钟");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设备 {Device.Identifier} 初始化当日在线时间失败");
        }
    }

    /// <summary>
    ///     修改获取总在线时间的逻辑
    /// </summary>
    /// <returns></returns>
    private TimeSpan GetTotalOnlineTime()
    {
        // 返回初始累计时间加上当前计时器时间
        return TimeSpan.FromMinutes(_todayOnlineMinutes) + _onlineTimeWatch.Elapsed;
    }

    /// <summary>
    ///     推送设备日志
    /// </summary>
    /// <param name="type"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    private async Task PushDeviceLog(string type, string message)
    {
        // 根据日志类型和资源状态确定是否推送
        LogLevel logLevel = type switch
        {
            "error" => LogLevel.Error,
            "warning" => LogLevel.Warning,
            _ => LogLevel.Information
        };

        // 如果资源管理器存在且日志不应该记录，则跳过推送
        if (_resourceManager != null && !_resourceManager.ShouldLogMessage(logLevel))
            return;

        var logMessage = DeviceLogHelper.CreateLogMessage(Device.Identifier, type, message);
        await _messagePushService.PushMessageAsync(MessageTopics.GetDeviceStatusTopic(Device.Id), logMessage);
    }

    /// <summary>
    /// 推送点位采集日志
    /// </summary>
    /// <param name="pointIdentifier">点位标识</param>
    /// <param name="pointName">点位名称</param>
    /// <param name="originalValue">原始值</param>
    /// <param name="processedValue">处理后的值</param>
    /// <param name="status">采集状态</param>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="elapsedMs">采集耗时</param>
    /// <param name="hasExpressionProcessing">是否有表达式处理</param>
    /// <returns></returns>
    private async Task PushPointCollectionLog(
        string pointIdentifier,
        string pointName,
        object originalValue,
        object processedValue,
        string status,
        string errorMessage = null,
        long elapsedMs = 0,
        bool hasExpressionProcessing = false)
    {
        // 根据采集状态确定日志级别
        LogLevel logLevel = status switch
        {
            VariableStatus.VariableStatusError => LogLevel.Error,
            VariableStatus.VariableStatusBad => LogLevel.Warning,
            _ => LogLevel.Information
        };

        // 如果资源管理器存在且日志不应该记录，则跳过推送
        if (_resourceManager != null && !_resourceManager.ShouldLogMessage(logLevel))
            return;

        // 频率控制：对于成功状态的日志，限制推送频率以避免性能问题
        var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        if (status == VariableStatus.VariableStatusGood)
        {
            // var lastLogTime = _lastPointLogTime.GetOrAdd(pointIdentifier, 0);
            // if (currentTime - lastLogTime < POINT_LOG_INTERVAL_MS)
            // {
            //     return; // 跳过推送，避免过于频繁
            // }
            _lastPointLogTime[pointIdentifier] = currentTime;
        }
        else
        {
            // 错误和警告状态的日志始终推送，但也更新时间戳
            _lastPointLogTime[pointIdentifier] = currentTime;
        }

        var pointLogMessage = DeviceLogHelper.CreatePointCollectionLogMessage(
            Device.Identifier,
            pointIdentifier,
            pointName,
            originalValue,
            processedValue,
            status,
            errorMessage,
            elapsedMs,
            hasExpressionProcessing);

        await _messagePushService.PushMessageAsync(MessageTopics.GetDevicePointCollectionTopic(Device.Id), pointLogMessage);
    }

    /// <summary>
    /// 发布设备线程性能指标事件
    /// </summary>
    /// <param name="logLevel">日志级别</param>
    private void PublishThreadMetrics(LogLevel logLevel = LogLevel.Information)
    {
        try
        {
            // 发布设备线程性能指标事件
            _eventPublisher.PublishAsync("device_thread_metrics", new DeviceThreadMetricsEvent
            {
                DeviceId = Device.Identifier,
                CpuUsage = GetCpuUsage(),
                MemoryUsageMB = GetMemoryUsage(),
                ReadTime = _readLabelWatch.ElapsedMilliseconds,
                ScriptTime = _runScriptWatch.ElapsedMilliseconds,
                ReconnectCount = _reconnectCount,
                MessageCount = _currentMinuteMessageCount,
                LogLevel = logLevel
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设备 {Device.Identifier} 发布性能指标事件失败");
        }
    }

    /// <summary>
    /// 获取CPU使用率
    /// </summary>
    /// <returns>CPU使用率（百分比）</returns>
    private double GetCpuUsage()
    {
        // 简单估算，后续可以接入更精确的性能计数器
        return Math.Min(100, Math.Max(0,
            _readLabelWatch.ElapsedMilliseconds +
            _runScriptWatch.ElapsedMilliseconds +
            _readStaticWatch.ElapsedMilliseconds) / 10.0);
    }

    /// <summary>
    /// 获取内存使用量
    /// </summary>
    /// <returns>内存使用量（MB）</returns>
    private double GetMemoryUsage()
    {
        // 简单估算，后续可以接入更精确的性能计数器
        return 50.0; // 固定值，实际项目中应该通过性能计数器获取
    }

    /// <summary>
    ///     写入上报通道
    /// </summary>
    /// <param name="payLoad"></param>
    /// <returns></returns>
    public async Task ReportData(PayLoad payLoad)
    {
        await _reportChannel.Writer.WriteAsync(payLoad);
    }

    /// <summary>
    ///     需要暴露这些属性供 DeviceAccess 使用
    /// </summary>
    public Channel<ReadDataResult> DataChannel => _dataChannel;

    /// <summary>
    ///     获取PayLoad
    /// </summary>
    public PayLoad PayLoad => _payLoad;

    /// <summary>
    ///     设置PayLoad中的变量值
    /// </summary>
    /// <param name="propertyKey">属性标识</param>
    /// <param name="value">属性值</param>
    /// <param name="isFictitious">是否为虚拟属性</param>
    /// <returns>是否设置成功</returns>
    public bool SetPayLoadVariable(string propertyKey, object value, bool isFictitious = true)
    {
        try
        {
            // 检查 ValueSourceEnum.Fictitious 键是否存在于字典中
            if (!ValueSourceDeviceLabel.TryGetValue(ValueSourceEnum.Fictitious, out var fictitiousLabels) || fictitiousLabels == null || !fictitiousLabels.Any())
            {
                _logger?.LogWarning($"设置PayLoad变量[{propertyKey}]失败: 虚拟标签集合不存在");
                return false;
            }

            // 虚拟数据中找到是否存在
            var fictitious = fictitiousLabels.FirstOrDefault(l => l.Identifier == propertyKey);
            if (fictitious == null) return false;

            // 设置PayLoad中的变量值，包含状态信息
            _payLoad.Labels.AddOrUpdate(propertyKey, new ParamValue
            {
                Name = fictitious.Name,
                Value = value,
                Time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                TransitionType = fictitious.TransitionType,
                Status = VariableStatus.VariableStatusGood, // 手动设置的值默认为成功状态
                ErrorMessage = null
            }, (_, _) => new ParamValue
            {
                Name = fictitious.Name,
                Value = value,
                Time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                TransitionType = fictitious.TransitionType,
                Status = VariableStatus.VariableStatusGood, // 手动设置的值默认为成功状态
                ErrorMessage = null
            });
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"设置PayLoad变量[{propertyKey}]失败: {ex.Message}");

            // 设置错误状态的变量值
            try
            {
                var fictitious = ValueSourceDeviceLabel[ValueSourceEnum.Fictitious]?.FirstOrDefault(l => l.Identifier == propertyKey);
                if (fictitious != null)
                {
                    _payLoad.Labels.AddOrUpdate(propertyKey, new ParamValue
                    {
                        Name = fictitious.Name,
                        Value = null,
                        Time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                        TransitionType = fictitious.TransitionType,
                        Status = VariableStatus.VariableStatusError,
                        ErrorMessage = ex.Message
                    }, (_, _) => new ParamValue
                    {
                        Name = fictitious.Name,
                        Value = null,
                        Time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                        TransitionType = fictitious.TransitionType,
                        Status = VariableStatus.VariableStatusError,
                        ErrorMessage = ex.Message
                    });
                }
            }
            catch
            {
                // 忽略设置错误状态时的异常
            }

            return false;
        }
    }

    /// <summary>
    ///     批量设置PayLoad中的变量值
    /// </summary>
    /// <param name="values">属性值字典</param>
    /// <param name="isFictitious">是否为虚拟属性</param>
    /// <returns>是否设置成功</returns>
    public bool SetPayLoadVariableBatch(Dictionary<string, object> values, bool isFictitious = true)
    {
        try
        {
            foreach (var pair in values) SetPayLoadVariable(pair.Key, pair.Value, isFictitious);

            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"批量设置PayLoad变量失败: {ex.Message}");
            return false;
        }
    }
}

/// <summary>
///     添加事件参数类
/// </summary>
public class DeviceStatusEventArgs : EventArgs
{
    /// <summary>
    ///     设备状态
    /// </summary>
    public bool Status { get; }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="status"></param>
    public DeviceStatusEventArgs(bool status)
    {
        Status = status;
    }
}

// 协议驱动适配器
public class DriverAdapter
{
    private readonly IDriver _driver;
    private readonly Dictionary<string, DeviceLabel> _labelCache = new Dictionary<string, DeviceLabel>();

    public DriverAdapter(IDriver driver)
    {
        _driver = driver;
    }

    // 预配置标签
    public void ConfigureLabels(Dictionary<string, DeviceLabel> labels)
    {
        // 更新标签配置缓存
        foreach (var pair in labels)
        {
            _labelCache[pair.Key] = pair.Value;
        }
    }

    // 优化的读取方法
    public async Task<List<ReadDataResult>> ReadLabels(List<string> activeLabels)
    {
        // 只传递活动的标签ID，内部处理参数构造
        var inputs = activeLabels
            .Where(id => _labelCache.ContainsKey(id))
            .Select(id => CreateReadInput(_labelCache[id]))
            .ToList();

        return await _driver.Read(inputs);
    }

    private DriverReadInput CreateReadInput(DeviceLabel label)
    {
        // 创建读取输入参数
        return new DriverReadInput(
            label.Identifier,
            label.RegisterAddress,
            label.DataType,
            label.Method,
            label.ReadLength,
            label.Encoding
        );
    }
}
