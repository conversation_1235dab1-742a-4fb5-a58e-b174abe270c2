using EdgeGateway.Device.Entity.Label;
using EdgeGateway.Driver.Entity.Enums;
using EdgeGateway.Device.Services.Driver;

namespace EdgeGateway.Device.Services.Clients;

/// <summary>边缘网关</summary>
public class EdgeGateway : IDisposable
{
    /// <summary>
    ///     Sqlsugar单例服务
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    ///     驱动服务
    /// </summary>
    private readonly DriverHostService _driverHosted;

    /// <summary>
    ///     日志服务
    /// </summary>
    private readonly ILogger<EdgeGateway> _logger;

    /// <summary>
    ///     设备线程工厂
    /// </summary>
    private readonly IDeviceThreadFactory _deviceThreadFactory;

    /// <summary>
    ///     驱动工厂
    /// </summary>
    private readonly IDriverFactory _driverFactory;

    /// <summary>
    ///     构造参数
    /// </summary>
    /// <param name="services"> 服务提供者 </param>
    /// <param name="db"> sqlSugar </param>
    /// <param name="driverHosted"> 驱动服务 </param>
    /// <param name="logger"> 日志服务 </param>
    /// <param name="deviceThreadFactory"> 设备线程工厂 </param>
    /// <param name="driverFactory"> 驱动工厂 </param>
    public EdgeGateway(
        IServiceProvider services,
        ISqlSugarClient db,
        DriverHostService driverHosted,
        ILogger<EdgeGateway> logger,
        IDeviceThreadFactory deviceThreadFactory,
        IDriverFactory driverFactory)
    {
        Services = services.CreateScope();
        _db = db;
        _driverHosted = driverHosted;
        _logger = logger;
        _deviceThreadFactory = deviceThreadFactory;
        _driverFactory = driverFactory;
    }

    #region 属性

    /// <summary>
    ///     设备采集线程
    /// </summary>
    public ConcurrentDictionary<long, DeviceThread> DeviceThreads { get; set; } = new();

    private IServiceScope Services { get; }

    #endregion

    #region 构造

    /// <summary>停止服务</summary>
    public void Dispose()
    {
        _logger.LogInformation("停止服务！");
        foreach (var (key, item) in DeviceThreads)
            item.Dispose();
        DeviceThreads.Clear();
    }

    #endregion

    /// <summary>开始服务</summary>
    public async Task Start()
    {
        try
        {
            // 获取所有启用的设备
            var rep = Services.ServiceProvider.GetRequiredService<SqlSugarRepository<Entity.Device>>();
            // 获取所有启用的设备,包含标签和事件
            var deviceList = await rep.AsQueryable().Where(w => w.Enable == true)
                .Includes(w => w.DeviceLabel.Where(s => s.Enable).ToList())
                .ToListAsync();
            // 遍历设备启动采集任务
            foreach (var device in deviceList)
                try
                {
                    await CreateDeviceThread(device);
                }
                catch (Exception e)
                {
                    Log.Error($"{device.Identifier} 采集失败：" + e.Message);
                }
        }
        catch (Exception ex)
        {
            Log.Error($"采集数据中心故障：{ex.Message}");
        }
    }

    #region 采集线程管理

    /// <summary>
    ///     创建设备采集线程
    /// </summary>
    /// <param name="deviceId"></param>
    public async Task CreateDeviceThread(long deviceId)
    {
        // todo 校验授权
        // await AuthorizationUtil.IsAuthorized();

        // 全部启动设备
        var device = await _db.Queryable<Entity.Device>()
            .Where(w => w.Id == deviceId)
            .Includes(c => c.DeviceLabel.Where(w => w.Enable).ToList())
            .Includes(c => c.EdgeChannel)
            .FirstAsync();

        await CreateDeviceThread(device);
    }

    /// <summary>
    ///     创建设备采集线程
    /// </summary>
    /// <param name="device"></param>
    public async Task CreateDeviceThread(Entity.Device device)
    {
        // todo 校验授权
        // await AuthorizationUtil.IsAuthorized();

        // 如果是串口设备
        if (device.ChannelId > 0)
        {
            // 获取通道组
            var channelGroup = _channelGroups.GetOrAdd(device.ChannelId.Value, new ChannelGroup
            {
                ChannelId = device.ChannelId.Value,
                ThreadPoolSize = device.EdgeChannel?.ThreadCount ?? 1 // 从设备配置获取线程池大小,默认1
            });

            lock (channelGroup)
            {
                // 检查设备是否已存在
                var existingDevice = channelGroup.Devices.FirstOrDefault(d => d.Id == device.Id);
                if (existingDevice != null)
                    // 更新现有设备信息
                    channelGroup.Devices.Remove(existingDevice);

                // 添加设备到组
                channelGroup.Devices.Add(device);

                // 更新通道组的最大间隔时间
                UpdateChannelGroupMaxInterval(channelGroup);

                // 添加到采集队列
                channelGroup.CollectionQueue.Writer.WriteAsync(device).GetAwaiter().GetResult(); // 避免在lock中使用await

                // 如果还没有工作线程，创建工作线程
                if (channelGroup.WorkerThreads.Count == 0)
                {
                    var driver = CreateDriver(device);
                    CreateDeviceThread(channelGroup, device, driver);
                }
            }
        }
        else
        {
            // 非串口设备
            var driver = CreateDriver(device);
            var deviceThread = _deviceThreadFactory.Create(device, driver);
            DeviceThreads.TryAdd(device.Id, deviceThread);
        }

        // 记录日志
        _logger.LogInformation($"设备 {device.Identifier} 创建采集线程成功");
    }

    /// <summary>
    ///     更新设备配置(不停止线程)
    /// </summary>
    /// <param name="device">设备信息</param>
    public async Task UpdateDeviceThread(Entity.Device device)
    {
        try
        {
            // 如果是串口设备
            if (device.ChannelId > 0)
            {
                if (_channelGroups.TryGetValue(device.ChannelId.Value, out var channelGroup))
                    lock (channelGroup)
                    {
                        // 更新设备信息
                        var existingDevice = channelGroup.Devices.FirstOrDefault(d => d.Id == device.Id);
                        if (existingDevice != null)
                        {
                            // 替换设备信息
                            channelGroup.Devices.Remove(existingDevice);
                            channelGroup.Devices.Add(device);

                            // 更新通道组的最大间隔时间
                            UpdateChannelGroupMaxInterval(channelGroup);

                            // 更新所有设备的配置
                            var allLabels = channelGroup.Devices.SelectMany(d => d.DeviceLabel).ToList();
                            foreach (var thread in channelGroup.WorkerThreads) thread.UpdateDeviceLabel(allLabels);
                        }
                    }
            }
            else
            {
                // 非串口设备
                if (DeviceThreads.TryGetValue(device.Id, out var thread))
                    // 直接更新设备配置
                    thread.UpdateDeviceLabel(device.DeviceLabel.ToList());
            }

            _logger.LogInformation($"设备 {device.Identifier} 配置更新完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"更新设备 {device.Identifier} 配置失败");
            throw;
        }
    }

    /// <summary>
    ///     重新创建设备采集线程
    /// </summary>
    /// <param name="device">设备信息</param>
    public async Task RestartDeviceThread(Entity.Device device)
    {
        try
        {
            // 先停止当前线程
            await StopDeviceThread(device);

            // 如果是串口设备
            if (device.ChannelId > 0)
            {
                // 获取通道组
                var channelGroup = _channelGroups.GetOrAdd(device.ChannelId.Value, new ChannelGroup
                {
                    // 通道组ID
                    ChannelId = device.ChannelId.Value
                });
                // 锁定通道组
                lock (channelGroup)
                {
                    // 更新设备信息
                    var existingDevice = channelGroup.Devices.FirstOrDefault(d => d.Id == device.Id);
                    if (existingDevice != null)
                        // 移除设备
                        channelGroup.Devices.Remove(existingDevice);
                    // 添加设备
                    channelGroup.Devices.Add(device);

                    // 更新通道组的最大间隔时间
                    UpdateChannelGroupMaxInterval(channelGroup);

                    // 如果已有线程，只需更新配置
                    if (channelGroup.WorkerThreads.Count > 0)
                    {
                        // 合并所有设备的配置
                        var allLabels = channelGroup.Devices.SelectMany(d => d.DeviceLabel).ToList();
                        // 更新线程中的采集点
                        foreach (var thread in channelGroup.WorkerThreads) thread.UpdateDeviceLabel(allLabels);
                        return;
                    }

                    // 创建新的采集线程
                    var driver = CreateDriver(device);
                    // 创建线程
                    CreateDeviceThread(channelGroup, device, driver);
                    // 添加线程
                    DeviceThreads.TryAdd(device.Id, channelGroup.WorkerThreads.First());
                }
            }
            else
            {
                // 非串口设备直接创建新线程
                var driver = CreateDriver(device);
                // 创建线程
                var deviceThread = _deviceThreadFactory.Create(device, driver);
                // 添加线程
                DeviceThreads.TryAdd(device.Id, deviceThread);
            }

            // 日志
            _logger.LogInformation($"设备 {device.Identifier} 重启完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"重启设备 {device.Identifier} 失败");
            throw;
        }
    }

    /// <summary>
    ///     停止设备采集线程
    /// </summary>
    /// <param name="device">设备信息</param>
    public async Task StopDeviceThread(Entity.Device device)
    {
        try
        {
            // 如果是串口设备
            if (device.ChannelId > 0)
                // 获取通道组
                if (_channelGroups.TryGetValue(device.ChannelId.Value, out var channelGroup))
                    // 锁定通道组
                    lock (channelGroup)
                    {
                        // 移除设备
                        channelGroup.Devices.RemoveAll(d => d.Id == device.Id);

                        // 更新通道组的最大间隔时间
                        UpdateChannelGroupMaxInterval(channelGroup);

                        // 如果还有其他设备，更新配置
                        if (channelGroup.Devices.Any())
                        {
                            // 合并所有设备的配置
                            var allLabels = channelGroup.Devices.SelectMany(d => d.DeviceLabel).ToList();
                            // 更新线程中的采集点
                            foreach (var workerThread in channelGroup.WorkerThreads) workerThread.UpdateDeviceLabel(allLabels);
                        }
                        // 如果没有设备了，停止并移除线程
                        else
                        {
                            // 停止线程
                            foreach (var workerThread in channelGroup.WorkerThreads) workerThread.Dispose();
                            channelGroup.WorkerThreads.Clear();
                            // 移除通道组
                            _channelGroups.TryRemove(device.ChannelId.Value, out _);
                        }
                    }

            // 停止并移除设备线程
            if (DeviceThreads.TryRemove(device.Id, out var thread))
                // 停止线程
                thread.Dispose();
            // 日志
            _logger.LogInformation($"设备 {device.Identifier} 停止完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"停止设备 {device.Identifier} 失败");
            throw;
        }
    }

    #endregion

    #region 采集点管理

    /// <summary>
    ///     添加设备采集点
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="Label">采集点信息</param>
    public async Task AddDeviceLabel(long deviceId, DeviceLabel Label)
    {
        try
        {
            // 获取设备信息
            var device = await GetDeviceInfo(deviceId);
            // 如果是串口设备，需要特殊处理
            if (device.ChannelId > 0)
            {
                // 获取通道组
                if (_channelGroups.TryGetValue(device.ChannelId.Value, out var channelGroup))
                    // 锁定通道组
                    lock (channelGroup)
                    {
                        // 更新设备的采集点
                        var targetDevice = channelGroup.Devices.First(d => d.Id == deviceId);
                        // 添加采集点
                        targetDevice.DeviceLabel.Add(Label);

                        // 更新线程中的采集点
                        var allLabels = channelGroup.Devices.SelectMany(d => d.DeviceLabel).ToList();
                        // 更新线程中的采集点
                        foreach (var thread in channelGroup.WorkerThreads) thread.UpdateDeviceLabel(allLabels, false);
                    }
            }
            else if (DeviceThreads.TryGetValue(deviceId, out var thread))
            {
                // 添加采集点
                thread.AddDeviceLabel(Label);
            }

            // 日志
            _logger.LogInformation($"设备 {device.Identifier} 添加采集点 {Label.Identifier} 成功");
        }
        catch (Exception ex)
        {
            // 错误日志
            _logger.LogError(ex, $"添加采集点失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    ///     批量添加设备采集点
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="labels">采集点列表</param>
    public async Task AddDeviceLabels(long deviceId, List<DeviceLabel> labels)
    {
        try
        {
            // 获取设备信息
            var device = await GetDeviceInfo(deviceId);
            // 如果是串口设备
            if (device.ChannelId > 0)
            {
                // 获取通道组
                if (_channelGroups.TryGetValue(device.ChannelId.Value, out var channelGroup))
                    // 锁定通道组
                    lock (channelGroup)
                    {
                        // 获取设备
                        var targetDevice = channelGroup.Devices.First(d => d.Id == deviceId);
                        // 添加采集点
                        targetDevice.DeviceLabel.AddRange(labels);

                        // 合并所有设备的采集点
                        var allLabels = channelGroup.Devices.SelectMany(d => d.DeviceLabel).ToList();
                        // 更新线程中的采集点
                        foreach (var thread in channelGroup.WorkerThreads) thread.UpdateDeviceLabel(allLabels, false);
                    }
            }
            else if (DeviceThreads.TryGetValue(deviceId, out var thread))
            {
                // 遍历采集点
                foreach (var label in labels)
                    // 添加采集点
                    thread.AddDeviceLabel(label);
            }

            // 日志
            _logger.LogInformation($"设备 {device.Identifier} 批量添加采集点成功");
        }
        catch (Exception ex)
        {
            // 错误日志
            _logger.LogError(ex, $"批量添加采集点失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    ///     更新设备采集点
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="labels">采集点列表</param>
    public async Task UpdateDeviceLabels(long deviceId, List<DeviceLabel> labels)
    {
        // 删除采集点
        await RemoveDeviceLabels(deviceId, labels.Select(x => x.Identifier).ToList());
        // 添加采集点
        await AddDeviceLabels(deviceId, labels);
    }

    /// <summary>
    ///     更新设备采集点
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="label">采集点</param>
    public async Task UpdateDeviceLabel(long deviceId, DeviceLabel label)
    {
        // 删除采集点
        await RemoveDeviceLabel(deviceId, label.Identifier);
        // 添加采集点
        await AddDeviceLabel(deviceId, label);
    }

    /// <summary>
    ///     删除设备采集点
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="LabelIdentifier">采集点标识符</param>
    public async Task RemoveDeviceLabel(long deviceId, string LabelIdentifier)
    {
        try
        {
            // 获取设备信息
            var device = await GetDeviceInfo(deviceId);
            // 如果是串口设备
            if (device.ChannelId > 0)
            {
                // 获取通道组
                if (_channelGroups.TryGetValue(device.ChannelId.Value, out var channelGroup))
                    // 锁定通道组
                    lock (channelGroup)
                    {
                        // 获取设备
                        var targetDevice = channelGroup.Devices.First(d => d.Id == deviceId);
                        // 移除采集点
                        targetDevice.DeviceLabel.RemoveAll(m => m.Identifier == LabelIdentifier);
                        // 合并所有设备的采集点
                        var allLabels = channelGroup.Devices.SelectMany(d => d.DeviceLabel).ToList();
                        // 更新线程中的采集点
                        foreach (var thread in channelGroup.WorkerThreads) thread.UpdateDeviceLabel(allLabels);
                    }
            }
            else if (DeviceThreads.TryGetValue(deviceId, out var thread))
            {
                // 移除采集点
                thread.RemoveDeviceLabel(LabelIdentifier);
            }

            // 日志
            _logger.LogInformation($"设备 {device.Identifier} 删除采集点 {LabelIdentifier} 成功");
        }
        catch (Exception ex)
        {
            // 错误日志
            _logger.LogError(ex, $"删除采集点失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    ///     批量删除设备采集点
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="LabelIdentifiers">采集点标识符列表</param>
    public async Task RemoveDeviceLabels(long deviceId, IEnumerable<string> LabelIdentifiers)
    {
        try
        {
            // 获取设备信息
            var device = await GetDeviceInfo(deviceId);
            // 如果是串口设备
            if (device.ChannelId > 0)
            {
                // 获取通道组
                if (_channelGroups.TryGetValue(device.ChannelId.Value, out var channelGroup))
                    lock (channelGroup)
                    {
                        // 获取设备
                        var targetDevice = channelGroup.Devices.First(d => d.Id == deviceId);
                        // 移除采集点
                        targetDevice.DeviceLabel.RemoveAll(m => LabelIdentifiers.Contains(m.Identifier));
                        // 合并所有设备的采集点
                        var allLabels = channelGroup.Devices.SelectMany(d => d.DeviceLabel).ToList();
                        // 更新线程中的采集点
                        foreach (var thread in channelGroup.WorkerThreads) thread.UpdateDeviceLabel(allLabels);
                    }
            }
            else if (DeviceThreads.TryGetValue(deviceId, out var thread))
            {
                // 移除采集点
                thread.RemoveDeviceLabels(LabelIdentifiers);
            }

            // 日志
            _logger.LogInformation($"设备 {device.Identifier} 批量删除采集点成功");
        }
        catch (Exception ex)
        {
            // 错误日志
            _logger.LogError(ex, $"批量删除采集点失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    ///     获取设备采集点
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="LabelIdentifier">采集点标识符</param>
    public async Task<DeviceLabel?> GetDeviceLabel(long deviceId, string LabelIdentifier)
    {
        // 获取设备信息
        var device = await GetDeviceInfo(deviceId);
        // 如果是串口设备
        if (device.ChannelId > 0)
        {
            // 获取通道组
            if (_channelGroups.TryGetValue(device.ChannelId.Value, out var channelGroup))
            {
                // 获取设备
                var targetDevice = channelGroup.Devices.First(d => d.Id == deviceId);
                // 获取采集点
                return targetDevice.DeviceLabel.FirstOrDefault(m => m.Identifier == LabelIdentifier);
            }
        }
        else if (DeviceThreads.TryGetValue(deviceId, out var thread))
        {
            // 获取采集点
            return thread.GetDeviceLabel(LabelIdentifier);
        }

        // 返回采集点
        return null;
    }

    /// <summary>
    ///     辅助方法：获取设备信息
    /// </summary>
    /// <param name="deviceId"></param>
    /// <returns></returns>
    private async Task<Entity.Device> GetDeviceInfo(long deviceId)
    {
        // 获取设备信息
        var device = await _db.Queryable<Entity.Device>()
            .Where(w => w.Id == deviceId)
            .Includes(c => c.DeviceLabel)
            .FirstAsync() ?? throw Oops.Oh($"设备不存在: {deviceId}");
        // 返回设备信息
        return device;
    }

    #endregion

    #region 手动调试

    /// <summary>
    ///     手动读取设备数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="address">地址</param>
    /// <param name="length">长度</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="encoding">编码</param>
    /// <returns>读取结果</returns>
    public async Task<List<ReadDataResult>> ManualRead(long deviceId, string address, ushort length = 1, string? dataType = null, string? encoding = null)
    {
        try
        {
            // 获取设备线程
            if (!DeviceThreads.TryGetValue(deviceId, out var deviceThread))
                throw Oops.Oh($"设备 {deviceId} 未启动或不存在");

            // 构建读取参数
            var input = new List<DriverReadInput>
            {
                new(address, address, "string", address, length)
            };

            // 调用驱动读取方法
            var result = await deviceThread.Driver.Read(input);

            // 记录日志
            _logger.LogInformation($"设备 {deviceId} 手动读取成功: 地址={address}, 长度={length}, 结果={result.ToJson()}");

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设备 {deviceId} 手动读取失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    ///     手动写入设备数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="address">地址</param>
    /// <param name="value">写入值</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="length">长度</param>
    /// <param name="encoding">编码</param>
    /// <param name="identifier">标签标识符（可选）</param>
    /// <param name="sourceType">写入来源类型（默认为手动写入）</param>
    /// <returns>写入结果</returns>
    public async Task<List<Dictionary<string, object>>> ManualWrite(long deviceId, string address, string value, string? dataType = null, ushort length = 1, string? encoding = null,
        string? identifier = null, WriteSourceType sourceType = WriteSourceType.Manual)
    {
        List<Dictionary<string, object>> result = null;
        var success = false;
        string errorMessage = null;

        try
        {
            // 获取设备信息
            var device = await _db.Queryable<Entity.Device>()
                .Where(w => w.Id == deviceId)
                .FirstAsync();

            if (device == null)
                throw Oops.Oh($"设备 {deviceId} 不存在");

            // 检查设备是否允许写入（第一层权限检查）
            if (!device.AllowWrite)
                throw Oops.Oh($"设备 {deviceId} 不允许写入操作");

            // 如果提供了标签标识符，则进行标签级别的权限检查
            if (!string.IsNullOrEmpty(identifier))
            {
                // 获取标签信息
                var label = await _db.Queryable<DeviceLabel>()
                    .Where(x => x.DeviceId == deviceId && x.Identifier == identifier)
                    .FirstAsync();

                if (label == null)
                    throw Oops.Oh($"标签 {identifier} 不存在");

                // 检查标签是否允许写入（第二层权限检查）
                if (label.ProtectType == ProtectTypeEnum.OnlyRead)
                    throw Oops.Oh($"标签 {identifier} 为只读，不允许写入");

                // 检查标签是否启用
                if (!label.Enable)
                    throw Oops.Oh($"标签 {identifier} 未启用，不允许写入");
            }

            // 获取设备线程
            if (!DeviceThreads.TryGetValue(deviceId, out var deviceThread))
                throw Oops.Oh($"设备 {deviceId} 未启动或不存在");

            // 检查设备是否在线
            if (!deviceThread.Driver.IsConnected)
                throw Oops.Oh($"设备 {deviceId} 离线，无法写入数据");

            // 构建写入参数
            var input = new List<DriverWriteInput>
            {
                new(identifier ?? address, address, value, dataType, length, encoding)
            };

            // 调用驱动写入方法
            result = await deviceThread.Driver.Write(input);

            // 写入成功
            success = true;

            // 记录日志
            _logger.LogInformation($"设备 {deviceId} 手动写入成功: 地址={address}, 值={value}");

            return result;
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            _logger.LogError(ex, $"设备 {deviceId} 手动写入失败: {ex.Message}");
            throw;
        }
        finally
        {
            // 无论成功或失败，都记录写入日志
            try
            {
                // 创建写入日志记录
                var writeLog = new DeviceWriteLog
                {
                    Id = YitIdHelper.NextId(),
                    DeviceId = deviceId,
                    LabelIdentifier = identifier,
                    Address = address,
                    Value = value,
                    DataType = dataType,
                    Length = length,
                    Encoding = encoding,
                    SourceType = sourceType,
                    WriteTime = DateTime.Now,
                    Success = success,
                    ErrorMessage = errorMessage
                };

                // 异步保存写入日志
                await _db.Insertable(writeLog).ExecuteCommandAsync();
            }
            catch (Exception logEx)
            {
                // 记录日志失败不应影响主流程
                _logger.LogError(logEx, $"记录设备写入日志失败: {logEx.Message}");
            }
        }
    }

    /// <summary>
    ///     批量写入设备数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="writeInputs">批量写入参数列表</param>
    /// <param name="sourceType">写入来源类型（默认为手动写入）</param>
    /// <returns>写入结果列表</returns>
    public async Task<List<Dictionary<string, object>>> BatchManualWrite(
        long deviceId,
        List<BatchWriteInput> writeInputs,
        WriteSourceType sourceType = WriteSourceType.Manual)
    {
        // 结果列表
        List<Dictionary<string, object>> results = new();

        // 获取设备信息
        var device = await _db.Queryable<Entity.Device>()
            .Where(w => w.Id == deviceId)
            .FirstAsync();

        if (device == null)
            throw Oops.Oh($"设备 {deviceId} 不存在");

        // 检查设备是否允许写入（第一层权限检查）
        if (!device.AllowWrite)
            throw Oops.Oh($"设备 {deviceId} 不允许写入操作");

        // 获取设备线程
        if (!DeviceThreads.TryGetValue(deviceId, out var deviceThread))
            throw Oops.Oh($"设备 {deviceId} 未启动或不存在");

        // 检查设备是否在线
        if (!deviceThread.Driver.IsConnected)
            throw Oops.Oh($"设备 {deviceId} 离线，无法写入数据");

        // 批量获取标签信息（如果有标签标识符）
        var identifiers = writeInputs.Where(w => !string.IsNullOrEmpty(w.Identifier))
            .Select(w => w.Identifier)
            .Distinct()
            .ToList();

        Dictionary<string, DeviceLabel> labelDict = new();
        if (identifiers.Any())
        {
            var labels = await _db.Queryable<DeviceLabel>()
                .Where(x => x.DeviceId == deviceId && identifiers.Contains(x.Identifier))
                .ToListAsync();

            labelDict = labels.ToDictionary(k => k.Identifier, v => v);

            // 检查所有标签是否存在
            foreach (var identifier in identifiers)
                if (!labelDict.ContainsKey(identifier))
                    throw Oops.Oh($"标签 {identifier} 不存在");
        }

        // 构建驱动写入参数列表
        var driverInputs = new List<DriverWriteInput>();

        // 记录每个输入的处理结果
        foreach (var input in writeInputs)
        {
            string errorMessage = null;
            var success = false;

            try
            {
                // 如果提供了标签标识符，进行标签级别权限检查
                if (!string.IsNullOrEmpty(input.Identifier) && labelDict.TryGetValue(input.Identifier, out var label))
                {
                    // 检查标签是否允许写入（第二层权限检查）
                    if (label.ProtectType == ProtectTypeEnum.OnlyRead)
                        throw new Exception($"标签 {input.Identifier} 为只读，不允许写入");

                    // 检查标签是否启用
                    if (!label.Enable)
                        throw new Exception($"标签 {input.Identifier} 未启用，不允许写入");

                    // 添加到驱动写入参数列表
                    driverInputs.Add(new DriverWriteInput(
                        input.Identifier,
                        label.RegisterAddress, // 使用标签的地址
                        input.Value,
                        label.DataType, // 使用标签的数据类型
                        label.ReadLength ?? 1, // 使用标签的长度
                        label.Encoding // 使用标签的编码
                    ));
                }
                else
                {
                    // 直接使用提供的地址和参数
                    driverInputs.Add(new DriverWriteInput(
                        input.Identifier ?? input.Address,
                        input.Address,
                        input.Value,
                        input.DataType,
                        input.Length,
                        input.Encoding
                    ));
                }

                success = true;
            }
            catch (Exception ex)
            {
                // 记录错误信息，但继续处理其他输入
                errorMessage = ex.Message;
                _logger.LogError(ex, $"设备 {deviceId} 准备写入 {input.Identifier ?? input.Address} 失败: {ex.Message}");

                // 添加失败结果
                results.Add(new Dictionary<string, object>
                {
                    { "Id", input.Identifier ?? input.Address },
                    { "Status", "Failed" },
                    { "Error", errorMessage }
                });
            }

            // 创建写入日志记录
            try
            {
                // 确定地址和其他参数
                var address = input.Address;
                var dataType = input.DataType;
                var length = input.Length;
                var encoding = input.Encoding;

                // 如果有标签，使用标签信息
                if (!string.IsNullOrEmpty(input.Identifier) && labelDict.TryGetValue(input.Identifier, out var labelInfo))
                {
                    address = labelInfo.RegisterAddress;
                    dataType = labelInfo.DataType;
                    length = labelInfo.ReadLength ?? 1;
                    encoding = labelInfo.Encoding;
                }

                var writeLog = new DeviceWriteLog
                {
                    Id = YitIdHelper.NextId(),
                    DeviceId = deviceId,
                    LabelIdentifier = input.Identifier,
                    Address = address,
                    Value = input.Value,
                    DataType = dataType,
                    Length = length,
                    Encoding = encoding,
                    SourceType = sourceType,
                    WriteTime = DateTime.Now,
                    Success = success,
                    ErrorMessage = errorMessage
                };

                // 异步保存写入日志
                await _db.Insertable(writeLog).ExecuteCommandAsync();
            }
            catch (Exception logEx)
            {
                // 记录日志失败不应影响主流程
                _logger.LogError(logEx, $"记录设备写入日志失败: {logEx.Message}");
            }
        }

        // 检查是否有要写入的参数
        if (driverInputs.Count == 0)
            return results;

        try
        {
            // 调用驱动执行批量写入
            var writeResults = await deviceThread.Driver.Write(driverInputs);

            // 合并结果
            foreach (var result in writeResults) results.Add(result);

            // 记录成功日志
            _logger.LogInformation($"设备 {deviceId} 批量写入成功: 共 {driverInputs.Count} 个点位");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设备 {deviceId} 批量写入失败: {ex.Message}");

            // 添加所有未处理的输入为失败结果
            foreach (var input in driverInputs)
                // 检查结果列表中是否已包含此输入
                if (!results.Any(r => r.ContainsKey("Id") && r["Id"].ToString() == input.Id))
                    results.Add(new Dictionary<string, object>
                    {
                        { "Id", input.Id },
                        { "Status", "Failed" },
                        { "Error", ex.Message }
                    });
        }

        return results;
    }

    /// <summary>
    ///     批量写入标签数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="inputs">标签写入参数列表</param>
    /// <param name="sourceType">写入来源类型（默认为手动写入）</param>
    /// <returns>写入结果列表</returns>
    public async Task<List<Dictionary<string, object>>> BatchWriteLabel(
        long deviceId,
        List<LabelWriteInput> inputs,
        WriteSourceType sourceType = WriteSourceType.Manual)
    {
        try
        {
            // 获取设备信息
            var device = await _db.Queryable<Entity.Device>()
                .Where(w => w.Id == deviceId)
                .FirstAsync();

            if (device == null)
                throw Oops.Oh($"设备 {deviceId} 不存在");

            // 检查设备是否允许写入
            if (!device.AllowWrite)
                throw Oops.Oh($"设备 {deviceId} 不允许写入操作");

            // 获取所有标签信息
            var identifiers = inputs.Select(x => x.Identifier).ToList();
            var labels = await _db.Queryable<DeviceLabel>()
                .Where(x => x.DeviceId == deviceId && identifiers.Contains(x.Identifier))
                .ToListAsync();

            // 检查所有标签是否存在
            if (labels.Count != identifiers.Count)
            {
                var missingLabels = identifiers.Except(labels.Select(x => x.Identifier)).ToList();
                throw Oops.Oh($"标签不存在: {string.Join(", ", missingLabels)}");
            }

            // 检查标签权限
            var disabledLabels = labels.Where(x => !x.Enable).Select(x => x.Identifier).ToList();
            if (disabledLabels.Any()) throw Oops.Oh($"以下标签未启用: {string.Join(", ", disabledLabels)}");

            var readOnlyLabels = labels.Where(x => x.ProtectType == ProtectTypeEnum.OnlyRead)
                .Select(x => x.Identifier).ToList();
            if (readOnlyLabels.Any()) throw Oops.Oh($"以下标签为只读: {string.Join(", ", readOnlyLabels)}");

            // 转换为BatchWriteInput格式
            var writeInputs = new List<BatchWriteInput>();
            foreach (var input in inputs)
            {
                var label = labels.First(x => x.Identifier == input.Identifier);
                writeInputs.Add(new BatchWriteInput
                {
                    Identifier = input.Identifier,
                    Address = label.RegisterAddress,
                    Value = input.Value,
                    DataType = label.DataType,
                    Length = label.ReadLength ?? 1,
                    Encoding = label.Encoding
                });
            }

            // 调用批量写入方法
            var results = await BatchManualWrite(deviceId, writeInputs, sourceType);

            // 记录日志
            _logger.LogInformation($"设备 {deviceId} 批量写入 {inputs.Count} 个标签成功");

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设备 {deviceId} 批量写入标签失败: {ex.Message}");
            throw;
        }
    }

    #endregion

    #region 内部方法

    /// <summary>
    ///     同步设备配置信息
    /// </summary>
    /// <param name="driverInfo">驱动信息</param>
    /// <param name="device">设备信息</param>
    /// <param name="driver">驱动接口</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    private async Task SyncDeviceConfig(Entity.Models.Driver driverInfo, Entity.Device device, IDriver driver)
    {
        // 同步最新配置
        _driverHosted.CreateDeviceConfig(device, driverInfo);
        // 检查设备配置是否同步
        foreach (var property in driverInfo.Type.GetProperties())
        {
            // 获取配置属性
            var config = property.GetCustomAttribute<ConfigParameterAttribute>();
            // 该配置是否忽略
            if (config == null || (!config.Display && config.DisplayExpress.IsNullOrEmpty())) continue;
            // 是否设置该置
            var deviceConfig = device.DeviceConfigs.FirstOrDefault(x => x.Identifier == property.Name);
            // 如果配置不存在
            if (deviceConfig == null)
                continue;
            // 转换配置值
            try
            {
                // 获取配置值
                deviceConfig.Value = ObjectExtension.GetJsonElementValue(deviceConfig.Value);
                // 转换配置值
                var value = await PropertyParse(property.PropertyType, deviceConfig.Value.ToString());
                // 设置配置值
                property.SetValue(driver, value);
            }
            catch (Exception ex)
            {
                // 错误日志
                var errorMsg = $"[设备配置]-故障，标识：{deviceConfig.Identifier}，Desc：{deviceConfig.Description}，value：{deviceConfig.Value}，Error：{ex.Message}";
                throw Oops.Oh(errorMsg);
            }
        }

        try
        {
            // 更新配置
            await _db.Updateable(device).UpdateColumns(w => w.DeviceConfigs).ExecuteCommandAsync();
        }
        catch (Exception e)
        {
            Log.Error($"[设备配置]-同步故障：{e.Message}");
        }
    }

    /// <summary>
    ///     根据属性类型转换成对应的值
    /// </summary>
    /// <param name="propertyType"></param>
    /// <param name="configValue"></param>
    /// <returns></returns>
    private Task<object> PropertyParse(Type propertyType, string? configValue)
    {
        object? value = null;
        // 如果属性类型是布尔类型
        if (propertyType == typeof(bool))
            value = configValue.ToLower() != "false" && configValue != "0";
        // 如果属性类型是枚举类型
        else if (propertyType.BaseType == typeof(Enum))
            value = Enum.Parse(propertyType, configValue);
        else
            try
            {
                // 转换配置值
                value = Convert.ChangeType(configValue, propertyType);
            }
            catch (Exception)
            {
                // 处理转换失败的情况
            }

        // 返回转换后的值
        return Task.FromResult(value);
    }

    /// <summary>
    ///     添加串口通道管理
    /// </summary>
    private class ChannelGroup
    {
        // 通道ID
        public long ChannelId { get; set; }

        // 设备列表
        public List<Entity.Device> Devices { get; } = new();

        // 线程池大小
        public int ThreadPoolSize { get; set; } = 1;

        // 设备采集队列
        public Channel<Entity.Device> CollectionQueue { get; } = Channel.CreateUnbounded<Entity.Device>();

        // 工作线程列表
        public List<DeviceThread> WorkerThreads { get; } = new();

        // 取消令牌
        public CancellationTokenSource CancellationTokenSource { get; } = new();

        // 缓存的最大间隔时间
        public int MaxInterval { get; set; } = 20;

        // 上次更新最大间隔时间的时间戳
        public long LastUpdateTime { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
    }

    // 串口通道管理
    private readonly ConcurrentDictionary<long, ChannelGroup> _channelGroups = new();

    /// <summary>
    ///     创建驱动
    /// </summary>
    /// <param name="device"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    private IDriver CreateDriver(Entity.Device device)
    {
        try
        {
            // 使用驱动工厂创建驱动实例
            var driver = _driverFactory.CreateDriver(device);

            // 记录日志
            _logger.LogInformation($"设备 {device.Identifier} 创建驱动成功: {device.Driver?.DriverName}");

            return driver;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设备 {device.Identifier} 创建驱动失败: {device.Driver?.DriverName}");
            throw;
        }
    }

    /// <summary>
    ///     创建设备线程
    /// </summary>
    /// <param name="channelGroup"></param>
    /// <param name="device"></param>
    /// <param name="driver"></param>
    private void CreateDeviceThread(ChannelGroup channelGroup, Entity.Device device, IDriver driver)
    {
        // 如果还没有创建工作线程
        if (channelGroup.WorkerThreads.Count == 0)
            // 根据配置的线程池大小创建工作线程
            for (var i = 0; i < channelGroup.ThreadPoolSize; i++)
            {
                var workerThread = _deviceThreadFactory.Create(device, driver);
                channelGroup.WorkerThreads.Add(workerThread);

                // 启动工作线程的采集任务
                _ = StartWorkerThread(channelGroup, workerThread);
            }
    }

    /// <summary>
    ///     启动工作线程
    /// </summary>
    private async Task StartWorkerThread(ChannelGroup channelGroup, DeviceThread workerThread)
    {
        try
        {
            // 循环从队列获取设备并采集
            await foreach (var device in channelGroup.CollectionQueue.Reader.ReadAllAsync(channelGroup.CancellationTokenSource.Token))
                try
                {
                    // 更新线程中的设备信息
                    workerThread.Device = device;

                    // 执行一轮采集
                    await CollectDeviceData(workerThread);

                    // 使用缓存的最大间隔时间
                    var maxInterval = channelGroup.MaxInterval;

                    // 采集完成后休眠最大间隔时间
                    await Task.Delay(maxInterval);

                    // 采集完成后重新加入队列
                    await channelGroup.CollectionQueue.Writer.WriteAsync(device);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"设备 {device.Identifier} 采集失败");

                    // 即使采集失败也需要重新加入队列，并休眠一段时间
                    // 使用缓存的最大间隔时间
                    await Task.Delay(channelGroup.MaxInterval);
                    await channelGroup.CollectionQueue.Writer.WriteAsync(device);
                }
        }
        catch (OperationCanceledException)
        {
            // 正常取消
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "工作线程异常退出");
        }
    }

    /// <summary>
    ///     获取设备列表中的最大间隔时间
    /// </summary>
    /// <param name="devices">设备列表</param>
    /// <returns>最大间隔时间(毫秒)</returns>
    private int GetMaxIntervalFromDevices(List<Entity.Device> devices)
    {
        // 默认最小间隔时间(毫秒)
        const int defaultInterval = 1000;

        if (devices == null || !devices.Any())
            return defaultInterval;

        // 从所有设备中获取MinPeriod并找出最大值
        var intervals = devices.Select(d => d.DeviceInfo?.MinPeriod ?? defaultInterval);

        // 返回最大值，如果没有有效值则返回默认值
        return intervals.Any() ? intervals.Max() : defaultInterval;
    }

    /// <summary>
    ///     执行设备数据采集
    /// </summary>
    private async Task CollectDeviceData(DeviceThread thread)
    {
        // 检查设备连接
        await thread.DeviceConnect();

        if (!thread.Driver.IsConnected) return;

        // 执行一轮采集
        var payLoad = new PayLoad
        {
            Labels = new ConcurrentDictionary<string, ParamValue>(),
            Id = thread.Device.Identifier,
            Name = thread.Device.Name ?? "",
            Ts = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };

        // 读取静态数据
        await thread.ReadStatic(payLoad);

        // 读取标签数据
        await thread.ReadLabel(payLoad);

        // 执行脚本
        await thread.RunScript(payLoad);

        // 写入上报通道
        await thread.ReportData(payLoad);
    }

    /// <summary>
    ///     获取设备线程
    /// </summary>
    /// <param name="deviceIdentifier">设备标识</param>
    /// <returns>设备线程实例</returns>
    public DeviceThread GetDeviceThread(string deviceIdentifier)
    {
        // 先在非串口设备中查找
        var device = DeviceThreads.Values.FirstOrDefault(x => x.Device.Identifier == deviceIdentifier);
        if (device != null) return device;

        // 在串口设备中查找
        foreach (var channelGroup in _channelGroups.Values)
        {
            var workerThread = channelGroup.WorkerThreads.FirstOrDefault();
            if (workerThread?.Device.Identifier == deviceIdentifier) return workerThread;
        }

        return null;
    }

    /// <summary>
    ///     更新通道组的最大间隔时间
    /// </summary>
    /// <param name="channelGroup">通道组</param>
    private void UpdateChannelGroupMaxInterval(ChannelGroup channelGroup)
    {
        // 计算并更新通道组的最大间隔时间
        channelGroup.MaxInterval = GetMaxIntervalFromDevices(channelGroup.Devices);
        channelGroup.LastUpdateTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        _logger.LogDebug($"通道组 {channelGroup.ChannelId} 更新最大间隔时间: {channelGroup.MaxInterval}ms");
    }

    #endregion


}