<Project Sdk="Microsoft.NET.Sdk">


    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <NoWarn>1701;1702;1591</NoWarn>
        <DocumentationFile>EdgeGateway.Application.xml</DocumentationFile>
        <ImplicitUsings>enable</ImplicitUsings>
        <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
        <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)Generated</CompilerGeneratedFilesOutputPath>
        <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
        <SourceGeneratorDebug>true</SourceGeneratorDebug>
        <Version>1.0.0</Version>
        <BuildDescription>常规构建</BuildDescription>
    </PropertyGroup>


    <ItemGroup>
        <None Remove="applicationsettings.json"/>
        <None Remove="EdgeGateway.Application.xml"/>
        <None Remove="EdgeGateway.Base.csproj.DotSettings"/>
        <None Remove="EdgeSnoop.Application.xml"/>
    </ItemGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <DebugType>none</DebugType>
    </PropertyGroup>
    
    <ItemGroup>
        <ProjectReference Include="..\..\..\03-CoreDependencies\EdgeGateway.Core\EdgeGateway.Core.csproj"/>
        <ProjectReference Include="..\..\..\03-CoreDependencies\EdgeGateway.WebSocket\EdgeGateway.WebSocket.csproj"/>
        <ProjectReference Include="..\..\01-Device\EdgeGateway.Device.Entity\EdgeGateway.Device.Entity.csproj" />
        <ProjectReference Include="..\EdgeGateway.Base.Entity\EdgeGateway.Base.Entity.csproj"/>
        <ProjectReference Include="..\EdgeGateway.Shared\EdgeGateway.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Content Update="Configuration\applicationsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Service\"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="System.Management" Version="9.0.7" />

    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="SharpZipLib" Version="1.4.2"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="System.Net.Http.Json" Version="9.0.7" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="SharpPcap" Version="6.3.1" />
    </ItemGroup>

</Project>
