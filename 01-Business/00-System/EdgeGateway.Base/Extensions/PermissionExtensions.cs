using EdgeGateway.Base.Service.Permission;
using EdgeGateway.Core.Service;
using Microsoft.Extensions.DependencyInjection;

namespace EdgeGateway.Base.Extensions;

/// <summary>
/// 权限系统扩展方法
/// </summary>
public static class PermissionExtensions
{
    /// <summary>
    /// 添加权限服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddPermissionServices(this IServiceCollection services)
    {
        // 注册权限服务
        services.AddScoped<IPermissionService, PermissionService>();
        
        // 注册内存缓存（如果还没有注册）
        services.AddMemoryCache();
        
        return services;
    }

    /// <summary>
    /// 清除用户权限缓存
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="userId">用户ID</param>
    public static void ClearUserPermissionCache(this IServiceProvider serviceProvider, long userId)
    {
        var permissionService = serviceProvider.GetService<IPermissionService>();
        permissionService?.ClearUserPermissionCache(userId);
    }

    /// <summary>
    /// 清除所有权限缓存
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    public static void ClearAllPermissionCache(this IServiceProvider serviceProvider)
    {
        var permissionService = serviceProvider.GetService<IPermissionService>();
        permissionService?.ClearAllPermissionCache();
    }
}
