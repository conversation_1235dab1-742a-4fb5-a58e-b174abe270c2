using Microsoft.AspNetCore.Builder;

namespace EdgeGateway.Base.OpenApi.Middleware;

/// <summary>
/// 开放API中间件扩展
/// </summary>
public static class OpenApiMiddlewareExtensions
{
  /// <summary>
  /// 使用开放API中间件
  /// </summary>
  /// <param name="builder">应用程序构建器</param>
  /// <returns>应用程序构建器</returns>
  public static IApplicationBuilder UseOpenApi(this IApplicationBuilder builder)
  {
    return builder.UseMiddleware<OpenApiMiddleware>();
  }
}