using System.Reflection;
using EdgeGateway.Base.OpenApi.Attributes;
using EdgeGateway.Base.OpenApi.Constants;
using EdgeGateway.Base.OpenApi.Models;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.Controllers;

namespace EdgeGateway.Base.OpenApi.Controllers;

/// <summary>
/// 开放API信息控制器
/// </summary>
[ApiDescriptionSettings("开放API管理")]
[Route("/api/openapi/info")]
[Authorize]
public class OpenApiInfoController : IDynamicApiController
{
  private readonly IApiDescriptionGroupCollectionProvider _apiDescriptionProvider;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="apiDescriptionProvider">API描述提供程序</param>
  public OpenApiInfoController(IApiDescriptionGroupCollectionProvider apiDescriptionProvider)
  {
    _apiDescriptionProvider = apiDescriptionProvider;
  }

  /// <summary>
  /// 获取所有开放API信息
  /// </summary>
  /// <returns>开放API信息列表</returns>
  [HttpGet]
  [OperationId(nameof(GetOpenApiInfo))]
  [DisplayName("获取所有开放API信息")]
  public List<OpenApiInfoModel> GetOpenApiInfo()
  {
    var openApiList = new List<OpenApiInfoModel>();

    // 获取所有API描述
    var apiDescriptions = _apiDescriptionProvider.ApiDescriptionGroups.Items
        .SelectMany(group => group.Items)
        .Where(api => api.ActionDescriptor is ControllerActionDescriptor)
        .ToList();

    foreach (var apiDescription in apiDescriptions)
    {
      var controllerActionDescriptor = (ControllerActionDescriptor)apiDescription.ActionDescriptor;

      // 检查方法上的OpenApi特性
      var methodOpenApiAttr = controllerActionDescriptor.MethodInfo.GetCustomAttribute<OpenApiAttribute>();

      // 检查控制器上的OpenApi特性
      var controllerOpenApiAttr = controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<OpenApiAttribute>();

      // 如果没有OpenApi特性，则跳过
      if (methodOpenApiAttr == null && controllerOpenApiAttr == null)
      {
        continue;
      }

      // 使用方法上的特性，如果没有则使用控制器上的特性
      var openApiAttr = methodOpenApiAttr ?? controllerOpenApiAttr;

      // 获取API的相关信息
      var apiPath = $"{OpenApiConstants.OpenApiRoutePrefix}{apiDescription.RelativePath}";
      var httpMethod = apiDescription.HttpMethod;
      var displayName = controllerActionDescriptor.MethodInfo.GetCustomAttribute<DisplayNameAttribute>()?.DisplayName
          ?? controllerActionDescriptor.MethodInfo.Name;
      var apiGroup = apiDescription.GroupName ?? openApiAttr.GroupName;
      var description = openApiAttr.Description
          ?? controllerActionDescriptor.MethodInfo.GetCustomAttribute<ApiDescriptionSettingsAttribute>()?.Description
          ?? displayName;

      // 获取参数信息
      var parameters = apiDescription.ParameterDescriptions
          .Select(p => new OpenApiParameterModel
          {
            Name = p.Name,
            Type = p.Type?.Name,
            Source = p.Source.Id,
            IsRequired = p.IsRequired
          })
          .ToList();

      // 创建开放API信息对象
      var openApiInfo = new OpenApiInfoModel
      {
        Path = apiPath,
        HttpMethod = httpMethod,
        Name = displayName,
        Description = description,
        GroupName = apiGroup,
        RequireAuthentication = openApiAttr.RequireAuthentication,
        RateLimit = openApiAttr.RateLimit,
        AllowCache = openApiAttr.AllowCache,
        CacheExpiration = openApiAttr.CacheExpiration,
        Parameters = parameters
      };

      openApiList.Add(openApiInfo);
    }

    return openApiList;
  }
}