using Furion.DynamicApiController;
using Furion.SpecificationDocument;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace EdgeGateway.Base.Service.Log;

/// <summary>
/// 系统日志管理服务
/// </summary>
[ApiDescriptionSettings("系统管理")]
[Route("/api/sysLog")]
[Authorize]
public class SysLogService : IDynamicApiController, ITransient
{
    private readonly ILogger<SysLogService> _logger;

    public SysLogService(ILogger<SysLogService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 获取系统日志分页列表
    /// </summary>
    [HttpGet("page")]
    [OperationId(nameof(GetPage))]
    public async Task<dynamic> GetPage(
        int page = 1,
        int pageSize = 20,
        string? level = null,
        string? category = null,
        string? action = null,
        string? module = null,
        int? userId = null,
        string? username = null,
        string? ipAddress = null,
        string? status = null,
        string? startTime = null,
        string? endTime = null,
        string? keyword = null)
    {
        try
        {
            var logs = new List<object>
            {
                new
                {
                    id = "1",
                    timestamp = "2025-07-30 23:15:22",
                    level = "info",
                    category = "用户管理",
                    action = "用户登录",
                    message = "用户 admin 成功登录系统",
                    userId = 1,
                    username = "admin",
                    ipAddress = "::1",
                    userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    module = "auth",
                    status = "success",
                    duration = 245,
                    createTime = "2025-07-30 23:15:22"
                },
                new
                {
                    id = "2",
                    timestamp = "2025-07-30 23:10:15",
                    level = "warning",
                    category = "设备管理",
                    action = "设备连接",
                    message = "设备 Device001 连接超时",
                    userId = 2,
                    username = "operator1",
                    ipAddress = "*************",
                    userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X)",
                    module = "device",
                    status = "warning",
                    duration = 5000,
                    createTime = "2025-07-30 23:10:15"
                }
            };

            return new { items = logs, total = logs.Count, page, pageSize };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统日志分页列表失败");
            throw;
        }
    }

    /// <summary>
    /// 获取日志统计信息
    /// </summary>
    [HttpGet("stats")]
    [OperationId(nameof(GetStats))]
    public async Task<dynamic> GetStats(string? startTime = null, string? endTime = null)
    {
        try
        {
            return new
            {
                totalLogs = 1247,
                todayLogs = 156,
                errorLogs = 23,
                warningLogs = 45,
                successLogs = 89,
                infoLogs = 78,
                debugLogs = 12,
                uniqueUsers = 8
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日志统计失败");
            throw;
        }
    }

    /// <summary>
    /// 导出日志
    /// </summary>
    [HttpGet("export")]
    [OperationId(nameof(ExportLogs))]
    public async Task<IActionResult> ExportLogs(
        string format = "excel",
        string? level = null,
        string? category = null,
        string? startTime = null,
        string? endTime = null,
        int maxRecords = 10000)
    {
        try
        {
            var fileName = $"system-logs-{DateTime.Now:yyyy-MM-dd}.{(format == "excel" ? "xlsx" : format)}";
            var contentType = format switch
            {
                "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "csv" => "text/csv",
                "json" => "application/json",
                _ => "application/octet-stream"
            };

            var content = "示例文件内容";
            var bytes = System.Text.Encoding.UTF8.GetBytes(content);

            return new FileContentResult(bytes, contentType)
            {
                FileDownloadName = fileName
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出日志失败");
            throw;
        }
    }

    /// <summary>
    /// 清理日志
    /// </summary>
    [HttpPost("clean")]
    [OperationId(nameof(CleanLogs))]
    public async Task<bool> CleanLogs([FromBody] dynamic request)
    {
        try
        {
            _logger.LogInformation("清理日志成功");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理日志失败");
            throw;
        }
    }
}