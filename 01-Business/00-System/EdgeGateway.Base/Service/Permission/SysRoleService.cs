using EdgeGateway.Base.Entity.Dto.Permission;
using EdgeGateway.Core.Service;
using EdgeGateway.Core.Attribute;
using System.Linq;

namespace EdgeGateway.Base.Service.Permission;

/// <summary>
/// 系统角色服务
/// </summary>
[ApiDescriptionSettings(Order = 600)]
[Route("/api/sysRole")]
[ModulePermission("ROLE_MANAGEMENT", "角色管理")]
public class SysRoleService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SysRole> _sysRoleRep;
    private readonly SqlSugarRepository<SysRoleMenu> _sysRoleMenuRep;
    private readonly SqlSugarRepository<SysUserRole> _sysUserRoleRep;
    private readonly SqlSugarRepository<SysMenu> _sysMenuRep;
    private readonly IPermissionService _permissionService;

    public SysRoleService(
        SqlSugarRepository<SysRole> sysRoleRep,
        SqlSugarRepository<SysRoleMenu> sysRoleMenuRep,
        SqlSugarRepository<SysUserRole> sysUserRoleRep,
        SqlSugarRepository<SysMenu> sysMenuRep,
        IPermissionService permissionService)
    {
        _sysRoleRep = sysRoleRep;
        _sysRoleMenuRep = sysRoleMenuRep;
        _sysUserRoleRep = sysUserRoleRep;
        _sysMenuRep = sysMenuRep;
        _permissionService = permissionService;
    }

    /// <summary>
    /// 分页获取角色列表
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>角色分页列表</returns>
    [DisplayName("分页获取角色列表")]
    [HttpGet("page")]
    public async Task<SqlSugarPagedList<RoleOutput>> GetRolePage([FromQuery] RolePageInput input)
    {
        var query = _sysRoleRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), r => r.Name.Contains(input.Name!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), r => r.Code.Contains(input.Code!))
            .WhereIF(input.Status.HasValue, r => r.Status == input.Status!.Value)
            .WhereIF(input.AccountType.HasValue, r => r.AccountType == input.AccountType!.Value)
            .OrderBy(r => r.Sort);

        var result = await query.ToPagedListAsync(input.Page, input.PageSize);

        var roleOutputs = result.Items.Select(r => new RoleOutput
        {
            Id = r.Id,
            Name = r.Name,
            Code = r.Code,
            Description = r.Description,
            Status = r.Status,
            IsDefault = r.IsDefault,
            AccountType = r.AccountType,
            AccountTypeDesc = r.AccountType?.ToString(),
            Sort = r.Sort,
            CreateTime = r.CreateTime,
            UpdateTime = r.UpdateTime
        }).ToList();

        return new SqlSugarPagedList<RoleOutput>
        {
            Page = result.Page,
            PageSize = result.PageSize,
            Total = result.Total,
            Items = roleOutputs
        };
    }

    /// <summary>
    /// 获取所有角色列表
    /// </summary>
    /// <returns>角色列表</returns>
    [DisplayName("获取所有角色列表")]
    [HttpGet("list")]
    public async Task<List<RoleOutput>> GetRoleList()
    {
        var roles = await _sysRoleRep.AsQueryable()
            .Where(r => r.Status)
            .OrderBy(r => r.Sort)
            .ToListAsync();

        return roles.Select(r => new RoleOutput
        {
            Id = r.Id,
            Name = r.Name,
            Code = r.Code,
            Description = r.Description,
            Status = r.Status,
            IsDefault = r.IsDefault,
            AccountType = r.AccountType,
            AccountTypeDesc = r.AccountType?.ToString(),
            Sort = r.Sort,
            CreateTime = r.CreateTime,
            UpdateTime = r.UpdateTime
        }).ToList();
    }

    /// <summary>
    /// 创建角色
    /// </summary>
    /// <param name="input">角色创建参数</param>
    /// <returns>角色ID</returns>
    [DisplayName("创建角色")]
    [HttpPost]
    public async Task<long> CreateRole([Required] RoleCreateInput input)
    {
        // 检查角色编码是否已存在
        var existRole = await _sysRoleRep.AsQueryable()
            .FirstAsync(r => r.Code == input.Code);
        if (existRole != null)
            throw Oops.Oh("角色编码已存在");

        var role = new SysRole
        {
            Name = input.Name,
            Code = input.Code,
            Description = input.Description,
            Status = input.Status,
            AccountType = input.AccountType,
            Sort = input.Sort,
            CreateTime = DateTime.Now,
            UpdateTime = DateTime.Now
        };

        var result = await _sysRoleRep.InsertReturnSnowflakeIdAsync(role);
        return result;
    }

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="input">角色更新参数</param>
    [DisplayName("更新角色")]
    [HttpPut]
    public async Task UpdateRole([Required] RoleUpdateInput input)
    {
        var role = await _sysRoleRep.GetByIdAsync(input.Id);
        if (role == null)
            throw Oops.Oh("角色不存在");

        // 检查角色编码是否已存在（排除自己）
        var existRole = await _sysRoleRep.AsQueryable()
            .FirstAsync(r => r.Code == input.Code && r.Id != input.Id);
        if (existRole != null)
            throw Oops.Oh("角色编码已存在");

        role.Name = input.Name;
        role.Code = input.Code;
        role.Description = input.Description;
        role.Status = input.Status;
        role.AccountType = input.AccountType;
        role.Sort = input.Sort;
        role.UpdateTime = DateTime.Now;

        await _sysRoleRep.UpdateAsync(role);
    }

    /// <summary>
    /// 删除角色
    /// </summary>
    /// <param name="id">角色ID</param>
    [DisplayName("删除角色")]
    [HttpDelete("{id}")]
    public async Task DeleteRole([Required] long id)
    {
        var role = await _sysRoleRep.GetByIdAsync(id);
        if (role == null)
            throw Oops.Oh("角色不存在");

        if (role.IsDefault)
            throw Oops.Oh("默认角色不能删除");

        // 检查是否有用户使用该角色
        var userRoleCount = await _sysUserRoleRep.AsQueryable()
            .CountAsync(ur => ur.RoleId == id);
        if (userRoleCount > 0)
            throw Oops.Oh("该角色已被用户使用，不能删除");

        // 删除角色菜单关联
        await _sysRoleMenuRep.DeleteAsync(rm => rm.RoleId == id);

        // 删除角色
        await _sysRoleRep.DeleteByIdAsync(id);
    }

    /// <summary>
    /// 获取角色菜单
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>菜单ID列表</returns>
    [DisplayName("获取角色菜单")]
    [HttpGet("{id}/menus")]
    public async Task<List<long>> GetRoleMenus([Required] long id)
    {
        var menuIds = await _sysRoleMenuRep.AsQueryable()
            .Where(rm => rm.RoleId == id)
            .Select(rm => rm.MenuId)
            .ToListAsync();

        return menuIds;
    }

    /// <summary>
    /// 分配角色菜单
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="menuIds">菜单ID列表</param>
    [DisplayName("分配角色菜单")]
    [HttpPost("{id}/menus")]
    public async Task AssignRoleMenus([Required] long id, [Required] List<long> menuIds)
    {
        var role = await _sysRoleRep.GetByIdAsync(id);
        if (role == null)
            throw Oops.Oh("角色不存在");

        // 删除原有的角色菜单关联
        await _sysRoleMenuRep.DeleteAsync(rm => rm.RoleId == id);

        // 添加新的角色菜单关联
        if (menuIds.Any())
        {
            var roleMenus = menuIds.Select(menuId => new SysRoleMenu
            {
                RoleId = id,
                MenuId = menuId,
                CreateTime = DateTime.Now
            }).ToList();

            await _sysRoleMenuRep.InsertRangeAsync(roleMenus);
        }

        // 清除所有权限缓存，因为角色权限发生了变化
        _permissionService.ClearAllPermissionCache();
    }
}
