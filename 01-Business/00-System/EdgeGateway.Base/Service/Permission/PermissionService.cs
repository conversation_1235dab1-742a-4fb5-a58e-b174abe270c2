using Microsoft.Extensions.Caching.Memory;
using EdgeGateway.Core.Service;
using System.Collections;

namespace EdgeGateway.Base.Service.Permission;

/// <summary>
/// 权限验证服务实现
/// </summary>
public class PermissionService : IPermissionService, ITransient
{
    private readonly SqlSugarRepository<SysUser> _sysUserRep;
    private readonly SqlSugarRepository<SysRole> _sysRoleRep;
    private readonly SqlSugarRepository<SysMenu> _sysMenuRep;
    private readonly SqlSugarRepository<SysUserRole> _sysUserRoleRep;
    private readonly SqlSugarRepository<SysRoleMenu> _sysRoleMenuRep;
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<PermissionService> _logger;

    // 缓存键前缀
    private const string USER_PERMISSIONS_CACHE_KEY = "user_permissions_{0}";
    private const string USER_PERMISSIONS_CACHE_PREFIX = "user_permissions_";
    
    // 缓存过期时间（30分钟）
    private static readonly TimeSpan CacheExpiration = TimeSpan.FromMinutes(30);

    public PermissionService(
        SqlSugarRepository<SysUser> sysUserRep,
        SqlSugarRepository<SysRole> sysRoleRep,
        SqlSugarRepository<SysMenu> sysMenuRep,
        SqlSugarRepository<SysUserRole> sysUserRoleRep,
        SqlSugarRepository<SysRoleMenu> sysRoleMenuRep,
        IMemoryCache memoryCache,
        ILogger<PermissionService> logger)
    {
        _sysUserRep = sysUserRep;
        _sysRoleRep = sysRoleRep;
        _sysMenuRep = sysMenuRep;
        _sysUserRoleRep = sysUserRoleRep;
        _sysRoleMenuRep = sysRoleMenuRep;
        _memoryCache = memoryCache;
        _logger = logger;
    }

    /// <summary>
    /// 检查用户是否有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionCode">权限代码</param>
    /// <returns>是否有权限</returns>
    public async Task<bool> HasPermissionAsync(long userId, string permissionCode)
    {
        if (userId <= 0 || string.IsNullOrWhiteSpace(permissionCode))
            return false;

        try
        {
            // 获取用户权限列表
            var userPermissions = await GetUserPermissionsAsync(userId);
            
            // 检查是否包含指定权限
            return userPermissions.Contains(permissionCode, StringComparer.OrdinalIgnoreCase);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户权限失败，用户ID: {UserId}, 权限代码: {PermissionCode}", userId, permissionCode);
            return false;
        }
    }

    /// <summary>
    /// 获取用户权限列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>权限代码列表</returns>
    public async Task<List<string>> GetUserPermissionsAsync(long userId)
    {
        if (userId <= 0)
            return new List<string>();

        var cacheKey = string.Format(USER_PERMISSIONS_CACHE_KEY, userId);

        // 尝试从缓存获取
        if (_memoryCache.TryGetValue(cacheKey, out List<string>? cachedPermissions) && cachedPermissions != null)
        {
            return cachedPermissions;
        }

        try
        {
            var permissions = new List<string>();

            // 获取用户信息
            var user = await _sysUserRep.GetByIdAsync(userId);
            if (user == null || !user.Status)
                return permissions;

            // 超级管理员拥有所有权限
            if (user.AccountType == AccountTypeEnum.SuperAdmin)
            {
                var allPermissions = await _sysMenuRep.AsQueryable()
                    .Where(m => m.Status)
                    .Select(m => m.Code)
                    .ToListAsync();
                
                permissions.AddRange(allPermissions);
            }
            else
            {
                // 获取用户角色
                var userRoles = await _sysUserRoleRep.AsQueryable()
                    .Where(ur => ur.UserId == userId)
                    .Select(ur => ur.RoleId)
                    .ToListAsync();

                // 如果用户没有分配角色，根据AccountType获取默认角色
                if (!userRoles.Any())
                {
                    var defaultRole = await _sysRoleRep.AsQueryable()
                        .FirstAsync(r => r.IsDefault && r.AccountType == user.AccountType && r.Status);
                    if (defaultRole != null)
                    {
                        userRoles.Add(defaultRole.Id);
                    }
                }

                if (userRoles.Any())
                {
                    // 获取角色对应的菜单权限代码
                    var rolePermissions = await _sysRoleMenuRep.AsQueryable()
                        .LeftJoin<SysMenu>((rm, m) => rm.MenuId == m.Id)
                        .Where((rm, m) => userRoles.Contains(rm.RoleId) && m.Status)
                        .Select((rm, m) => m.Code)
                        .Distinct()
                        .ToListAsync();

                    permissions.AddRange(rolePermissions);
                }
            }

            // 缓存权限列表
            _memoryCache.Set(cacheKey, permissions, CacheExpiration);

            return permissions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户权限列表失败，用户ID: {UserId}", userId);
            return new List<string>();
        }
    }

    /// <summary>
    /// 清除用户权限缓存
    /// </summary>
    /// <param name="userId">用户ID</param>
    public void ClearUserPermissionCache(long userId)
    {
        var cacheKey = string.Format(USER_PERMISSIONS_CACHE_KEY, userId);
        _memoryCache.Remove(cacheKey);
        
        _logger.LogInformation("已清除用户权限缓存，用户ID: {UserId}", userId);
    }

    /// <summary>
    /// 清除所有权限缓存
    /// </summary>
    public void ClearAllPermissionCache()
    {
        // 由于MemoryCache没有直接的方法来删除所有以特定前缀开头的键
        // 这里我们使用反射来获取所有缓存项并删除权限相关的缓存
        try
        {
            var field = typeof(MemoryCache).GetField("_coherentState", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (field?.GetValue(_memoryCache) is object coherentState)
            {
                var entriesCollection = coherentState.GetType()
                    .GetProperty("EntriesCollection", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (entriesCollection?.GetValue(coherentState) is IDictionary entries)
                {
                    var keysToRemove = new List<object>();
                    
                    foreach (DictionaryEntry entry in entries)
                    {
                        if (entry.Key.ToString()?.StartsWith(USER_PERMISSIONS_CACHE_PREFIX) == true)
                        {
                            keysToRemove.Add(entry.Key);
                        }
                    }
                    
                    foreach (var key in keysToRemove)
                    {
                        _memoryCache.Remove(key);
                    }
                    
                    _logger.LogInformation("已清除所有权限缓存，共清除 {Count} 个缓存项", keysToRemove.Count);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "清除所有权限缓存时发生异常，将使用备用方法");
            
            // 备用方法：如果反射失败，我们只能记录警告
            // 在实际应用中，可以考虑使用Redis等外部缓存来更好地管理缓存清理
        }
    }
}
