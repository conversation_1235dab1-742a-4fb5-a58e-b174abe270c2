using EdgeGateway.Base.Entity.Dto.Permission;
using EdgeGateway.Base.Entity.Entity;
using EdgeGateway.Base.Entity.Enums;
using EdgeGateway.Core.Service;
using EdgeGateway.Core.Attribute;
using Furion.DynamicApiController;
using Furion.SpecificationDocument;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Furion.DataValidation;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace EdgeGateway.Base.Service.Permission;

/// <summary>
///     系统用户服务
/// </summary>
[ApiDescriptionSettings(Order = 602)]
[Route("/api/sysUser")]
[ModulePermission("USER_MANAGEMENT", "用户管理")]
public class SysUserService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SysUser> _sysUserRep;
    private readonly SqlSugarRepository<SysUserRole> _sysUserRoleRep;
    private readonly SqlSugarRepository<SysRole> _sysRoleRep;
    private readonly IPermissionService _permissionService;

    public SysUserService(
        SqlSugarRepository<SysUser> sysUserRep,
        SqlSugarRepository<SysUserRole> sysUserRoleRep,
        SqlSugarRepository<SysRole> sysRoleRep,
        IPermissionService permissionService)
    {
        _sysUserRep = sysUserRep;
        _sysUserRoleRep = sysUserRoleRep;
        _sysRoleRep = sysRoleRep;
        _permissionService = permissionService;
    }

    /// <summary>
    ///     分页获取用户列表
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>用户分页列表</returns>
    [DisplayName("分页获取用户列表")]
    [HttpGet("page")]
    public async Task<SqlSugarPagedList<UserOutput>> GetUserPage([FromQuery] UserPageInput input)
    {
        var query = _sysUserRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Account), u => u.Account.Contains(input.Account!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name!))
            .WhereIF(input.Status.HasValue, u => u.Status == input.Status!.Value)
            .WhereIF(input.AccountType.HasValue, u => u.AccountType == input.AccountType!.Value)
            .OrderBy(u => u.LastLoginTime);

        var result = await query.ToPagedListAsync(input.Page, input.PageSize);

        var userOutputs = result.Items.Select(u => new UserOutput
        {
            Id = u.Id,
            Account = u.Account,
            Name = u.Name,
            AccountType = u.AccountType,
            Status = u.Status,
            LastLoginTime = u.LastLoginTime,
            LastLoginIp = u.LastLoginIp
        }).ToList();

        return new SqlSugarPagedList<UserOutput>
        {
            Page = result.Page,
            PageSize = result.PageSize,
            Total = result.Total,
            Items = userOutputs
        };
    }

    /// <summary>
    ///     获取所有用户列表
    /// </summary>
    /// <returns>用户列表</returns>
    [DisplayName("获取所有用户列表")]
    [HttpGet("list")]
    public async Task<List<UserOutput>> GetUserList()
    {
        var users = await _sysUserRep.AsQueryable()
            .Where(u => u.Status)
            .OrderBy(u => u.LastLoginTime)
            .ToListAsync();

        return users.Select(u => new UserOutput
        {
            Id = u.Id,
            Account = u.Account,
            Name = u.Name,
            AccountType = u.AccountType,
            Status = u.Status,
            LastLoginTime = u.LastLoginTime,
            LastLoginIp = u.LastLoginIp
        }).ToList();
    }

    /// <summary>
    ///     创建用户
    /// </summary>
    /// <param name="input">用户创建参数</param>
    /// <returns>用户ID</returns>
    [DisplayName("创建用户")]
    [HttpPost]
    public async Task<long> CreateUser([Required] UserCreateInput input)
    {
        // 检查账号是否已存在
        var existUser = await _sysUserRep.AsQueryable()
            .FirstAsync(u => u.Account == input.Account);
        if (existUser != null)
            throw Oops.Oh("账号已存在");

        // 密码加密
        var passwordHash = MD5Encryption.Encrypt(input.Password);

        var user = new SysUser
        {
            Account = input.Account,
            Name = input.Name,
            Password = passwordHash,
            AccountType = input.AccountType,
            Status = input.Status
        };

        var result = await _sysUserRep.InsertReturnSnowflakeIdAsync(user);

        // 为用户分配默认角色
        await AssignDefaultRole(result, input.AccountType);

        return result;
    }

    /// <summary>
    ///     更新用户
    /// </summary>
    /// <param name="input">用户更新参数</param>
    [DisplayName("更新用户")]
    [HttpPut]
    public async Task UpdateUser([Required] UserUpdateInput input)
    {
        var user = await _sysUserRep.GetByIdAsync(input.Id);
        if (user == null)
            throw Oops.Oh("用户不存在");

        // 检查账号是否已存在（排除自己）
        var existUser = await _sysUserRep.AsQueryable()
            .FirstAsync(u => u.Account == input.Account && u.Id != input.Id);
        if (existUser != null)
            throw Oops.Oh("账号已存在");

        user.Account = input.Account;
        user.Name = input.Name;
        user.AccountType = input.AccountType;
        user.Status = input.Status;

        await _sysUserRep.UpdateAsync(user);

        // 如果账号类型发生变化，重新分配默认角色
        if (user.AccountType != input.AccountType) await ReassignDefaultRole(input.Id, input.AccountType);

        // 清除用户权限缓存
        _permissionService.ClearUserPermissionCache(input.Id);
    }

    /// <summary>
    ///     删除用户
    /// </summary>
    /// <param name="id">用户ID</param>
    [DisplayName("删除用户")]
    [HttpDelete("{id}")]
    public async Task DeleteUser([Required] long id)
    {
        var user = await _sysUserRep.GetByIdAsync(id);
        if (user == null)
            throw Oops.Oh("用户不存在");

        // 删除用户角色关联
        await _sysUserRoleRep.DeleteAsync(ur => ur.UserId == id);

        // 删除用户
        await _sysUserRep.DeleteByIdAsync(id);

        // 清除用户权限缓存
        _permissionService.ClearUserPermissionCache(id);
    }

    /// <summary>
    ///     切换用户状态
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="input">状态输入</param>
    [DisplayName("切换用户状态")]
    [HttpPatch("{id}/status")]
    public async Task ToggleUserStatus([Required] long id, [Required] UserStatusInput input)
    {
        var user = await _sysUserRep.GetByIdAsync(id);
        if (user == null)
            throw Oops.Oh("用户不存在");

        user.Status = input.Status;

        await _sysUserRep.UpdateAsync(user);

        // 清除用户权限缓存
        _permissionService.ClearUserPermissionCache(id);
    }

    /// <summary>
    ///     重置用户密码
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="input">密码输入</param>
    [DisplayName("重置用户密码")]
    [HttpPatch("{id}/password")]
    public async Task ResetUserPassword([Required] long id, [Required] UserPasswordInput input)
    {
        var user = await _sysUserRep.GetByIdAsync(id);
        if (user == null)
            throw Oops.Oh("用户不存在");

        // 密码加密
        var passwordHash = MD5Encryption.Encrypt(input.Password);
        user.Password = passwordHash;

        await _sysUserRep.UpdateAsync(user);
    }

    /// <summary>
    ///     获取用户角色
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>角色ID列表</returns>
    [DisplayName("获取用户角色")]
    [HttpGet("{id}/roles")]
    public async Task<List<long>> GetUserRoles([Required] long id)
    {
        var roleIds = await _sysUserRoleRep.AsQueryable()
            .Where(ur => ur.UserId == id)
            .Select(ur => ur.RoleId)
            .ToListAsync();

        return roleIds;
    }

    /// <summary>
    ///     分配用户角色
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="roleIds">角色ID列表</param>
    [DisplayName("分配用户角色")]
    [HttpPost("{id}/roles")]
    public async Task AssignUserRoles([Required] long id, [Required] List<long> roleIds)
    {
        var user = await _sysUserRep.GetByIdAsync(id);
        if (user == null)
            throw Oops.Oh("用户不存在");

        // 删除原有的用户角色关联
        await _sysUserRoleRep.DeleteAsync(ur => ur.UserId == id);

        // 添加新的用户角色关联
        if (roleIds.Any())
        {
            var userRoles = roleIds.Select(roleId => new SysUserRole
            {
                UserId = id,
                RoleId = roleId,
                CreateTime = DateTime.Now
            }).ToList();

            await _sysUserRoleRep.InsertRangeAsync(userRoles);
        }

        // 清除用户权限缓存
        _permissionService.ClearUserPermissionCache(id);
    }

    /// <summary>
    ///     为用户分配默认角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="accountType">账号类型</param>
    private async Task AssignDefaultRole(long userId, AccountTypeEnum accountType)
    {
        var defaultRole = await _sysRoleRep.AsQueryable()
            .FirstAsync(r => r.IsDefault && r.AccountType == accountType && r.Status);

        if (defaultRole != null)
        {
            var userRole = new SysUserRole
            {
                UserId = userId,
                RoleId = defaultRole.Id,
                CreateTime = DateTime.Now
            };

            await _sysUserRoleRep.InsertAsync(userRole);
        }
    }

    /// <summary>
    ///     重新分配默认角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="accountType">账号类型</param>
    private async Task ReassignDefaultRole(long userId, AccountTypeEnum accountType)
    {
        // 删除原有的默认角色关联
        var defaultRoles = await _sysRoleRep.AsQueryable()
            .Where(r => r.IsDefault)
            .Select(r => r.Id)
            .ToListAsync();

        await _sysUserRoleRep.DeleteAsync(ur => ur.UserId == userId && defaultRoles.Contains(ur.RoleId));

        // 分配新的默认角色
        await AssignDefaultRole(userId, accountType);
    }
}