using EdgeGateway.Base.Entity.Dto.Permission;
using EdgeGateway.Core.Attribute;
using System.Linq;

namespace EdgeGateway.Base.Service.Permission;

/// <summary>
/// 系统菜单服务
/// </summary>
[ApiDescriptionSettings(Order = 601)]
[Route("/api/sysMenu")]
[ModulePermission("MENU_MANAGEMENT", "菜单管理")]
public class SysMenuService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SysMenu> _sysMenuRep;
    private readonly SqlSugarRepository<SysRoleMenu> _sysRoleMenuRep;

    public SysMenuService(
        SqlSugarRepository<SysMenu> sysMenuRep,
        SqlSugarRepository<SysRoleMenu> sysRoleMenuRep)
    {
        _sysMenuRep = sysMenuRep;
        _sysRoleMenuRep = sysRoleMenuRep;
    }

    /// <summary>
    /// 获取菜单树
    /// </summary>
    /// <returns>菜单树</returns>
    [DisplayName("获取菜单树")]
    [HttpGet("tree")]
    public async Task<List<MenuTreeOutput>> GetMenuTree()
    {
        var menus = await _sysMenuRep.AsQueryable()
            .Where(m => m.Status)
            .OrderBy(m => m.Sort)
            .ToListAsync();

        return BuildMenuTree(menus, null);
    }

    /// <summary>
    /// 分页获取菜单列表
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>菜单分页列表</returns>
    [DisplayName("分页获取菜单列表")]
    [HttpGet("page")]
    public async Task<SqlSugarPagedList<MenuOutput>> GetMenuPage([FromQuery] MenuPageInput input)
    {
        var query = _sysMenuRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), m => m.Name.Contains(input.Name!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), m => m.Code.Contains(input.Code!))
            .WhereIF(input.ParentId.HasValue, m => m.ParentId == input.ParentId!.Value)
            .WhereIF(input.MenuType.HasValue, m => m.MenuType == input.MenuType!.Value)
            .WhereIF(input.Status.HasValue, m => m.Status == input.Status!.Value)
            .OrderBy(m => m.Sort);

        var result = await query.ToPagedListAsync(input.Page, input.PageSize);

        // 获取父菜单信息
        var parentIds = result.Items.Where(m => m.ParentId.HasValue).Select(m => m.ParentId!.Value).Distinct().ToList();
        var parentMenus = new Dictionary<long, string>();
        if (parentIds.Any())
        {
            var parents = await _sysMenuRep.AsQueryable()
                .Where(m => parentIds.Contains(m.Id))
                .Select(m => new { m.Id, m.Name })
                .ToListAsync();
            parentMenus = parents.ToDictionary(p => p.Id, p => p.Name);
        }

        var menuOutputs = result.Items.Select(m => new MenuOutput
        {
            Id = m.Id,
            Name = m.Name,
            Code = m.Code,
            Path = m.Path,
            Icon = m.Icon,
            ParentId = m.ParentId,
            ParentName = m.ParentId.HasValue && parentMenus.ContainsKey(m.ParentId.Value) ? parentMenus[m.ParentId.Value] : null,
            MenuType = m.MenuType,
            MenuTypeDesc = m.MenuType.ToString(),
            Sort = m.Sort,
            Status = m.Status,
            Hidden = m.Hidden,
            CreateTime = m.CreateTime,
            UpdateTime = m.UpdateTime
        }).ToList();

        return new SqlSugarPagedList<MenuOutput>
        {
            Page = result.Page,
            PageSize = result.PageSize,
            Total = result.Total,
            Items = menuOutputs
        };
    }

    /// <summary>
    /// 创建菜单
    /// </summary>
    /// <param name="input">菜单创建参数</param>
    /// <returns>菜单ID</returns>
    [DisplayName("创建菜单")]
    [HttpPost]
    public async Task<long> CreateMenu([Required] MenuCreateInput input)
    {
        // 检查菜单编码是否已存在
        var existMenu = await _sysMenuRep.AsQueryable()
            .FirstAsync(m => m.Code == input.Code);
        if (existMenu != null)
            throw Oops.Oh("菜单编码已存在");

        // 检查父菜单是否存在
        if (input.ParentId.HasValue)
        {
            var parentMenu = await _sysMenuRep.GetByIdAsync(input.ParentId.Value);
            if (parentMenu == null)
                throw Oops.Oh("父菜单不存在");
        }

        var menu = new SysMenu
        {
            Name = input.Name,
            Code = input.Code,
            Path = input.Path,
            Icon = input.Icon,
            ParentId = input.ParentId,
            MenuType = input.MenuType,
            Sort = input.Sort,
            Status = input.Status,
            Hidden = input.Hidden,
            CreateTime = DateTime.Now,
            UpdateTime = DateTime.Now
        };

        var result = await _sysMenuRep.InsertReturnSnowflakeIdAsync(menu);
        return result;
    }

    /// <summary>
    /// 更新菜单
    /// </summary>
    /// <param name="input">菜单更新参数</param>
    [DisplayName("更新菜单")]
    [HttpPut]
    public async Task UpdateMenu([Required] MenuUpdateInput input)
    {
        var menu = await _sysMenuRep.GetByIdAsync(input.Id);
        if (menu == null)
            throw Oops.Oh("菜单不存在");

        // 检查菜单编码是否已存在（排除自己）
        var existMenu = await _sysMenuRep.AsQueryable()
            .FirstAsync(m => m.Code == input.Code && m.Id != input.Id);
        if (existMenu != null)
            throw Oops.Oh("菜单编码已存在");

        // 检查父菜单是否存在
        if (input.ParentId.HasValue)
        {
            var parentMenu = await _sysMenuRep.GetByIdAsync(input.ParentId.Value);
            if (parentMenu == null)
                throw Oops.Oh("父菜单不存在");

            // 不能将自己设为父菜单
            if (input.ParentId.Value == input.Id)
                throw Oops.Oh("不能将自己设为父菜单");
        }

        menu.Name = input.Name;
        menu.Code = input.Code;
        menu.Path = input.Path;
        menu.Icon = input.Icon;
        menu.ParentId = input.ParentId;
        menu.MenuType = input.MenuType;
        menu.Sort = input.Sort;
        menu.Status = input.Status;
        menu.Hidden = input.Hidden;
        menu.UpdateTime = DateTime.Now;

        await _sysMenuRep.UpdateAsync(menu);
    }

    /// <summary>
    /// 删除菜单
    /// </summary>
    /// <param name="id">菜单ID</param>
    [DisplayName("删除菜单")]
    [HttpDelete("{id}")]
    public async Task DeleteMenu([Required] long id)
    {
        var menu = await _sysMenuRep.GetByIdAsync(id);
        if (menu == null)
            throw Oops.Oh("菜单不存在");

        // 检查是否有子菜单
        var childCount = await _sysMenuRep.AsQueryable()
            .CountAsync(m => m.ParentId == id);
        if (childCount > 0)
            throw Oops.Oh("该菜单下有子菜单，不能删除");

        // 检查是否有角色使用该菜单
        var roleMenuCount = await _sysRoleMenuRep.AsQueryable()
            .CountAsync(rm => rm.MenuId == id);
        if (roleMenuCount > 0)
            throw Oops.Oh("该菜单已被角色使用，不能删除");

        await _sysMenuRep.DeleteByIdAsync(id);
    }

    /// <summary>
    /// 构建菜单树
    /// </summary>
    /// <param name="menus">菜单列表</param>
    /// <param name="parentId">父菜单ID</param>
    /// <returns>菜单树</returns>
    private List<MenuTreeOutput> BuildMenuTree(List<SysMenu> menus, long? parentId)
    {
        var result = new List<MenuTreeOutput>();

        var children = menus.Where(m => m.ParentId == parentId).ToList();
        foreach (var menu in children)
        {
            var menuTree = new MenuTreeOutput
            {
                Id = menu.Id,
                Name = menu.Name,
                Code = menu.Code,
                Path = menu.Path,
                Icon = menu.Icon,
                ParentId = menu.ParentId,
                MenuType = menu.MenuType,
                MenuTypeDesc = menu.MenuType.ToString(),
                Sort = menu.Sort,
                Status = menu.Status,
                Hidden = menu.Hidden,
                CreateTime = menu.CreateTime,
                Children = BuildMenuTree(menus, menu.Id)
            };

            result.Add(menuTree);
        }

        return result.OrderBy(m => m.Sort).ToList();
    }
}
