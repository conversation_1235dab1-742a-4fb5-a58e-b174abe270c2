namespace EdgeGateway.Base.Auth.Dto;

/// <summary>
///     用户登录信息
/// </summary>
public class LoginUserOutput
{
    /// <summary>
    ///     用户id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     账号名称
    /// </summary>
    public string Account { get; set; }

    /// <summary>
    ///     真实姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     账号类型
    /// </summary>
    public AccountTypeEnum AccountType { get; set; }

    /// <summary>
    ///     最后登录时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    ///     最后登录IP
    /// </summary>
    public string? LastLoginIp { get; set; }

}