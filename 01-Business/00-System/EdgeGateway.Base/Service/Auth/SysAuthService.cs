namespace EdgeGateway.Base.Auth;
using EdgeGateway.Base.Auth.Dto;
using EdgeGateway.Base.Entity.Dto.Permission;
using EdgeGateway.Core.Service;
using EdgeGateway.Core.Attribute;
using System.Linq;

/// <summary>
///     系统登录授权服务
/// </summary>
[ApiDescriptionSettings(Order = 500)]
[Route("/api/sysAuth")]
public class SysAuthService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     用户表
    /// </summary>
    private readonly SqlSugarRepository<SysUser> _sysUserRep;

    /// <summary>
    ///     配置文件    
    /// </summary>
    private readonly IniConfiguration _config;

    /// <summary>
    ///     上下文
    /// </summary>
    private readonly IHttpContextAccessor _httpContextAccessor;

    /// <summary>
    ///     用户管理
    /// </summary>
    private readonly UserManager _userManager;

    /// <summary>
    ///     日志
    /// </summary>
    private readonly ILogger<SysAuthService> _logger;

    /// <summary>
    ///     IP定位
    /// </summary>
    private readonly IIPLocatorProvider _ipLocator;

    /// <summary>
    ///     角色表
    /// </summary>
    private readonly SqlSugarRepository<SysRole> _sysRoleRep;

    /// <summary>
    ///     菜单表
    /// </summary>
    private readonly SqlSugarRepository<SysMenu> _sysMenuRep;

    /// <summary>
    ///     用户角色关联表
    /// </summary>
    private readonly SqlSugarRepository<SysUserRole> _sysUserRoleRep;

    /// <summary>
    ///     角色菜单关联表
    /// </summary>
    private readonly SqlSugarRepository<SysRoleMenu> _sysRoleMenuRep;

    /// <summary>
    ///     权限服务
    /// </summary>
    private readonly IPermissionService _permissionService;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="userManager"> 用户管理 </param>
    /// <param name="sysUserRep"> 用户表 </param>
    /// <param name="httpContextAccessor"> 上下文 </param>
    /// <param name="logger"> 日志 </param>
    /// <param name="ipLocator"> IP定位 </param>
    /// <param name="config"> 配置文件 </param>
    /// <param name="sysRoleRep"> 角色表 </param>
    /// <param name="sysMenuRep"> 菜单表 </param>
    /// <param name="sysUserRoleRep"> 用户角色关联表 </param>
    /// <param name="sysRoleMenuRep"> 角色菜单关联表 </param>
    /// <param name="permissionService"> 权限服务 </param>
    public SysAuthService(UserManager userManager,
        SqlSugarRepository<SysUser> sysUserRep,
        IHttpContextAccessor httpContextAccessor,
        ILogger<SysAuthService> logger,
        IIPLocatorProvider ipLocator,
        IniConfiguration config,
        SqlSugarRepository<SysRole> sysRoleRep,
        SqlSugarRepository<SysMenu> sysMenuRep,
        SqlSugarRepository<SysUserRole> sysUserRoleRep,
        SqlSugarRepository<SysRoleMenu> sysRoleMenuRep,
        IPermissionService permissionService)
    {
        _userManager = userManager;
        _sysUserRep = sysUserRep;
        _httpContextAccessor = httpContextAccessor;
        _config = config;
        _logger = logger;
        _ipLocator = ipLocator;
        _sysRoleRep = sysRoleRep;
        _sysMenuRep = sysMenuRep;
        _sysUserRoleRep = sysUserRoleRep;
        _sysRoleMenuRep = sysRoleMenuRep;
        _permissionService = permissionService;
    }

    /// <summary>
    ///     账号密码登录
    /// </summary>
    /// <param name="input"></param>
    /// <remarks>用户名/密码：admin/e10adc3949ba59abbe56e057f20f883e</remarks>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("账号密码登录")]
    [HttpPost("login")]
    [OperationId(nameof(Login))]
    public async Task<LoginOutput> Login([Required] LoginInput input)
    {
        try
        {
            // 获取客户端IP和位置信息
            var clientIP = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "未知IP";
            var userAgent = _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].ToString() ?? "未知设备";
            var ipLocation = await _ipLocator.GetIPLocationAsync(clientIP);
            var loginTime = DateTime.Now;

            // 账号是否存在
            var user = await _sysUserRep.AsQueryable().ClearFilter().FirstAsync(u => u.Account.Equals(input.Account));
            _ = user ?? throw Oops.Oh("登录账号不存在，请确认后重试");

            // 校验密码
            VerifyPassword(input, user);

            var result = await CreateToken(user);

            // 记录详细的登录成功日志
            _logger.LogInformation(
                "用户登录成功 - 用户信息: {Account}({Name}), IP: {IP}, 位置: {Location}, 时间: {Time}, 设备: {Device}",
                user.Account,
                user.Name,
                clientIP,
                ipLocation?.ToString() ?? "未知位置",
                loginTime.ToString("yyyy-MM-dd HH:mm:ss"),
                userAgent
            );

            // 更新最后登录时间
            user.LastLoginTime = loginTime;
            user.LastLoginIp = clientIP;
            await _sysUserRep.UpdateAsync(user);

            return result;
        }
        catch (Exception ex)
        {
            // 记录详细的登录失败日志
            var clientIP = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "未知IP";
            var userAgent = _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].ToString() ?? "未知设备";
            var ipLocation = await _ipLocator.GetIPLocationAsync(clientIP);
            var loginTime = DateTime.Now;

            _logger.LogWarning(
                "用户登录失败 - 账号: {Account}, IP: {IP}, 位置: {Location}, 时间: {Time}, 设备: {Device}, 原因: {Message}",
                input.Account,
                clientIP,
                ipLocation?.ToString() ?? "未知位置",
                loginTime.ToString("yyyy-MM-dd HH:mm:ss"),
                userAgent,
                ex.Message
            );
            throw;
        }
    }

    /// <summary>
    ///     获取登录账号
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取登录账号")]
    [HttpGet("userInfo")]
    [OperationId(nameof(GetUserInfo))]
    public async Task<LoginUserOutput> GetUserInfo()
    {
        var user = await _sysUserRep.GetFirstAsync(u => u.Id == _userManager.UserId) ?? throw Oops.Oh("非法操作，未登录").StatusCode(401);
        return new LoginUserOutput
        {
            Id = user.Id,
            Account = user.Account,
            Name = user.Name,
            AccountType = user.AccountType,
            LastLoginTime = user.LastLoginTime,
            LastLoginIp = user.LastLoginIp
        };
    }

    /// <summary>
    ///     获取刷新Token
    /// </summary>
    /// <param name="accessToken"></param>
    /// <returns></returns>
    [DisplayName("获取刷新Token")]
    [HttpGet("getRefreshToken")]
    [OperationId(nameof(GetRefreshToken))]
    public async Task<string> GetRefreshToken([FromQuery] string accessToken)
    {
        var refreshTokenExpire = _config.GetValue<int>("system_System", ConfigConst.SysRefreshTokenExpire);
        return JWTEncryption.GenerateRefreshToken(accessToken, refreshTokenExpire);
    }

    /// <summary>
    ///     退出系统
    /// </summary>
    [DisplayName("退出系统")]
    [HttpPost("logout")]
    [OperationId(nameof(Logout))]
    public void Logout()
    {
        if (string.IsNullOrWhiteSpace(_userManager.Account))
            return;
        _httpContextAccessor.HttpContext.SignoutToSwagger();
    }

    /// <summary>
    ///     心跳检查接口
    /// </summary>
    /// <returns>返回200表示服务正常</returns>
    [AllowAnonymous]
    [DisplayName("心跳检查")]
    [HttpGet("heartbeat")]
    [OperationId(nameof(HeartBeat))]
    public int HeartBeat()
    {
        return 200;
    }

    /// <summary>
    ///     锁屏解锁
    /// </summary>
    /// <param name="input">解锁输入参数</param>
    /// <returns>解锁结果</returns>
    [DisplayName("锁屏解锁")]
    [HttpPost("unlockScreen")]
    [OperationId(nameof(UnlockScreen))]
    public async Task<bool> UnlockScreen([Required] UnlockScreenInput input)
    {
        try
        {
            // 获取当前用户信息
            var user = await _sysUserRep.GetFirstAsync(u => u.Id == _userManager.UserId)
                ?? throw Oops.Oh("非法操作，未登录").StatusCode(401);

            // 验证密码
            if (user.Password != input.Password)
                throw Oops.Oh("密码不正确");

            return true;
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    #region swagger

    /// <summary>
    ///     Swagger登录检查
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("/api/swagger/checkUrl")]
    [NonUnify]
    [DisplayName("Swagger登录检查")]
    public int SwaggerCheckUrl()
    {
        return _httpContextAccessor.HttpContext.User.Identity.IsAuthenticated ? 200 : 401;
    }

    /// <summary>
    ///     Swagger登录提交
    /// </summary>
    /// <param name="auth"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("/api/swagger/submitUrl")]
    [NonUnify]
    [DisplayName("Swagger登录提交")]
    public async Task<int> SwaggerSubmitUrl([FromForm] SpecificationAuth auth)
    {
        try
        {
            await Login(new LoginInput
            {
                Account = auth.UserName,
                Password = MD5Encryption.Encrypt(auth.Password)
            });
            return 200;
        }
        catch (Exception)
        {
            return 401;
        }
    }

    #endregion

    #region Private

    /// <summary>
    ///     验证用户密码
    /// </summary>
    /// <param name="input"></param>
    /// <param name="user"></param>
    private void VerifyPassword(LoginInput input, SysUser user)
    {
        if (user.Password != input.Password)
            throw Oops.Oh("密码不正确");
    }

    /// <summary>
    ///     生成Token令牌
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    [NonAction]
    private async Task<LoginOutput> CreateToken(SysUser user)
    {
        // 生成Token令牌
        var tokenExpire = _config.GetValue<int>("system_System", ConfigConst.SysTokenExpire);
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, user.Id },
            { ClaimConst.Account, user.Account },
            { ClaimConst.RealName, user.Name },
            { ClaimConst.AccountType, user.AccountType }
        }, tokenExpire);

        // swagger 自动登录
        _httpContextAccessor.HttpContext.SigninToSwagger(accessToken);

        // 生成刷新Token令牌
        var refreshTokenExpire = _config.GetValue<int>("system_System", ConfigConst.SysRefreshTokenExpire);
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, refreshTokenExpire);

        // 设置响应报文头
        _httpContextAccessor.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);

        // Swagger Knife4UI-AfterScript登录脚本
        // ke.global.setAllHeader('Authorization', 'Bearer ' + ke.response.headers['access-token']);
        return new LoginOutput
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken
        };
    }

    /// <summary>
    /// 获取当前用户菜单树
    /// </summary>
    /// <returns>菜单树</returns>
    [DisplayName("获取当前用户菜单树")]
    [HttpGet("userMenus")]
    public async Task<List<MenuTreeOutput>> GetUserMenus()
    {
        var userId = _userManager.UserId;
        if (userId <= 0)
            throw Oops.Oh("用户未登录").StatusCode(401);

        // 获取用户角色
        var userRoles = await _sysUserRoleRep.AsQueryable()
            .Where(ur => ur.UserId == userId)
            .Select(ur => ur.RoleId)
            .ToListAsync();

        if (!userRoles.Any())
        {
            // 如果用户没有分配角色，根据AccountType获取默认角色
            var user = await _sysUserRep.GetByIdAsync(userId);
            if (user != null)
            {
                var defaultRole = await _sysRoleRep.AsQueryable()
                    .FirstAsync(r => r.IsDefault && r.AccountType == user.AccountType && r.Status);
                if (defaultRole != null)
                {
                    userRoles.Add(defaultRole.Id);
                }
            }
        }

        if (!userRoles.Any())
            return new List<MenuTreeOutput>();

        // 获取角色对应的菜单
        var menuIds = await _sysRoleMenuRep.AsQueryable()
            .Where(rm => userRoles.Contains(rm.RoleId))
            .Select(rm => rm.MenuId)
            .Distinct()
            .ToListAsync();

        if (!menuIds.Any())
            return new List<MenuTreeOutput>();

        // 获取菜单信息
        var menus = await _sysMenuRep.AsQueryable()
            .Where(m => menuIds.Contains(m.Id) && m.Status && !m.Hidden)
            .OrderBy(m => m.Sort)
            .ToListAsync();

        return BuildMenuTree(menus, null);
    }

    /// <summary>
    /// 获取当前用户权限代码列表
    /// </summary>
    /// <returns>权限代码列表</returns>
    [DisplayName("获取当前用户权限代码列表")]
    [HttpGet("userPermissions")]
    public async Task<List<string>> GetUserPermissions()
    {
        var userId = _userManager.UserId;
        if (userId <= 0)
            throw Oops.Oh("用户未登录").StatusCode(401);

        // 获取用户角色
        var userRoles = await _sysUserRoleRep.AsQueryable()
            .Where(ur => ur.UserId == userId)
            .Select(ur => ur.RoleId)
            .ToListAsync();

        if (!userRoles.Any())
        {
            // 如果用户没有分配角色，根据AccountType获取默认角色
            var user = await _sysUserRep.GetByIdAsync(userId);
            if (user != null)
            {
                var defaultRole = await _sysRoleRep.AsQueryable()
                    .FirstAsync(r => r.IsDefault && r.AccountType == user.AccountType && r.Status);
                if (defaultRole != null)
                {
                    userRoles.Add(defaultRole.Id);
                }
            }
        }

        if (!userRoles.Any())
            return new List<string>();

        // 获取角色对应的菜单权限代码
        var permissions = await _sysRoleMenuRep.AsQueryable()
            .LeftJoin<SysMenu>((rm, m) => rm.MenuId == m.Id)
            .Where((rm, m) => userRoles.Contains(rm.RoleId) && m.Status)
            .Select((rm, m) => m.Code)
            .Distinct()
            .ToListAsync();

        return permissions;
    }

    /// <summary>
    /// 获取用户角色
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>角色列表</returns>
    [DisplayName("获取用户角色")]
    [HttpGet("{id}/roles")]
    [MenuPermission("USER_MANAGEMENT", "用户管理")]
    public async Task<List<RoleOutput>> GetUserRoles([Required] long id)
    {
        var userRoles = await _sysUserRoleRep.AsQueryable()
            .LeftJoin<SysRole>((ur, r) => ur.RoleId == r.Id)
            .Where((ur, r) => ur.UserId == id && r.Status)
            .Select((ur, r) => r)
            .ToListAsync();

        return userRoles.Select(r => new RoleOutput
        {
            Id = r.Id,
            Name = r.Name,
            Code = r.Code,
            Description = r.Description,
            Status = r.Status,
            IsDefault = r.IsDefault,
            AccountType = r.AccountType,
            AccountTypeDesc = r.AccountType?.ToString(),
            Sort = r.Sort,
            CreateTime = r.CreateTime,
            UpdateTime = r.UpdateTime
        }).ToList();
    }

    /// <summary>
    /// 分配用户角色
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="roleIds">角色ID列表</param>
    [DisplayName("分配用户角色")]
    [HttpPost("{id}/roles")]
    [MenuPermission("USER_MANAGEMENT", "用户管理")]
    public async Task AssignUserRoles([Required] long id, [Required] List<long> roleIds)
    {
        var user = await _sysUserRep.GetByIdAsync(id);
        if (user == null)
            throw Oops.Oh("用户不存在");

        // 删除原有的用户角色关联
        await _sysUserRoleRep.DeleteAsync(ur => ur.UserId == id);

        // 添加新的用户角色关联
        if (roleIds.Any())
        {
            var userRoles = roleIds.Select(roleId => new SysUserRole
            {
                UserId = id,
                RoleId = roleId,
                CreateTime = DateTime.Now
            }).ToList();

            await _sysUserRoleRep.InsertRangeAsync(userRoles);
        }

        // 清除该用户的权限缓存
        _permissionService.ClearUserPermissionCache(id);
    }

    /// <summary>
    /// 移除用户角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    [DisplayName("移除用户角色")]
    [HttpDelete("{userId}/roles/{roleId}")]
    [MenuPermission("USER_MANAGEMENT", "用户管理")]
    public async Task RemoveUserRole([Required] long userId, [Required] long roleId)
    {
        await _sysUserRoleRep.DeleteAsync(ur => ur.UserId == userId && ur.RoleId == roleId);

        // 清除该用户的权限缓存
        _permissionService.ClearUserPermissionCache(userId);
    }

    /// <summary>
    /// 构建菜单树
    /// </summary>
    /// <param name="menus">菜单列表</param>
    /// <param name="parentId">父菜单ID</param>
    /// <returns>菜单树</returns>
    private List<MenuTreeOutput> BuildMenuTree(List<SysMenu> menus, long? parentId)
    {
        var result = new List<MenuTreeOutput>();

        var children = menus.Where(m => m.ParentId == parentId).ToList();
        foreach (var menu in children)
        {
            var menuTree = new MenuTreeOutput
            {
                Id = menu.Id,
                Name = menu.Name,
                Code = menu.Code,
                Path = menu.Path,
                Icon = menu.Icon,
                ParentId = menu.ParentId,
                MenuType = menu.MenuType,
                MenuTypeDesc = menu.MenuType.ToString(),
                Sort = menu.Sort,
                Status = menu.Status,
                Hidden = menu.Hidden,
                CreateTime = menu.CreateTime,
                Children = BuildMenuTree(menus, menu.Id)
            };

            result.Add(menuTree);
        }

        return result.OrderBy(m => m.Sort).ToList();
    }

    #endregion
}