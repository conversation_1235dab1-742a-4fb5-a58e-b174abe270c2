/// <summary>
///     系统更新服务接口
/// </summary>
public interface ISystemUpdateService
{
    /// <summary>
    ///     检查系统更新
    /// </summary>
    /// <returns>返回更新检查结果，包含是否有新版本及相关信息</returns>
    Task<CheckUpdateOutput> CheckUpdateAsync();

    /// <summary>
    ///     上传更新包文件
    /// </summary>
    /// <param name="file">更新包文件</param>
    /// <returns>上传是否成功</returns>
    Task<bool> UploadUpdatePackageAsync(IFormFile file);

    /// <summary>
    ///     执行远程更新
    /// </summary>
    /// <param name="downloadUrl">更新包下载地址</param>
    /// <returns>更新执行是否成功</returns>
    Task<bool> ExecuteRemoteUpdateAsync(string downloadUrl);
}