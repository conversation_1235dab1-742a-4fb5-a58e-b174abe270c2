namespace EdgeGateway.Base.Entity.SeedData;

/// <summary>
///     角色菜单关联表种子数据
/// </summary>
[IgnoreUpdateSeed]
public class SysRoleMenuSeedData : ISqlSugarEntitySeedData<SysRoleMenu>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysRoleMenu> HasData()
    {
        var now = DateTime.Now;
        var roleMenus = new List<SysRoleMenu>();
        
        // 超级管理员角色ID
        const long superAdminRoleId = 1300000000201;
        // 系统管理员角色ID
        const long sysAdminRoleId = 1300000000202;
        // 普通用户角色ID
        const long normalUserRoleId = 1300000000203;
        // 操作员角色ID
        const long operatorRoleId = 1300000000204;
        // 观察员角色ID
        const long viewerRoleId = 1300000000205;

        // 所有菜单ID列表
        var allMenuIds = new[]
        {
            // 一级菜单
            1300000000301L, // 仪表盘
            1300000000302L, // 数据采集
            1300000000303L, // 数据转发
            1300000000304L, // 任务中心
            1300000000305L, // 数据监控
            1300000000306L, // 数据分析
            1300000000307L, // 历史数据
            1300000000308L, // 开发工具
            1300000000309L, // 系统管理
            
            // 数据采集子菜单
            1300000000401L, // 设备列表
            1300000000402L, // 设备报警
            1300000000403L, // 设备事件
            1300000000404L, // 设备分组
            1300000000405L, // 设备模板
            
            // 数据转发子菜单
            1300000000501L, // 配置管理
            1300000000502L, // 数据统计
            1300000000503L, // 离线数据
            
            // 任务中心子菜单
            1300000000601L, // 任务概览
            1300000000602L, // 定时任务
            1300000000603L, // 任务配置
            
            // 数据监控子菜单
            1300000000701L, // 监控仪表盘
            1300000000702L, // 数据库管理
            
            // 开发工具子菜单
            1300000000801L, // API管理
            1300000000802L, // 调试工具
            1300000000803L, // 脚本模块
            1300000000804L, // 组件示例
            
            // 系统管理子菜单
            1300000000901L, // 用户管理
            1300000000902L, // 角色管理
            1300000000903L, // 菜单管理
            1300000000904L  // 系统设置
        };

        // 基础菜单（所有角色都有的）
        var basicMenuIds = new[]
        {
            1300000000301L, // 仪表盘
            1300000000306L, // 数据分析
            1300000000307L  // 历史数据
        };

        // 操作菜单（操作员及以上角色）
        var operationMenuIds = new[]
        {
            1300000000302L, // 数据采集
            1300000000401L, // 设备列表
            1300000000402L, // 设备报警
            1300000000403L, // 设备事件
            1300000000404L, // 设备分组
            1300000000405L, // 设备模板
            1300000000303L, // 数据转发
            1300000000501L, // 配置管理
            1300000000502L, // 数据统计
            1300000000503L, // 离线数据
            1300000000304L, // 任务中心
            1300000000601L, // 任务概览
            1300000000602L, // 定时任务
            1300000000603L  // 任务配置
        };

        // 监控菜单（系统管理员及以上角色）
        var monitoringMenuIds = new[]
        {
            1300000000305L, // 数据监控
            1300000000701L, // 监控仪表盘
            1300000000702L  // 数据库管理
        };

        // 开发工具菜单（系统管理员及以上角色）
        var devToolsMenuIds = new[]
        {
            1300000000308L, // 开发工具
            1300000000801L, // API管理
            1300000000802L, // 调试工具
            1300000000803L, // 脚本模块
            1300000000804L  // 组件示例
        };

        // 系统管理菜单（系统管理员及以上角色）
        var systemMenuIds = new[]
        {
            1300000000309L, // 系统管理
            1300000000901L, // 用户管理
            1300000000902L, // 角色管理
            1300000000903L, // 菜单管理
            1300000000904L  // 系统设置
        };

        long id = 1300000001001;

        // 1. 超级管理员 - 拥有所有权限
        foreach (var menuId in allMenuIds)
        {
            roleMenus.Add(new SysRoleMenu
            {
                Id = id++,
                RoleId = superAdminRoleId,
                MenuId = menuId,
                CreateTime = now
            });
        }

        // 2. 系统管理员 - 拥有除开发工具外的所有权限
        var sysAdminMenuIds = basicMenuIds
            .Concat(operationMenuIds)
            .Concat(monitoringMenuIds)
            .Concat(systemMenuIds)
            .ToArray();

        foreach (var menuId in sysAdminMenuIds)
        {
            roleMenus.Add(new SysRoleMenu
            {
                Id = id++,
                RoleId = sysAdminRoleId,
                MenuId = menuId,
                CreateTime = now
            });
        }

        // 3. 普通用户 - 拥有基础和操作权限
        var normalUserMenuIds = basicMenuIds
            .Concat(operationMenuIds)
            .ToArray();

        foreach (var menuId in normalUserMenuIds)
        {
            roleMenus.Add(new SysRoleMenu
            {
                Id = id++,
                RoleId = normalUserRoleId,
                MenuId = menuId,
                CreateTime = now
            });
        }

        // 4. 操作员 - 拥有基础和部分操作权限
        var operatorMenuIds = basicMenuIds
            .Concat(new[]
            {
                1300000000302L, // 数据采集
                1300000000401L, // 设备列表
                1300000000402L, // 设备报警
                1300000000403L, // 设备事件
                1300000000303L, // 数据转发
                1300000000501L, // 配置管理
                1300000000502L  // 数据统计
            })
            .ToArray();

        foreach (var menuId in operatorMenuIds)
        {
            roleMenus.Add(new SysRoleMenu
            {
                Id = id++,
                RoleId = operatorRoleId,
                MenuId = menuId,
                CreateTime = now
            });
        }

        // 5. 观察员 - 只有基础查看权限
        foreach (var menuId in basicMenuIds)
        {
            roleMenus.Add(new SysRoleMenu
            {
                Id = id++,
                RoleId = viewerRoleId,
                MenuId = menuId,
                CreateTime = now
            });
        }

        return roleMenus;
    }
}
