namespace EdgeGateway.Base.Entity.SeedData;

/// <summary>
///     系统菜单表种子数据
/// </summary>
[IgnoreUpdateSeed]
public class SysMenuSeedData : ISqlSugarEntitySeedData<SysMenu>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysMenu> HasData()
    {
        var now = DateTime.Now;
        
        return new[]
        {
            // 一级菜单（模块）
            new SysMenu 
            { 
                Id = 1300000000301, 
                Name = "仪表盘", 
                Code = "DASHBOARD", 
                Path = "/dashboard", 
                Icon = "LayoutDashboard", 
                ParentId = null, 
                MenuType = MenuTypeEnum.Module, 
                Sort = 1, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000302, 
                Name = "数据采集", 
                Code = "DATA_COLLECTION", 
                Path = "/devices/data-collection", 
                Icon = "Server", 
                ParentId = null, 
                MenuType = MenuTypeEnum.Module, 
                Sort = 2, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000303, 
                Name = "数据转发", 
                Code = "DATA_FORWARDING", 
                Path = "/data-forwarding", 
                Icon = "Share", 
                ParentId = null, 
                MenuType = MenuTypeEnum.Module, 
                Sort = 3, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000304, 
                Name = "任务中心", 
                Code = "TASK_CENTER", 
                Path = "/task-center", 
                Icon = "CalendarClock", 
                ParentId = null, 
                MenuType = MenuTypeEnum.Module, 
                Sort = 4, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000305, 
                Name = "数据监控", 
                Code = "MONITORING", 
                Path = "/monitoring", 
                Icon = "Activity", 
                ParentId = null, 
                MenuType = MenuTypeEnum.Module, 
                Sort = 5, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000306, 
                Name = "数据分析", 
                Code = "ANALYTICS", 
                Path = "/analytics", 
                Icon = "BarChart2", 
                ParentId = null, 
                MenuType = MenuTypeEnum.Module, 
                Sort = 6, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000307, 
                Name = "历史数据", 
                Code = "DATA_HISTORY", 
                Path = "/data-history", 
                Icon = "BarChart", 
                ParentId = null, 
                MenuType = MenuTypeEnum.Module, 
                Sort = 7, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000308, 
                Name = "开发工具", 
                Code = "DEV_TOOLS", 
                Path = "/dev-tools", 
                Icon = "Code", 
                ParentId = null, 
                MenuType = MenuTypeEnum.Module, 
                Sort = 8, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000309, 
                Name = "系统管理", 
                Code = "SYSTEM_MANAGEMENT", 
                Path = "/system", 
                Icon = "Settings", 
                ParentId = null, 
                MenuType = MenuTypeEnum.Module, 
                Sort = 9, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },

            // 数据采集子菜单
            new SysMenu 
            { 
                Id = 1300000000401, 
                Name = "设备列表", 
                Code = "DEVICE_LIST", 
                Path = "/devices", 
                Icon = "List", 
                ParentId = 1300000000302, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 1, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000402, 
                Name = "设备报警", 
                Code = "DEVICE_ALARMS", 
                Path = "/devices/alarms", 
                Icon = "BellRing", 
                ParentId = 1300000000302, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 2, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000403, 
                Name = "设备事件", 
                Code = "DEVICE_EVENTS", 
                Path = "/devices/events", 
                Icon = "CalendarClock", 
                ParentId = 1300000000302, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 3, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000404, 
                Name = "设备分组", 
                Code = "DEVICE_GROUPS", 
                Path = "/devices/groups", 
                Icon = "Group", 
                ParentId = 1300000000302, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 4, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000405, 
                Name = "设备模板", 
                Code = "DEVICE_TEMPLATES", 
                Path = "/devices/templates", 
                Icon = "FileCode", 
                ParentId = 1300000000302, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 5, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },

            // 数据转发子菜单
            new SysMenu 
            { 
                Id = 1300000000501, 
                Name = "配置管理", 
                Code = "FORWARDING_CONFIG", 
                Path = "/data-forwarding", 
                Icon = "Settings", 
                ParentId = 1300000000303, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 1, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000502, 
                Name = "数据统计", 
                Code = "FORWARDING_STATISTICS", 
                Path = "/data-forwarding/statistics", 
                Icon = "TrendingUp", 
                ParentId = 1300000000303, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 2, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000503, 
                Name = "离线数据", 
                Code = "OFFLINE_DATA", 
                Path = "/data-forwarding/offline-data", 
                Icon = "HardDrive", 
                ParentId = 1300000000303, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 3, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },

            // 任务中心子菜单
            new SysMenu 
            { 
                Id = 1300000000601, 
                Name = "任务概览", 
                Code = "TASK_OVERVIEW", 
                Path = "/task-center", 
                Icon = "LayoutDashboard", 
                ParentId = 1300000000304, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 1, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000602, 
                Name = "定时任务", 
                Code = "SCHEDULED_TASKS", 
                Path = "/task-center/scheduled-tasks", 
                Icon = "Clock", 
                ParentId = 1300000000304, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 2, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000603, 
                Name = "任务配置", 
                Code = "TASK_CONFIG", 
                Path = "/task-center/task-config", 
                Icon = "Settings", 
                ParentId = 1300000000304, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 3, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },

            // 数据监控子菜单
            new SysMenu 
            { 
                Id = 1300000000701, 
                Name = "监控仪表盘", 
                Code = "MONITORING_DASHBOARD", 
                Path = "/monitoring", 
                Icon = "LayoutDashboard", 
                ParentId = 1300000000305, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 1, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000702, 
                Name = "数据库管理", 
                Code = "DATABASE_MANAGEMENT", 
                Path = "/monitoring/database", 
                Icon = "Database", 
                ParentId = 1300000000305, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 2, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },

            // 开发工具子菜单
            new SysMenu 
            { 
                Id = 1300000000801, 
                Name = "API管理", 
                Code = "API_MANAGEMENT", 
                Path = "/api-management", 
                Icon = "Code", 
                ParentId = 1300000000308, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 1, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000802, 
                Name = "调试工具", 
                Code = "DEBUG_TOOLS", 
                Path = "/debug-tools", 
                Icon = "Terminal", 
                ParentId = 1300000000308, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 2, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000803, 
                Name = "脚本模块", 
                Code = "SCRIPT_MODULES", 
                Path = "/script-modules", 
                Icon = "FileCode", 
                ParentId = 1300000000308, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 3, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000804, 
                Name = "组件示例", 
                Code = "COMPONENT_EXAMPLES", 
                Path = "/examples", 
                Icon = "BookOpen", 
                ParentId = 1300000000308, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 4, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },

            // 系统管理子菜单
            new SysMenu 
            { 
                Id = 1300000000901, 
                Name = "用户管理", 
                Code = "USER_MANAGEMENT", 
                Path = "/system/users", 
                Icon = "Users", 
                ParentId = 1300000000309, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 1, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000902, 
                Name = "角色管理", 
                Code = "ROLE_MANAGEMENT", 
                Path = "/system/roles", 
                Icon = "Shield", 
                ParentId = 1300000000309, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 2, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000903, 
                Name = "菜单管理", 
                Code = "MENU_MANAGEMENT", 
                Path = "/system/menus", 
                Icon = "Menu", 
                ParentId = 1300000000309, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 3, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            },
            new SysMenu 
            { 
                Id = 1300000000904, 
                Name = "系统设置", 
                Code = "SYSTEM_SETTINGS", 
                Path = "/system/settings", 
                Icon = "Settings", 
                ParentId = 1300000000309, 
                MenuType = MenuTypeEnum.Menu, 
                Sort = 4, 
                Status = true, 
                Hidden = false,
                CreateTime = now,
                UpdateTime = now
            }
        };
    }
}
