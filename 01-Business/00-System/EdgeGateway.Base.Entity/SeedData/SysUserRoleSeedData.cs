namespace EdgeGateway.Base.Entity.SeedData;

/// <summary>
///     用户角色关联表种子数据
/// </summary>
[IgnoreUpdateSeed]
public class SysUserRoleSeedData : ISqlSugarEntitySeedData<SysUserRole>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysUserRole> HasData()
    {
        var now = DateTime.Now;
        
        return new[]
        {
            // 为超级管理员用户分配超级管理员角色
            new SysUserRole
            {
                Id = 1300000002001,
                UserId = 1300000000101, // superAdmin用户ID
                RoleId = 1300000000201, // 超级管理员角色ID
                CreateTime = now
            },
            
            // 为系统管理员用户分配系统管理员角色
            new SysUserRole
            {
                Id = 1300000002002,
                UserId = 1300000000111, // admin用户ID
                RoleId = 1300000000202, // 系统管理员角色ID
                CreateTime = now
            }
        };
    }
}
