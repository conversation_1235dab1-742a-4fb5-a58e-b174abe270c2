namespace EdgeGateway.Base.Entity.SeedData;

/// <summary>
///     系统角色表种子数据
/// </summary>
[IgnoreUpdateSeed]
public class SysRoleSeedData : ISqlSugarEntitySeedData<SysRole>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysRole> HasData()
    {
        return new[]
        {
            new SysRole 
            { 
                Id = *************, 
                Name = "超级管理员", 
                Code = "SUPER_ADMIN", 
                Description = "系统超级管理员，拥有所有权限", 
                Status = true, 
                IsDefault = true, 
                AccountType = AccountTypeEnum.SuperAdmin, 
                Sort = 1,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            },
            new SysRole 
            { 
                Id = *************, 
                Name = "系统管理员", 
                Code = "SYS_ADMIN", 
                Description = "系统管理员，拥有系统管理权限", 
                Status = true, 
                IsDefault = true, 
                AccountType = AccountTypeEnum.SysAdmin, 
                Sort = 2,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            },
            new SysRole 
            { 
                Id = *************, 
                Name = "普通用户", 
                Code = "NORMAL_USER", 
                Description = "普通用户，拥有基本操作权限", 
                Status = true, 
                IsDefault = true, 
                AccountType = AccountTypeEnum.NormalUser, 
                Sort = 3,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            },
            new SysRole 
            { 
                Id = *************, 
                Name = "操作员", 
                Code = "OPERATOR", 
                Description = "设备操作员，拥有设备操作权限", 
                Status = true, 
                IsDefault = false, 
                AccountType = null, 
                Sort = 4,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            },
            new SysRole 
            { 
                Id = *************, 
                Name = "观察员", 
                Code = "VIEWER", 
                Description = "只读用户，仅能查看数据", 
                Status = true, 
                IsDefault = false, 
                AccountType = null, 
                Sort = 5,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            }
        };
    }
}
