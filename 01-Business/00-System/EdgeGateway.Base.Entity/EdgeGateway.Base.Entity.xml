<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EdgeGateway.Base.Entity</name>
    </assembly>
    <members>
        <member name="T:EdgeGateway.Base.Entity.Dto.ActivateInput">
            <summary>
                激活请求
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.ActivateInput.ActivationCode">
            <summary>
                激活码
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Database.DatabaseDto">
            <summary>
            数据库信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.DatabaseDto.Name">
            <summary>
            数据库名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.DatabaseDto.Size">
            <summary>
            数据库大小(MB)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.DatabaseDto.TableCount">
            <summary>
            表数量
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Database.TableDto">
            <summary>
            数据表信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.TableDto.Name">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.TableDto.Comment">
            <summary>
            表注释
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.TableDto.RowCount">
            <summary>
            记录数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.TableDto.DataSize">
            <summary>
            数据大小(MB)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.TableDto.IndexSize">
            <summary>
            索引大小(MB)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Database.ColumnDto">
            <summary>
            表字段信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.ColumnDto.Name">
            <summary>
            字段名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.ColumnDto.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.ColumnDto.Length">
            <summary>
            长度
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.ColumnDto.IsNullable">
            <summary>
            是否可空
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.ColumnDto.DefaultValue">
            <summary>
            默认值
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.ColumnDto.Comment">
            <summary>
            字段注释
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.ColumnDto.IsPrimaryKey">
            <summary>
            是否主键
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Database.TableDataInput">
            <summary>
            表数据查询输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.TableDataInput.Database">
            <summary>
            数据库名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.TableDataInput.Table">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.TableDataInput.Where">
            <summary>
            查询条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.TableDataInput.OrderBy">
            <summary>
            排序
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Database.ExecuteSqlInput">
            <summary>
            SQL执行输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.ExecuteSqlInput.Database">
            <summary>
            数据库名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.ExecuteSqlInput.Sql">
            <summary>
            SQL语句
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Database.BackupDatabaseInput">
            <summary>
            数据库备份输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.BackupDatabaseInput.Database">
            <summary>
            数据库名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Database.BackupDatabaseInput.BackupPath">
            <summary>
            备份文件路径
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.GenerateActivationCodeInput">
            <summary>
                生成激活码请求
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.GenerateActivationCodeInput.MachineCode">
            <summary>
                机器码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.GenerateActivationCodeInput.DeviceLimit">
            <summary>
                授权设备数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.GenerateActivationCodeInput.TagLimit">
            <summary>
                授权采集标签数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.GenerateActivationCodeInput.StartTime">
            <summary>
                授权开始时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.GenerateActivationCodeInput.ExpireTime">
            <summary>
                授权结束时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.GenerateActivationCodeInput.Edition">
            <summary>
                授权版本
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.GenerateActivationCodeInput.Customer">
            <summary>
                客户信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.LicenseRecordPageInput">
            <summary>
                授权记录分页查询输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.LicenseRecordPageInput.MachineCode">
            <summary>
                机器码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.LicenseRecordPageInput.IsActivated">
            <summary>
                激活状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.LicenseRecordPageInput.Edition">
            <summary>
                授权版本
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.LicenseRecordPageInput.Customer">
            <summary>
                客户信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.LicenseRecordPageInput.ActivateStartTime">
            <summary>
                激活开始时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.LicenseRecordPageInput.ActivateEndTime">
            <summary>
                激活结束时间
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Log.LogFileDto">
            <summary>
            日志文件信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogFileDto.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogFileDto.FileSize">
            <summary>
            文件大小(KB)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogFileDto.LastWriteTime">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogFileDto.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogFileDto.LineCount">
            <summary>
            文件总行数
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Log.LogFileGroupDto">
            <summary>
            日志文件分组信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogFileGroupDto.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogFileGroupDto.Files">
            <summary>
            该级别下的日志文件列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Log.LogContentInput">
            <summary>
            日志内容查询参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentInput.LogLevel">
            <summary>
            日志级别目录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentInput.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentInput.Lines">
            <summary>
            读取行数,默认1000行,最大10000行
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentInput.StartLine">
            <summary>
            起始行号(从1开始),默认为1
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentInput.InfoOnly">
            <summary>
            是否只获取文件信息(不读取内容)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Log.LogContentDto">
            <summary>
            日志内容返回结构
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentDto.Content">
            <summary>
            日志内容
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentDto.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentDto.FileInfo">
            <summary>
            文件信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentDto.TotalLines">
            <summary>
            文件总行数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentDto.StartLine">
            <summary>
            当前返回的起始行号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentDto.EndLine">
            <summary>
            当前返回的结束行号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogContentDto.HasMore">
            <summary>
            是否还有更多数据
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Log.StreamLogContentDto">
            <summary>
            流式日志内容返回结构
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.StreamLogContentDto.Content">
            <summary>
            日志内容片段
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.StreamLogContentDto.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.StreamLogContentDto.FileInfo">
            <summary>
            文件信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.StreamLogContentDto.LineNumber">
            <summary>
            当前行号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.StreamLogContentDto.TotalLinesRead">
            <summary>
            已读取的总行数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.StreamLogContentDto.LinesReturned">
            <summary>
            已返回的行数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.StreamLogContentDto.IsCompleted">
            <summary>
            是否已完成读取
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.StreamLogContentDto.Error">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Log.LogFileDeleteInput">
            <summary>
            日志文件删除输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogFileDeleteInput.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Log.LogFileDeleteInput.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.RolePageInput">
            <summary>
            角色分页查询输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RolePageInput.Page">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RolePageInput.PageSize">
            <summary>
            页大小
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RolePageInput.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RolePageInput.Code">
            <summary>
            角色编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RolePageInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RolePageInput.AccountType">
            <summary>
            账户类型
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.RoleCreateInput">
            <summary>
            角色创建输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleCreateInput.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleCreateInput.Code">
            <summary>
            角色编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleCreateInput.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleCreateInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleCreateInput.AccountType">
            <summary>
            适用账户类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleCreateInput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.RoleUpdateInput">
            <summary>
            角色更新输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleUpdateInput.Id">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleUpdateInput.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleUpdateInput.Code">
            <summary>
            角色编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleUpdateInput.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleUpdateInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleUpdateInput.AccountType">
            <summary>
            适用账户类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleUpdateInput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.MenuPageInput">
            <summary>
            菜单分页查询输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuPageInput.Page">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuPageInput.PageSize">
            <summary>
            页大小
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuPageInput.Name">
            <summary>
            菜单名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuPageInput.Code">
            <summary>
            菜单编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuPageInput.ParentId">
            <summary>
            父级菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuPageInput.MenuType">
            <summary>
            菜单类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuPageInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput">
            <summary>
            菜单创建输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput.Name">
            <summary>
            菜单名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput.Code">
            <summary>
            菜单编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput.Path">
            <summary>
            路由路径
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput.ParentId">
            <summary>
            父级菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput.MenuType">
            <summary>
            菜单类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuCreateInput.Hidden">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput">
            <summary>
            菜单更新输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.Id">
            <summary>
            菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.Name">
            <summary>
            菜单名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.Code">
            <summary>
            菜单编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.Path">
            <summary>
            路由路径
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.ParentId">
            <summary>
            父级菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.MenuType">
            <summary>
            菜单类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuUpdateInput.Hidden">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.RoleMenuAssignInput">
            <summary>
            角色菜单分配输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleMenuAssignInput.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleMenuAssignInput.MenuIds">
            <summary>
            菜单ID列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.UserPageInput">
            <summary>
            用户分页查询输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPageInput.Page">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPageInput.PageSize">
            <summary>
            页大小
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPageInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPageInput.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPageInput.AccountType">
            <summary>
            账号类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPageInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.UserCreateInput">
            <summary>
            用户创建输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserCreateInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserCreateInput.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserCreateInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserCreateInput.AccountType">
            <summary>
            账号类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserCreateInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.UserUpdateInput">
            <summary>
            用户更新输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserUpdateInput.Id">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserUpdateInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserUpdateInput.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserUpdateInput.AccountType">
            <summary>
            账号类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserUpdateInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.UserStatusInput">
            <summary>
            用户状态输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserStatusInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.UserPasswordInput">
            <summary>
            用户密码输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPasswordInput.Password">
            <summary>
            新密码
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.UserRoleAssignInput">
            <summary>
            用户角色分配输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserRoleAssignInput.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserRoleAssignInput.RoleIds">
            <summary>
            角色ID列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput">
            <summary>
            菜单树输出
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.Id">
            <summary>
            菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.Name">
            <summary>
            菜单名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.Code">
            <summary>
            菜单编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.Path">
            <summary>
            路由路径
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.ParentId">
            <summary>
            父级菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.MenuType">
            <summary>
            菜单类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.MenuTypeDesc">
            <summary>
            菜单类型描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.Hidden">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuTreeOutput.Children">
            <summary>
            子菜单列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput">
            <summary>
            角色输出
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.Id">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.Code">
            <summary>
            角色编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.IsDefault">
            <summary>
            是否默认角色
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.AccountType">
            <summary>
            适用账户类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.AccountTypeDesc">
            <summary>
            账户类型描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.RoleOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput">
            <summary>
            菜单输出
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.Id">
            <summary>
            菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.Name">
            <summary>
            菜单名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.Code">
            <summary>
            菜单编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.Path">
            <summary>
            路由路径
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.ParentId">
            <summary>
            父级菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.ParentName">
            <summary>
            父级菜单名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.MenuType">
            <summary>
            菜单类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.MenuTypeDesc">
            <summary>
            菜单类型描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.Hidden">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.MenuOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.UserPermissionOutput">
            <summary>
            用户权限输出
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPermissionOutput.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPermissionOutput.Account">
            <summary>
            用户账号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPermissionOutput.Name">
            <summary>
            用户姓名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPermissionOutput.AccountType">
            <summary>
            账户类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPermissionOutput.Roles">
            <summary>
            用户角色列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPermissionOutput.Permissions">
            <summary>
            权限代码列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserPermissionOutput.Menus">
            <summary>
            菜单树
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.Permission.UserOutput">
            <summary>
            用户输出
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserOutput.Id">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserOutput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserOutput.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserOutput.AccountType">
            <summary>
            账号类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserOutput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserOutput.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.Permission.UserOutput.LastLoginIp">
            <summary>
            最后登录IP
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Dto.UpdateConfigInput">
            <summary>
            更新配置输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.UpdateConfigInput.Section">
            <summary>
            配置节
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.UpdateConfigInput.Key">
            <summary>
            配置键
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Dto.UpdateConfigInput.Value">
            <summary>
            配置值
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Entity.DatabaseBackup">
            <summary>
            数据库备份记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackup.Database">
            <summary>
            数据库名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackup.FilePath">
            <summary>
            备份文件路径
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackup.FileSize">
            <summary>
            备份文件大小(MB)
            </summary> 
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackup.BackupType">
            <summary>
            备份类型(Manual/Auto)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackup.Status">
            <summary>
            备份状态(Success/Failed)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackup.Error">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Entity.DatabaseBackupConfig">
            <summary>
            数据库备份配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupConfig.EnableAutoBackup">
            <summary>
            是否启用自动备份
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupConfig.BackupInterval">
            <summary>
            备份周期(天)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupConfig.BackupTime">
            <summary>
            备份时间(HH:mm)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupConfig.KeepBackupCount">
            <summary>
            保留备份数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupConfig.BackupPath">
            <summary>
            备份路径
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Entity.DatabaseBackupInput">
            <summary>
            数据库备份查询输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupInput.Database">
            <summary>
            数据库名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupInput.BackupType">
            <summary>
            备份类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupInput.Status">
            <summary>
            备份状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.DatabaseBackupInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Entity.SysMenu">
            <summary>
                系统菜单表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysMenu.Name">
            <summary>
                菜单名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysMenu.Code">
            <summary>
                菜单编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysMenu.Path">
            <summary>
                路由路径
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysMenu.Icon">
            <summary>
                图标
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysMenu.ParentId">
            <summary>
                父级菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysMenu.MenuType">
            <summary>
                菜单类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysMenu.Sort">
            <summary>
                排序
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysMenu.Status">
            <summary>
                状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysMenu.Hidden">
            <summary>
                是否隐藏
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Entity.SysRole">
            <summary>
                系统角色表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRole.Name">
            <summary>
                角色名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRole.Code">
            <summary>
                角色编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRole.Description">
            <summary>
                角色描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRole.Status">
            <summary>
                状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRole.IsDefault">
            <summary>
                是否默认角色
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRole.AccountType">
            <summary>
                适用账户类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRole.Sort">
            <summary>
                排序
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Entity.SysRoleMenu">
            <summary>
                角色菜单关联表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRoleMenu.RoleId">
            <summary>
                角色ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRoleMenu.MenuId">
            <summary>
                菜单ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysRoleMenu.CreateTime">
            <summary>
                创建时间
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Entity.SysUser">
            <summary>
                系统用户表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysUser.Account">
            <summary>
                账号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysUser.Password">
            <summary>
                密码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysUser.Name">
            <summary>
                姓名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysUser.AccountType">
            <summary>
                账号类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysUser.Status">
            <summary>
                启用状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Entity.SysUserRole">
            <summary>
                用户角色关联表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysUserRole.UserId">
            <summary>
                用户ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysUserRole.RoleId">
            <summary>
                角色ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Entity.SysUserRole.CreateTime">
            <summary>
                创建时间
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.LicenseRecord">
            <summary>
                授权记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.MachineCode">
            <summary>
                机器码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.ActivationCode">
            <summary>
                激活码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.IsActivated">
            <summary>
                授权状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.DeviceLimit">
            <summary>
                授权设备数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.TagLimit">
            <summary>
                授权采集标签数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.StartTime">
            <summary>
                授权开始时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.ExpireTime">
            <summary>
                授权结束时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.Edition">
            <summary>
                授权版本
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.Customer">
            <summary>
                客户信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.ActivateTime">
            <summary>
                激活时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.Operator">
            <summary>
                操作人
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseRecord.Remark">
            <summary>
                备注
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NetworkNodeHistory">
            <summary>
            网络节点历史记录
            用于记录节点状态变化和性能指标的历史数据
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeHistory.NodeId">
            <summary>
            节点ID
            关联NetworkNodeInfo的Id
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeHistory.Status">
            <summary>
            状态
            记录节点的在线状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeHistory.Latency">
            <summary>
            延迟(ms)
            节点的响应时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeHistory.RecordTime">
            <summary>
            记录时间
            数据记录的时间戳
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NetworkAnalytics">
            <summary>
            网络分析结果
            用于统计和分析网络节点的性能指标
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkAnalytics.NodeAvailability">
            <summary>
            节点可用性统计(%)
            Key: 节点ID
            Value: 可用性百分比
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkAnalytics.AverageLatency">
            <summary>
            平均延迟统计(ms)
            Key: 节点ID
            Value: 平均响应时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkAnalytics.FailureCount">
            <summary>
            故障次数统计
            Key: 节点ID
            Value: 故障发生次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkAnalytics.StartTime">
            <summary>
            分析开始时间
            统计分析的起始时间点
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkAnalytics.EndTime">
            <summary>
            分析结束时间
            统计分析的结束时间点
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NetworkDiscoveryResult">
            <summary>
            网络发现结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryResult.DiscoveredNodes">
            <summary>
            发现的节点列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryResult.DiscoveredLinks">
            <summary>
            发现的连接列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryResult.DiscoveryTime">
            <summary>
            发现时间
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.BaseIdInput">
            <summary>
                主键Id输入参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.BaseIdInput.Id">
            <summary>
                主键Id
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.BaseIdInput`1">
            <summary>
            主键Id输入参数
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.BaseIdInput`1.Id">
            <summary>
                主键Id
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.ConfigPathRequest">
            <summary>
            配置路径请求类，用于POST请求获取配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.ConfigPathRequest.FileName">
            <summary>
            配置文件名（如app.json或system.ini）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.ConfigPathRequest.IncludeSchema">
            <summary>
            是否包含Schema信息
            </summary>
            <remarks>
            当设置为true时，返回结果将包含配置文件的Schema定义（如果存在）
            </remarks>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.LicenseInfo">
            <summary>
                授权信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseInfo.MachineCode">
            <summary>
                机器码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseInfo.ActivationCode">
            <summary>
                激活码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseInfo.IsActivated">
            <summary>
                授权状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseInfo.DeviceLimit">
            <summary>
                授权设备数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseInfo.TagLimit">
            <summary>
                授权采集标签数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseInfo.StartTime">
            <summary>
                授权开始时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseInfo.ExpireTime">
            <summary>
                授权结束时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseInfo.Edition">
            <summary>
                授权版本
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.LicenseInfo.Customer">
            <summary>
                客户信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.CaptureFilter">
            <summary>
            抓包过滤配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.CaptureFilter.InterfaceName">
            <summary>
            网络接口名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.CaptureFilter.SourceIp">
            <summary>
            源IP地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.CaptureFilter.DestinationIp">
            <summary>
            目标IP地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.CaptureFilter.SourcePort">
            <summary>
            源端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.CaptureFilter.DestinationPort">
            <summary>
            目标端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.CaptureFilter.Protocol">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.CaptureFilter.Keyword">
            <summary>
            关键字
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.CaptureFilter.MaxPackets">
            <summary>
            最大捕获包数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.CaptureFilter.CaptureTimeout">
            <summary>
            捕获超时时间(ms)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.ProtocolType">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.ProtocolType.TCP">
            <summary>
            TCP协议
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.ProtocolType.UDP">
            <summary>
            UDP协议
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.ProtocolType.ICMP">
            <summary>
            ICMP协议
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.ProtocolType.HTTP">
            <summary>
            HTTP协议
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.ProtocolType.HTTPS">
            <summary>
            HTTPS协议
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.PacketInfo">
            <summary>
            数据包信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.Id">
            <summary>
            数据包ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.SourceIp">
            <summary>
            源IP地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.DestinationIp">
            <summary>
            目标IP地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.SourcePort">
            <summary>
            源端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.DestinationPort">
            <summary>
            目标端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.Protocol">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.Length">
            <summary>
            数据包长度
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.HexData">
            <summary>
            数据内容(HEX)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.AsciiData">
            <summary>
            数据内容(ASCII)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PacketInfo.AdditionalInfo">
            <summary>
            附加信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig">
            <summary>
            网络发现配置类
            用于配置网络拓扑发现的相关参数和选项
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig.EnableAutoDiscovery">
            <summary>
            是否启用自动发现
            当设置为true时,系统会定期自动扫描网络发现新设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig.DiscoveryInterval">
            <summary>
            自动发现间隔(毫秒)
            两次自动发现之间的时间间隔,默认5分钟
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig.ScanTimeout">
            <summary>
            扫描超时时间(毫秒)
            单个设备扫描的最大等待时间,超过此时间视为超时
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig.ConcurrentScans">
            <summary>
            并发扫描数
            同时进行扫描的最大线程数,建议根据系统性能调整
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig.ScanRanges">
            <summary>
            要扫描的IP地址范围
            格式: ["***********/24", "10.0.0.0/16"]
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig.EnableHistoryLogging">
            <summary>
            是否记录历史数据
            当设置为true时,系统会保存节点状态变化的历史记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig.HistoryRetentionDays">
            <summary>
            历史数据保留天数
            超过此天数的历史数据会被自动清理
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig.EnableStatusAlerts">
            <summary>
            是否启用状态告警
            当设置为true时,节点状态变化会触发告警
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkDiscoveryConfig.LatencyAlertThreshold">
            <summary>
            延迟告警阈值(毫秒)
            节点响应时间超过此值时触发告警
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.HttpRequestConfig">
            <summary>
            HTTP请求配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestConfig.Url">
            <summary>
            请求URL
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestConfig.Method">
            <summary>
            请求方法
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestConfig.Headers">
            <summary>
            请求头
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestConfig.Parameters">
            <summary>
            请求参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestConfig.Body">
            <summary>
            请求体
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestConfig.ContentType">
            <summary>
            内容类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestConfig.Timeout">
            <summary>
            超时时间(ms)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestConfig.AllowRedirect">
            <summary>
            是否跟随重定向
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.HttpResponseInfo">
            <summary>
            HTTP响应信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpResponseInfo.StatusCode">
            <summary>
            状态码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpResponseInfo.Headers">
            <summary>
            响应头
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpResponseInfo.Body">
            <summary>
            响应体
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpResponseInfo.ResponseTime">
            <summary>
            响应时间(ms)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpResponseInfo.Error">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.HttpRequestLog">
            <summary>
            HTTP请求记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestLog.Request">
            <summary>
            请求配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestLog.Response">
            <summary>
            响应信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.HttpRequestLog.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.HttpMethod">
            <summary>
            HTTP方法
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.HttpMethod.GET">
            <summary>
            GET
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.HttpMethod.POST">
            <summary>
            POST
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.HttpMethod.PUT">
            <summary>
            PUT
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.HttpMethod.DELETE">
            <summary>
            DELETE
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.HttpMethod.PATCH">
            <summary>
            PATCH
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.HttpMethod.HEAD">
            <summary>
            HEAD
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.HttpMethod.OPTIONS">
            <summary>
            OPTIONS
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.MqttClientConfig">
            <summary>
            MQTT客户端配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttClientConfig.Host">
            <summary>
            服务器地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttClientConfig.Port">
            <summary>
            服务器端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttClientConfig.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttClientConfig.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttClientConfig.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttClientConfig.UseTls">
            <summary>
            是否使用TLS
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttClientConfig.Timeout">
            <summary>
            连接超时时间(秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttClientConfig.KeepAlive">
            <summary>
            保持连接时间(秒)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.MqttMessage">
            <summary>
            MQTT消息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessage.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessage.Payload">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessage.Qos">
            <summary>
            服务质量等级
            0: 至多一次
            1: 至少一次
            2: 恰好一次
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessage.Retain">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.MqttMessageLog">
            <summary>
            MQTT消息记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessageLog.Type">
            <summary>
            消息类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessageLog.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessageLog.Payload">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessageLog.Qos">
            <summary>
            服务质量等级
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessageLog.Retain">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.MqttMessageLog.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.PingRequest">
            <summary>
            Ping请求参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PingRequest.Targets">
            <summary>
            目标主机列表
            IP地址或域名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PingRequest.Timeout">
            <summary>
            超时时间(毫秒)
            默认1000ms
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PingRequest.Ttl">
            <summary>
            TTL值
            默认64
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.PingResult">
            <summary>
            Ping结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PingResult.Target">
            <summary>
            目标主机
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PingResult.ResponseTime">
            <summary>
            响应时间(ms)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PingResult.Ttl">
            <summary>
            TTL值
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PingResult.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.PingStatus">
            <summary>
            Ping状态
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.PingStatus.Success">
            <summary>
            成功
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.PingStatus.Failed">
            <summary>
            失败
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.PortScanRequest">
            <summary>
            端口扫描请求
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanRequest.Target">
            <summary>
            目标主机
            IP地址或域名
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanRequest.StartPort">
            <summary>
            起始端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanRequest.EndPort">
            <summary>
            结束端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanRequest.Timeout">
            <summary>
            超时时间(毫秒)
            默认1000ms
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanRequest.ConcurrentScans">
            <summary>
            并发扫描数
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.PortScanResult">
            <summary>
            端口检测结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanResult.Target">
            <summary>
            目标主机
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanResult.Port">
            <summary>
            端口号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanResult.ResponseTime">
            <summary>
            响应时间(ms)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanResult.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.PortScanResult.Service">
            <summary>
            可能的服务
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.PortStatus">
            <summary>
            端口状态
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.PortStatus.Open">
            <summary>
            开放
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.PortStatus.Closed">
            <summary>
            关闭
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.TcpMessageType">
            <summary>
            TCP消息类型
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.TcpMessageType.Send">
            <summary>
            发送
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.TcpMessageType.Receive">
            <summary>
            接收
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.TcpMessageLog">
            <summary>
            TCP消息记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpMessageLog.Type">
            <summary>
            消息类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpMessageLog.Content">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpMessageLog.Format">
            <summary>
            消息格式
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpMessageLog.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.TcpClientConfig">
            <summary>
            TCP连接配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientConfig.Host">
            <summary>
            目标主机
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientConfig.Port">
            <summary>
            目标端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientConfig.ConnectTimeout">
            <summary>
            连接超时时间(ms)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientConfig.ReceiveTimeout">
            <summary>
            接收超时时间(ms)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientConfig.SendTimeout">
            <summary>
            发送超时时间(ms)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.TcpServerConfig">
            <summary>
            TCP服务器配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpServerConfig.Port">
            <summary>
            监听端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpServerConfig.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpServerConfig.ReceiveBufferSize">
            <summary>
            接收缓冲区大小
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.TcpClientInfo">
            <summary>
            TCP客户端连接信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientInfo.Id">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientInfo.RemoteAddress">
            <summary>
            远程地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientInfo.RemotePort">
            <summary>
            远程端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientInfo.ConnectedTime">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientInfo.LastActiveTime">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientInfo.ReceivedBytes">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpClientInfo.SentBytes">
            <summary>
            发送字节数
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.TcpServerEventLog">
            <summary>
            TCP服务器事件记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpServerEventLog.Type">
            <summary>
            事件类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpServerEventLog.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpServerEventLog.Description">
            <summary>
            事件描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.TcpServerEventLog.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.TcpServerEventType">
            <summary>
            TCP服务器事件类型
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.TcpServerEventType.ServerStarted">
            <summary>
            服务器启动
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.TcpServerEventType.ServerStopped">
            <summary>
            服务器停止
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.TcpServerEventType.ClientConnected">
            <summary>
            客户端连接
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.TcpServerEventType.ClientDisconnected">
            <summary>
            客户端断开
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.TcpServerEventType.MessageReceived">
            <summary>
            消息接收
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.TcpServerEventType.MessageSent">
            <summary>
            消息发送
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.TcpServerEventType.Error">
            <summary>
            错误
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NetworkTopology">
            <summary>
            网络拓扑信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkTopology.Nodes">
            <summary>
            网络节点列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkTopology.Links">
            <summary>
            网络连接列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NetworkNodeInfo">
            <summary>
            网络节点信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.Id">
            <summary>
            节点ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.Name">
            <summary>
            节点名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.MacAddress">
            <summary>
            MAC地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.Type">
            <summary>
            节点类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.Status">
            <summary>
            节点状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.LastStatus">
            <summary>
            上一次状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.Latency">
            <summary>
            延迟(ms)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.LastChecked">
            <summary>
            最后检查时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.IsManual">
            <summary>
            手动添加标记
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkNodeInfo.Properties">
            <summary>
            附加属性
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NetworkLinkInfo">
            <summary>
            网络连接信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkLinkInfo.Id">
            <summary>
            连接ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkLinkInfo.SourceId">
            <summary>
            源节点ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkLinkInfo.TargetId">
            <summary>
            目标节点ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkLinkInfo.Type">
            <summary>
            连接类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkLinkInfo.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkLinkInfo.Bandwidth">
            <summary>
            带宽(Mbps)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NodeType">
            <summary>
            节点类型
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.NodeType.Gateway">
            <summary>
            网关
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.NodeType.Router">
            <summary>
            路由器
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.NodeType.Switch">
            <summary>
            交换机
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.NodeType.Host">
            <summary>
            主机
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.NodeType.Other">
            <summary>
            其他
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.NodeType.Local">
            <summary>
                本地
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NodeStatus">
            <summary>
            节点状态
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.NodeStatus.Online">
            <summary>
            在线
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.NodeStatus.Offline">
            <summary>
            离线
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.NodeStatus.Unknown">
            <summary>
            未知
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.LinkType">
            <summary>
            连接类型
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.LinkType.Ethernet">
            <summary>
            以太网
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.LinkType.Wifi">
            <summary>
            无线
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.LinkType.Other">
            <summary>
            其他
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.LinkStatus">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.LinkStatus.Active">
            <summary>
            活动
            表示连接正常且性能良好
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.LinkStatus.Unstable">
            <summary>
            不稳定
            表示连接存在但性能不佳
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.LinkStatus.Inactive">
            <summary>
            非活动
            表示连接断开
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.LinkStatus.Unknown">
            <summary>
            未知
            表示无法确定连接状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.UdpClientConfig">
            <summary>
            UDP客户端配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.UdpClientConfig.Host">
            <summary>
            目标主机
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.UdpClientConfig.Port">
            <summary>
            目标端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.UdpClientConfig.LocalPort">
            <summary>
            本地端口
            0表示随机端口
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.UdpClientConfig.ReceiveTimeout">
            <summary>
            接收超时时间(ms)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.UdpClientConfig.SendTimeout">
            <summary>
            发送超时时间(ms)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.UdpMessageLog">
            <summary>
            UDP消息记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.UdpMessageLog.RemoteAddress">
            <summary>
            远程地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.UdpMessageLog.RemotePort">
            <summary>
            远程端口
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.SaveConfigRequest">
            <summary>
            保存配置请求类，用于POST请求保存配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SaveConfigRequest.FileName">
            <summary>
            配置文件名（如app.json或system.ini）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SaveConfigRequest.Content">
            <summary>
            要保存的配置内容
            - 对于JSON文件：任意有效的JSON内容
            - 对于INI文件：必须包含Key和Value属性的对象
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.SerialPortConfig">
            <summary>
            串口配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialPortConfig.PortName">
            <summary>
            端口名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialPortConfig.BaudRate">
            <summary>
            波特率
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialPortConfig.DataBits">
            <summary>
            数据位
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialPortConfig.StopBits">
            <summary>
            停止位
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialPortConfig.Parity">
            <summary>
            校验位
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialPortConfig.Handshake">
            <summary>
            流控制
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.SerialMessage">
            <summary>
            串口消息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialMessage.Content">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialMessage.Format">
            <summary>
            数据格式
            ASCII/HEX
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialMessage.Append">
            <summary>
            追加字符
            None/CR/LF/CRLF
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.SerialMessageLog">
            <summary>
            串口消息记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialMessageLog.Type">
            <summary>
            消息类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialMessageLog.Content">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialMessageLog.Format">
            <summary>
            数据格式
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SerialMessageLog.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.MessageType">
            <summary>
            消息类型
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.MessageType.Send">
            <summary>
            发送
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Model.MessageType.Receive">
            <summary>
            接收
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.SystemDetailedInfo">
            <summary>
            系统详细信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemDetailedInfo.BasicInfo">
            <summary>
            基本系统信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemDetailedInfo.NetworkInfos">
            <summary>
            网络信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemDetailedInfo.DiskInfos">
            <summary>
            磁盘信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.SystemBasicInfo">
            <summary>
            系统基本信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.SystemUptime">
            <summary>
            系统运行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.ApplicationUptime">
            <summary>
            应用程序运行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.CpuUsage">
            <summary>
            CPU使用率
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.MemoryUsage">
            <summary>
            内存使用率
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.TotalMemory">
            <summary>
            内存总量（MB）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.UsedMemory">
            <summary>
            已使用内存（MB）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.ThreadCount">
            <summary>
            当前进程线程数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.SystemVersion">
            <summary>
            系统版本
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.OsName">
            <summary>
            操作系统名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.ProcessorName">
            <summary>
            处理器名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemBasicInfo.ProcessorCount">
            <summary>
            处理器核心数
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.NetworkInfo">
            <summary>
            网络信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.Name">
            <summary>
            网卡名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.Description">
            <summary>
            网卡描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.MacAddress">
            <summary>
            MAC地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.IsConnected">
            <summary>
            是否连接
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.IpAddresses">
            <summary>
            IP地址列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.SubnetMasks">
            <summary>
            子网掩码列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.GatewayAddresses">
            <summary>
            网关地址列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.DnsAddresses">
            <summary>
            DNS服务器地址列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.UploadSpeed">
            <summary>
            上传速率（KB/s）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.DownloadSpeed">
            <summary>
            下载速率（KB/s）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.UploadUtilization">
            <summary>
            上传带宽利用率(%)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.DownloadUtilization">
            <summary>
            下载带宽利用率(%)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.TotalSentMB">
            <summary>
            累计发送数据量(MB)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.NetworkInfo.TotalReceivedGB">
            <summary>
            累计接收数据量(GB)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.DiskInfo">
            <summary>
            磁盘信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.DiskInfo.Name">
            <summary>
            磁盘名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.DiskInfo.DriveType">
            <summary>
            磁盘类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.DiskInfo.FileSystem">
            <summary>
            文件系统格式
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.DiskInfo.TotalSizeGB">
            <summary>
            磁盘总容量（GB）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.DiskInfo.AvailableFreeSpaceGB">
            <summary>
            可用空间（GB）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.DiskInfo.UsedSpaceGB">
            <summary>
            已用空间（GB）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.DiskInfo.UsagePercentage">
            <summary>
            使用率(%)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.SystemSetting">
            <summary>
            系统设置
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemSetting.SystemName">
            <summary>
            系统名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemSetting.Description">
            <summary>
            系统描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemSetting.LogoUrl">
            <summary>
            系统Logo
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemSetting.FaviconUrl">
            <summary>
            网站图标
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemSetting.Copyright">
            <summary>
            版权信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemSetting.IcpNumber">
            <summary>
            ICP备案号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemSetting.AutoUpdate">
            <summary>
            是否自动更新
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.SystemStatistics">
            <summary>
                系统统计数据
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Model.SystemStatistics.TimeSeriesDataPoint">
            <summary>
            表示一个带时间戳的数据点
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.TimeSeriesDataPoint.Value">
            <summary>
            数据值
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.TimeSeriesDataPoint.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.TotalOfflineCount">
            <summary>
            总离线消息数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.TotalSendCount">
            <summary>
            总发送消息数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.TotalReceiveCount">
            <summary>
            总接收消息数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.TotalFailCount">
            <summary>
            总失败消息数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.SystemUptime">
            <summary>
                系统开机时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.ApplicationUptime">
            <summary>
                程序运行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.DateTime">
            <summary>
                当前时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.CpuUsage">
            <summary>
                CPU使用率
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.MemoryUsage">
            <summary>
                内存使用率
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.UploadSpeed">
            <summary>
                网络上传速率（KB/s）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.DownloadSpeed">
            <summary>
                网络下载速率（KB/s）
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.MachineCode">
            <summary>
                机器唯一标识码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.SystemVersion">
            <summary>
                系统版本号
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.UploadUtilization">
            <summary>
            上传带宽利用率(%)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.DownloadUtilization">
            <summary>
            下载带宽利用率(%)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.DeviceCount">
            <summary>
            设备总数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.LabelCount">
            <summary>
            标签总数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.MessageSendCount">
            <summary>
                发送消息总数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.MessageFailCount">
            <summary>
                失败消息总数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.MessageReceiveCount">
            <summary>
                接收消息总数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.MessageQueuedCount">
            <summary>
                队列消息总数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.OnlineDeviceCount">
            <summary>
            在线设备数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.OfflineDeviceCount">
            <summary>
            离线设备数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.DisabledDeviceCount">
            <summary>
            禁用设备数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.UploadSpeedHistory">
            <summary>
                上传历史数据队列
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.DownloadSpeedHistory">
            <summary>
                下载历史数据队列
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.CpuUsageHistory">
            <summary>
                Cpu历史数据队列
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.MemoryUsageHistory">
            <summary>
                内存历史数据队列
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.DiskUsageHistory">
            <summary>
                磁盘历史数据队列
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.TotalSentMB">
            <summary>
                累计发送数据量(MB)
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.Model.SystemStatistics.TotalReceivedGB">
            <summary>
                累计接收数据量(GB)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Enums.DeviceStatusTypeEnum">
            <summary>
                采集设备整体状态
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.DeviceStatusTypeEnum.Good">
            <summary>
                Good
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.DeviceStatusTypeEnum.PartGood">
            <summary>
                PartGood
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.DeviceStatusTypeEnum.Bad">
            <summary>
                Bad
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Enums.SendTypeEnum">
            <summary>
                数据上报方式
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.SendTypeEnum.Always">
            <summary>
                总是上报
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.SendTypeEnum.Never">
            <summary>
                永不上报
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.SendTypeEnum.Changed">
            <summary>
                变化上报
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.SendTypeEnum.Timed">
            <summary>
                定时上报
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.Enums.ValueSourceEnum">
            <summary>
                数据来源
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.ValueSourceEnum.Read">
            <summary>
                设备读取
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.ValueSourceEnum.Calculate">
            <summary>
                统计计算
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.ValueSourceEnum.Static">
            <summary>
                自然数据
            </summary>
        </member>
        <member name="F:EdgeGateway.Base.Entity.Enums.ValueSourceEnum.Fictitious">
            <summary>
                虚拟属性
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.BaseFilter">
            <summary>
                过滤条件基类
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.BaseFilter.Keyword">
            <summary>
                模糊查询关键字
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.BasePageInput">
            <summary>
                全局分页查询输入参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.BasePageInput.Page">
            <summary>
                当前页码
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.BasePageInput.PageSize">
            <summary>
                页码容量
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.BasePageInput.Field">
            <summary>
                排序字段
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.BasePageInput.Order">
            <summary>
                排序方向
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.BasePageInput.DescStr">
            <summary>
                降序排序
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.UploadImageRequest">
            <summary>
            上传图片请求模型
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.UploadImageRequest.File">
            <summary>
            图片文件
            </summary>
        </member>
        <member name="P:EdgeGateway.Base.Entity.UploadImageRequest.Type">
            <summary>
            图片类型(logo/favicon)
            </summary>
        </member>
        <member name="T:EdgeGateway.Base.Entity.SeedData.SysMenuSeedData">
            <summary>
                系统菜单表种子数据
            </summary>
        </member>
        <member name="M:EdgeGateway.Base.Entity.SeedData.SysMenuSeedData.HasData">
            <summary>
                种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:EdgeGateway.Base.Entity.SeedData.SysRoleMenuSeedData">
            <summary>
                角色菜单关联表种子数据
            </summary>
        </member>
        <member name="M:EdgeGateway.Base.Entity.SeedData.SysRoleMenuSeedData.HasData">
            <summary>
                种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:EdgeGateway.Base.Entity.SeedData.SysRoleSeedData">
            <summary>
                系统角色表种子数据
            </summary>
        </member>
        <member name="M:EdgeGateway.Base.Entity.SeedData.SysRoleSeedData.HasData">
            <summary>
                种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:EdgeGateway.Base.Entity.SeedData.SysUserRoleSeedData">
            <summary>
                用户角色关联表种子数据
            </summary>
        </member>
        <member name="M:EdgeGateway.Base.Entity.SeedData.SysUserRoleSeedData.HasData">
            <summary>
                种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:EdgeGateway.Base.Entity.SeedData.SysUserSeedData">
            <summary>
                系统用户表种子数据
            </summary>
        </member>
        <member name="M:EdgeGateway.Base.Entity.SeedData.SysUserSeedData.HasData">
            <summary>
                种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="P:CheckUpdateOutput.HasUpdate">
            <summary>
            是否有更新
            </summary>
        </member>
        <member name="P:CheckUpdateOutput.LatestVersion">
            <summary>
            最新版本信息
            </summary>
        </member>
        <member name="T:BroadcastMessageInput">
            <summary>
            广播消息输入
            </summary>
        </member>
        <member name="P:BroadcastMessageInput.Message">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:BroadcastMessageInput.EndWith">
            <summary>
            结尾拼接 none , 或者 n r  rn crc16
            </summary>
        </member>
        <member name="T:SendToClientInput">
            <summary>
            发送消息到指定客户端输入
            </summary>
        </member>
        <member name="P:SendToClientInput.ClientIndex">
            <summary>
            客户端索引
            </summary>
        </member>
        <member name="T:ForwardStatisticsEvent">
            <summary>
            转发统计事件
            </summary>
        </member>
        <member name="P:ForwardStatisticsEvent.SendCount">
            <summary>
            发送成功消息数
            </summary>
        </member>
        <member name="P:ForwardStatisticsEvent.ReceiveCount">
            <summary>
            接收消息数
            </summary>
        </member>
        <member name="P:ForwardStatisticsEvent.FailCount">
            <summary>
            发送失败消息数
            </summary>
        </member>
        <member name="P:ForwardStatisticsEvent.QueuedMessageCount">
            <summary>
            队列中的消息数
            </summary>
        </member>
        <member name="T:NetworkInterfaceConfig">
            <summary>
            网络接口配置
            </summary>
        </member>
        <member name="P:NetworkInterfaceConfig.Name">
            <summary>
            接口名称
            </summary>
        </member>
        <member name="P:NetworkInterfaceConfig.Description">
            <summary>
            接口描述
            </summary>
        </member>
        <member name="P:NetworkInterfaceConfig.Type">
            <summary>
            接口类型
            </summary>
        </member>
        <member name="P:NetworkInterfaceConfig.Status">
            <summary>
            接口状态
            </summary>
        </member>
        <member name="P:NetworkInterfaceConfig.MacAddress">
            <summary>
            MAC地址
            </summary>
        </member>
        <member name="P:NetworkInterfaceConfig.IpConfigs">
            <summary>
            IP配置列表
            </summary>
        </member>
        <member name="P:NetworkInterfaceConfig.DnsServers">
            <summary>
            DNS服务器列表
            </summary>
        </member>
        <member name="P:NetworkInterfaceConfig.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:IpConfig">
            <summary>
            IP配置
            </summary>
        </member>
        <member name="P:IpConfig.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:IpConfig.SubnetMask">
            <summary>
            子网掩码
            </summary>
        </member>
        <member name="P:IpConfig.Gateway">
            <summary>
            网关
            </summary>
        </member>
        <member name="P:IpConfig.Metric">
            <summary>
            路由优先级（数值越小优先级越高）
            </summary>
        </member>
        <member name="T:DnsConfig">
            <summary>
            DNS配置
            </summary>
        </member>
        <member name="P:DnsConfig.DnsServers">
            <summary>
            DNS服务器列表
            </summary>
        </member>
        <member name="P:UpdatePackage.Version">
            <summary>
            更新包版本号
            </summary>
        </member>
        <member name="P:UpdatePackage.Description">
            <summary>
            更新包描述
            </summary>
        </member>
        <member name="P:UpdatePackage.Type">
            <summary>
            更新包类型：前端/后端/全量
            </summary>
        </member>
        <member name="P:UpdatePackage.Size">
            <summary>
            更新包大小（字节）
            </summary>
        </member>
        <member name="P:UpdatePackage.Md5">
            <summary>
            更新包MD5校验值
            </summary>
        </member>
        <member name="P:UpdatePackage.DownloadUrl">
            <summary>
            远程下载地址（如果有）
            </summary>
        </member>
        <member name="P:UpdatePackage.PublishTime">
            <summary>
            发布时间
            </summary>
        </member>
    </members>
</doc>
