namespace EdgeGateway.Base.Entity.Entity;

/// <summary>
///     用户角色关联表
/// </summary>
[SugarTable("sys_user_role", "用户角色关联表")]
public class SysUserRole : EntityBaseId
{
    /// <summary>
    ///     用户ID
    /// </summary>
    [SugarColumn(ColumnDescription = "用户ID")]
    public long UserId { get; set; }

    /// <summary>
    ///     角色ID
    /// </summary>
    [SugarColumn(ColumnDescription = "角色ID")]
    public long RoleId { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; } = DateTime.Now;
}
