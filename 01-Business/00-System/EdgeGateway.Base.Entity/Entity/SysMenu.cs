namespace EdgeGateway.Base.Entity.Entity;

/// <summary>
///     系统菜单表
/// </summary>
[SugarTable("sys_menu", "系统菜单表")]
public class SysMenu : EntityBase
{
    /// <summary>
    ///     菜单名称
    /// </summary>
    [SugarColumn(ColumnDescription = "菜单名称", Length = 50)]
    [Required]
    [MaxLength(50)]
    public string Name { get; set; }

    /// <summary>
    ///     菜单编码
    /// </summary>
    [SugarColumn(ColumnDescription = "菜单编码", Length = 50)]
    [Required]
    [MaxLength(50)]
    public string Code { get; set; }

    /// <summary>
    ///     路由路径
    /// </summary>
    [SugarColumn(ColumnDescription = "路由路径", Length = 200)]
    [MaxLength(200)]
    public string? Path { get; set; }

    /// <summary>
    ///     图标
    /// </summary>
    [SugarColumn(ColumnDescription = "图标", Length = 50)]
    [MaxLength(50)]
    public string? Icon { get; set; }

    /// <summary>
    ///     父级菜单ID
    /// </summary>
    [SugarColumn(ColumnDescription = "父级菜单ID")]
    public long? ParentId { get; set; }

    /// <summary>
    ///     菜单类型
    /// </summary>
    [SugarColumn(ColumnDescription = "菜单类型")]
    public MenuTypeEnum MenuType { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int Sort { get; set; } = 0;

    /// <summary>
    ///     状态
    /// </summary>
    [SugarColumn(ColumnDescription = "状态")]
    public bool Status { get; set; } = true;

    /// <summary>
    ///     是否隐藏
    /// </summary>
    [SugarColumn(ColumnDescription = "是否隐藏")]
    public bool Hidden { get; set; } = false;
}
