namespace EdgeGateway.Base.Entity.Model;

/// <summary>
///     授权记录
/// </summary>
[SugarTable("license_record", "授权记录")]
public class LicenseRecord : EntityBaseId
{
    /// <summary>
    ///     机器码
    /// </summary>
    [SugarColumn(ColumnDescription = "机器码", Length = 32)]
    public virtual string MachineCode { get; set; }

    /// <summary>
    ///     激活码
    /// </summary>
    [SugarColumn(ColumnDescription = "激活码", Length = 512)]
    public virtual string ActivationCode { get; set; }

    /// <summary>
    ///     授权状态
    /// </summary>
    [SugarColumn(ColumnDescription = "授权状态")]
    public bool IsActivated { get; set; }

    /// <summary>
    ///     授权设备数量
    /// </summary>
    [SugarColumn(ColumnDescription = "授权设备数量")]
    public int DeviceLimit { get; set; }

    /// <summary>
    ///     授权采集标签数量
    /// </summary>
    [SugarColumn(ColumnDescription = "授权采集标签数量")]
    public int TagLimit { get; set; }

    /// <summary>
    ///     授权开始时间
    /// </summary>
    [SugarColumn(ColumnDescription = "授权开始时间")]
    public DateTime StartTime { get; set; }

    /// <summary>
    ///     授权结束时间
    /// </summary>
    [SugarColumn(ColumnDescription = "授权结束时间")]
    public DateTime ExpireTime { get; set; }

    /// <summary>
    ///     授权版本
    /// </summary>
    [SugarColumn(ColumnDescription = "授权版本")]
    public string Edition { get; set; }

    /// <summary>
    ///     客户信息
    /// </summary>
    [SugarColumn(ColumnDescription = "客户信息", Length = 512)]
    public string Customer { get; set; }

    /// <summary>
    ///     激活时间
    /// </summary>
    [SugarColumn(ColumnDescription = "激活时间")]
    public DateTime ActivateTime { get; set; }

    /// <summary>
    ///     操作人
    /// </summary>
    [SugarColumn(ColumnDescription = "操作人", Length = 32)]
    public string Operator { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 512)]
    public string Remark { get; set; }
}
