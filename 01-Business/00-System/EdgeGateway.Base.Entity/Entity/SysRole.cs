namespace EdgeGateway.Base.Entity.Entity;

/// <summary>
///     系统角色表
/// </summary>
[SugarTable("sys_role", "系统角色表")]
public class SysRole : EntityBase
{
    /// <summary>
    ///     角色名称
    /// </summary>
    [SugarColumn(ColumnDescription = "角色名称", Length = 50)]
    [Required]
    [MaxLength(50)]
    public string Name { get; set; }

    /// <summary>
    ///     角色编码
    /// </summary>
    [SugarColumn(ColumnDescription = "角色编码", Length = 50)]
    [Required]
    [MaxLength(50)]
    public string Code { get; set; }

    /// <summary>
    ///     角色描述
    /// </summary>
    [SugarColumn(ColumnDescription = "角色描述", Length = 200)]
    [MaxLength(200)]
    public string? Description { get; set; }

    /// <summary>
    ///     状态
    /// </summary>
    [SugarColumn(ColumnDescription = "状态")]
    public bool Status { get; set; } = true;

    /// <summary>
    ///     是否默认角色
    /// </summary>
    [SugarColumn(ColumnDescription = "是否默认角色")]
    public bool IsDefault { get; set; } = false;

    /// <summary>
    ///     适用账户类型
    /// </summary>
    [SugarColumn(ColumnDescription = "适用账户类型")]
    public AccountTypeEnum? AccountType { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int Sort { get; set; } = 0;
}
