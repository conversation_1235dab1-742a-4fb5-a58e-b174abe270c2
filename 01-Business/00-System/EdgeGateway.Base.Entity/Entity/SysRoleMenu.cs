namespace EdgeGateway.Base.Entity.Entity;

/// <summary>
///     角色菜单关联表
/// </summary>
[SugarTable("sys_role_menu", "角色菜单关联表")]
public class SysRoleMenu : EntityBaseId
{
    /// <summary>
    ///     角色ID
    /// </summary>
    [SugarColumn(ColumnDescription = "角色ID")]
    public long RoleId { get; set; }

    /// <summary>
    ///     菜单ID
    /// </summary>
    [SugarColumn(ColumnDescription = "菜单ID")]
    public long MenuId { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; } = DateTime.Now;
}
