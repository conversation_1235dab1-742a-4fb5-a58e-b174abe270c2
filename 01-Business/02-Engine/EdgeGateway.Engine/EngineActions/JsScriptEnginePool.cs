using Microsoft.Extensions.Logging;
using EdgeGateway.Engine.EngineActions;
using Jint;
using EdgeGateway.Engine.Extensions;

/// <summary>
/// JavaScript脚本引擎对象池
/// 用于管理和复用JsScriptEngine实例，避免频繁创建和销毁实例，提高性能
/// 
/// 使用示例:
/// <code>
/// // 1. 在Startup.cs中注册服务
/// services.AddSingleton&lt;JsScriptEnginePool&gt;();
/// 
/// // 2. 在需要执行脚本的服务中注入
/// public class YourService 
/// {
///     private readonly JsScriptEnginePool _enginePool;
///     
///     public YourService(JsScriptEnginePool enginePool)
///     {
///         _enginePool = enginePool;
///     }
///     
///     // 3. 执行脚本
///     public async Task&lt;object&gt; ExecuteYourScript(string script, object data)
///     {
///         return await _enginePool.ExecuteScriptAsync(script, data);
///     }
/// }
/// </code>
/// </summary>
public class JsScriptEnginePool : ISingleton
{
    /// <summary>
    /// 存储JavaScript引擎实例的线程安全队列
    /// </summary>
    private readonly ConcurrentQueue<JsScriptEngine> _pool;

    /// <summary>
    /// 对象池最大容量
    /// </summary>
    private readonly int _maxSize;

    /// <summary>
    /// 用于创建JsScriptEngine实例的服务提供者
    /// </summary>
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// 用于控制并发访问的信号量
    /// </summary>
    private readonly SemaphoreSlim _semaphore;

    /// <summary>
    /// 用于记录日志的Logger
    /// </summary>
    private readonly ILogger<JsScriptEnginePool> _logger;

    /// <summary>
    /// 初始化JavaScript引擎对象池
    /// </summary>
    /// <param name="serviceProvider">服务提供者，用于创建JsScriptEngine实例</param>
    /// <param name="logger">用于记录日志的Logger</param>
    /// <param name="maxSize">对象池最大容量，默认为10</param>
    public JsScriptEnginePool(
        IServiceProvider serviceProvider,
        ILogger<JsScriptEnginePool> logger,
        int maxSize = 20)
    {
        _pool = new ConcurrentQueue<JsScriptEngine>();
        _maxSize = maxSize;
        _serviceProvider = serviceProvider;
        _logger = logger;

        // 根据CPU核心数调整信号量
        _semaphore = new SemaphoreSlim(
            Math.Min(maxSize, Environment.ProcessorCount * 2),
            maxSize
        );

        // 预热对象池
        PrewarmPool();
    }

    /// <summary>
    /// 预热对象池
    /// </summary>
    private void PrewarmPool()
    {
        // 根据CPU核心数调整预热数量
        var initialCount = Math.Min(_maxSize, Environment.ProcessorCount);
        // 遍历预热
        for (int i = 0; i < initialCount; i++)
        {
            // 创建引擎并添加到池
            _pool.Enqueue(CreateEngine());
        }
    }

    /// <summary>
    /// 创建JavaScript引擎实例
    /// </summary>
    /// <returns>JavaScript引擎实例</returns>
    private JsScriptEngine CreateEngine()
    {
        // 创建JavaScript引擎实例
        return ActivatorUtilities.CreateInstance<JsScriptEngine>(_serviceProvider);
    }

    /// <summary>
    /// 从对象池中获取一个JavaScript引擎实例
    /// 如果池中没有可用实例，则创建新实例
    /// </summary>
    /// <returns>JavaScript引擎实例</returns>
    public async Task<JsScriptEngine> RentAsync()
    {
        // 等待信号量
        await _semaphore.WaitAsync();

        // 如果池中没有可用实例，则创建新实例
        if (_pool.TryDequeue(out JsScriptEngine? engine))
        {
            return engine;
        }

        // 创建新实例
        return ActivatorUtilities.CreateInstance<JsScriptEngine>(_serviceProvider);
    }

    /// <summary>
    /// 将使用完的JavaScript引擎实例返回到对象池
    /// 如果池已满，则释放该实例
    /// </summary>
    /// <param name="engine">要返回的JavaScript引擎实例</param>
    public void Return(JsScriptEngine engine)
    {
        // 如果池未满
        if (_pool.Count < _maxSize)
        {
            // 将引擎返回到池
            _pool.Enqueue(engine);
        }
        else
        {
            // 释放引擎
            engine.Dispose();
        }

        // 释放信号量
        _semaphore.Release();
    }

    /// <summary>
    /// 用于设置脚本变量的键值对
    /// </summary>
    public class ScriptVariable : Dictionary<string, object>
    {
        /// <summary>
        /// 添加变量
        /// </summary>
        /// <param name="name">变量名</param>
        /// <param name="value">变量值</param>
        public new void Add(string name, object value)
        {
            base.Add(name, value);
        }
    }

    /// <summary>
    /// 执行脚本，支持自定义变量
    /// </summary>
    /// <param name="scriptKey">脚本标识（用于日志）</param>
    /// <param name="script">脚本内容</param>
    /// <param name="data">主要数据</param>
    /// <param name="variables">自定义变量字典</param>
    /// <returns>执行结果</returns>
    /// <example>
    /// <code>
    /// var result = await enginePool.ExecuteScriptAsync(
    ///     "calculate-price",
    ///     "return data.value * price + tax;",
    ///     new { value = 100 },
    ///     new Dictionary&lt;string, object&gt;
    ///     {
    ///         ["price"] = 10,
    ///         ["tax"] = 2
    ///     }
    /// );
    /// </code>
    /// </example>
    public async Task<object> ExecuteScriptAsync(
        string scriptKey,
        string script,
        IDictionary<string, object>? variables = null)
    {
        // 计时
        var sw = Stopwatch.StartNew();
        // 获取引擎
        JsScriptEngine engine = await RentAsync();

        try
        {
            // 设置当前上下文
            JsScriptEngine.SetCurrentContext(engine.LogContext);

            if (variables != null)
            {
                // 遍历变量
                foreach (var variable in variables)
                {
                    // 设置变量
                    engine.Engine.SetValue(variable.Key, variable.Value.GetJsValue());
                }
            }

            // 执行脚本
            var result = engine.Engine.Evaluate(script, new ScriptParsingOptions
            {
                Tolerant = true,
                AllowReturnOutsideFunction = true
            }).ToObject();

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Script {Key} execution failed", scriptKey);
            throw;
        }
        finally
        {
            // 清除当前上下文
            JsScriptEngine.SetCurrentContext(null);
            engine.ResetEngine();
            Return(engine);
        }
    }

    /// <summary>
    /// 执行脚本并返回结果和日志
    /// </summary>
    /// <returns>包含执行结果和日志的元组</returns>
    public async Task<(object Result, List<object> Log, long ElapsedMilliseconds)> ExecuteScriptWithLogAsync(
        string scriptKey,
        string script,
        IDictionary<string, object>? variables = null)
    {
        // 计时
        var sw = Stopwatch.StartNew();
        // 获取引擎
        var engine = await RentAsync();
        try
        {
            // 设置当前上下文
            JsScriptEngine.SetCurrentContext(engine.LogContext);

            if (variables != null)
            {
                // 遍历变量
                foreach (var variable in variables)
                {
                    // 设置变量
                    engine.Engine.SetValue(variable.Key, variable.Value.GetJsValue());
                }
            }

            // 执行脚本
            var result = engine.Engine.Evaluate(script, new ScriptParsingOptions
            {
                Tolerant = true,
                AllowReturnOutsideFunction = true
            }).ToObject();

            // 获取日志副本
            var logs = engine.LogContext.Logs.ToList();

            // 返回结果和日志副本
            return (result, logs, sw.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Script {Key} execution failed", scriptKey);
            throw;
        }
        finally
        {
            // 清除当前上下文
            JsScriptEngine.SetCurrentContext(null);
            engine.ResetEngine();
            Return(engine);
        }
    }

    /// <summary>
    /// 创建一个带有隔离上下文的执行器
    /// </summary>
    public async Task<IScriptContext> CreateContextAsync()
    {
        // 获取引擎
        var engine = await RentAsync();
        // 创建上下文
        return new ScriptContext(engine, this);
    }
}

/// <summary>
/// 脚本上下文接口
/// </summary>
public interface IScriptContext : IAsyncDisposable
{
    /// <summary>
    /// 设置变量
    /// </summary>
    /// <param name="name">变量名</param>
    /// <param name="value">变量值</param>
    /// <returns>当前上下文</returns>
    IScriptContext SetVariable(string name, object value);
    /// <summary>
    /// 执行脚本
    /// </summary>
    /// <param name="scriptKey">脚本标识</param>
    /// <param name="script">脚本内容</param>
    /// <param name="data">数据</param>
    /// <returns>执行结果和日志</returns>
    Task<(object Result, List<object> Log)> ExecuteAsync(string scriptKey, string script, object data);
    /// <summary>
    /// 获取日志
    /// </summary>
    /// <returns>日志</returns>
    List<object> GetLog();
}

/// <summary>
/// 脚本上下文实现
/// </summary>
internal class ScriptContext : IScriptContext
{
    /// <summary>
    /// 引擎
    /// </summary>
    private readonly JsScriptEngine _engine;
    /// <summary>
    /// 对象池
    /// </summary>
    private readonly JsScriptEnginePool _pool;
    /// <summary>
    /// 自定义变量
    /// </summary>
    private readonly HashSet<string> _customVariables = new();

    /// <summary>
    /// 初始化脚本上下文
    /// </summary>
    /// <param name="engine">引擎</param>
    /// <param name="pool">对象池</param>
    public ScriptContext(JsScriptEngine engine, JsScriptEnginePool pool)
    {
        _engine = engine;
        _pool = pool;
    }

    /// <summary>
    /// 设置变量
    /// </summary>
    /// <param name="name">变量名</param>
    /// <param name="value">变量值</param>
    /// <returns>当前上下文</returns>
    public IScriptContext SetVariable(string name, object value)
    {
        _engine.Engine.SetValue(name, value.GetJsValue());
        _customVariables.Add(name);
        return this;
    }

    /// <summary>
    /// 执行脚本
    /// </summary>
    /// <param name="scriptKey">脚本标识</param>
    /// <param name="script">脚本内容</param>
    /// <param name="data">数据</param>
    /// <returns>执行结果和日志</returns>
    public async Task<(object Result, List<object> Log)> ExecuteAsync(string scriptKey, string script, object data)
    {
        try
        {
            // 设置当前上下文
            JsScriptEngine.SetCurrentContext(_engine.LogContext);
            // 设置数据
            _engine.Engine.SetValue("data", data.GetJsValue());
            // 执行脚本
            var result = _engine.Engine.Evaluate(script, new ScriptParsingOptions
            {
                Tolerant = true,
                AllowReturnOutsideFunction = true
            }).ToObject();

            // 获取日志副本
            var logs = _engine.LogContext.Logs.ToList();
            // 返回结果和日志
            return (result, logs);
        }
        finally
        {
            // 清除当前上下文
            JsScriptEngine.SetCurrentContext(null);
        }
    }

    /// <summary>
    /// 获取日志
    /// </summary>
    /// <returns>日志</returns>
    public List<object> GetLog()
    {
        // 获取日志
        return _engine.LogContext.Logs.ToList();
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        // 清理自定义变量
        foreach (var variable in _customVariables)
        {
            _engine.Engine.SetValue(variable, string.Empty);
        }
        // 清理自定义变量
        _customVariables.Clear();

        // 重置引擎并返回池
        _engine.ResetEngine();
        _pool.Return(_engine);
    }
}