using System.IO.Ports;
using System.Text;
using EdgeGateway.Engine.Attributes;

namespace EdgeGateway.Engine.EngineMethod;

/// <summary>
/// 串口通信操作
/// </summary>
[Engine]
public class SerialEngine : ISingleton
{
  private readonly Dictionary<string, SerialPort> _ports = new();
  private readonly Dictionary<string, CancellationTokenSource> _cancellationTokens = new();
  private readonly object _lock = new();

  /// <summary>
  /// 获取可用串口列表
  /// </summary>
  /// <returns>串口名称列表</returns>
  [EngineMethod("serial.GetPorts()",
      "获取可用串口",
      "获取系统可用的串口列表",
      "var ports = serial.GetPorts();\nreturn ports; // 返回：['COM1', 'COM2', ...]",
      "串口",
      result: "该方法返回系统可用的串口名称列表。例如：获取可用串口列表返回['COM1', 'COM2', ...]。")]
  public string[] GetPorts()
  {
    return SerialPort.GetPortNames();
  }

  /// <summary>
  /// 打开串口
  /// </summary>
  /// <param name="id">串口ID</param>
  /// <param name="portName">串口名称</param>
  /// <param name="baudRate">波特率</param>
  /// <param name="dataBits">数据位</param>
  /// <param name="parity">校验位</param>
  /// <param name="stopBits">停止位</param>
  /// <returns>是否打开成功</returns>
  [EngineMethod("serial.Open(id, portName, baudRate, dataBits, parity, stopBits)",
      "打开串口",
      "打开指定的串口\n参数一: 'id' 串口标识\n参数二: 'portName' 串口名称\n参数三: 'baudRate' 波特率，默认9600\n参数四: 'dataBits' 数据位，默认8\n参数五: 'parity' 校验位(None/Odd/Even/Mark/Space)，默认'None'\n参数六: 'stopBits' 停止位(1/1.5/2)，默认'1'",
      "var success = serial.Open('port1', 'COM1', 9600, 8, 'None', '1');\nreturn success; // 返回：true/false",
      "串口",
      result: "该方法返回布尔值，表示串口是否打开成功。例如：打开'COM1'串口返回true。")]
  public bool Open(string id, string portName, int baudRate = 9600, int dataBits = 8, string parity = "None", string stopBits = "1")
  {
    try
    {
      lock (_lock)
      {
        // 如果已存在相同ID的串口，先关闭它
        if (_ports.ContainsKey(id))
        {
          Close(id);
        }

        // 解析校验位
        var parityValue = parity switch
        {
          "Odd" => Parity.Odd,
          "Even" => Parity.Even,
          "Mark" => Parity.Mark,
          "Space" => Parity.Space,
          _ => Parity.None
        };

        // 解析停止位
        var stopBitsValue = stopBits switch
        {
          "1.5" => StopBits.OnePointFive,
          "2" => StopBits.Two,
          _ => StopBits.One
        };

        // 创建新的串口
        var serialPort = new SerialPort
        {
          PortName = portName,
          BaudRate = baudRate,
          DataBits = dataBits,
          Parity = parityValue,
          StopBits = stopBitsValue,
          ReadTimeout = 500,
          WriteTimeout = 500
        };

        // 打开串口
        serialPort.Open();

        // 存储串口
        _ports[id] = serialPort;

        return true;
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"打开串口失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 关闭串口
  /// </summary>
  /// <param name="id">串口ID</param>
  /// <returns>是否关闭成功</returns>
  [EngineMethod("serial.Close(id)",
      "关闭串口",
      "关闭指定的串口\n参数一: 'id' 串口标识",
      "serial.Close('port1');\n// 返回：true/false",
      "串口",
      result: "该方法返回布尔值，表示串口是否关闭成功。例如：关闭'port1'串口返回true。")]
  public bool Close(string id)
  {
    try
    {
      lock (_lock)
      {
        // 取消接收操作
        if (_cancellationTokens.TryGetValue(id, out var cts))
        {
          cts.Cancel();
          _cancellationTokens.Remove(id);
        }

        // 关闭串口
        if (_ports.TryGetValue(id, out var port))
        {
          if (port.IsOpen)
          {
            port.Close();
          }

          port.Dispose();
          _ports.Remove(id);
          return true;
        }

        return false;
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"关闭串口失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 发送数据
  /// </summary>
  /// <param name="id">串口ID</param>
  /// <param name="data">要发送的数据</param>
  /// <returns>是否发送成功</returns>
  [EngineMethod("serial.Send(id, data)",
      "发送数据",
      "通过串口发送数据\n参数一: 'id' 串口标识\n参数二: 'data' 要发送的数据(字符串)",
      "var success = serial.Send('port1', 'Hello World');\nreturn success; // 返回：true/false",
      "串口",
      result: "该方法返回布尔值，表示数据是否发送成功。例如：发送'Hello World'返回true。")]
  public bool Send(string id, string data)
  {
    try
    {
      lock (_lock)
      {
        if (!_ports.TryGetValue(id, out var port) || !port.IsOpen)
        {
          return false;
        }

        // 发送数据
        port.Write(data);

        return true;
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"发送数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 发送十六进制数据
  /// </summary>
  /// <param name="id">串口ID</param>
  /// <param name="hexData">十六进制字符串</param>
  /// <returns>是否发送成功</returns>
  [EngineMethod("serial.SendHex(id, hexData)",
      "发送十六进制数据",
      "通过串口发送十六进制数据\n参数一: 'id' 串口标识\n参数二: 'hexData' 十六进制字符串",
      "var success = serial.SendHex('port1', '48656C6C6F');\nreturn success; // 发送'Hello'",
      "串口",
      result: "该方法返回布尔值，表示十六进制数据是否发送成功。例如：发送'48656C6C6F'返回true。")]
  public bool SendHex(string id, string hexData)
  {
    try
    {
      lock (_lock)
      {
        if (!_ports.TryGetValue(id, out var port) || !port.IsOpen)
        {
          return false;
        }

        // 移除所有空格
        hexData = hexData.Replace(" ", "");

        // 检查是否为有效的十六进制字符串
        if (hexData.Length % 2 != 0)
        {
          throw new Exception("无效的十六进制字符串");
        }

        // 将十六进制字符串转换为字节数组
        var bytes = new byte[hexData.Length / 2];
        for (var i = 0; i < bytes.Length; i++)
        {
          bytes[i] = Convert.ToByte(hexData.Substring(i * 2, 2), 16);
        }

        // 发送数据
        port.Write(bytes, 0, bytes.Length);

        return true;
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"发送十六进制数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 接收数据
  /// </summary>
  /// <param name="id">串口ID</param>
  /// <param name="timeout">超时时间(毫秒)</param>
  /// <returns>接收到的数据</returns>
  [EngineMethod("serial.Receive(id, timeout)",
      "接收数据",
      "从串口接收数据\n参数一: 'id' 串口标识\n参数二: 'timeout' 超时时间(毫秒)，默认1000",
      "var data = serial.Receive('port1', 3000);\nreturn data;",
      "串口",
      result: "该方法返回接收到的数据字符串。例如：接收'port1'串口数据返回'Hello World'。")]
  public string Receive(string id, int timeout = 1000)
  {
    try
    {
      lock (_lock)
      {
        if (!_ports.TryGetValue(id, out var port) || !port.IsOpen)
        {
          return string.Empty;
        }

        // 设置超时
        port.ReadTimeout = timeout;

        // 创建取消令牌
        var cts = new CancellationTokenSource(timeout);
        _cancellationTokens[id] = cts;

        try
        {
          // 读取可用数据
          var buffer = new byte[port.BytesToRead > 0 ? port.BytesToRead : 1024];

          // 如果没有可用数据，等待一段时间
          if (port.BytesToRead == 0)
          {
            Thread.Sleep(timeout);
          }

          // 再次检查可用数据
          if (port.BytesToRead == 0)
          {
            return string.Empty;
          }

          // 读取数据
          var bytesRead = port.Read(buffer, 0, Math.Min(buffer.Length, port.BytesToRead));

          if (bytesRead > 0)
          {
            return Encoding.UTF8.GetString(buffer, 0, bytesRead);
          }

          return string.Empty;
        }
        catch (TimeoutException)
        {
          return string.Empty;
        }
        finally
        {
          _cancellationTokens.Remove(id);
        }
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"接收数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 接收十六进制数据
  /// </summary>
  /// <param name="id">串口ID</param>
  /// <param name="timeout">超时时间(毫秒)</param>
  /// <returns>十六进制字符串</returns>
  [EngineMethod("serial.ReceiveHex(id, timeout)",
      "接收十六进制数据",
      "从串口接收十六进制数据\n参数一: 'id' 串口标识\n参数二: 'timeout' 超时时间(毫秒)，默认1000",
      "var hexData = serial.ReceiveHex('port1', 3000);\nreturn hexData;",
      "串口",
      result: "该方法返回接收到的十六进制数据字符串。例如：接收'port1'串口数据返回'48656C6C6F'。")]
  public string ReceiveHex(string id, int timeout = 1000)
  {
    try
    {
      lock (_lock)
      {
        if (!_ports.TryGetValue(id, out var port) || !port.IsOpen)
        {
          return string.Empty;
        }

        // 设置超时
        port.ReadTimeout = timeout;

        // 创建取消令牌
        var cts = new CancellationTokenSource(timeout);
        _cancellationTokens[id] = cts;

        try
        {
          // 读取可用数据
          var buffer = new byte[port.BytesToRead > 0 ? port.BytesToRead : 1024];

          // 如果没有可用数据，等待一段时间
          if (port.BytesToRead == 0)
          {
            Thread.Sleep(timeout);
          }

          // 再次检查可用数据
          if (port.BytesToRead == 0)
          {
            return string.Empty;
          }

          // 读取数据
          var bytesRead = port.Read(buffer, 0, Math.Min(buffer.Length, port.BytesToRead));

          if (bytesRead > 0)
          {
            // 将字节数组转换为十六进制字符串
            var hex = new StringBuilder(bytesRead * 2);
            for (var i = 0; i < bytesRead; i++)
            {
              hex.AppendFormat("{0:X2}", buffer[i]);
            }

            return hex.ToString();
          }

          return string.Empty;
        }
        catch (TimeoutException)
        {
          return string.Empty;
        }
        finally
        {
          _cancellationTokens.Remove(id);
        }
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"接收十六进制数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 检查串口是否打开
  /// </summary>
  /// <param name="id">串口ID</param>
  /// <returns>是否已打开</returns>
  [EngineMethod("serial.IsOpen(id)",
      "检查串口状态",
      "检查串口是否已打开\n参数一: 'id' 串口标识",
      "var isOpen = serial.IsOpen('port1');\nreturn isOpen; // 返回：true/false",
      "串口",
      result: "该方法返回布尔值，表示串口是否已打开。例如：检查'port1'串口状态返回true。")]
  public bool IsOpen(string id)
  {
    lock (_lock)
    {
      return _ports.TryGetValue(id, out var port) && port.IsOpen;
    }
  }

  /// <summary>
  /// 获取所有打开的串口
  /// </summary>
  /// <returns>串口ID列表</returns>
  [EngineMethod("serial.GetOpenPorts()",
      "获取已打开串口",
      "获取所有已打开的串口ID列表",
      "var ports = serial.GetOpenPorts();\nreturn ports;",
      "串口",
      result: "该方法返回已打开的串口ID列表。例如：获取已打开串口返回['port1', 'port2', ...]。")]
  public string[] GetOpenPorts()
  {
    lock (_lock)
    {
      return _ports.Keys.ToArray();
    }
  }
}