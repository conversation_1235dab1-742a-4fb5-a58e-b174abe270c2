using EdgeGateway.Engine.Attributes;
using EdgeGateway.Shared.Storage;
using EdgeGateway.Engine.Abstractions;

namespace EdgeGateway.Engine.EngineMethod;

/// <summary>
///     设备相关
/// </summary>
[Engine]
public class DeviceEngine : ISingleton
{
    /// <summary>
    /// 设备访问器
    /// </summary>
    private readonly IDeviceAccess _deviceAccess;

    /// <summary>
    /// </summary>
    /// <param name="deviceAccess">设备访问接口</param>
    public DeviceEngine(IDeviceAccess deviceAccess)
    {
        _deviceAccess = deviceAccess;
    }

    /// <summary>
    ///     获取设备属性的上一次值
    /// </summary>
    /// <param name="deviceId">设备标识</param>
    /// <param name="propertyKey">属性标识</param>
    /// <returns>返回设备属性的上一次值</returns>
    [EngineMethod("device.Prev(deviceId, propertyKey)",
        "获取属性上一次值",
        "获取指定设备属性的上一次记录值\n参数一: 'deviceId' 设备标识\n参数二: 'propertyKey' 属性标识",
        "var prevValue = device.Prev('device1', 'temperature');\nreturn prevValue;",
        "设备方法",
        result: "该方法返回上一次属性值，类型取决于属性本身。例如：数值型属性返回数值如25.5；布尔型属性返回true/false；字符串型属性返回文本如'正常'。若无历史值则返回null。")]
    public object? Prev(string deviceId, string propertyKey)
    {
        try
        {
            var key = $"{deviceId}.{propertyKey}";
            var property = DataStorage.Instance.Get(key);
            if (property == null)
                return null;

            var previousValue = property.CookieValue;
            if (previousValue == null)
                return null;

            return previousValue;
        }
        catch (Exception ex)
        {
            throw new Exception($"获取设备[{deviceId}]属性[{propertyKey}]的上一次值失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     获取设备在线状态
    /// </summary>
    /// <param name="deviceId">设备标识</param>
    /// <returns>返回设备是否在线</returns>
    [EngineMethod("device.IsOnline(deviceId)",
        "获取设备在线状态",
        "获取指定设备的在线状态\n参数一: 'deviceId' 设备标识",
        "var online = device.IsOnline('device1');\nreturn online; // 返回：true/false",
        "设备方法",
        result: "该方法返回布尔值，true表示设备在线，false表示设备离线。")]
    public bool IsOnline(string deviceId)
    {
        return DataStorage.Instance.GetDeviceStatus(deviceId);
    }

    /// <summary>
    ///     获取设备状态统计
    /// </summary>
    /// <returns>返回设备状态统计</returns>
    [EngineMethod("device.Stats()",
        "获取设备状态统计",
        "获取所有设备的在线和离线数量统计",
        "var stats = device.Stats();\nreturn stats; // 返回：{online: 5, offline: 2}",
        "设备方法",
        result: "该方法返回包含设备状态统计信息的对象，格式为：{online: 在线设备数量, offline: 离线设备数量}。例如：{online: 5, offline: 2}")]
    public object Stats()
    {
        var (online, offline) = DataStorage.Instance.GetDeviceStatusStatistics();
        return new { online, offline };
    }

    /// <summary>
    ///     获取设备在线时长
    /// </summary>
    /// <param name="deviceId">设备标识</param>
    /// <returns>返回设备在线时长(分钟)</returns>
    [EngineMethod("device.OnlineTime(deviceId)",
        "获取设备在线时长",
        "获取指定设备的在线时长(分钟)\n参数一: 'deviceId' 设备标识",
        "var time = device.OnlineTime('device1');\nreturn time; // 返回：120.5",
        "设备方法",
        result: "该方法返回浮点数，表示设备在线时长，单位为分钟。例如：120.5表示设备已在线120.5分钟。")]
    public double OnlineTime(string deviceId)
    {
        return DataStorage.Instance.GetOnlineTime(deviceId, DateTime.Now).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     获取设备属性值
    /// </summary>
    /// <param name="deviceId">设备标识</param>
    /// <param name="propertyKey">属性标识</param>
    /// <returns>返回属性值</returns>
    [EngineMethod("device.Get(deviceId, propertyKey)",
        "获取设备属性值",
        "获取指定设备的指定属性值\n参数一: 'deviceId' 设备标识\n参数二: 'propertyKey' 属性标识",
        "var value = device.Get('device1', 'temperature');\nreturn value;",
        "设备方法",
        result: "该方法返回属性当前值，类型取决于属性本身。例如：数值型属性返回数值如25.5；布尔型属性返回true/false；字符串型属性返回文本如'正常'。若属性不存在则返回null。")]
    public object? Get(string deviceId, string propertyKey)
    {
        var key = $"{deviceId}.{propertyKey}";
        var property = DataStorage.Instance.Get(key);
        return property?.Value;
    }

    /// <summary>
    ///     获取设备属性信息
    /// </summary>
    /// <param name="deviceId">设备标识</param>
    /// <param name="propertyKey">属性标识</param>
    /// <returns>返回属性信息</returns>
    [EngineMethod("device.Info(deviceId, propertyKey)",
        "获取设备属性信息",
        "获取指定设备的指定属性的完整信息\n参数一: 'deviceId' 设备标识\n参数二: 'propertyKey' 属性标识",
        "var info = device.Info('device1', 'temperature');\nreturn info;",
        "设备方法",
        result: "该方法返回包含属性完整信息的对象，格式为：{value: 当前值, readTime: 读取时间, status: 状态码, lastValue: 上一次值, lastTime: 上一次读取时间, errorMessage: 错误信息}。例如：{value: 25.5, readTime: '2023-09-15T14:30:25', status: 0, lastValue: 24.8, lastTime: '2023-09-15T14:25:25', errorMessage: null}")]
    public object? Info(string deviceId, string propertyKey)
    {
        var key = $"{deviceId}.{propertyKey}";
        var property = DataStorage.Instance.Get(key);
        if (property == null) return null;

        return new
        {
            value = property.Value,
            readTime = property.ReadTime,
            status = property.Status,
            lastValue = property.CookieValue,
            lastTime = property.CookieTime,
            errorMessage = property.ErrMsg
        };
    }

    /// <summary>
    ///     批量获取设备属性值
    /// </summary>
    /// <param name="deviceId">设备标识</param>
    /// <param name="propertyKeys">属性标识列表</param>
    /// <returns>返回属性值字典</returns>
    [EngineMethod("device.Get(deviceId, propertyKeys)",
        "批量获取设备属性值",
        "批量获取指定设备的多个属性值\n参数一: 'deviceId' 设备标识\n参数二: 'propertyKeys' 属性标识数组",
        "var values = device.Get('device1', ['temp', 'humidity']);\nreturn values;",
        "设备方法",
        result: "该方法返回属性值字典，键为属性标识，值为属性当前值。例如：{'temp': 25.5, 'humidity': 60, 'status': '正常'}")]
    public Dictionary<string, object?> Get(string deviceId, string[] propertyKeys)
    {
        var result = new Dictionary<string, object?>();
        foreach (var key in propertyKeys)
        {
            var fullKey = $"{deviceId}.{key}";
            var property = DataStorage.Instance.Get(fullKey);
            result[key] = property?.Value;
        }
        return result;
    }

    /// <summary>
    ///     写入设备属性值
    /// </summary>
    /// <param name="deviceId">设备标识</param>
    /// <param name="propertyKey">属性标识</param>
    /// <param name="value">属性值</param>
    /// <returns>返回是否成功</returns>
    [EngineMethod("device.Write(deviceId, propertyKey, value)",
        "写入设备属性值",
        "向指定设备的指定属性写入值\n参数一: 'deviceId' 设备标识\n参数二: 'propertyKey' 属性标识\n参数三: 'value' 要写入的值",
        "await device.Write('device1', 'temperature', 25.5);\n// 返回：true/false",
        "设备方法",
        result: "该方法返回布尔值，true表示写入成功，false表示写入失败。")]
    public bool Write(string deviceId, string propertyKey, object value)
    {
        return _deviceAccess.WriteValue(deviceId, propertyKey, value).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     批量写入设备属性值
    /// </summary>
    /// <param name="deviceId">设备标识</param>
    /// <param name="values">属性值字典</param>
    /// <returns>返回是否成功</returns>
    [EngineMethod("device.WriteBatch(deviceId, values)",
        "批量写入设备属性值",
        "向指定设备批量写入多个属性值\n参数一: 'deviceId' 设备标识\n参数二: 'values' 属性值字典",
        "await device.WriteBatch('device1', {'temp': 25.5, 'humidity': 60});\n// 返回：true/false",
        "设备方法",
        result: "该方法返回布尔值，true表示全部写入成功，false表示存在写入失败的属性。")]
    public bool WriteBatch(string deviceId, Dictionary<string, object> values)
    {
        return _deviceAccess.WriteBatch(deviceId, values).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     设置单个虚拟值
    /// </summary>  
    /// <param name="deviceId">设备标识</param>
    /// <param name="propertyKey">属性标识</param>
    /// <param name="value">属性值</param>
    /// <returns>返回是否成功</returns>
    [EngineMethod("device.SetVar(deviceId, propertyKey, value)",
        "设置虚拟值",
        "设置指定设备的虚拟值\n参数一: 'deviceId' 设备标识\n参数二: 'propertyKey' 属性标识\n参数三: 'value' 属性值",
        "await device.SetVar('device1', 'temperature', 25.5);\n// 返回：true/false",
        "设备方法",
        result: "该方法返回布尔值，true表示设置成功，false表示设置失败。")]
    public bool SetVar(string deviceId, string propertyKey, object value)
    {
        return _deviceAccess.SetPayLoadVariable(deviceId, propertyKey, value).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     批量设置虚拟值
    /// </summary>  
    /// <param name="deviceId">设备标识</param>
    /// <param name="values">属性值字典</param>
    /// <returns>返回是否成功</returns>
    [EngineMethod("device.SetVarsBatch(deviceId, values)",
        "批量设置虚拟值",
        "批量设置指定设备的虚拟值\n参数一: 'deviceId' 设备标识\n参数二: 'values' 属性值字典",
        "await device.SetVarsBatch('device1', {'temp': 25.5, 'humidity': 60});\n// 返回：true/false",
        "设备方法",
        result: "该方法返回布尔值，true表示全部设置成功，false表示存在设置失败的虚拟值。")]
    public bool SetVarsBatch(string deviceId, Dictionary<string, object> values)
    {
        return _deviceAccess.SetPayLoadVariableBatch(deviceId, values).GetAwaiter().GetResult();
    }
}