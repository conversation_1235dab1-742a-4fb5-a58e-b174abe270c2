using System.Net;
using System.Net.Sockets;
using EdgeGateway.Engine.Attributes;

namespace EdgeGateway.Engine.EngineMethod;

/// <summary>
/// UDP客户端操作
/// </summary>
[Engine]
public class UdpEngine : ISingleton
{
  /// <summary>
  /// 客户端字典
  /// </summary>
  private readonly Dictionary<string, UdpClient> _clients = new();
  /// <summary>
  /// 远程端点字典
  /// </summary>
  private readonly Dictionary<string, IPEndPoint> _remoteEndPoints = new();
  /// <summary>
  /// 取消令牌字典
  /// </summary>
  private readonly Dictionary<string, CancellationTokenSource> _cancellationTokens = new();
  /// <summary>
  /// 锁
  /// </summary>
  private readonly object _lock = new();

  /// <summary>
  /// 创建UDP客户端
  /// </summary>
  /// <param name="id">客户端ID</param>
  /// <param name="localPort">本地端口，0表示随机端口</param>
  /// <returns>是否创建成功</returns>
  [EngineMethod("udp.Create(id, localPort)",
      "创建UDP客户端",
      "创建UDP客户端\n参数一: 'id' 客户端标识\n参数二: 'localPort' 本地端口，0表示随机端口",
      "var success = udp.Create('client1', 8888);\nreturn success; // 返回：true/false",
      "网络",
      result: "该方法返回Boolean类型值。")]
  public bool Create(string id, int localPort = 0)
  {
    try
    {
      lock (_lock)
      {
        // 如果已存在相同ID的客户端，先关闭它
        if (_clients.ContainsKey(id))
        {
          Close(id);
        }

        // 创建新的UDP客户端
        var client = localPort > 0
            ? new UdpClient(localPort)
            : new UdpClient();

        // 存储客户端
        _clients[id] = client;

        return true;
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"创建UDP客户端失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 关闭UDP客户端
  /// </summary>
  /// <param name="id">客户端ID</param>
  /// <returns>是否关闭成功</returns>
  [EngineMethod("udp.Close(id)",
      "关闭UDP客户端",
      "关闭UDP客户端\n参数一: 'id' 客户端标识",
      "udp.Close('client1');\n// 返回：true/false",
      "网络",
      result: "该方法返回Boolean类型值。")]
  public bool Close(string id)
  {
    try
    {
      lock (_lock)
      {
        // 取消接收操作
        if (_cancellationTokens.TryGetValue(id, out var cts))
        {
          cts.Cancel();
          _cancellationTokens.Remove(id);
        }

        // 移除远程端点
        _remoteEndPoints.Remove(id);

        // 关闭客户端
        if (_clients.TryGetValue(id, out var client))
        {
          client.Close();
          _clients.Remove(id);
          return true;
        }

        return false;
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"关闭UDP客户端失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 设置远程端点
  /// </summary>
  /// <param name="id">客户端ID</param>
  /// <param name="host">远程主机地址</param>
  /// <param name="port">远程端口</param>
  /// <returns>是否设置成功</returns>
  [EngineMethod("udp.SetRemote(id, host, port)",
      "设置远程端点",
      "设置UDP远程端点\n参数一: 'id' 客户端标识\n参数二: 'host' 远程主机地址\n参数三: 'port' 远程端口",
      "var success = udp.SetRemote('client1', '*************', 8888);\nreturn success; // 返回：true/false",
      "网络",
      result: "该方法返回Boolean类型值。")]
  public bool SetRemote(string id, string host, int port)
  {
    try
    {
      lock (_lock)
      {
        if (!_clients.ContainsKey(id))
        {
          return false;
        }

        // 创建远程端点
        var remoteEP = new IPEndPoint(IPAddress.Parse(host), port);
        _remoteEndPoints[id] = remoteEP;

        return true;
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"设置远程端点失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 发送数据
  /// </summary>
  /// <param name="id">客户端ID</param>
  /// <param name="data">要发送的数据</param>
  /// <returns>是否发送成功</returns>
  [EngineMethod("udp.Send(id, data)",
      "发送数据",
      "通过UDP发送数据\n参数一: 'id' 客户端标识\n参数二: 'data' 要发送的数据(字符串)",
      "var success = udp.Send('client1', 'Hello World');\nreturn success; // 返回：true/false",
      "网络",
      result: "该方法返回Boolean类型值。")]
  public bool Send(string id, string data)
  {
    try
    {
      lock (_lock)
      {
        if (!_clients.TryGetValue(id, out var client) || !_remoteEndPoints.TryGetValue(id, out var remoteEP))
        {
          return false;
        }

        // 将字符串转换为字节数组
        var bytes = Encoding.UTF8.GetBytes(data);

        // 发送数据
        client.Send(bytes, bytes.Length, remoteEP);

        return true;
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"发送数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 发送十六进制数据
  /// </summary>
  /// <param name="id">客户端ID</param>
  /// <param name="hexData">十六进制字符串</param>
  /// <returns>是否发送成功</returns>
  [EngineMethod("udp.SendHex(id, hexData)",
      "发送十六进制数据",
      "通过UDP发送十六进制数据\n参数一: 'id' 客户端标识\n参数二: 'hexData' 十六进制字符串",
      "var success = udp.SendHex('client1', '48656C6C6F');\nreturn success; // 发送'Hello'",
      "网络",
      result: "该方法返回Boolean类型值。")]
  public bool SendHex(string id, string hexData)
  {
    try
    {
      lock (_lock)
      {
        if (!_clients.TryGetValue(id, out var client) || !_remoteEndPoints.TryGetValue(id, out var remoteEP))
        {
          return false;
        }

        // 移除所有空格
        hexData = hexData.Replace(" ", "");

        // 检查是否为有效的十六进制字符串
        if (hexData.Length % 2 != 0)
        {
          throw new Exception("无效的十六进制字符串");
        }

        // 将十六进制字符串转换为字节数组
        var bytes = new byte[hexData.Length / 2];
        for (var i = 0; i < bytes.Length; i++)
        {
          bytes[i] = Convert.ToByte(hexData.Substring(i * 2, 2), 16);
        }

        // 发送数据
        client.Send(bytes, bytes.Length, remoteEP);

        return true;
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"发送十六进制数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 接收数据
  /// </summary>
  /// <param name="id">客户端ID</param>
  /// <param name="timeout">超时时间(毫秒)</param>
  /// <returns>接收到的数据</returns>
  [EngineMethod("udp.Receive(id, timeout)",
      "接收数据",
      "从UDP接收数据\n参数一: 'id' 客户端标识\n参数二: 'timeout' 超时时间(毫秒)，默认5000",
      "var data = udp.Receive('client1', 3000);\nreturn data;",
      "网络",
      result: "该方法返回String类型值。")]
  public string Receive(string id, int timeout = 5000)
  {
    try
    {
      lock (_lock)
      {
        if (!_clients.TryGetValue(id, out var client))
        {
          return string.Empty;
        }

        // 设置超时
        var cts = new CancellationTokenSource(timeout);
        _cancellationTokens[id] = cts;

        try
        {
          // 创建临时远程端点
          var remoteEP = new IPEndPoint(IPAddress.Any, 0);

          // 异步接收数据
          var task = client.ReceiveAsync();
          if (task.Wait(timeout))
          {
            var result = task.Result;

            // 更新远程端点
            _remoteEndPoints[id] = result.RemoteEndPoint;

            // 返回接收到的数据
            return Encoding.UTF8.GetString(result.Buffer);
          }

          return string.Empty;
        }
        catch (OperationCanceledException)
        {
          return string.Empty;
        }
        finally
        {
          _cancellationTokens.Remove(id);
        }
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"接收数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 接收十六进制数据
  /// </summary>
  /// <param name="id">客户端ID</param>
  /// <param name="timeout">超时时间(毫秒)</param>
  /// <returns>十六进制字符串</returns>
  [EngineMethod("udp.ReceiveHex(id, timeout)",
      "接收十六进制数据",
      "从UDP接收十六进制数据\n参数一: 'id' 客户端标识\n参数二: 'timeout' 超时时间(毫秒)，默认5000",
      "var hexData = udp.ReceiveHex('client1', 3000);\nreturn hexData;",
      "网络",
      result: "该方法返回String类型值。")]
  public string ReceiveHex(string id, int timeout = 5000)
  {
    try
    {
      lock (_lock)
      {
        if (!_clients.TryGetValue(id, out var client))
        {
          return string.Empty;
        }

        // 设置超时
        var cts = new CancellationTokenSource(timeout);
        _cancellationTokens[id] = cts;

        try
        {
          // 创建临时远程端点
          var remoteEP = new IPEndPoint(IPAddress.Any, 0);

          // 异步接收数据
          var task = client.ReceiveAsync();
          if (task.Wait(timeout))
          {
            var result = task.Result;

            // 更新远程端点
            _remoteEndPoints[id] = result.RemoteEndPoint;

            // 将字节数组转换为十六进制字符串
            var hex = new StringBuilder(result.Buffer.Length * 2);
            foreach (var b in result.Buffer)
            {
              hex.AppendFormat("{0:X2}", b);
            }

            return hex.ToString();
          }

          return string.Empty;
        }
        catch (OperationCanceledException)
        {
          return string.Empty;
        }
        finally
        {
          _cancellationTokens.Remove(id);
        }
      }
    }
    catch (Exception ex)
    {
      throw new Exception($"接收十六进制数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 获取所有客户端
  /// </summary>
  /// <returns>客户端ID列表</returns>
  [EngineMethod("udp.GetClients()",
      "获取所有客户端",
      "获取所有UDP客户端的ID列表",
      "var clients = udp.GetClients();\nreturn clients;",
      "网络",
      result: "该方法返回String类型数组。")]
  public string[] GetClients()
  {
    lock (_lock)
    {
      return _clients.Keys.ToArray();
    }
  }
}