using Microsoft.Data.Sqlite;
using Dapper;
using EdgeGateway.Core.Extension;
using EdgeGateway.TimeSeriesStorage.Abstractions.Configurations;
using EdgeGateway.TimeSeriesStorage.Abstractions.Interfaces;
using EdgeGateway.TimeSeriesStorage.Abstractions.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace EdgeGateway.TimeSeriesStorage.SQLite.Services
{
  /// <summary>
  /// SQLite时序数据存储实现
  /// </summary>
  /// <remarks>
  /// 提供基于SQLite的时序数据存储功能
  /// 支持数据写入、查询、删除等基本操作
  /// 实现了自动分表和数据清理机制
  /// </remarks>
  public class SQLiteTimeSeriesStorage : ITimeSeriesStorage, IDisposable
  {
    /// <summary>
    /// 存储配置选项
    /// </summary>
    private readonly SQLiteStorageOptions _options;
    /// <summary>
    /// 写入缓冲池
    /// </summary>
    private readonly WriteBufferPool _writeBuffer;
    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<SQLiteTimeSeriesStorage> _logger;
    /// <summary>
    /// 表管理器
    /// </summary>
    private readonly TableManager _tableManager;
    /// <summary>
    /// 监控器
    /// </summary>
    private readonly StorageMonitor _monitor;
    /// <summary>
    /// 查询优化器
    /// </summary>
    private readonly QueryOptimizer _queryOptimizer;

    /// <summary>
    /// 存储类型标识
    /// </summary>
    public string StorageType => "SQLite";

    /// <summary>
    /// 写入时序数据
    /// </summary>
    /// <param name="data">时序数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public Task WriteAsync(TimeSeriesData data, CancellationToken cancellationToken = default)
    {
      return _writeBuffer.AddAsync(data);
    }

    /// <summary>
    /// 批量写入时序数据
    /// </summary>
    /// <param name="dataList">时序数据列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public Task WriteBatchAsync(IEnumerable<TimeSeriesData> dataList, CancellationToken cancellationToken = default)
    {
      return Task.WhenAll(dataList.Select(data => _writeBuffer.AddAsync(data)));
    }

    /// <summary>
    /// 查询时序数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签ID</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>时序数据列表</returns>
    public async Task<IEnumerable<TimeSeriesData>> QueryAsync(
        string deviceId,
        IEnumerable<string> tagNames,
        DateTime startTime,
        DateTime endTime,
        CancellationToken cancellationToken = default)
    {
      using var connection = CreateConnection();
      var result = await _tableManager.QueryAsync(
          connection,
          deviceId,
          tagNames,
          startTime,
          endTime);

      return result.Cast<TimeSeriesData>();
    }

    /// <summary>
    /// 初始化SQLite时序数据存储
    /// </summary>
    /// <param name="options">存储配置选项</param>
    /// <param name="logger">日志记录器</param>
    public SQLiteTimeSeriesStorage(IOptions<SQLiteStorageOptions> options, ILogger<SQLiteTimeSeriesStorage> logger,
      StorageMonitor monitor, QueryOptimizer queryOptimizer, TableManager tableManager)
    {
      _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));

      try
      {
        // 初始化监控和优化器
        _monitor = monitor;
        _queryOptimizer = queryOptimizer;
        _tableManager = tableManager;

        // 确保数据库目录存在
        EnsureDatabaseDirectory();

        // 初始化写入缓冲池
        _writeBuffer = new WriteBufferPool(_options, _tableManager);

        // 初始化数据库结构
        Initialize();

        _logger.LogInformation("SQLite时序数据存储初始化完成: {path}", _options.DatabasePath);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "SQLite时序数据存储初始化失败");
        throw;
      }
    }

    /// <summary>
    /// 确保数据库目录存在
    /// </summary>
    private void EnsureDatabaseDirectory()
    {
      try
      {
        var dbDir = Path.GetDirectoryName(_options.DatabasePath);
        if (string.IsNullOrEmpty(dbDir))
        {
          throw new InvalidOperationException("数据库路径无效");
        }

        if (!Directory.Exists(dbDir))
        {
          Directory.CreateDirectory(dbDir);
          _logger.LogInformation("创建数据库目录: {directory}", dbDir);
        }
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "创建数据库目录失败");
        throw;
      }
    }

    /// <summary>
    /// 初始化数据库
    /// </summary>
    private void Initialize()
    {
      try
      {
        using var connection = CreateConnection();

        // 验证数据库连接和基本操作
        ValidateDatabase(connection);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "数据库初始化失败");
        throw;
      }
    }

    /// <summary>
    /// 验证数据库
    /// </summary>
    private void ValidateDatabase(SqliteConnection connection)
    {
      try
      {
        // 执行简单查询验证数据库是否正常
        connection.ExecuteScalar<int>("SELECT 1");

        _logger.LogInformation("数据库验证成功");
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "数据库验证失败");
        throw;
      }
    }

    /// <summary>
    /// 创建SQLite连接
    /// </summary>
    private SqliteConnection CreateConnection()
    {
      try
      {
        var connection = new SqliteConnection($"Data Source={_options.DatabasePath};");
        connection.Open();
        return connection;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "创建数据库连接失败");
        throw;
      }
    }

    /// <summary>
    /// 删除指定时间范围内的数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否删除成功</returns>
    public async Task<bool> DeleteAsync(
        string deviceId,
        DateTime startTime,
        DateTime endTime,
        CancellationToken cancellationToken = default)
    {
      try
      {
        using var connection = CreateConnection();
        var tableName = _tableManager.GetDeviceTableName(deviceId);

        var sql = $@"DELETE FROM {tableName} 
                     WHERE timestamp BETWEEN @startTicks AND @endTicks";

        var parameters = new
        {
          startTicks = startTime.ToUnixTimeMilliseconds(),
          endTicks = endTime.ToUnixTimeMilliseconds()
        };

        await connection.ExecuteAsync(sql, parameters);
        return true;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "删除设备 {DeviceId} 的时间范围数据失败: {StartTime} - {EndTime}",
            deviceId, startTime, endTime);
        return false;
      }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
      try
      {
        _writeBuffer?.Dispose();
        _logger.LogInformation("SQLite时序数据存储资源已释放");
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "释放资源时发生错误");
      }
    }

    /// <summary>
    /// 分页查询时序数据
    /// </summary>
    public async Task<PagedResult<TimeSeriesData>> QueryPagedAsync(
        string deviceId,
        IEnumerable<string> tagNames,
        DateTime startTime,
        DateTime endTime,
        int pageSize,
        int pageNumber,
        bool descending = false,
        CancellationToken cancellationToken = default)
    {
      try
      {
        using var connection = CreateConnection();

        // 查询分页数据
        var data = await _tableManager.QueryPagedAsync(
            connection,
            deviceId,
            tagNames,
            startTime,
            endTime,
            pageNumber,
            pageSize,
            descending);

        return data;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "分页查询数据失败: {DeviceId}", deviceId);
        throw;
      }
    }

    /// <summary>
    /// 获取设备最新数据
    /// </summary>
    public async Task<TimeSeriesData> GetLatestAsync(
        string deviceId,
        IEnumerable<string> tagNames = null,
        CancellationToken cancellationToken = default)
    {
      try
      {
        using var connection = CreateConnection();
        var data = await _tableManager.QueryLatestAsync(connection, deviceId, tagNames);
        return data as TimeSeriesData;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取最新数据失败: {DeviceId}", deviceId);
        throw;
      }
    }

    /// <summary>
    /// 批量获取设备最新数据
    /// </summary>
    public async Task<IDictionary<string, TimeSeriesData>> GetLatestBatchAsync(
        IEnumerable<string> deviceIds,
        IEnumerable<string> tagNames = null,
        CancellationToken cancellationToken = default)
    {
      try
      {
        using var connection = CreateConnection();
        var result = new Dictionary<string, TimeSeriesData>();

        foreach (var deviceId in deviceIds)
        {
          var data = await _tableManager.QueryLatestAsync(connection, deviceId, tagNames);
          if (data != null)
          {
            result[deviceId] = data as TimeSeriesData;
          }
        }

        return result;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "批量获取最新数据失败");
        throw;
      }
    }

    /// <summary>
    /// 删除设备的所有数据
    /// </summary>
    public async Task<bool> DeleteDeviceAsync(
        string deviceId,
        CancellationToken cancellationToken = default)
    {
      try
      {
        using var connection = CreateConnection();
        return await _tableManager.DeleteDeviceDataAsync(connection, deviceId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "删除设备数据失败: {DeviceId}", deviceId);
        return false;
      }
    }

    /// <summary>
    /// 批量删除设备数据
    /// </summary>
    public async Task<bool> DeleteDeviceBatchAsync(
        IEnumerable<string> deviceIds,
        CancellationToken cancellationToken = default)
    {
      try
      {
        using var connection = CreateConnection();
        var success = true;

        foreach (var deviceId in deviceIds)
        {
          success &= await _tableManager.DeleteDeviceDataAsync(connection, deviceId);
        }

        return success;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "批量删除设备数据失败");
        return false;
      }
    }

    /// <summary>
    /// 获取数据统计信息
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签名称</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>数据统计信息</returns>
    public async Task<TimeSeriesStatistics> GetStatisticsAsync(
        string deviceId,
        IEnumerable<string> tagNames,
        DateTime startTime,
        DateTime endTime,
        CancellationToken cancellationToken = default)
    {
      try
      {
        using var connection = CreateConnection();
        return await _tableManager.GetStatisticsAsync(
            connection,
            deviceId,
            tagNames,
            startTime,
            endTime);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取统计信息失败: {DeviceId}", deviceId);
        throw;
      }
    }

    /// <summary>
    /// 获取存储状态信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>存储状态信息</returns>
    public async Task<StorageStatus> GetStorageStatusAsync(CancellationToken cancellationToken = default)
    {
      try
      {
        using var connection = CreateConnection();
        var dbPath = _options.DatabasePath;
        var fileInfo = new FileInfo(dbPath);

        return new StorageStatus
        {
          TotalSpace = fileInfo.Length, // 总空间
          UsedSpace = fileInfo.Length, // 已用空间
          FreeSpace = new DriveInfo(fileInfo.Directory.Root.FullName).AvailableFreeSpace, // 可用空间
          DeviceCount = await _tableManager.GetDeviceCountAsync(connection), // 设备总数
          DataPointCount = await _tableManager.GetTotalDataPointCountAsync(connection), // 数据点总数
          EarliestDataTime = await _tableManager.GetEarliestDataTimeAsync(connection), // 最早数据时间
          LatestDataTime = await _tableManager.GetLatestDataTimeAsync(connection) // 最新数据时间
        };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取存储状态失败");
        throw;
      }
    }

    /// <summary>
    /// 获取性能报告
    /// </summary>
    /// <returns>性能报告</returns>
    public Task<PerformanceReport> GetPerformanceReportAsync()
    {
      try
      {
        var report = _monitor.GetPerformanceReport();
        return Task.FromResult(report);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取性能报告失败");
        throw;
      }
    }

    /// <summary>
    /// 获取查询建议
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签名称列表，为空时获取设备级别的建议</param>
    /// <returns>查询建议</returns>
    public Task<QuerySuggestion> GetQuerySuggestionAsync(string deviceId, IEnumerable<string> tagNames = null)
    {
      try
      {
        if (string.IsNullOrEmpty(deviceId))
        {
          throw new ArgumentNullException(nameof(deviceId));
        }

        var suggestion = _queryOptimizer.GetQuerySuggestion(deviceId, tagNames);
        return Task.FromResult(suggestion);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取查询建议失败: DeviceId={DeviceId}", deviceId);
        throw;
      }
    }
  }
}