<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="SqlSugarCore" Version="5.1.4.188" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\IotPlatform.Core\IotPlatform.Core.csproj"/>
    </ItemGroup>

</Project>
