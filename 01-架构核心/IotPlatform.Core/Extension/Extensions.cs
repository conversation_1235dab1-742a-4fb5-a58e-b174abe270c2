using System.Collections;
using System.Data;
using System.Text.RegularExpressions;

namespace IotPlatform.Core.Extension;

/// <summary>
///     转换扩展类.
/// </summary>
public static class Extensions
{
    #region 转换为long

    /// <summary>
    ///     将object转换为long,若转换失败,则返回0.不抛出异常.
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static long ParseToLong(this object obj)
    {
        try
        {
            return long.Parse(obj.ToString() ?? string.Empty);
        }
        catch
        {
            return 0L;
        }
    }

    /// <summary>
    ///     将object转换为long,若转换失败,则返回指定值.不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static long ParseToLong(this string str, long defaultValue)
    {
        try
        {
            return long.Parse(str);
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion

    #region 转换为int

    /// <summary>
    ///     将object转换为int，若转换失败，则返回0。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static int ParseToInt(this object str)
    {
        try
        {
            return Convert.ToInt32(str);
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    ///     将object转换为int，若转换失败，则返回指定值。不抛出异常
    ///     null返回默认值.
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static int ParseToInt(this object str, int defaultValue)
    {
        if (str == null)
        {
            return defaultValue;
        }

        try
        {
            return Convert.ToInt32(str);
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion

    #region 转换为short

    /// <summary>
    ///     将object转换为short，若转换失败，则返回0。不抛出异常.
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static short ParseToShort(this object obj)
    {
        try
        {
            return short.Parse(obj.ToString() ?? string.Empty);
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    ///     将object转换为short，若转换失败，则返回指定值。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static short ParseToShort(this object str, short defaultValue)
    {
        try
        {
            return short.Parse(str.ToString() ?? string.Empty);
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion

    #region 转换为demical

    /// <summary>
    ///     将object转换为demical，若转换失败，则返回指定值。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static decimal ParseToDecimal(this object str, decimal defaultValue)
    {
        try
        {
            return decimal.Parse(str.ToString() ?? string.Empty);
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    ///     将object转换为demical，若转换失败，则返回0。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static decimal ParseToDecimal(this object str)
    {
        try
        {
            return decimal.Parse(str.ToString() ?? string.Empty);
        }
        catch
        {
            return 0;
        }
    }

    #endregion

    #region 转化为bool

    /// <summary>
    ///     将object转换为bool，若转换失败，则返回false。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static bool ParseToBool(this object str)
    {
        try
        {
            if (str == null)
            {
                return false;
            }

            bool? value = GetBool(str);
            if (value != null)
            {
                return value.Value;
            }

            bool result;
            return bool.TryParse(str.ToString(), out result) && result;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     将object转换为bool，若转换失败，则返回指定值。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    public static bool ParseToBool(this object str, bool result)
    {
        try
        {
            return bool.Parse(str.ToString() ?? string.Empty);
        }
        catch
        {
            return result;
        }
    }

    /// <summary>
    ///     获取布尔值.
    /// </summary>
    private static bool? GetBool(this object data)
    {
        switch (data.ToString().Trim().ToLower())
        {
            case "0":
                return false;
            case "1":
                return true;
            case "是":
                return true;
            case "否":
                return false;
            case "yes":
                return true;
            case "no":
                return false;
            default:
                return null;
        }
    }

    #endregion

    #region 转换为float

    /// <summary>
    ///     将object转换为float，若转换失败，则返回0。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static float ParseToFloat(this object str)
    {
        try
        {
            return float.Parse(str.ToString() ?? string.Empty);
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    ///     将object转换为float，若转换失败，则返回指定值。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    public static float ParseToFloat(this object str, float result)
    {
        try
        {
            return float.Parse(str.ToString() ?? string.Empty);
        }
        catch
        {
            return result;
        }
    }

    #endregion

    #region 转换为Guid

    /// <summary>
    ///     将string转换为Guid，若转换失败，则返回Guid.Empty。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static Guid ParseToGuid(this string str)
    {
        try
        {
            return new Guid(str);
        }
        catch
        {
            return Guid.Empty;
        }
    }

    #endregion

    #region 转换为string
    
    /// <summary>
    ///     将object转换为string，若转换失败，则返回""。不抛出异常.
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static string ParseToString(this object obj)
    {
        try
        {
            return obj == null ? string.Empty : obj.ToString();
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    ///     将object转换为string.
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static string ParseToStrings<T>(this object obj)
    {
        try
        {
            if (obj is IEnumerable<T> list)
            {
                return string.Join(",", list);
            }

            return obj.ToString();
        }
        catch
        {
            return string.Empty;
        }
    }

    #endregion

    #region 转换为double

    /// <summary>
    ///     将object转换为double，若转换失败，则返回0。不抛出异常.
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static double ParseToDouble(this object obj)
    {
        try
        {
            return double.Parse(obj.ToString() ?? string.Empty);
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    ///     将object转换为double，若转换失败，则返回指定值。不抛出异常.
    /// </summary>
    /// <param name="str"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static double ParseToDouble(this object str, double defaultValue)
    {
        try
        {
            return double.Parse(str.ToString() ?? string.Empty);
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion

    #region 强制转换类型

    /// <summary>
    ///     强制转换类型.
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="source"></param>
    /// <returns></returns>
    public static IEnumerable<TResult> CastSuper<TResult>(this IEnumerable source)
    {
        return from object item in source select (TResult) Convert.ChangeType(item, typeof(TResult));
    }

    #endregion

    #region 转换为ToUnixTime

    public static long ParseToUnixTime(this System.DateTime nowTime)
    {
        DateTimeOffset dto = new(nowTime);
        return dto.ToUnixTimeMilliseconds();
    }

    #endregion

    #region 转换为帕斯卡命名法

    /// <summary>
    ///     将字符串转为帕斯卡命名法.
    /// </summary>
    /// <param name="original">源字符串.</param>
    /// <returns></returns>
    public static string ParseToPascalCase(this string original)
    {
        Regex invalidCharsRgx = new("[^_a-zA-Z0-9]");
        Regex whiteSpace = new(@"(?<=\s)");
        Regex startsWithLowerCaseChar = new("^[a-z]");
        Regex firstCharFollowedByUpperCasesOnly = new("(?<=[A-Z])[A-Z0-9]+$");
        Regex lowerCaseNextToNumber = new("(?<=[0-9])[a-z]");
        Regex upperCaseInside = new("(?<=[A-Z])[A-Z]+?((?=[A-Z][a-z])|(?=[0-9]))");

        // 用undescore替换空白，然后用空字符串替换所有无效字符
        IEnumerable<string> pascalCase = invalidCharsRgx.Replace(whiteSpace.Replace(original, "_"), string.Empty)

            // 用下划线分割
            .Split(new[] {'_'}, StringSplitOptions.RemoveEmptyEntries)

            // 首字母设置为大写
            .Select(w => startsWithLowerCaseChar.Replace(w, m => m.Value.ToUpper()))

            // 如果没有下一个小写字母(ABC -> Abc)，则将第二个及所有后面的大写字母替换为小写字母
            .Select(w => firstCharFollowedByUpperCasesOnly.Replace(w, m => m.Value.ToLower()))

            // 数字后面的第一个小写字母 设置大写(Ab9cd -> Ab9Cd)
            .Select(w => lowerCaseNextToNumber.Replace(w, m => m.Value.ToUpper()))

            // 第二个小写字母和下一个大写字母，除非最后一个字母后跟任何小写字母 (ABcDEf -> AbcDef)
            .Select(w => upperCaseInside.Replace(w, m => m.Value.ToLower()));

        return string.Concat(pascalCase);
    }

    #endregion

    #region IsEmpty

    /// <summary>
    ///     是否为空.
    /// </summary>
    /// <param name="value">值.</param>
    public static bool IsEmpty(this string value)
    {
        return string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    ///     是否为空.
    /// </summary>
    /// <param name="value">值.</param>
    public static bool IsEmpty(this Guid? value)
    {
        if (value == null)
        {
            return true;
        }

        return IsEmpty(value.Value);
    }

    /// <summary>
    ///     是否为空.
    /// </summary>
    /// <param name="value">值.</param>
    public static bool IsEmpty(this Guid value)
    {
        if (value == Guid.Empty)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    ///     是否为空.
    /// </summary>
    /// <param name="value">值.</param>
    public static bool IsEmpty(this object value)
    {
        if (value != null && !string.IsNullOrEmpty(value.ToString()))
        {
            return false;
        }

        return true;
    }

    /// <summary>
    ///     判断是否为Null或者空.
    /// </summary>
    /// <param name="obj">对象.</param>
    /// <returns></returns>
    public static bool IsNullOrEmpty(this object obj)
    {
        if (obj == null)
        {
            return true;
        }

        string objStr = obj.ToString();
        return string.IsNullOrEmpty(objStr);
    }

    #endregion

    #region IsNotEmptyOrNull

    /// <summary>
    ///     不为空.
    /// </summary>
    /// <param name="thisValue"></param>
    /// <returns></returns>
    public static string ObjToString(this object thisValue)
    {
        if (thisValue != null)
        {
            return thisValue.ToString().Trim();
        }

        return string.Empty;
    }

    /// <summary>
    ///     不为空.
    /// </summary>
    /// <param name="thisValue"></param>
    /// <returns></returns>
    public static bool IsNotEmptyOrNull(this object? thisValue)
    {
        if (thisValue == null)
        {
            return false;
        }

        return ObjToString(thisValue) != string.Empty && ObjToString(thisValue) != "undefined" && ObjToString(thisValue) != "null";
    }

    #endregion

    #region List

    /// <summary>
    ///  DataTable转List
    /// </summary>
    /// <param name="dataTable"></param>
    /// <returns></returns>
    public static List<dynamic> ConvertDataTableToList(this DataTable dataTable)  
    {  
        List<dynamic> list = new List<dynamic>();  

        foreach (DataRow row in dataTable.Rows)  
        {  
            var expando = new System.Dynamic.ExpandoObject() as IDictionary<string, Object>;  
            foreach (DataColumn column in dataTable.Columns)  
            {  
                expando[column.ColumnName] = row[column];  
            }  
            list.Add(expando);  
        }  

        return list;  
    } 

    /// <summary>
    ///     嵌套List解析
    ///     仅限于列表查询条件多选.
    /// </summary>
    /// <param name="list"></param>
    /// <returns></returns>
    public static List<string> ParseToNestedList(this List<List<string>> list)
    {
        List<string> result = new();
        if (list != null && list.Count > 0)
        {
            foreach (List<string> item in list)
            {
                result.Add(item.Last());
            }
        }

        return result;
    }

    #endregion

    /// <summary>
    ///     获取服务地址
    /// </summary>
    /// <returns></returns>
    public static string GetLocalhost()
    {
        string result = $"{App.HttpContext.Request.Scheme}://{App.HttpContext.Request.Host.Value}";
        // 代理模式：获取真正的本机地址
        // X-Original-Host=原始请求
        // X-Forwarded-Server=从哪里转发过来
        if (App.HttpContext.Request.Headers.ContainsKey("X-Original-Host"))
        {
            result = $"{App.HttpContext.Request.Scheme}://{App.HttpContext.Request.Headers["X-Original-Host"]}";
        }

        return result;
    }
}