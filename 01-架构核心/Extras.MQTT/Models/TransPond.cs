using Extras.MQTT.Dto;

namespace Extras.MQTT.Models;

public class TransPond
{
    /// <summary>
    ///     
    /// </summary>
    public string Identifier { get; set; }
    public TransPondTypeEnum TransPondType { get; set; }
    public string Config { get; set; }
    public bool Enable { get; set; }
    public bool Master { get; set; }
    public List<TransPondTopic> TransPondTopic { get; set; }
    public MqttConfModel MqttConfModel { get; set; }
    public bool IsConnected { get; set; }
    public string? CreatedTime { get; set; }
    public string? UpdatedTime { get; set; }
    public long? CreatedUserId { get; set; }
    public long? UpdatedUserId { get; set; }
    public string? CreatedUserName { get; set; }
    public string? UpdatedUserName { get; set; }
    public long Id { get; set; }
}

public class TransPondTopic
{
    public string Topic { get; set; }
    public TransPondTopicPurposeEnum TransPondTopicPurpose { get; set; }
    public int Rule { get; set; }
    public TransPondTopicTypeEnum TransPondTopicType { get; set; }
    public long TransPondId { get; set; }
    public string Config { get; set; }
    public string Description { get; set; }
    public int Qos { get; set; }
    public int TimeOut { get; set; }
    public string QosName { get; set; }
    public string TransPondTopicPurposeName { get; set; }
    public string TransPondTopicTypeName { get; set; }
    public string RuleName { get; set; }
    public long Id { get; set; }
}

public class MqttConfModel
{
    public string Ip { get; set; }
    public int Port { get; set; }
    public string UserName { get; set; }
    public string Password { get; set; }
    public string ClientId { get; set; }
    public int KeepAlive { get; set; }
    public IoTPlatformType IoTPlatformType { get; set; }
    public int OffLinePubSpeedType { get; set; }
    public List<MqttConfigExtend> MqttConfigExtend { get; set; }
    public int SendType { get; set; }
    public int PubPeriod { get; set; }
    public string PubPeriodUnit { get; set; }
}

/// <summary>
///     上行转发配置表
/// </summary>
public class MqttConfigExtend
{
    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }
}