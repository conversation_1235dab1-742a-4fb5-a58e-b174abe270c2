<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="TDengIne\Dto\" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Extras.DatabaseAccessor.SqlSugar\Extras.DatabaseAccessor.SqlSugar.csproj" />
      <ProjectReference Include="..\IotPlatform.Core\IotPlatform.Core.csproj" />
    </ItemGroup>

</Project>
