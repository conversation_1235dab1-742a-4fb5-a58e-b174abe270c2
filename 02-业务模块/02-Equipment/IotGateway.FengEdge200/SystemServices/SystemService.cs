using System.Text;
using Common.Extension;
using Feng.IotGateway.Core.Const;
using Furion.LinqBuilder;
using Furion.Logging;
using Furion.TaskQueue;
using Furion.Templates;
using IotGateway.Equipment.SystemServices.Dto;
using IotGateway.Equipment.Util;
using Microsoft.AspNetCore.Authorization;
using Console = Feng.Common.Extension.Console;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.SystemServices;

/// <summary>
///     网关默认配置
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-12-19
/// </summary>
[ApiDescriptionSettings("网关配置")]
public class SystemService : IDynamicApiController, ITransient
{
    #region 网关授权

    private const string BasePath = "/etc/DeviceConf/";

    /// <summary>
    ///     网卡名称集合
    /// </summary>
    [HttpGet("/shell/networks")]
    public async Task<dynamic> GetNetworks()
    {
        var result = await ShellUtil.Bash("ip addr show | grep inet | awk '$NF ~ /^[a-zA-Z0-9]+$/ && $NF !~ /^(docker0|host|link|lo|tap0)$/ {print $2,$NF}'");
        var lines = result.Split('\n');
        var ipToInterface = new Dictionary<string, string> { { "不指定", "" } };
        foreach (var line in lines)
        {
            var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 2) continue;
            var ipAddress = parts[0];
            var interfaceName = parts[1];
            ipToInterface[interfaceName] = ipAddress;
        }

        return ipToInterface;
    }

    /// <summary>
    ///     支持串口集合
    /// </summary>
    [HttpGet("/shell/serialPorts")]
    public Task<dynamic> GetSerialPorts()
    {
        return Task.FromResult<dynamic>(new Dictionary<string, string>
        {
            { "/dev/ttyS5", "ttyS5： A1/B1(485-1)" },
            { "/dev/ttyS1", "ttyS1： A2/B2(485-2)" },
            { "/dev/ttyS3", "ttyS3： T3/R3(232-1) A3/B3(485-3)" },
            { "/dev/ttyS0", "ttyS0： T4/R4(232-2) A4/B4(485-4)" },
        });
    }

    /// <summary>
    ///     授权
    /// </summary>
    /// <returns></returns>
    [HttpPost("/authorization/set")]
    public async Task<SetAuthorizeOutput> SetAuthorization(BaseId<string> input)
    {
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(input.Id, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            throw new Exception("Sn不一致！");
        //校验是否到期
        if (DateTime.Now() >= Convert.ToDateTime(authorize.EndTime))
            throw new Exception("已过期！");

        await File.WriteAllTextAsync(BasePath + "key.txt", input.Id);
        if (!File.Exists(BasePath + "Ident.txt"))
        {
            await File.WriteAllTextAsync(BasePath + "Ident.txt", "fengEdge-200");
            authorize.Ident = "fengEdge-200";
        }
        else
        {
            var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
            authorize.Ident = readIdent ?? "fengEdge-200";
        }

        return authorize;
    }

    /// <summary>
    ///     校验是否授权
    /// </summary>
    /// <returns></returns>
    public async Task<bool> Authorization()
    {
        if (!File.Exists(BasePath + "key.txt"))
            File.CreateText(BasePath + "key.txt");
        var value = await File.ReadAllTextAsync(BasePath + "key.txt");
        if (value == null)
            throw Oops.Oh("未授权！");
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(value, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            return false;
        return DateTime.Now() < Convert.ToDateTime(authorize.EndTime);
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/getAuthorization")]
    [AllowAnonymous]
    public async Task<SetAuthorizeOutput> GetAuthorization()
    {
        var setAuthor = await AuthorizationUtil.GetAuthorization();
        if (setAuthor != null) return setAuthor;
        var sn = await GatewayUtil.GetSn();
        var auth = new SetAuthorizeOutput
        {
            Fingerprint = "",
            Ident = "fengEdge-200",
            Sn = sn,
            Version = MachineUtil.Version,
            DeviceNumber = 0,
            EndTime = null,
            VariableNumber = 0
        };
        if (!File.Exists(BasePath + "Ident.txt")) return auth;
        var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
        auth.Ident = readIdent ?? "fengEdge-200";
        return auth;
    }

    #endregion

    #region 网络配置

    /// <summary>
    ///     保存服务器配置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/save")]
    public async Task SetNetWork(NetworkSettingModel input)
    {
        Console.WriteLine("开始保存网络配置...");
        Console.WriteLine($"输入参数: {JSON.Serialize(input)}");

        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .IgnoreUnmatchedProperties()
            .Build();

        var serializer = new SerializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .IgnoreFields()
            .Build();

        var defNetwork = "";

        // 处理有线网络配置
        try
        {
            Console.WriteLine("读取有线网络配置文件...");
            var networkYaml = await File.ReadAllTextAsync("/etc/netplan/01-eth.yaml");
            Console.WriteLine($"当前有线网络配置:\n{networkYaml}");

            var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);

            foreach (var network in input.Network)
            {
                Console.WriteLine($"配置网卡 {network.NetWorkName}");
                var networkThereNets = new NetworkThereNets
                {
                    Dhcp = network.NetWorkType == NetWorkTypeEnum.Dhcp ? "true" : null,
                    Gateway = null
                };

                if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
                {
                    if (network.IPAddress.IsNotNull())
                    {
                        networkThereNets.Addresses ??= new List<string>();
                        networkThereNets.Addresses.Add(network.IPAddress + "/24");
                        Console.WriteLine($"设置静态IP: {network.IPAddress}/24");
                    }
                }
                else
                {
                    Console.WriteLine("使用DHCP配置");
                }

                if (network.Dns.IsNotNull() || network.DnsBack.IsNotNull())
                {
                    networkThereNets.nameservers = new Nameservers { Addresses = new List<string>() };
                    if (network.Dns.IsNotNull())
                    {
                        networkThereNets.nameservers.Addresses.Add(network.Dns);
                        Console.WriteLine($"设置主DNS: {network.Dns}");
                    }

                    if (network.DnsBack.IsNotNull())
                    {
                        networkThereNets.nameservers.Addresses.Add(network.DnsBack);
                        Console.WriteLine($"设置备用DNS: {network.DnsBack}");
                    }
                }

                networkThereNets.Routes ??= new List<Routes>();
                var routes = new Routes
                {
                    To = "0.0.0.0/0",
                    Via = network.Gateway,
                    Metric = "101"
                };
                if (network.DefRoute)
                {
                    defNetwork = network.NetWorkName;
                    routes.Metric = "100";
                    Console.WriteLine($"设置 {network.NetWorkName} 为默认路由");
                }

                networkThereNets.Routes.Add(routes);
                networkInit.Network.Ethernets ??= new Dictionary<string, NetworkThereNets>();
                networkInit.Network.Ethernets[network.NetWorkName] = networkThereNets;

                Console.WriteLine($"{network.NetWorkName} 配置完成: {JSON.Serialize(networkThereNets)}");
            }

            var yaml = serializer.Serialize(networkInit);

            Console.WriteLine($"生成的有线网络配置:\n{yaml}");
            await File.WriteAllTextAsync("/etc/netplan/01-eth.yaml", yaml);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"保存有线网络配置失败: {ex.Message}");
            throw;
        }

        // 处理 WiFi 配置
        if (input.Wifi != null && input.Wifi.Enable)
            try
            {
                Console.WriteLine("开始配置WiFi...");
                NetworkInit wifiInit;
                string wifiYaml;

                try
                {
                    Console.WriteLine("读取现有WiFi配置...");
                    wifiYaml = await File.ReadAllTextAsync("/etc/netplan/02-wifi.yaml");
                    wifiInit = deserializer.Deserialize<NetworkInit>(wifiYaml);
                    Console.WriteLine($"当前WiFi配置:\n{wifiYaml}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"读取WiFi配置失败，创建新配置: {ex.Message}");
                    wifiInit = new NetworkInit
                    {
                        Network = new Network
                        {
                            Version = 2,
                            Wifis = new Dictionary<string, NetworkThereNets>()
                        }
                    };
                }

                Console.WriteLine($"配置WiFi连接: {input.Wifi.UserName}");
                var wifiConfig = new NetworkThereNets
                {
                    AccessPoints = new Dictionary<string, AccessPoint>
                    {
                        [input.Wifi.UserName] = new()
                        {
                            Password = input.Wifi.Password
                        }
                    },
                    Dhcp = input.Wifi.NetWorkType == NetWorkTypeEnum.Dhcp ? "true" : null,
                    Optional = false
                };

                if (input.Wifi.NetWorkType != NetWorkTypeEnum.Dhcp)
                {
                    Console.WriteLine("配置WiFi静态IP设置");
                    wifiConfig.Addresses = new List<string> { $"{input.Wifi.IPAddress}/24" };

                    if (input.Wifi.Dns.IsNotNull() || input.Wifi.DnsBack.IsNotNull())
                    {
                        wifiConfig.nameservers = new Nameservers { Addresses = new List<string>() };
                        if (input.Wifi.Dns.IsNotNull())
                        {
                            wifiConfig.nameservers.Addresses.Add(input.Wifi.Dns);
                            Console.WriteLine($"设置WiFi DNS: {input.Wifi.Dns}");
                        }

                        if (input.Wifi.DnsBack.IsNotNull())
                        {
                            wifiConfig.nameservers.Addresses.Add(input.Wifi.DnsBack);
                            Console.WriteLine($"设置WiFi备用DNS: {input.Wifi.DnsBack}");
                        }
                    }

                    wifiConfig.Routes = new List<Routes>
                    {
                        new()
                        {
                            To = "0.0.0.0/0",
                            Via = input.Wifi.Gateway,
                            Metric = input.Wifi.DefRoute ? "100" : "101"
                        }
                    };

                    if (input.Wifi.DefRoute) Console.WriteLine("设置WiFi为默认路由");
                }
                else
                {
                    Console.WriteLine("WiFi使用DHCP配置");
                }

                wifiInit.Network.Wifis["wlan0"] = wifiConfig;

                Console.WriteLine($"WiFi配置完成: {JSON.Serialize(wifiConfig)}");
                wifiYaml = serializer.Serialize(wifiInit);
                Console.WriteLine($"生成的WiFi配置:\n{wifiYaml}");

                await File.WriteAllTextAsync("/etc/netplan/02-wifi.yaml", wifiYaml);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存WiFi配置失败: {ex.Message}");
                throw;
            }
        else
            Console.WriteLine("WiFi未启用，跳过配置");

        // 处理 4G 配置
        if (input.Mobile != null && input.Mobile.Enable)
            try
            {
                NetworkInit mobileInit;
                string mobileYaml;
                Console.WriteLine("读取4G配置文件...");
                mobileYaml = await File.ReadAllTextAsync("/etc/netplan/03-4g.yaml");
                Console.WriteLine($"当前4G配置:\n{mobileYaml}");
                mobileInit = deserializer.Deserialize<NetworkInit>(mobileYaml);

                var mobileConfig = new NetworkThereNets
                {
                    Dhcp = "true", // 4G 配置使用 DHCP
                    Optional = true,
                    Routes = new List<Routes>
                    {
                        new()
                        {
                            To = "0.0.0.0/0",
                            Via = "0.0.0.0",
                            Metric = input.Mobile.DefRoute ? "100" : "101"
                        }
                    }
                };

                mobileInit.Network.Ethernets["usb0"] = mobileConfig;

                mobileYaml = serializer.Serialize(mobileInit);
                Console.WriteLine($"生成的4G配置:\n{mobileYaml}");
                await File.WriteAllTextAsync("/etc/netplan/03-4g.yaml", mobileYaml);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存4G配置失败: {ex.Message}");
                throw;
            }

        // 应用网络配置
        try
        {
            if (!string.IsNullOrEmpty(defNetwork))
            {
                await File.WriteAllTextAsync(GatewayFilePath.DefRoutePath, defNetwork);
                Console.WriteLine($"保存默认路由: {defNetwork}");
            }

            await TaskQueued.EnqueueAsync(async (_, cancellationToken) =>
            {
                Console.WriteLine("应用网络配置...");
                await ShellUtil.Bash("netplan apply");
                Console.WriteLine("网络配置应用完成");
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"应用网络配置失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    ///     获取服务器ip配置列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/list")]
    [AllowAnonymous]
    public async Task<NetworkSettingModel> NetWorkList()
    {
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .IgnoreUnmatchedProperties()
            .Build();

        Console.WriteLine("开始读取网络配置...");
        var resultData = new NetworkSettingModel
        {
            Network = new List<NetworkInfoModel>()
        };

        // 读取有线网络配置
        try
        {
            Console.WriteLine("读取有线网络配置文件: /etc/netplan/01-eth.yaml");
            var networkYaml = await File.ReadAllTextAsync("/etc/netplan/01-eth.yaml");
            Console.WriteLine($"有线网络配置内容:\n{networkYaml}");

            var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
            Console.WriteLine($"解析后的有线网络配置: {JSON.Serialize(networkInit)}");

            foreach (var ethernets in networkInit.Network.Ethernets)
            {
                Console.WriteLine($"处理网卡 {ethernets.Key} 的配置");
                var netWork = new NetworkInfoModel
                {
                    NetWorkName = ethernets.Key
                };
                if (ethernets.Value.Dhcp != null)
                {
                    netWork.DefRoute = false;
                    netWork.NetWorkType = NetWorkTypeEnum.Dhcp;
                }
                else
                {
                    netWork.Gateway = ethernets.Value.Gateway ?? ethernets.Value.Routes.FirstOrDefault()?.Via;
                    netWork.DefRoute = false;
                    netWork.SubnetMark = "*************";
                    netWork.IPAddress = ethernets.Value.Addresses.FirstOrDefault()?.Replace("/24", "");
                    netWork.NetWorkType = NetWorkTypeEnum.Static;
                }

                if (ethernets.Value.nameservers != null && ethernets.Value.nameservers.Addresses != null)
                {
                    if (ethernets.Value.nameservers.Addresses.Count == 2)
                    {
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                        netWork.DnsBack = ethernets.Value.nameservers.Addresses[1];
                    }

                    if (ethernets.Value.nameservers.Addresses.Count == 1)
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                }

                if (File.Exists(GatewayFilePath.DefRoutePath))
                    netWork.DefRoute = await File.ReadAllTextAsync(GatewayFilePath.DefRoutePath) == netWork.NetWorkName;
                resultData.Network.Add(netWork);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取有线网络配置失败: {ex.Message}");
        }

        // 读取WiFi配置
        try
        {
            Console.WriteLine("读取WiFi配置文件: /etc/netplan/02-wifi.yaml");
            var wifiYaml = await File.ReadAllTextAsync("/etc/netplan/02-wifi.yaml");
            Console.WriteLine($"WiFi配置内容:\n{wifiYaml}");

            var wifiInit = deserializer.Deserialize<NetworkInit>(wifiYaml);
            Console.WriteLine($"解析后的WiFi配置: {JSON.Serialize(wifiInit)}");

            if (wifiInit.Network.Wifis != null)
            {
                Console.WriteLine($"WiFi网络列表: {JSON.Serialize(wifiInit.Network.Wifis)}");
                if (wifiInit.Network.Wifis.TryGetValue("wlan0", out var wifiConfig))
                {
                    Console.WriteLine($"找到wlan0配置: {JSON.Serialize(wifiConfig)}");
                    resultData.Wifi = new WifiModel
                    {
                        Enable = true,
                        NetWorkName = "wlan0",
                        UserName = wifiConfig.AccessPoints?.Keys.FirstOrDefault(),
                        Password = wifiConfig.AccessPoints?.Values.FirstOrDefault()?.Password,
                        NetWorkType = wifiConfig.Dhcp != null ? NetWorkTypeEnum.Dhcp : NetWorkTypeEnum.Static
                    };
                    Console.WriteLine($"转换后的WiFi配置: {JSON.Serialize(resultData.Wifi)}");

                    if (resultData.Wifi.NetWorkType == NetWorkTypeEnum.Static)
                    {
                        Console.WriteLine("处理静态IP配置");
                        resultData.Wifi.IPAddress = wifiConfig.Addresses?.FirstOrDefault()?.Replace("/24", "");
                        resultData.Wifi.Gateway = wifiConfig.Gateway ?? wifiConfig.Routes?.FirstOrDefault()?.Via;
                        resultData.Wifi.SubnetMark = "*************";
                        Console.WriteLine($"静态IP信息: IP={resultData.Wifi.IPAddress}, Gateway={resultData.Wifi.Gateway}");

                        if (wifiConfig.nameservers?.Addresses != null)
                        {
                            Console.WriteLine($"DNS服务器: {JSON.Serialize(wifiConfig.nameservers.Addresses)}");
                            if (wifiConfig.nameservers.Addresses.Count >= 1)
                                resultData.Wifi.Dns = wifiConfig.nameservers.Addresses[0];
                            if (wifiConfig.nameservers.Addresses.Count >= 2)
                                resultData.Wifi.DnsBack = wifiConfig.nameservers.Addresses[1];
                        }
                    }

                    resultData.Wifi.DefRoute = wifiConfig.Routes?.FirstOrDefault()?.Metric == "100";
                    Console.WriteLine($"是否为默认路由: {resultData.Wifi.DefRoute}");
                }
                else
                {
                    Console.WriteLine("未找到wlan0配置");
                }
            }
            else
            {
                Console.WriteLine("WiFi配置为空");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取WiFi配置失败: {ex.Message}");
            resultData.Wifi = new WifiModel
            {
                Enable = false,
                NetWorkType = NetWorkTypeEnum.Dhcp
            };
            Console.WriteLine("使用默认WiFi配置");
        }

        // 读取4G配置
        try
        {
            Console.WriteLine("读取4G配置文件: /etc/netplan/03-4g.yaml");
            var mobileYaml = await File.ReadAllTextAsync("/etc/netplan/03-4g.yaml");
            Console.WriteLine($"4G配置内容:\n{mobileYaml}");

            var mobileInit = deserializer.Deserialize<NetworkInit>(mobileYaml);
            Console.WriteLine($"解析后的4G配置: {JSON.Serialize(mobileInit)}");

            if (mobileInit.Network.Ethernets != null && mobileInit.Network.Ethernets.TryGetValue("usb0", out var mobileConfig))
            {
                Console.WriteLine($"找到usb0配置: {JSON.Serialize(mobileConfig)}");
                resultData.Mobile = new MobileModel
                {
                    Enable = true,
                    DefRoute = mobileConfig.Routes?.FirstOrDefault()?.Metric == "100",
                    Apn = "internet",
                    NetworkType = 1,
                    OperatorName = "中国电信",
                    UserName = "",
                    Password = "",
                    IsApn = true
                };
                Console.WriteLine($"转换后的4G配置: {JSON.Serialize(resultData.Mobile)}");
            }
            else
            {
                Console.WriteLine("未找到usb0配置");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"读取4G配置失败: {ex.Message}");
            resultData.Mobile = new MobileModel
            {
                Enable = false,
                DefRoute = false,
                Apn = "internet",
                NetworkType = 1,
                OperatorName = "中国电信",
                UserName = "",
                Password = "",
                IsApn = true
            };
            Console.WriteLine("使用默认4G配置");
        }

        Console.WriteLine($"最终网络配置: {JSON.Serialize(resultData)}");
        return resultData;
    }

    #endregion

    #region Shell

    /// <summary>
    ///     IpAddress
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/ipAddress")]
    public async Task<List<string>> Ip()
    {
        var networkNames = new List<string> { "eth0", "eth1", "wlan0", "usb0" };
        var ipList = new List<string>();
        foreach (var networkName in networkNames)
        {
            var config = await GatewayUtil.IfConfig(networkName);
            // Console.WriteLine($"networkName：{networkName}：" +config);
            if (config.IsNullOrEmpty())
                continue;
            var ip = config.Contains("addr:") ? config.GetRegex("addr:", " ") : "";
            var bcast = config.Contains("Bcast:") ? config.GetRegex("Bcast:", " ") : "";
            var mask = config.Contains("Mask:") ? config.GetRegex("Mask:", " ") : "";
            var hWaddr = config.Contains("HWaddr:") ? config.GetRegex("HWaddr:", " ") : "";

            var content = new List<string>();
            if (ip.IsNotNullOrWhiteSpace())
                content.Add("##IP地址##" + "【" + ip + "】");
            if (bcast.IsNotNullOrWhiteSpace())
                content.Add("##广播地址##" + "【" + bcast + "】");
            if (mask.IsNotNullOrWhiteSpace())
                content.Add("##子网掩码##" + "【" + mask + "】");
            if (hWaddr.IsNotNullOrWhiteSpace())
                content.Add("##Mac地址##" + "【" + hWaddr + "】");

            var message = TP.Wrapper(networkName, $"##时间## 【{DateTime.NowString()}】", content.ToArray());
            ipList.Add(message);
        }

        return ipList;
    }

    #endregion

    #region NTP设置

    /// <summary>
    ///     ntp配置
    /// </summary>
    /// <returns></returns>
    [HttpGet("/ntp/info")]
    public async Task<dynamic> Info()
    {
        var status = await ShellUtil.Bash("timedatectl");
        status = status.TrimStart();
        var statusSpl = status.Split("\n");
        var runStatus = false;
        var synchronized = false;
        foreach (var spl in statusSpl)
        {
            if (!spl.Contains("System clock synchronized") && !spl.Contains("NTP service")) continue;
            var splObj = spl.Split(":");
            if (spl.Contains("System clock synchronized"))
            {
                if (splObj.Length > 1)
                    synchronized = splObj[1].Trim(' ') == "yes";
            }
            else
            {
                if (splObj.Length > 1)
                    runStatus = splObj[1].Trim(' ') == "active";
            }
        }

        // 读取ntp配置文件
        var timeSyncd = File.OpenText("/etc/systemd/timesyncd.conf");
        var ntp = "ntp1.aliyun.com";
        var interVal = 2048;
        while (!timeSyncd.EndOfStream)
        {
            var line = await timeSyncd.ReadLineAsync();
            if (line.Contains("NTP=") && !line.Contains("FallbackNTP="))
            {
                if (!line.EndsWith(" "))
                {
                    var ntpSp = line.Trim().Split("NTP=");
                    if (ntpSp.Length > 1)
                    {
                        foreach (var sp in ntpSp)
                            Log.Information(sp);
                        ntp = string.IsNullOrWhiteSpace(ntpSp[1]) ? "ntp1.aliyun.com" : ntpSp[1];
                    }
                }

                continue;
            }

            if (line.Contains("PollIntervalMaxSec="))
            {
                var ntpSp = line.Split("PollIntervalMaxSec=");
                if (ntpSp.Length > 1)
                    interVal = Convert.ToInt32(ntpSp[1]) / 60;
            }
        }

        timeSyncd.Dispose();
        return new
        {
            DateTime = DateTime.Now(),
            RunStatus = runStatus,
            Synchronized = synchronized,
            Ntp = ntp,
            InterVal = interVal
        };
    }

    /// <summary>
    ///     保存ntp设置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/ntp/save")]
    public async Task NtpSave(NtpSaveInput input)
    {
        if (input.RunStatus)
        {
            await ShellUtil.Bash("mv /usr/sbin/ntpd /usr/sbin/ntpd-bak");
            // 更新配置
            var timeSyncd = File.OpenText("/etc/systemd/timesyncd.conf");
            var builder = new StringBuilder();
            while (!timeSyncd.EndOfStream)
            {
                var line = await timeSyncd.ReadLineAsync();
                if (line.Contains("NTP=") && !line.Contains("FallbackNTP="))
                {
                    line = $"NTP={input.Ntp}";
                    builder.AppendLine(line);
                    continue;
                }

                if (line.Contains("PollIntervalMaxSec="))
                {
                    line = $"PollIntervalMaxSec={input.InterVal * 60}";
                    builder.AppendLine(line);
                    continue;
                }

                builder.AppendLine(line);
            }

            await File.WriteAllTextAsync("/etc/systemd/timesyncd.conf", builder.ToString());
            // 重启服务
            await ShellUtil.Bash("sudo systemctl restart systemd-timesyncd.service");
            timeSyncd.Dispose();
        }
        else
        {
            await ShellUtil.Bash("sudo timedatectl set-ntp no");
        }
    }

    #endregion
}