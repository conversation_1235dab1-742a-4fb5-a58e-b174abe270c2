using System.Dynamic;
using System.Globalization;

namespace IotGateway.Equipment.Util;

/// <summary>
///     网关工具
/// </summary>
public static class GatewayUtil
{
    /// <summary>
    ///     获取资源使用信息
    /// </summary>
    /// <returns></returns>
    public static async Task<dynamic> UseInfo()
    {
        try
        {
            var ramInfo = MachineUtil.RamInfo();
            var cpuRate = Math.Ceiling(double.Parse(await CpuRate()));
            var hddInfo = ReadHddInfo();
            var networkList = new[] { "eth0", "eth1", "eth2", "wlan0" };
            var ipAddr = new Dictionary<string, string>();

            foreach (var network in networkList)
            {
                var ip = await ReadIp(network);
                if (string.IsNullOrEmpty(ip)) continue;

                ipAddr.Add(network, ip);
                UpdateMachineIp(network, ip);
            }

            MachineUtil.UseInfo = new UserInfo
            {
                Network = ipAddr,
                DiskInfo = hddInfo,
                MemInfo = ramInfo,
                CpuRate = cpuRate
            };
            return MachineUtil.UseInfo;
        }
        catch (Exception ex)
        {
            throw new Exception($"获取资源使用信息失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 更新网关IP
    /// </summary>
    /// <param name="network">网络名称</param>
    /// <param name="ip">IP地址</param>
    private static void UpdateMachineIp(string network, string ip)
    {
        switch (network)
        {
            case "eth0":
                MachineUtil.Clay.Eth0 = ip; // 更新网关IP
                break;
            case "eth1":
                MachineUtil.Clay.Eth1 = ip; // 更新网关IP
                break;
            case "eth2" or "usb0":
                MachineUtil.Clay.Eth2 = ip; // 更新网关IP
                break;
            case "wlan0":
                MachineUtil.Clay.Wifi = ip; // 更新网关IP
                break;
        }
    }

    /// <summary>
    /// 读取磁盘信息
    /// </summary>
    /// <returns></returns>
    public static DiskInfo ReadHddInfo()
    {
        try
        {
            // 读取磁盘信息
            var hddInfo = ShellUtil.Bash("df -hk").GetAwaiter().GetResult();
            return ParseDiskInfo(hddInfo);
        }
        catch (Exception ex)
        {
            throw new Exception($"读取磁盘信息失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 解析磁盘信息
    /// </summary>
    /// <param name="hddInfo"></param>
    /// <returns></returns>
    private static DiskInfo ParseDiskInfo(string hddInfo)
    {
        var lines = hddInfo.Split('\n'); // 分割磁盘信息    
        long totalSize = 0; // 总大小
        long totalUsed = 0; // 已使用
        long totalAvailable = 0; // 可用

        foreach (var line in lines)
        {
            // 跳过标题行
            if (line.StartsWith("Filesystem") || string.IsNullOrWhiteSpace(line)) continue;

            var items = line.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (items.Length < 6) continue;  // 确保有足够的数据列

            // 尝试解析每一列的数据
            if (!long.TryParse(items[1], out var size) ||
                !long.TryParse(items[2], out var used) ||
                !long.TryParse(items[3], out var available))
            {
                continue;
            }

            totalSize += size; // 总大小
            totalUsed += used; // 已使用
            totalAvailable += available; // 可用
        }

        if (totalSize == 0) return null;

        // 计算总使用率
        var usageRate = Math.Round((double)totalUsed / totalSize * 100, 2);

        return new DiskInfo
        {
            DiskSize = totalSize, // 总大小
            DiskUsed = totalUsed, // 已使用
            DiskAvailable = totalAvailable, // 可用
            DiskRate = usageRate // 使用率
        };
    }

    /// <summary>
    ///     获取CPU使用率
    /// </summary>
    /// <returns></returns>
    public static async Task<string> CpuRate()
    {
        string cpuRate;
        var output = await ShellUtil.Bash("top -b -n1 | grep \"Cpu(s)\" | awk '{print $2}'");
        cpuRate = output.Trim();
        return cpuRate;
    }

    /// <summary>
    ///     获取Sn码
    /// </summary>
    /// <returns></returns>
    public static async Task<string> GetSn()
    {
        // var val = await ShellUtil.Bash("/hogo/utility/hogodevinfotools sn -r");
        var val = await ShellUtil.Bash("/home/<USER>/utility/bin/hogodevinfotools sn -r");
        var number = val.Replace("read the sn :", "").Trim();
        return number;
    }

    /// <summary>
    ///     IfConfig
    /// </summary>
    /// <returns></returns>
    public static async Task<string> IfConfig(string ipName)
    {
        var hddInfo = await ShellUtil.Bash($"ifconfig {ipName}");
        var lines = hddInfo.Split('\n');
        var output = "";
        
        foreach (var item in lines)
        {
            if (item.Contains("inet "))
            {
                var spList = item.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                if (spList.Length > 4)
                    output += $"addr:{spList[1]} Mask:{spList[3]} Bcast:{spList[5]}";
                else
                    output += $"addr:{spList[1]} Mask:{spList[3]} ";
            }

            if (item.Contains("ether"))
            {
                var spList = item.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                output += $" HWaddr:{spList[1]} ";
                return output;
            }
        }

        return output;
    }

    /// <summary>
    ///     获取网关 IP信息
    /// </summary>
    /// <returns></returns>
    private static async Task<string> ReadIp(string networkName)
    {
        var ifconfigOutput = await ShellUtil.Bash("ifconfig");
        var lines = ifconfigOutput.Split('\n');
        var isTargetNetwork = false;

        foreach (var line in lines)
        {
            if (line.StartsWith(networkName))
            {
                isTargetNetwork = true;
                continue;
            }

            if (!isTargetNetwork || !line.Contains("inet")) continue;

            var parts = line.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            return parts.Length > 1 ? parts[1].Trim() : null;
        }

        return null;
    }
}