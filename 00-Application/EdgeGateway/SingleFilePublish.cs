using System.Reflection;
using Furion;

namespace EdgeGateway;

public class SingleFilePublish : ISingleFilePublish
{
    public Assembly[] IncludeAssemblies()
    {
        return Array.Empty<Assembly>();
    }

    public string[] IncludeAssemblyNames()
    {
        return new[]
        {
            "EdgeGateway.Mapper",
            "EdgeGateway.Core",
            "EdgeGateway.Web.Core"
        };
    }
}