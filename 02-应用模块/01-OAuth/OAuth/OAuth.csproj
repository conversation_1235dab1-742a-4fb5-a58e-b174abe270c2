<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\01-架构核心\Extras.DatabaseAccessor.SqlSugar\Extras.DatabaseAccessor.SqlSugar.csproj"/>
        <ProjectReference Include="..\..\00-Common\Common.Core\Common.Core.csproj"/>
        <ProjectReference Include="..\..\02-System\Systems.Core\Systems.Core.csproj"/>
        <ProjectReference Include="..\..\02-System\Systems.Entity\Systems.Entity.csproj"/>
    </ItemGroup>

</Project>
