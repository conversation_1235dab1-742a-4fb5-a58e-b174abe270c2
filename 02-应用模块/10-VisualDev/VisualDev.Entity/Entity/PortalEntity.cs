namespace VisualDev.Entity;

/// <summary>
///     门户表.
/// </summary>
[SugarTable("BASE_PORTAL")]
public class PortalEntity : EntityTenant
{
    /// <summary>
    ///     描述.
    /// </summary>
    [SugarColumn(ColumnName = "F_DESCRIPTION")]
    public string? Description { get; set; }

    /// <summary>
    ///     名称.
    /// </summary>
    [SugarColumn(ColumnName = "F_FULL_NAME")]
    public string FullName { get; set; }

    /// <summary>
    ///     编码.
    /// </summary>
    [SugarColumn(ColumnName = "F_EN_CODE")]
    public string EnCode { get; set; }

    /// <summary>
    ///     分类Id.
    /// </summary>
    [SugarColumn(ColumnName = "F_CATEGORY")]
    public long Category { get; set; }

    /// <summary>
    ///     锁定（0-锁定，1-自定义）.
    /// </summary>
    [SugarColumn(ColumnName = "F_ENABLED_LOCK")]
    public int? EnabledLock { get; set; }

    /// <summary>
    ///     状态（0-未发步，1-已发布，2-已修改）.
    /// </summary>
    [SugarColumn(ColumnName = "F_STATE")]
    public int? State { get; set; }

    /// <summary>
    ///     发布选中平台.
    /// </summary>
    [SugarColumn(ColumnName = "F_PLATFORM_RELEASE")]
    public string? PlatformRelease { get; set; }
    
    /// <summary>
    /// 排序码.
    /// </summary>
    [SugarColumn(ColumnName = "F_SORT_CODE", ColumnDescription = "排序码")]
    public virtual long? SortCode { get; set; }

    /// <summary>
    /// 获取或设置 启用标识
    /// 0-禁用,1-启用.
    /// </summary>
    [SugarColumn(ColumnName = "F_ENABLED_MARK", ColumnDescription = "启用标识")]
    public virtual int? EnabledMark { get; set; } = 1;

}