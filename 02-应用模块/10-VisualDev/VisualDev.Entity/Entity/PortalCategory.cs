namespace VisualDev.Entity;

/// <summary>
///     门户分类
/// </summary>
[SugarTable("base_portal_category")]
public class PortalCategory : EntityTenant
{
    /// <summary>
    ///     分类名称
    /// </summary>
    [SugarColumn(ColumnDescription = "分类名称")]
    public string Name { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父Id")]
    public long Pid { get; set; }

    #region 忽略字段

    /// <summary>
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<PortalCategory> Children { get; set; }

    #endregion
}