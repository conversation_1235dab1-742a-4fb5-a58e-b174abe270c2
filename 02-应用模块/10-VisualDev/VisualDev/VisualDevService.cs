namespace VisualDev;

/// <summary>
///     可视化开发基础 .
/// </summary>
[ApiDescriptionSettings("功能设计", Tag = "VisualDev", Order = 171)]
[Route("visualdev/")]
public class VisualDevService : IVisualDevService, IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<VisualDevEntity> _visualDevRepository;

    /// <summary>
    ///     切库.
    /// </summary>
    private readonly IDataBaseManager _changeDataBase;

    /// <summary>
    ///     用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    ///     在线开发运行服务.
    /// </summary>
    private readonly RunService _runService;

    private readonly SysMenuService _menuService;

    /// <summary>
    ///     初始化一个<see cref="VisualDevService" />类型的新实例.
    /// </summary>
    public VisualDevService(
        ISqlSugarRepository<VisualDevEntity> visualDevRepository,
        IDataBaseManager changeDataBase,
        IUserManager userManager,
        RunService runService, SysMenuService menuService)
    {
        _visualDevRepository = visualDevRepository;
        _userManager = userManager;
        _runService = runService;
        _menuService = menuService;
        _changeDataBase = changeDataBase;
    }

    #region Get

    /// <summary>
    ///     获取功能列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("list")]
    public async Task<dynamic> GetIntegrateList([FromQuery] VisualDevListQueryInput input)
    {
        SqlSugarPagedList<VisualDevIntergrateListOutput>? data = await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>()
            .WhereIF(!string.IsNullOrEmpty(input.keyword), it => it.FullName.Contains(input.keyword))
            .WhereIF(input.enableFlow != null, it => it.EnableFlow.Equals(input.enableFlow))
            .WhereIF(input.category > 0, it => it.Category == input.category)
            .Where(it => it.Type == 1 && it.WebType == 2)
            .OrderBy(it => it.CreatedTime, OrderByType.Desc)
            .Select(it => new VisualDevIntergrateListOutput
            {
                id = it.Id,
                fullName = it.FullName,
                enCode = it.EnCode,
                enableFlow = it.EnableFlow
            }).ToPagedListAsync(input.currentPage, input.pageSize);
        return data;
    }

    /// <summary>
    ///     已发布菜单路径.
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}/getReleaseMenu")]
    public async Task<dynamic> GetReleaseMenu(long id)
    {
        VisualDevEntity? visualDev = await _visualDevRepository.AsSugarClient().Queryable<VisualDevEntity>()
            .Where(it => it.Id == id)
            .FirstAsync();
        if (visualDev == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }
        SysMenu? menu = await _visualDevRepository.AsSugarClient().Queryable<SysMenu>()
            .FirstAsync(u => u.ModuleId == visualDev.Id);
        string webPath = await _menuService.GetMenuTreeByModuleId(visualDev.Id);
        return new
        {
            webMenuPath = webPath,
            pcIsRelease = 1,
            pcModuleParentId = menu?.Pid == 0 ? ((await _visualDevRepository.AsSugarClient().Queryable<SysService>().FirstAsync(f => f.Code == menu.Application))?.Id ?? 0) : menu?.Pid,
            pcSystemId = menu?.Application,
            code = menu != null
                ? (await _visualDevRepository.AsSugarClient().Queryable<SysMenu>()
                    .FirstAsync(u => u.Id == menu.Pid))?.Code
                : ""
        };
    }

    /// <summary>
    ///     获取功能列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("page")]
    public async Task<dynamic> GetList([FromQuery] VisualDevListQueryInput input)
    {
        SqlSugarPagedList<VisualDevListOutput>? data = await _visualDevRepository.AsSugarClient().Queryable<VisualDevEntity>()
            .WhereIF(!string.IsNullOrEmpty(input.keyword), a => a.FullName.Contains(input.keyword) || a.EnCode.Contains(input.keyword))
            .WhereIF(input.category > 0, a => a.Category == input.category)
            .WhereIF(input.isRelease.IsNotEmptyOrNull(), a => a.State == input.isRelease)
            .WhereIF(input.webType.Equals(1), a => a.EnableFlow.Equals(0) && !a.WebType.Equals(4))
            .WhereIF(input.webType.Equals(2), a => a.EnableFlow.Equals(1))
            .WhereIF(input.webType.Equals(4), a => a.WebType.Equals(4))
            .WhereIF(input.enableFlow.IsNotEmptyOrNull(), a => a.EnableFlow.Equals(input.enableFlow))
            .Where(a => a.Type == input.type)
            .OrderBy(a => a.CreatedTime, OrderByType.Desc)
            .Select(a => new VisualDevListOutput
            {
                id = a.Id,
                fullName = a.FullName,
                enCode = a.EnCode,
                state = a.State,
                type = a.Type,
                webType = a.WebType,
                tables = a.Tables,
                description = a.Description,
                creatorTime = a.CreatedTime,
                lastModifyTime = a.UpdatedTime,
                deleteMark = 0,
                sortCode = 0,
                parentId = a.Category.ToString(),
                isRelease = a.State,
                enableFlow = a.EnableFlow,
                // pcIsRelease = SqlFunc.Subqueryable<ModuleEntity>().Where(m => m.ModuleId == a.Id && m.Category == "Web" && m.DeleteMark == null && m.ModuleId != null).Count(),
                // appIsRelease = SqlFunc.Subqueryable<ModuleEntity>().Where(m => m.ModuleId == a.Id && m.Category == "App" && m.DeleteMark == null && m.ModuleId != null).Count(),
                categoryName = SqlFunc.Subqueryable<VisualDevCategory>().Where(d => d.Id == a.Category).Select(d => d.Name),
                category = a.Category,
                creatorUser = SqlFunc.Subqueryable<SysUser>().Where(u => u.Id == a.CreatedUserId).Select(u => u.Name),
                lastModifyUser = SqlFunc.Subqueryable<SysUser>().Where(u => u.Id == a.UpdatedUserId).Select(u => u.Name)
            }).ToPagedListAsync(input.currentPage, input.pageSize);
        foreach (VisualDevListOutput item in data.Rows.Where(x => x.isRelease.IsNullOrEmpty()))
        {
            if (item.pcIsRelease > 0 || item.appIsRelease > 0)
            {
                item.isRelease = 1;
            }
        }

        return data;
    }

    /// <summary>
    ///     获取功能列表下拉框.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("select")]
    public async Task<dynamic> GetSelector([FromQuery] VisualDevSelectorInput input)
    {
        List<int> webType = input.webType.IsNotEmptyOrNull() ? input.webType.Split(',').ToObjectOld<List<int>>() : new List<int>();
        List<VisualDevSelectorOutput>? output = await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>()
            .Where(v => v.Type == input.type)
            .WhereIF(webType.Any(), v => webType.Contains(v.WebType))
            .OrderBy(a => a.Category)
            .Select(it => new VisualDevSelectorOutput
            {
                id = it.Id.ToString(),
                fullName = it.FullName,
                SortCode = 0,
                parentId = it.Category.ToString(),
                webType = it.WebType
            }).ToListAsync();
        IEnumerable<string>? parentIds = output.Select(x => x.parentId).ToList().Distinct();
        List<VisualDevSelectorOutput>? pList = new();
        List<VisualDevCategory>? parentData = await _visualDevRepository.AsSugarClient().Queryable<VisualDevCategory>().Where(d => parentIds.Contains(d.Id.ToString()))
            .OrderBy(x => x.Id).ToListAsync();
        foreach (VisualDevCategory? item in parentData)
        {
            VisualDevSelectorOutput? pData = item.Adapt<VisualDevSelectorOutput>();
            pData.parentId = "-1";
            pData.fullName = item.Name;
            pData.SortCode = 0;
            pList.Add(pData);
        }

        return new { list = output.Union(pList).ToList().ToTree("-1") };
    }

    /// <summary>
    ///     获取功能信息.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<dynamic> GetInfo(long id)
    {
        VisualDevEntity? data = await _visualDevRepository.AsQueryable().FirstAsync(v => v.Id == id);
        VisualDevInfoOutput output = data.Adapt<VisualDevInfoOutput>();
        // output.interfaceName = await _visualDevRepository.AsSugarClient().Queryable<DataInterfaceEntity>()
        //     .Where(it => it.DeleteMark == null && it.Id.Equals(output.interfaceId))
        //     .Select(it => it.FullName)
        //     .FirstAsync();
        return output;
    }

    /// <summary>
    ///     获取表单主表属性下拉框.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="filterType">1：过滤指定控件.</param>
    /// <returns></returns>
    [HttpGet("{id}/FormDataFields")]
    public async Task<dynamic> GetFormDataFields(long id, [FromQuery] int filterType)
    {
        VisualDevReleaseEntity? templateEntity = await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().FirstAsync(v => v.Id == id);
        TemplateParsingBase? tInfo = new(templateEntity.Adapt<VisualDevEntity>()); // 解析模板
        List<FieldsModel>? fieldsModels = tInfo.SingleFormData.FindAll(x => x.__vModel__.IsNotEmptyOrNull() && !JnpfKeyConst.RELATIONFORM.Equals(x.__config__.jnpfKey));
        if (filterType.Equals(1))
        {
            fieldsModels = fieldsModels.FindAll(x => !JnpfKeyConst.UPLOADIMG.Equals(x.__config__.jnpfKey) && !JnpfKeyConst.UPLOADFZ.Equals(x.__config__.jnpfKey)
                                                                                                          && !JnpfKeyConst.MODIFYUSER.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.MODIFYTIME.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.LINK.Equals(x.__config__.jnpfKey)
                                                                                                          && !JnpfKeyConst.BUTTON.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.ALERT.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.JNPFTEXT.Equals(x.__config__.jnpfKey)
                                                                                                          && !JnpfKeyConst.BARCODE.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.QRCODE.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.TABLE.Equals(x.__config__.jnpfKey)
                                                                                                          && !JnpfKeyConst.CREATEUSER.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.CREATETIME.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.BILLRULE.Equals(x.__config__.jnpfKey)
                                                                                                          && !JnpfKeyConst.POPUPSELECT.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.CURRORGANIZE.Equals(x.__config__.jnpfKey)
                                                                                                          && !JnpfKeyConst.IFRAME.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.POPUPATTR.Equals(x.__config__.jnpfKey) &&
                                                                                                          !JnpfKeyConst.RELATIONFORMATTR.Equals(x.__config__.jnpfKey));
        }

        List<VisualDevFormDataFieldsOutput>? output = fieldsModels.Select(x => new VisualDevFormDataFieldsOutput
        {
            label = x.__config__.label,
            vmodel = x.__vModel__
        }).ToList();
        return new { list = output };
    }

    /// <summary>
    ///     获取表单主表属性列表.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("{id}/FieldDataSelect")]
    [AllowAnonymous]
    public async Task<dynamic> GetFieldDataSelect(long id, [FromBody] VisualDevDataFieldDataListInput input)
    {
        Dictionary<string, object> queryDic = new();

        if (input.relationField.IsNullOrEmpty() && input.columnOptions.IsNotEmptyOrNull())
        {
            input.relationField = input.columnOptions.Split(',').First();
        }

        if (!string.IsNullOrWhiteSpace(input.columnOptions) && !string.IsNullOrWhiteSpace(input.keyword))
        {
            foreach (string item in input.columnOptions.Split(','))
            {
                queryDic.Add(item, input.keyword);
            }
        }

        VisualDevEntity? templateEntity = await GetInfoById(id.ToString(), true); // 取数据
        TemplateParsingBase? tInfo = new(templateEntity); // 解析模板

        // 指定查询字段
        if (input.IsNotEmptyOrNull() && input.columnOptions.IsNotEmptyOrNull())
        {
            List<string>? showFieldList = input.columnOptions.Split(',').ToList(); // 显示的所有 字段
            //List<FieldsModel>? flist = new List<FieldsModel>();
            List<IndexGridFieldModel>? clist = new();

            // 获取 调用 该功能表单 的功能模板
            FieldsModel? smodel = tInfo.FieldsModelList.Where(x => x.__vModel__ == input.relationField).First();
            smodel.searchType = 2;
            //flist.Add(smodel); // 添加 关联查询字段
            if (tInfo.ColumnData == null)
            {
                tInfo.ColumnData = new ColumnDesignModel
                {
                    columnList = new List<IndexGridFieldModel> { new() { prop = input.relationField, label = input.relationField } },
                    searchList = new List<IndexSearchFieldModel> { smodel.Adapt<IndexSearchFieldModel>() }
                };
            }

            if (!tInfo.ColumnData.columnList.Where(x => x.prop == input.relationField).Any())
            {
                tInfo.ColumnData.columnList.Add(new IndexGridFieldModel { prop = input.relationField, label = input.relationField });
            }
            //if (tInfo.ColumnData.defaultSidx.IsNotEmptyOrNull() && tInfo.FieldsModelList.Any(x => x.__vModel__ == tInfo.ColumnData?.defaultSidx))
            //    flist.Add(tInfo.FieldsModelList.Where(x => x.__vModel__ == tInfo.ColumnData?.defaultSidx).FirstOrDefault()); // 添加 关联排序字段

            //tInfo.FieldsModelList.ForEach(item =>
            //{
            //    if (showFieldList.Find(x => x == item.__vModel__) != null) flist.Add(item);
            //});
            clist.Add(tInfo.ColumnData.columnList.Where(x => x.prop == input.relationField).FirstOrDefault()); // 添加 关联查询字段
            if (tInfo.ColumnData.defaultSidx.IsNotEmptyOrNull() && tInfo.FieldsModelList.Any(x => x.__vModel__ == tInfo.ColumnData?.defaultSidx))
            {
                clist.Add(tInfo.ColumnData.columnList.Where(x => x.prop == tInfo.ColumnData?.defaultSidx).FirstOrDefault()); // 添加 关联排序字段
            }

            showFieldList.ForEach(item =>
            {
                if (!tInfo.ColumnData.columnList.Where(x => x.prop == item).Any())
                {
                    clist.Add(new IndexGridFieldModel { prop = item, label = item });
                }
                else
                {
                    clist.Add(tInfo.ColumnData.columnList.Find(x => x.prop == item));
                }
            });

            //if (flist.Count > 0)
            //{
            //    tInfo.FormModel.fields = flist.Distinct().ToList();
            //    templateEntity.FormData = tInfo.FormModel.ToJsonString();
            //}

            if (clist.Count > 0)
            {
                tInfo.ColumnData.columnList = clist.Distinct().ToList();
                templateEntity.ColumnData = tInfo.ColumnData.ToJson();
            }
        }

        // 获取值 无分页
        VisualDevModelListQueryInput listQueryInput = new()
        {
            queryJson = queryDic.ToJson(),
            currentPage = input.currentPage > 0 ? input.currentPage : 1,
            pageSize = input.pageSize > 0 ? input.pageSize : 20,
            dataType = "1",
            sidx = tInfo.ColumnData.defaultSidx,
            sort = tInfo.ColumnData.sort
        };

        return await _runService.GetRelationFormList(templateEntity, listQueryInput, dataFilteringModel: input.ruleList);
    }

    /// <summary>
    ///     回滚模板.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpGet("{id}/Actions/RollbackTemplate")]
    public async Task RollbackTemplate(string id)
    {
        VisualDevReleaseEntity? vREntity = await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().FirstAsync(x => x.Id.Equals(id));
        if (vREntity == null)
        {
            throw Oops.Oh(ErrorCode.D1415);
        }

        VisualDevEntity? entity = vREntity.Adapt<VisualDevEntity>();
        entity.State = 1;
        await _visualDevRepository.AsSugarClient().Updateable(entity).ExecuteCommandAsync();
    }

    #endregion

    #region Post

    /// <summary>
    ///     新建功能信息.
    /// </summary>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPost("add")]
    [UnitOfWork]
    public async Task Create([FromBody] VisualDevCrInput input)
    {
        VisualDevEntity? entity = input.Adapt<VisualDevEntity>();
        // 验证名称和编码是否重复
        if (await _visualDevRepository.IsAnyAsync(x => x.Type == input.type && (x.FullName == input.fullName || x.EnCode == input.enCode)))
        {
            throw Oops.Oh(ErrorCode.D1406);
        }

        if (input.formData.IsNotEmptyOrNull())
        {
            TemplateParsingBase? tInfo = new(entity); // 解析模板
            if (!tInfo.VerifyTemplate())
            {
                throw Oops.Oh(ErrorCode.D1401); // 验证模板
            }

            await VerifyPrimaryKeyPolicy(tInfo, entity.DbLinkId); // 验证雪花Id 和自增长Id 主键是否支持
        }

        entity.State = 0;

        // 添加功能
        entity = await _visualDevRepository.AsSugarClient().Insertable(entity).IgnoreColumns(true).ExecuteReturnEntityAsync();

        // // 同步流程相关
        // if (entity.EnableFlow.Equals(1) && (!entity.Type.Equals(3) && !entity.Type.Equals(4)))
        // {
        //     var fEntity = entity.Adapt<FlowFormEntity>();
        //     fEntity.PropertyJson = entity.FormData;
        //     fEntity.TableJson = entity.Tables;
        //     fEntity.DraftJson = fEntity.ToJsonString();
        //     fEntity.FlowType = 1;
        //     fEntity.FormType = 2;
        //     fEntity.FlowId = entity.Id;
        //     await _visualDevRepository.AsSugarClient().Insertable(fEntity).IgnoreColumns(ignoreNullColumn: true).CallEntityMethod(m => m.Create()).ExecuteReturnEntityAsync();
        //     await SaveFlowTemplate(entity);
        // }

        await SyncField(entity);
    }

    /// <summary>
    ///     修改接口.
    /// </summary>
    /// <param name="id">主键id</param>
    /// <param name="input">参数</param>
    /// <returns></returns>
    [HttpPost("{id}/update")]
    [UnitOfWork]
    public async Task Update(long id, [FromBody] VisualDevUpInput input)
    {
        VisualDevEntity? entity = input.Adapt<VisualDevEntity>();
        if (!input.webType.Equals(4) && await _visualDevRepository.AsQueryable().AnyAsync(x => x.Id.Equals(id) && x.State.Equals(1)) &&
            (entity.Tables.IsNullOrEmpty() || entity.Tables.Equals("[]")))
        {
            throw Oops.Oh(ErrorCode.D1416); // 已发布的模板  表不能为空.
        }

        // 验证名称和编码是否重复
        if (await _visualDevRepository.IsAnyAsync(x => x.Id != entity.Id && x.Type == input.type && (x.FullName == input.fullName || x.EnCode == input.enCode)))
        {
            throw Oops.Oh(ErrorCode.D1406);
        }

        int state = await _visualDevRepository.AsQueryable()
            .Where(it => it.Id.Equals(id))
            .Select(it => it.State).FirstAsync();
        if (state == 1)
        {
            entity.State = 2;
        }

        if (input.formData.IsNotEmptyOrNull())
        {
            TemplateParsingBase? tInfo = new(entity); // 解析模板
            if (!tInfo.VerifyTemplate())
            {
                throw Oops.Oh(ErrorCode.D1401); // 验证模板
            }

            await VerifyPrimaryKeyPolicy(tInfo, entity.DbLinkId); // 验证雪花Id 和自增长Id 主键是否支持
        }

        // 修改功能
        await _visualDevRepository.AsSugarClient().Updateable(entity).IgnoreColumns(true).ExecuteCommandAsync();

        // // 同步流程相关
        // if (entity.EnableFlow.Equals(1) && (!entity.Type.Equals(3) && !entity.Type.Equals(4)))
        // {
        //     var fEntity = await _visualDevRepository.AsSugarClient().Queryable<FlowFormEntity>().FirstAsync(x => x.Id.Equals(id));
        //     if (fEntity != null)
        //     {
        //         // EnabledMark=0 未发布，EnabledMark=1 已发布
        //         if (fEntity.EnabledMark.Equals(0))
        //         {
        //             fEntity.FullName = entity.FullName;
        //             fEntity.EnCode = entity.EnCode;
        //             fEntity.TableJson = entity.Tables;
        //             fEntity.PropertyJson = entity.FormData;
        //             fEntity.DraftJson = fEntity.ToJsonString();
        //         }
        //         else
        //         {
        //             var dEntity = fEntity.Copy();
        //             dEntity.TableJson = entity.Tables;
        //             dEntity.PropertyJson = entity.FormData;
        //             fEntity = new FlowFormEntity();
        //             fEntity.DraftJson = dEntity.ToJsonString();
        //             fEntity.Id = id;
        //         }
        //         fEntity.FlowType = 1;
        //         fEntity.FormType = 2;
        //         fEntity.FlowId = id;
        //
        //         await _visualDevRepository.AsSugarClient().Updateable(fEntity).IgnoreColumns(ignoreAllNullColumns: true).CallEntityMethod(m => m.LastModify()).ExecuteCommandAsync();
        //         await SaveFlowTemplate(entity);
        //     }
        //     else
        //     {
        //         fEntity = entity.Adapt<FlowFormEntity>();
        //         fEntity.PropertyJson = entity.FormData;
        //         fEntity.TableJson = entity.Tables;
        //         fEntity.DraftJson = fEntity.ToJsonString();
        //         fEntity.FlowType = 1;
        //         fEntity.FormType = 2;
        //         fEntity.FlowId = id;
        //         await _visualDevRepository.AsSugarClient().Insertable(fEntity).IgnoreColumns(ignoreNullColumn: true).CallEntityMethod(m => m.Create()).ExecuteReturnEntityAsync();
        //         await SaveFlowTemplate(entity);
        //     }
        // }

        await SyncField(entity);
    }

    /// <summary>
    ///     删除接口.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpPost("{id}/delete")]
    [UnitOfWork]
    public async Task Delete(string id)
    {
        VisualDevEntity? entity = await _visualDevRepository.AsQueryable().Where(v => v.Id == id.ParseToLong())
            .FirstAsync();
        await _visualDevRepository.AsSugarClient().Deleteable(entity).ExecuteCommandAsync();

        // 删除菜单
        if (await _visualDevRepository.AsSugarClient().Queryable<SysMenu>().AnyAsync(a => a.ModuleId == entity.Id))
        {
            await _visualDevRepository.AsSugarClient().Deleteable<SysMenu>().Where(w => w.ModuleId == entity.Id).ExecuteCommandAsync();
        }

        // 同步删除线上版本
        VisualDevReleaseEntity? rEntity = await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().FirstAsync(v => v.Id == id.ParseToLong());
        if (rEntity != null)
        {
            await _visualDevRepository.AsSugarClient().Deleteable(rEntity).ExecuteCommandAsync();
        }

        // // 同步删除流程表单
        // var fEntity = await _visualDevRepository.AsSugarClient().Queryable<FlowFormEntity>().FirstAsync(v => v.Id == id && v.DeleteMark == null);
        // if (fEntity != null)
        // {
        //     await _visualDevRepository.AsSugarClient().Deleteable<FlowFormEntity>().Where(x => x.Id.Equals(fEntity.Id)).ExecuteCommandAsync();
        //     await _visualDevRepository.AsSugarClient().Deleteable<FlowFormRelationEntity>().Where(x => x.FormId.Equals(fEntity.FlowId)).ExecuteCommandAsync();
        //
        //     // 功能流程存在发起的流程，流程设计不应该删除.
        //     if (!await _visualDevRepository.AsSugarClient().Queryable<FlowTaskEntity>().AnyAsync(ft => ft.TemplateId.Equals(fEntity.Id) && ft.DeleteMark == null))
        //     {
        //         await _visualDevRepository.AsSugarClient().Deleteable<FlowTemplateEntity>().Where(it => it.Id.Equals(fEntity.Id)).ExecuteCommandAsync();
        //         await _visualDevRepository.AsSugarClient().Deleteable<FlowTemplateJsonEntity>().Where(it => it.TemplateId.Equals(fEntity.Id)).ExecuteCommandAsync();
        //     }
        // }
    }

    /// <summary>
    ///     复制.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpPost("{id}/Actions/Copy")]
    public async Task ActionsCopy(string id)
    {
        string? random = new Random().NextLetterAndNumberString(5);
        VisualDevEntity? entity = await _visualDevRepository.AsQueryable().FirstAsync(v => v.Id == id.ParseToLong());
        if (entity.State.Equals(1) && !entity.Type.Equals(3) && !entity.Type.Equals(4))
        {
            VisualDevReleaseEntity? vREntity = await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().FirstAsync(v => v.Id == id.ParseToLong());
            entity = vREntity.Adapt<VisualDevEntity>();
            entity.State = 0;
        }

        entity.FullName = entity.FullName + ".副本" + random;
        entity.EnCode += random;
        entity.State = 0;
        entity.Id = 0; // 复制的数据需要把Id清空，否则会主键冲突错误

        try
        {
            entity = await _visualDevRepository.AsSugarClient().Insertable(entity).IgnoreColumns(true).ExecuteReturnEntityAsync();
        }
        catch
        {
            if (entity.FullName.Length >= 100 || entity.EnCode.Length >= 50)
            {
                throw Oops.Oh(ErrorCode.D1403); // 数据长度超过 字段设定长度
            }

            throw;
        }

        // // 同步流程相关
        // if (entity.EnableFlow.Equals(1) && (!entity.Type.Equals(3) && !entity.Type.Equals(4)))
        // {
        //     var fEntity = entity.Adapt<FlowFormEntity>();
        //     fEntity.PropertyJson = entity.FormData;
        //     fEntity.TableJson = entity.Tables;
        //     fEntity.DraftJson = fEntity.ToJsonString();
        //     fEntity.FlowType = 1;
        //     fEntity.FormType = 2;
        //     fEntity.FlowId = entity.Id;
        //     await _visualDevRepository.AsSugarClient().Insertable(fEntity).IgnoreColumns(ignoreNullColumn: true).CallEntityMethod(m => m.Create()).ExecuteReturnEntityAsync();
        //     await SaveFlowTemplate(entity);
        // }
    }

    /// <summary>
    ///     功能同步菜单.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("{id}/Actions/Release")]
    [UnitOfWork]
    public async Task FuncToMenu(long id, [FromBody] VisualDevToMenuInput input)
    {
        string oldPcSystemCode = input.pcSystemId;
        string oldPcModuleParentId = input.pcModuleParentId;

        input.id = id.ToString();
        VisualDevEntity entity = await _visualDevRepository.AsQueryable()
            .Where(x => x.Id == input.id.ParseToLong())
            .FirstAsync();
        if (entity == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 服务Id
        string? systemId = (await _visualDevRepository.AsSugarClient().Queryable<SysService>().Where(w => w.Code == oldPcSystemCode).FirstAsync())?.Id.ToString();

        input.pcModuleParentId = input.pcModuleParentId.IsNullOrWhiteSpace() ? "-1" : input.pcModuleParentId;

        if (entity.FormData.IsNullOrEmpty() && !entity.WebType.Equals(4))
        {
            throw Oops.Oh(ErrorCode.COM1013);
        }

        if ((entity.WebType.Equals(2) || entity.WebType.Equals(4)) && entity.ColumnData.IsNullOrEmpty())
        {
            throw Oops.Oh(ErrorCode.COM1014);
        }

        #region 旧的菜单、权限数据

        entity.Menu = await _visualDevRepository.AsSugarClient().Queryable<SysMenu>().Where(w => w.ModuleId == entity.Id).FirstAsync();
        List<SysMenu>? oldWebModuleButtonEntity = entity.Menu != null
            ? await _visualDevRepository.AsSugarClient().Queryable<SysMenu>().Where(w => w.ModuleId == entity.Id && w.Pid == entity.Menu.Id).ToListAsync()
            : new List<SysMenu>();
        ;

        #endregion

        if (input.pc.Equals(1) && oldPcSystemCode.IsNullOrEmpty() && oldPcModuleParentId.IsNullOrEmpty() && entity.Menu == null)
        {
            throw Oops.Oh(ErrorCode.D4017);
        }

        long oldWebId = entity.Menu?.Id ?? 0;
        if (await _visualDevRepository.AsSugarClient().Queryable<SysMenu>().AnyAsync(x => x.Code == entity.EnCode && x.Id != oldWebId))
        {
            throw Oops.Oh(ErrorCode.COM1025);
        }

        if (await _visualDevRepository.AsSugarClient().Queryable<SysMenu>()
                .AnyAsync(x => x.Name == entity.FullName && x.Id != oldWebId && x.Pid == input.pcModuleParentId.ParseToLong() && x.Application == oldPcSystemCode))
        {
            throw Oops.Oh(ErrorCode.COM1024);
        }

        // 删除旧的菜单
        if (entity.Menu != null)
        {
            await _visualDevRepository.AsSugarClient().Deleteable(entity.Menu).ExecuteCommandAsync();
        }

        #region 数据视图

        SysMenu? oldWebModule = entity.Menu;
        if (entity.WebType.Equals(4))
        {
            #region 菜单组装

            SysMenu moduleModel = new()
            {
                Pids = await _menuService.CreateNewPids(oldWebModule != null ? oldWebModule.Pid : input.pcModuleParentId.Equals(systemId) ? 0 : input.pcModuleParentId.ParseToLong()),
                Id = oldWebModule != null ? entity.Id : YitIdHelper.NextId(),
                Pid = oldWebModule != null ? oldWebModule.Pid : input.pcModuleParentId.Equals(systemId) ? 0 : input.pcModuleParentId.ParseToLong(), // 父级菜单节点
                Name = entity.FullName,
                Code = entity.EnCode,
                Icon = oldWebModule != null ? oldWebModule.Icon : "icon-ym icon-ym-webForm",
                // moduleModel.UrlAddress = oldWebModule != null ? oldWebModule.UrlAddress : "model/" + entity.EnCode;
                Type = MenuEnum.MENU,
                Status = true,
                Sort = oldWebModule != null ? oldWebModule.Sort : 999,
                Application = oldWebModule != null ? oldWebModule.Application : oldPcSystemCode,
                Router = oldWebModule != null ? oldWebModule.Router : input.code.IsNotEmptyOrNull() ? "/" + input.code + "/" + entity.EnCode : "/" + entity.EnCode,
                Component = oldWebModule != null ? oldWebModule.Component : input.code.IsNotEmptyOrNull() ? input.code + "/" + entity.EnCode : "/" + entity.EnCode,
                Visible = true,
                Weight = oldWebModule != null ? oldWebModule.Weight : MenuWeight.DEFAULT_WEIGHT,
                OpenType = oldWebModule != null ? oldWebModule.OpenType : MenuOpenEnum.NONE,
                ModuleId = entity.Id
            };

            #endregion

            // 添加PC菜单
            if (input.pc == 1)
            {
                StorageableResult<SysMenu>? storModuleModel = await _visualDevRepository.AsSugarClient().Storageable(moduleModel).Saveable().ToStorageAsync(); // 存在更新不存在插入 根据主键
                await storModuleModel.AsInsertable.ExecuteCommandAsync(); // 执行插入
                await storModuleModel.AsUpdateable.ExecuteCommandAsync(); // 执行更新
            }

            entity.State = 1;
            // 修改功能发布状态
            await _visualDevRepository.AsSugarClient().Updateable(entity).UpdateColumns(w => new { w.State }).ExecuteCommandAsync();

            // 线上版本
            if (!await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().AnyAsync(x => x.Id.Equals(id)))
            {
                VisualDevReleaseEntity vReleaseEntity = entity.Adapt<VisualDevReleaseEntity>();
                await _visualDevRepository.AsSugarClient().Insertable(vReleaseEntity).IgnoreColumns(true).ExecuteCommandAsync();
            }
            else
            {
                VisualDevReleaseEntity vReleaseEntity = entity.Adapt<VisualDevReleaseEntity>();
                await _visualDevRepository.AsSugarClient().Updateable(vReleaseEntity).ExecuteCommandAsync();
            }

            return;
        }

        #endregion

        TemplateParsingBase tInfo = new(entity); // 解析模板

        // 无表转有表
        if (!tInfo.IsHasTable)
        {
            string mTableName = "mt" + entity.Id; // 主表名称
            VisualDevEntity? res = await NoTblToTable(entity, mTableName);
            if (res != null)
            {
                await _visualDevRepository.AsSugarClient().Updateable(entity).IgnoreColumns(true).ExecuteCommandAsync();
            }
            else
            {
                throw Oops.Oh(ErrorCode.D1414);
            }

            tInfo = new TemplateParsingBase(res); // 解析模板
            entity = res;
        }

        ColumnDesignModel? columnData;

        // 列配置模型
        if (!string.IsNullOrWhiteSpace(entity.ColumnData))
        {
            columnData = entity.ColumnData.ToObjectOld<ColumnDesignModel>();
        }
        else
        {
            columnData = new ColumnDesignModel
            {
                btnsList = new List<ButtonConfigModel>(),
                columnBtnsList = new List<ButtonConfigModel>(),
                customBtnsList = new List<ButtonConfigModel>(),
                columnList = new List<IndexGridFieldModel>(),
                defaultColumnList = new List<IndexGridFieldModel>()
            };
        }

        columnData.btnsList = columnData.btnsList.Union(columnData.columnBtnsList).ToList();
        if (columnData.customBtnsList.Count != 0)
        {
            columnData.btnsList = columnData.btnsList.Union(columnData.customBtnsList).ToList();
        }

        ColumnDesignModel? appColumnData;

        // App列配置模型
        if (!string.IsNullOrWhiteSpace(entity.AppColumnData))
        {
            appColumnData = tInfo.AppColumnData;
        }
        else
        {
            appColumnData = new ColumnDesignModel
            {
                btnsList = new List<ButtonConfigModel>(),
                columnBtnsList = new List<ButtonConfigModel>(),
                customBtnsList = new List<ButtonConfigModel>(),
                columnList = new List<IndexGridFieldModel>(),
                defaultColumnList = new List<IndexGridFieldModel>()
            };
        }

        appColumnData.btnsList = appColumnData.btnsList.Union(appColumnData.columnBtnsList).ToList();
        if (appColumnData.customBtnsList.Count != 0)
        {
            appColumnData.btnsList = appColumnData.btnsList.Union(appColumnData.customBtnsList).ToList();
        }

        #region 菜单组装

        try
        {
            SysMenu moduleModel = new()
            {
                Id = entity.Id,
                Pid = input.pcModuleParentId.Equals(systemId) ? 0 : input.pcModuleParentId.ParseToLong(), // 父级菜单节点
                Pids = await _menuService.CreateNewPids(input.pcModuleParentId.Equals(systemId) ? 0 : input.pcModuleParentId.ParseToLong()),
                Name = entity.FullName,
                Code = entity.EnCode,
                Icon = "icon-ym icon-ym-webForm",
                // moduleModel.UrlAddress =  "model/" + entity.EnCode;
                Type = MenuEnum.MENU,
                Status = true,
                Sort = 999,
                Application = oldPcSystemCode,
                Router = input.code.IsNotEmptyOrNull() ? "/" + input.code + "/" + entity.EnCode : "/" + entity.EnCode,
                Component = input.code.IsNotEmptyOrNull() ? input.code + "/" + entity.EnCode : "/" + entity.EnCode,
                Visible = true,
                Weight = MenuWeight.DEFAULT_WEIGHT,
                OpenType = MenuOpenEnum.COMPONENT,
                ModuleId = entity.Id
            };

            #endregion

            #region 配置权限

            string router = input.code != null ? "/" + input.code + "/" + entity.EnCode : "/" + entity.EnCode;
            string component = input.code != null ? input.code + "/" + entity.EnCode : entity.EnCode;
            // 按钮权限
            List<SysMenu> btnAuth = new();
            btnAuth.Add(new SysMenu
            {
                Status = true, Sort = 0, Pid = moduleModel.Id, Code = "btn_add", Name = "新增", Type = MenuEnum.Btn, Icon = "icon-ym icon-ym-add", Router = router + "/add",
                Component = component + "/add",
                Application = moduleModel.Application
            });
            btnAuth.Add(new SysMenu
            {
                Status = true, Sort = 0, Pid = moduleModel.Id, Code = "btn_download", Name = "导出", Type = MenuEnum.Btn, Icon = "icon-ym icon-ym-download",
                Component = component + "/download",
                Router = router + "/download", Application = moduleModel.Application
            });
            btnAuth.Add(new SysMenu
            {
                Status = true, Sort = 0, Pid = moduleModel.Id, Code = "btn_upload", Name = "导入", Type = MenuEnum.Btn, Icon = "icon-ym icon-ym-upload", Router = router + "/upload",
                Component = component + "/upload",
                Application = moduleModel.Application
            });
            btnAuth.Add(new SysMenu
            {
                Status = true, Sort = 0, Pid = moduleModel.Id, Code = "btn_batchRemove", Name = "批量删除", Type = MenuEnum.Btn, Icon = "icon-ym icon-ym-batchRemove",
                Component = component + "/batchRemove",
                Router = router + "/batchRemove", Application = moduleModel.Application
            });
            btnAuth.Add(new SysMenu
            {
                Status = true, Sort = 0, Pid = moduleModel.Id, Code = "btn_edit", Name = "编辑", Type = MenuEnum.Btn, Icon = "icon-ym icon-ym-edit", Router = router + "/edit",
                Component = component + "/edit",
                Application = moduleModel.Application
            });
            btnAuth.Add(new SysMenu
            {
                Status = true, Sort = 0, Pid = moduleModel.Id, Code = "btn_remove", Name = "删除", Type = MenuEnum.Btn, Icon = "icon-ym icon-ym-remove", Router = router + "/remove",
                Component = component + "/remove",
                Application = moduleModel.Application
            });
            btnAuth.Add(new SysMenu
            {
                Status = true, Sort = 0, Pid = moduleModel.Id, Code = "btn_detail", Name = "详情", Type = MenuEnum.Btn, Icon = "icon-ym icon-ym-detail", Router = router + "/detail",
                Component = component + "/detail",
                Application = moduleModel.Application
            });
            btnAuth.Add(new SysMenu
            {
                Status = true, Sort = 0, Pid = moduleModel.Id, Code = "btn_batchPrint", Name = "批量打印", Type = MenuEnum.Btn, Icon = "icon-ym icon-ym-print",
                Component = component + "/batchPrint",
                Router = router + "/batchPrint", Application = moduleModel.Application
            });
            columnData.customBtnsList.ForEach(item =>
            {
                btnAuth.Add(new SysMenu
                {
                    Status = true, Sort = 0, Pid = moduleModel.Id, Code = item.value, Name = item.label, Type = MenuEnum.Btn, Router = router + item.value,
                    Component = component + item.value,
                    Application = moduleModel.Application
                });
            });

            columnData.btnsList.ForEach(item =>
            {
                SysMenu? aut = btnAuth.Find(x => x.Code == "btn_" + item.value);
                if (aut != null)
                {
                    aut.Visible = true;
                    aut.Status = true;
                }
            });
            //
            // // 表单权限
            // var columnAuth = new List<ModuleColumnEntity>();
            // var fieldList = tInfo.AllFieldsModel;
            // var formAuth = new List<ModuleFormEntity>();
            //
            // var ctList = tInfo.AllFieldsModel.Where(x => x.__config__.jnpfKey == JnpfKeyConst.TABLE).ToList();
            // var childTableIndex = new Dictionary<string, string>();
            // for (var i = 0; i < ctList.Count; i++) childTableIndex.Add(ctList[i].__vModel__, ctList[i].__config__.label + (i + 1));
            //
            // fieldList = fieldList.Where(x => x.__config__.jnpfKey != JnpfKeyConst.TABLE).ToList();
            // fieldList.Where(x => x.__vModel__.IsNotEmptyOrNull()).ToList().ForEach(item =>
            // {
            //     var fRule = item.__vModel__.Contains("_jnpf_") ? 1 : 0;
            //     fRule = item.__vModel__.ToLower().Contains("tablefield") && item.__vModel__.Contains("-") ? 2 : fRule;
            //     var ctName = item.__vModel__.Split("-");
            //     formAuth.Add(new ModuleFormEntity()
            //     {
            //         ParentId = "-1",
            //         EnCode = item.__vModel__,
            //         BindTable = fRule.Equals(2) ? item.__config__.relationTable : item.__config__.tableName,
            //         ChildTableKey = fRule.Equals(2) ? ctName.FirstOrDefault() : string.Empty,
            //         FieldRule = fRule,
            //         ModuleId = moduleModel.Id,
            //         FullName = fRule.Equals(2) ? childTableIndex[item.__vModel__.Split('-').First()] + "-" + item.__config__.label : item.__config__.label,
            //         EnabledMark = 1,
            //         SortCode = 0
            //     });
            // });
            // ctList.ForEach(item =>
            // {
            //
            //     formAuth.Add(new ModuleFormEntity()
            //     {
            //         ParentId = "-1",
            //         EnCode = item.__vModel__,
            //         BindTable = tInfo.MainTableName,
            //         ChildTableKey = item.__vModel__,
            //         FieldRule = 0,
            //         ModuleId = moduleModel.Id,
            //         FullName = childTableIndex[item.__vModel__],
            //         EnabledMark = 1,
            //         SortCode = 0
            //     });
            // });
            //
            // // 列表权限
            // columnData.defaultColumnList.ForEach(item =>
            // {
            //     var itemModel = fieldList.FirstOrDefault(x => x.__config__.jnpfKey == item.__config__.jnpfKey && x.__vModel__ == item.prop);
            //     if (itemModel != null)
            //     {
            //         var fRule = itemModel.__vModel__.Contains("_jnpf_") ? 1 : 0;
            //         fRule = itemModel.__vModel__.ToLower().Contains("tablefield") && itemModel.__vModel__.Contains("-") ? 2 : fRule;
            //         var ctName = item.__vModel__.Split("-");
            //         columnAuth.Add(new ModuleColumnEntity()
            //         {
            //             ParentId = "-1",
            //             EnCode = itemModel.__vModel__,
            //             BindTable = fRule.Equals(2) ? itemModel.__config__.relationTable : itemModel.__config__.tableName,
            //             ChildTableKey = fRule.Equals(2) ? itemModel.__vModel__.Split("-").FirstOrDefault() : string.Empty,
            //             FieldRule = fRule,
            //             ModuleId = moduleModel.Id,
            //             FullName = fRule.Equals(2) ? childTableIndex[itemModel.__vModel__.Split('-').First()] + "-" + itemModel.__config__.label : itemModel.__config__.label,
            //             EnabledMark = 0,
            //             SortCode = 0
            //         });
            //     }
            // });

            // columnData.columnList.ForEach(item =>
            // {
            //     var aut = columnAuth.Find(x => x.EnCode == item.prop);
            //     if (aut != null) aut.EnabledMark = 1;
            // });

            #endregion

            // 添加PC菜单和权限
            if (input.pc == 1)
            {
                StorageableResult<SysMenu>? storModuleModel = await _visualDevRepository.AsSugarClient().Storageable(moduleModel).Saveable().ToStorageAsync(); // 存在更新不存在插入 根据主键
                await storModuleModel.AsInsertable.ExecuteCommandAsync(); // 执行插入
                await storModuleModel.AsUpdateable.ExecuteCommandAsync(); // 执行更新
                //
                // #region 表单权限
                // if (columnData.useFormPermission)
                // {
                //     if (!oldWebModuleFormEntity.Any())
                //     {
                //         await _visualDevRepository.AsSugarClient().Insertable(formAuth).CallEntityMethod(m => m.Creator()).ExecuteCommandAsync();
                //     }
                //     else
                //     {
                //         var formAuthAddList = new List<ModuleFormEntity>();
                //         formAuth.ForEach(item =>
                //         {
                //             if (!oldWebModuleFormEntity.Any(x => x.EnCode == item.EnCode)) formAuthAddList.Add(item);
                //         });
                //         if (formAuthAddList.Any()) await _visualDevRepository.AsSugarClient().Insertable(formAuthAddList).CallEntityMethod(m => m.Creator()).ExecuteCommandAsync();
                //         oldWebModuleFormEntity.ForEach(item =>
                //         {
                //             var it = formAuth.FirstOrDefault(x => x.EnCode == item.EnCode);
                //             if (it != null) item.EnabledMark = 1; // 显示标识
                //         });
                //         await _visualDevRepository.AsSugarClient().Updateable(oldWebModuleFormEntity).CallEntityMethod(m => m.LastModify()).ExecuteCommandAsync();
                //     }
                // }
                // #endregion

                #region 按钮权限

                if (columnData.useBtnPermission)
                {
                    if (oldWebModuleButtonEntity != null && !oldWebModuleButtonEntity.Any()) // 新增数据
                    {
                        // todo 暂时不添加菜单按钮
                        // await _visualDevRepository.AsSugarClient().Insertable(btnAuth).ExecuteCommandAsync();
                    }
                    else // 修改增加数据权限
                    {
                        List<SysMenu> btnAuthAddList = new();
                        btnAuth.ForEach(item =>
                        {
                            if (!oldWebModuleButtonEntity.Any(x => x.Code == item.Code))
                            {
                                btnAuthAddList.Add(item);
                            }
                        });
                        if (btnAuthAddList.Any())
                        {
                            await _visualDevRepository.AsSugarClient().Insertable(btnAuthAddList).ExecuteCommandAsync();
                        }

                        oldWebModuleButtonEntity.ForEach(item =>
                        {
                            SysMenu? it = btnAuth.FirstOrDefault(x => x.Code == item.Code);
                            if (it != null)
                            {
                                item.Visible = it.Visible;
                                item.Status = it.Status; // 显示标识
                            }
                        });
                        await _visualDevRepository.AsSugarClient().Updateable(oldWebModuleButtonEntity).ExecuteCommandAsync();
                    }
                }

                #endregion

                //
                // #region 列表权限
                // if (columnData.useColumnPermission)
                // {
                //     if (!oldWebModuleColumnEntity.Any()) // 新增数据
                //     {
                //         await _visualDevRepository.AsSugarClient().Insertable(columnAuth).CallEntityMethod(m => m.Creator()).ExecuteCommandAsync();
                //     }
                //     else // 修改增加数据权限
                //     {
                //         var columnAuthAddList = new List<ModuleColumnEntity>();
                //         columnAuth.ForEach(item =>
                //         {
                //             if (!oldWebModuleColumnEntity.Any(x => x.EnCode == item.EnCode)) columnAuthAddList.Add(item);
                //         });
                //         if (columnAuthAddList.Any()) await _visualDevRepository.AsSugarClient().Insertable(columnAuthAddList).CallEntityMethod(m => m.Creator()).ExecuteCommandAsync();
                //         oldWebModuleColumnEntity.ForEach(item =>
                //         {
                //             var it = columnAuth.FirstOrDefault(x => x.EnCode == item.EnCode);
                //             if (it != null) item.EnabledMark = it.EnabledMark; // 显示标识
                //         });
                //         await _visualDevRepository.AsSugarClient().Updateable(oldWebModuleColumnEntity).CallEntityMethod(m => m.LastModify()).ExecuteCommandAsync();
                //     }
                // }
                // #endregion
                //
                // #region 数据权限
                // if (columnData.useDataPermission)
                // {
                //     if (!_visualDevRepository.AsSugarClient().Queryable<ModuleDataAuthorizeSchemeEntity>().Where(x => x.EnCode.Equals("jnpf_alldata") && x.ModuleId == moduleModel.Id && x.DeleteMark == null).Any())
                //     {
                //         // 全部数据权限方案
                //         var AllDataAuthScheme = new ModuleDataAuthorizeSchemeEntity()
                //         {
                //             FullName = "全部数据",
                //             EnCode = "jnpf_alldata",
                //             AllData = 1,
                //             ConditionText = string.Empty,
                //             ConditionJson = string.Empty,
                //             ModuleId = moduleModel.Id
                //         };
                //         await _visualDevRepository.AsSugarClient().Insertable(AllDataAuthScheme).CallEntityMethod(m => m.Create()).ExecuteCommandAsync();
                //     }
                //
                //     // 创建用户和所属组织权限方案
                //     // 只添加 主表控件的数据权限
                //     var fList = fieldList.Where(x => !x.__vModel__.Contains("_jnpf_") && x.__vModel__.IsNotEmptyOrNull() && x.__config__.visibility.Contains("pc"))
                //         .Where(x => x.__config__.jnpfKey == JnpfKeyConst.CREATEUSER || x.__config__.jnpfKey == JnpfKeyConst.CURRORGANIZE).ToList();
                //
                //     var authList = await MenuMergeDataAuth(moduleModel.Id, fList);
                //     await MenuMergeDataAuthScheme(moduleModel.Id, authList, fList);
                // }
                // #endregion
            }

            // 修改功能发布状态
            entity.State = 1;
            await _visualDevRepository.AsSugarClient().Updateable(entity).UpdateColumns(w => new { w.State }).ExecuteCommandAsync();

            // 线上版本
            if (!await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().AnyAsync(x => x.Id.Equals(id)))
            {
                VisualDevReleaseEntity vReleaseEntity = entity.Adapt<VisualDevReleaseEntity>();
                await _visualDevRepository.AsSugarClient().Insertable(vReleaseEntity).IgnoreColumns(true).ExecuteCommandAsync();

                // // 同步添加流程表单
                // if (entity.EnableFlow.Equals(1))
                // {
                //     if (!_visualDevRepository.AsSugarClient().Queryable<FlowTemplateJsonEntity>().Any(x => x.TemplateId.Equals(entity.Id))) throw Oops.Oh(ErrorCode.D1421);
                //     var fEntity = entity.Adapt<FlowFormEntity>();
                //     fEntity.TableJson = entity.Tables;
                //     fEntity.FlowType = 1;
                //     fEntity.FormType = 2;
                //     fEntity.EnabledMark = 1;
                //     fEntity.PropertyJson = entity.FormData;
                //     fEntity.DraftJson = fEntity.ToJsonString();
                //     await _visualDevRepository.AsSugarClient().Updateable(fEntity).IgnoreColumns(ignoreAllNullColumns: true).CallEntityMethod(m => m.LastModify()).ExecuteCommandAsync();
                //     await _visualDevRepository.AsSugarClient().Updateable<FlowTemplateEntity>().SetColumns(x => x.EnabledMark == 1).Where(it => it.Id == entity.Id).ExecuteCommandHasChangeAsync();
                // }
            }
            else
            {
                VisualDevReleaseEntity vReleaseEntity = entity.Adapt<VisualDevReleaseEntity>();
                await _visualDevRepository.AsSugarClient().Updateable(vReleaseEntity).ExecuteCommandAsync();

                // if (entity.EnableFlow.Equals(1))
                // {
                //     if (!_visualDevRepository.AsSugarClient().Queryable<FlowTemplateJsonEntity>().Any(x => x.TemplateId.Equals(entity.Id))) throw Oops.Oh(ErrorCode.D1421);
                //     var fEntity = entity.Adapt<FlowFormEntity>();
                //     fEntity.TableJson = entity.Tables;
                //     fEntity.FlowType = 1;
                //     fEntity.FormType = 2;
                //     fEntity.EnabledMark = 1;
                //     fEntity.PropertyJson = entity.FormData;
                //     fEntity.DraftJson = fEntity.ToJsonString();
                //     if (!await _visualDevRepository.AsSugarClient().Queryable<FlowFormEntity>().AnyAsync(x => x.Id.Equals(id)))
                //         await _visualDevRepository.AsSugarClient().Insertable(fEntity).IgnoreColumns(ignoreNullColumn: true).ExecuteReturnEntityAsync();
                //     else
                //         await _visualDevRepository.AsSugarClient().Updateable(fEntity).IgnoreColumns(ignoreAllNullColumns: true).CallEntityMethod(m => m.LastModify()).ExecuteCommandAsync();
                //
                //     await _visualDevRepository.AsSugarClient().Updateable<FlowTemplateEntity>().SetColumns(x => x.EnabledMark == 1).Where(it => it.Id == entity.Id).ExecuteCommandHasChangeAsync();
                // }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    /// <summary>
    ///     撤销发布
    /// </summary>
    /// <param name="id"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("{id}/Actions/Release/Cancel")]
    [UnitOfWork]
    public async Task FuncToMenuCancel(string id)
    {
        VisualDevEntity entity = await _visualDevRepository.AsQueryable()
            .Where(x => x.Id == id.ParseToLong())
            .FirstAsync();
        if (entity == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 修改功能发布状态
        entity.State = 0;
        await _visualDevRepository.AsSugarClient().Updateable(entity).UpdateColumns(w => new { w.State }).ExecuteCommandAsync();

        entity.Menu = await _visualDevRepository.AsSugarClient().Queryable<SysMenu>()
            .Where(w => w.ModuleId == entity.Id).FirstAsync();
        if (entity.Menu == null)
        {
            return;
        }

        List<SysMenu> oldWebModuleButtonEntity = await _visualDevRepository.AsSugarClient().Queryable<SysMenu>()
            .Where(w => w.ModuleId == entity.Id && w.Pid == entity.Menu.Id)
            .ToListAsync();

        oldWebModuleButtonEntity.Add(entity.Menu);
        // 删除菜单
        await _visualDevRepository.AsSugarClient().Deleteable(oldWebModuleButtonEntity).ExecuteCommandAsync();
        // 删除授权菜单
        await _visualDevRepository.AsSugarClient().Deleteable<SysRoleMenu>()
            .Where(u => oldWebModuleButtonEntity.Select(s => s.Id).Contains(u.MenuId))
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     移动分组
    /// </summary>
    /// <param name="id"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("{id}/category/update")]
    [UnitOfWork]
    public async Task CategoryUpdate(long id, CategoryUpdateInput input)
    {
        VisualDevEntity entity = await _visualDevRepository.AsQueryable()
            .Where(x => x.Id == id)
            .FirstAsync();
        if (entity == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        entity.Category = input.category;
        await _visualDevRepository.AsSugarClient().Updateable(entity).UpdateColumns(w => new { w.Category }).ExecuteCommandAsync();

        // 线上版本
        VisualDevReleaseEntity? visualDevRelease = await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().FirstAsync(f => f.Id == id);
        if (visualDevRelease != null)
        {
            visualDevRelease.Category = input.category;
            await _visualDevRepository.AsSugarClient().Updateable(visualDevRelease).UpdateColumns(w => new { w.Category }).ExecuteCommandAsync();
        }
    }

    #endregion

    #region PublicMethod

    /// <summary>
    ///     获取功能信息.
    /// </summary>
    /// <param name="id">主键ID.</param>
    /// <param name="isGetRelease">是否获取发布版本.</param>
    /// <returns></returns>
    [NonAction]
    public async Task<VisualDevEntity> GetInfoById(string id, bool isGetRelease = false)
    {
        if (isGetRelease)
        {
            VisualDevReleaseEntity? vREntity = await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().FirstAsync(x => x.Id == id.ParseToLong());
            // if (vREntity != null && vREntity.EnableFlow == 1 && _visualDevRepository.AsSugarClient().Queryable<FlowFormEntity>().Any(x => x.Id.Equals(id)))
            // {
            //     vREntity.FlowId = await _visualDevRepository.AsSugarClient().Queryable<FlowFormEntity>().Where(x => x.Id.Equals(id)).Select(x => x.FlowId).FirstAsync();
            //     if (vREntity.FlowId.IsNotEmptyOrNull())
            //     {
            //         if (!_visualDevRepository.AsSugarClient().Queryable<FlowTemplateEntity>().Where(x => x.Id.Equals(vREntity.FlowId) && x.EnabledMark.Equals(1)).Any())
            //             vREntity.EnableFlow = -1;
            //     }
            // }
            if (vREntity == null)
            {
                throw Oops.Oh("表单未发布");
            }
            return vREntity.Adapt<VisualDevEntity>();
        }

        VisualDevEntity? vEntity = await _visualDevRepository.AsQueryable().FirstAsync(x => x.Id == id.ParseToLong());
        // if (_visualDevRepository.AsSugarClient().Queryable<FlowFormEntity>().Any(x => x.Id.Equals(id)))
        //     vEntity.FlowId = await _visualDevRepository.AsSugarClient().Queryable<FlowFormEntity>().Where(x => x.Id.Equals(id)).Select(x => x.FlowId).FirstAsync();
        if (vEntity == null)
        {
            throw Oops.Oh("表单已经被发布");
        }
        return vEntity.Adapt<VisualDevEntity>();
    }

    /// <summary>
    ///     新增导入数据.
    /// </summary>
    /// <param name="input"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    [NonAction]
    public async Task CreateImportData(VisualDevEntity input, int type)
    {
        List<string> errorMsgList = new();
        List<string> errorList = new();
        if (await _visualDevRepository.AsQueryable().AnyAsync(it => it.Id.Equals(input.Id)))
        {
            errorList.Add("ID");
        }

        if (await _visualDevRepository.AsQueryable().AnyAsync(it => it.EnCode.Equals(input.EnCode)))
        {
            errorList.Add("编码");
        }

        if (await _visualDevRepository.AsQueryable().AnyAsync(it => it.FullName.Equals(input.FullName)))
        {
            errorList.Add("名称");
        }

        if (errorList.Any())
        {
            if (type.Equals(0))
            {
                string error = string.Join("、", errorList);
                errorMsgList.Add(string.Format("{0}重复", error));
            }
            else
            {
                string random = new Random().NextLetterAndNumberString(5);
                input.Id = YitIdHelper.NextId();
                input.FullName = string.Format("{0}.副本{1}", input.FullName, random);
                input.EnCode += random;
            }
        }

        if (errorMsgList.Any() && type.Equals(0))
        {
            throw Oops.Oh(ErrorCode.COM1018, string.Join(";", errorMsgList));
        }

        input.State = 0;
        input.DbLinkId = 0;
        input.CreatedUserId = _userManager.UserId;
        input.UpdatedTime = null;
        input.UpdatedUserId = null;
        try
        {
            StorageableResult<VisualDevEntity>? storModuleModel = _visualDevRepository.AsSugarClient().Storageable(input).Saveable().ToStorage(); // 存在更新不存在插入 根据主键
            await storModuleModel.AsInsertable.ExecuteCommandAsync(); // 执行插入
            await storModuleModel.AsUpdateable.ExecuteCommandAsync(); // 执行更新
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ErrorCode.COM1020, ex.Message);
        }

        // // 流程表单
        // if (input.EnableFlow.Equals(1) && !input.Type.Equals(3) && !input.Type.Equals(4))
        // {
        //     var fEntity = input.Adapt<FlowFormEntity>();
        //     fEntity.PropertyJson = input.FormData;
        //     fEntity.TableJson = input.Tables;
        //     fEntity.DraftJson = fEntity.ToJsonString();
        //     fEntity.FlowType = 1;
        //     fEntity.FormType = 2;
        //     fEntity.FlowId = input.Id;
        //     try
        //     {
        //         await _visualDevRepository.AsSugarClient().Insertable(fEntity).IgnoreColumns(ignoreNullColumn: true).CallEntityMethod(m => m.Create()).ExecuteReturnEntityAsync();
        //         await SaveFlowTemplate(input);
        //     }
        //     catch (Exception ex)
        //     {
        //         throw Oops.Oh(ErrorCode.COM1020, ex.Message);
        //     }
        // }
    }

    /// <summary>
    ///     功能模板 无表 转 有表.
    /// </summary>
    /// <param name="vEntity">功能实体.</param>
    /// <param name="mainTableName">主表名称.</param>
    /// <returns></returns>
    [NonAction]
    public async Task<VisualDevEntity> NoTblToTable(VisualDevEntity vEntity, string mainTableName)
    {
        string dbtype = _visualDevRepository.AsSugarClient().CurrentConnectionConfig.DbType.ToString();
        bool isUpper = false; // 是否大写
        if (dbtype.ToLower().Equals("oracle") || dbtype.ToLower().Equals("dm") || dbtype.ToLower().Equals("dm8"))
        {
            isUpper = true;
        }
        else
        {
            isUpper = false;
        }

        // Oracle和Dm数据库 表名全部大写, 其他全部小写
        mainTableName = isUpper ? mainTableName.ToUpper() : mainTableName.ToLower();

        FormDataModel formModel = vEntity.FormData.ToObjectOld<FormDataModel>();
        List<FieldsModel>? fieldsModelList = TemplateAnalysis.AnalysisTemplateData(formModel.fields);

        #region 创表信息组装

        List<DbTableAndFieldModel>? addTableList = new(); // 表集合

        // 主表信息
        DbTableAndFieldModel? mainInfo = new();
        mainInfo.table = mainTableName;
        mainInfo.tableName = vEntity.FullName;
        mainInfo.FieldList = FieldsModelToTableFile(fieldsModelList, formModel.primaryKeyPolicy == 2);
        mainInfo.FieldList.Add(new DbTableFieldModel { field = "f_inte_assistant", fieldName = "集成助手数据标识", dataType = "int", dataLength = "1", allowNull = 1 });
        if (vEntity.EnableFlow.Equals(1))
        {
            mainInfo.FieldList.Add(new DbTableFieldModel { field = "f_flow_task_id", fieldName = "流程任务Id", dataType = "varchar", dataLength = "50", allowNull = 1 });
            mainInfo.FieldList.Add(new DbTableFieldModel { field = "f_flow_id", fieldName = "流程引擎Id", dataType = "varchar", dataLength = "50", allowNull = 1 });
        }

        if (formModel.logicalDelete)
        {
            mainInfo.FieldList.Add(new DbTableFieldModel { field = "f_delete_mark", fieldName = "删除标识", dataType = "int", dataLength = "1", allowNull = 1 });
            mainInfo.FieldList.Add(new DbTableFieldModel { field = "f_delete_user_id", fieldName = "删除用户", dataType = "varchar", dataLength = "50", allowNull = 1 });
            mainInfo.FieldList.Add(new DbTableFieldModel { field = "f_delete_time", fieldName = "删除时间", dataType = "datetime", dataLength = "50", allowNull = 1 });
        }
        // if (_tenant.MultiTenancy && _userManager.CurrentTenantInformation.type.Equals(1)) mainInfo.FieldList.Add(new DbTableFieldModel() { DbColumnName = "f_tenant_id", ColumnDescription = "租户Id", DataType = "varchar", Length = 50, IsNullable = true });

        // 子表信息
        Dictionary<string, string>? childTableDic = new();
        fieldsModelList.Where(x => x.__config__.jnpfKey == JnpfKeyConst.TABLE).ToList().ForEach(item =>
        {
            DbTableAndFieldModel? childTInfo = new();
            childTInfo.table = "ct" + YitIdHelper.NextId();
            childTInfo.table = isUpper ? childTInfo.table.ToUpper() : childTInfo.table.ToLower();
            childTableDic.Add(item.__vModel__, childTInfo.table);
            childTInfo.tableName = vEntity.FullName + "_子表";
            childTInfo.FieldList = FieldsModelToTableFile(item.__config__.children, formModel.primaryKeyPolicy == 2);
            childTInfo.FieldList.Add(new DbTableFieldModel { dataLength = "50", allowNull = 1, dataType = "varchar", field = "f_foreign_id", fieldName = vEntity.FullName + "_关联外键" });
            addTableList.Add(childTInfo);
        });

        #endregion

        #region 修改功能模板 有表改无表

        List<TableModel>? modelTableList = new();

        // 处理主表
        TableModel? mainTable = new();
        mainTable.fields = new List<EntityFieldModel>();
        mainTable.table = mainInfo.table;
        mainTable.tableName = mainInfo.tableName;
        mainTable.typeId = "1";
        mainInfo.FieldList.ForEach(item => // 表字段
        {
            EntityFieldModel? etFieldModel = new();
            etFieldModel.DataLength = Convert.ToInt32(item.dataLength);
            etFieldModel.PrimaryKey = true;
            etFieldModel.DataType = item.dataType;
            etFieldModel.Field = item.field;
            etFieldModel.FieldName = item.fieldName;
            mainTable.fields.Add(etFieldModel);
        });

        // 处理子表
        addTableList.ForEach(item =>
        {
            TableModel? childInfo = new();
            childInfo.fields = new List<EntityFieldModel>();
            childInfo.table = item.table;
            childInfo.tableName = item.tableName;
            childInfo.tableField = isUpper ? "F_FOREIGN_ID" : "f_foreign_id"; // 关联外键
            childInfo.relationField = isUpper ? "F_ID" : "f_id"; // 关联主键
            childInfo.typeId = "0";
            item.FieldList.ForEach(it => // 子表字段
            {
                EntityFieldModel? etFieldModel = new();
                etFieldModel.DataLength = Convert.ToInt32(it.dataLength);
                etFieldModel.PrimaryKey = it.primaryKey;
                etFieldModel.DataType = it.dataType;
                etFieldModel.Field = it.field;
                etFieldModel.FieldName = it.fieldName;
                childInfo.fields.Add(etFieldModel);
            });
            modelTableList.Add(childInfo);
        });
        modelTableList.Add(mainTable);

        #region 给控件绑定 tableName、relationTable 属性

        // 用字典反序列化， 避免多增加不必要的属性
        Dictionary<string, object>? dicFormModel = vEntity.FormData.ToObjectOld<Dictionary<string, object>>();
        List<Dictionary<string, object>>? dicFieldsModelList = dicFormModel.FirstOrDefault(x => x.Key == "fields").Value.ToJson().ToObjectOld<List<Dictionary<string, object>>>();

        // 给控件绑定 tableName
        FieldBindTable(dicFieldsModelList, childTableDic, mainTableName);

        #endregion

        dicFormModel["fields"] = dicFieldsModelList; // 修改表单控件
        vEntity.FormData = dicFormModel.ToJson(); // 修改模板
        vEntity.Tables = modelTableList.ToJson(); // 修改模板涉及表

        addTableList.Add(mainInfo);

        #endregion

        try
        {
            DbLink link = await _runService.GetDbLink(vEntity.DbLinkId);
            foreach (DbTableAndFieldModel? item in addTableList)
            {
                bool res = _changeDataBase.Create(link, item, item.FieldList);
                if (!res)
                {
                    throw null;
                }
            }

            if (await _visualDevRepository.IsAnyAsync(x => x.Id.Equals(vEntity.Id)))
            {
                await _visualDevRepository.AsUpdateable(vEntity).IgnoreColumns(true).ExecuteCommandAsync();
            }
            else
            {
                await _visualDevRepository.AsInsertable(vEntity).IgnoreColumns(true).ExecuteCommandAsync();
            }


            return vEntity;
        }
        catch (Exception e)
        {
            return null;
        }
    }

    #endregion

    #region Private

    /// <summary>
    ///     组件转换表字段.
    /// </summary>
    /// <param name="fmList">表单列表.</param>
    /// <param name="isIdentity">主键是否自增长.</param>
    /// <returns></returns>
    [NonAction]
    private List<DbTableFieldModel> FieldsModelToTableFile(List<FieldsModel> fmList, bool isIdentity)
    {
        List<DbTableFieldModel>? fieldList = new(); // 表字段
        List<FieldsModel>? mList = fmList.Where(x => x.__config__.jnpfKey.IsNotEmptyOrNull())
            .Where(x => x.__config__.jnpfKey != JnpfKeyConst.QRCODE && x.__config__.jnpfKey != JnpfKeyConst.BARCODE && x.__config__.jnpfKey != JnpfKeyConst.TABLE).ToList(); // 非存储字段
        fieldList.Add(new DbTableFieldModel
        {
            primaryKey = true,
            dataType = isIdentity ? "int" : "varchar",
            dataLength = "50",
            identity = isIdentity,
            field = "f_id",
            fieldName = "主键"
        });

        foreach (FieldsModel item in mList)
        {
            // 不生成数据库字段(控件类型为：展示数据)，关联表单、弹窗选择、计算公式.
            if ((item.__config__.jnpfKey == JnpfKeyConst.RELATIONFORMATTR || item.__config__.jnpfKey == JnpfKeyConst.POPUPATTR || item.__config__.jnpfKey == JnpfKeyConst.CALCULATE) &&
                item.isStorage.Equals(0))
            {
                continue;
            }

            DbTableFieldModel? field = new();
            field.field = item.__vModel__;
            field.fieldName = item.__config__.label;
            field.allowNull = 1;

            switch (item.__config__.jnpfKey)
            {
                case JnpfKeyConst.NUMINPUT:
                case JnpfKeyConst.CALCULATE:
                    field.dataType = "decimal";
                    field.dataLength = "38";
                    field.decimalDigits = item.precision.IsNullOrEmpty() ? 3 : item.precision;
                    break;
                case JnpfKeyConst.TIME:
                    field.dataType = "varchar";
                    field.dataLength = "50";
                    field.allowNull = 1;
                    break;
                case JnpfKeyConst.DATE:
                case JnpfKeyConst.CREATETIME:
                case JnpfKeyConst.MODIFYTIME:
                    field.dataType = "DateTime";
                    field.allowNull = 1;
                    break;
                case JnpfKeyConst.EDITOR:
                case JnpfKeyConst.UPLOADFZ:
                case JnpfKeyConst.UPLOADIMG:
                case JnpfKeyConst.SIGN:
                    field.dataType = "longtext";
                    field.allowNull = 1;
                    break;
                case JnpfKeyConst.RATE:
                    field.dataType = "decimal";
                    field.dataLength = "38";
                    field.decimalDigits = 1;
                    field.allowNull = 1;
                    break;
                case JnpfKeyConst.SLIDER:
                    field.dataType = "decimal";
                    field.dataLength = "38";
                    field.decimalDigits = 3;
                    field.allowNull = 1;
                    break;
                default:
                    field.dataType = "varchar";
                    field.dataLength = "500";
                    field.allowNull = 1;
                    break;
            }

            if (field.field.IsNotEmptyOrNull())
            {
                fieldList.Add(field);
            }
        }

        return fieldList;
    }

    // /// <summary>
    // /// 组装菜单 数据权限 字段管理数据.
    // /// </summary>
    // /// <param name="menuId">菜单ID.</param>
    // /// <param name="fields">功能模板控件集合.</param>
    // /// <returns></returns>
    // private async Task<List<ModuleDataAuthorizeEntity>> MenuMergeDataAuth(string menuId, List<FieldsModel> fields)
    // {
    //     // 旧的自动生成的 字段管理
    //     List<ModuleDataAuthorizeEntity>? oldDataAuth = await _visualDevRepository.AsSugarClient().Queryable<ModuleDataAuthorizeEntity>()
    //         .Where(x => x.ModuleId == menuId && x.DeleteMark == null && x.SortCode.Equals(-9527))
    //         .Where(x => (x.ConditionText == "@organizationAndSuborganization" && x.ConditionSymbol == "in") || (x.ConditionText == "@organizeId" && x.ConditionSymbol == "==")
    //         || (x.ConditionText == "@userAraSubordinates" && x.ConditionSymbol == "in") || (x.ConditionText == "@userId" && x.ConditionSymbol == "==")
    //         || (x.ConditionText == "@branchManageOrganize" && x.ConditionSymbol == "in"))
    //         .ToListAsync();
    //
    //     List<ModuleDataAuthorizeEntity>? authList = new List<ModuleDataAuthorizeEntity>(); // 字段管理
    //     List<ModuleDataAuthorizeEntity>? noDelData = new List<ModuleDataAuthorizeEntity>(); // 记录未删除
    //
    //     // 当前用户
    //     FieldsModel? item = fields.FirstOrDefault(x => x.__config__.jnpfKey == JnpfKeyConst.CREATEUSER);
    //     if (item != null)
    //     {
    //         var fRule = item.__vModel__.Contains("_jnpf_") ? 1 : 0;
    //         fRule = item.__vModel__.ToLower().Contains("tablefield") && item.__vModel__.Contains("-") ? 2 : fRule;
    //
    //         // 新增
    //         if (!oldDataAuth.Any(x => x.EnCode == item.__vModel__ && x.ConditionText == "@userId"))
    //         {
    //             authList.Add(new ModuleDataAuthorizeEntity()
    //             {
    //                 Id = SnowflakeIdHelper.NextId(),
    //                 ConditionSymbol = "==", // 条件符号
    //                 Type = "varchar", // 字段类型
    //                 FullName = item.__config__.label, // 字段说明
    //                 ConditionText = "@userId", // 条件内容（当前用户）
    //                 EnabledMark = 1,
    //                 SortCode = -9527,
    //                 FieldRule = fRule, // 主表/副表/子表
    //                 EnCode = fRule.Equals(1) ? item.__vModel__.Split("jnpf_").LastOrDefault() : item.__vModel__,
    //                 BindTable = fRule.Equals(2) ? item.__config__.relationTable : item.__config__.tableName,
    //                 ModuleId = menuId
    //             });
    //         }
    //
    //         if (!oldDataAuth.Any(x => x.EnCode == item.__vModel__ && x.ConditionText == "@userAraSubordinates"))
    //         {
    //             authList.Add(new ModuleDataAuthorizeEntity()
    //             {
    //                 Id = SnowflakeIdHelper.NextId(),
    //                 ConditionSymbol = "in", // 条件符号
    //                 Type = "varchar", // 字段类型
    //                 FullName = item.__config__.label, // 字段说明
    //                 ConditionText = "@userAraSubordinates", // 条件内容（当前用户及下属）
    //                 EnabledMark = 1,
    //                 SortCode = -9527,
    //                 FieldRule = fRule, // 主表/副表/子表
    //                 EnCode = fRule.Equals(1) ? item.__vModel__.Split("jnpf_").LastOrDefault() : item.__vModel__,
    //                 BindTable = fRule.Equals(2) ? item.__config__.relationTable : item.__config__.tableName,
    //                 ModuleId = menuId
    //             });
    //         }
    //
    //         // 删除
    //         List<ModuleDataAuthorizeEntity>? delData = oldDataAuth.Where(x => x.EnCode != item.__vModel__ && (x.ConditionText == "@userId" || x.ConditionText == "@userAraSubordinates")).ToList();
    //         await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //
    //         noDelData = oldDataAuth.Except(delData).ToList(); // 记录未删除
    //     }
    //     else
    //     {
    //         // 删除
    //         List<ModuleDataAuthorizeEntity>? delData = oldDataAuth.Where(x => x.ConditionText == "@userId" || x.ConditionText == "@userAraSubordinates").ToList();
    //         await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //     }
    //
    //     // 所属组织
    //     item = fields.FirstOrDefault(x => x.__config__.jnpfKey == JnpfKeyConst.CURRORGANIZE);
    //     if (item != null)
    //     {
    //         var fRule = item.__vModel__.Contains("_jnpf_") ? 1 : 0;
    //         fRule = item.__vModel__.ToLower().Contains("tablefield") && item.__vModel__.Contains("-") ? 2 : fRule;
    //
    //         // 新增
    //         if (!oldDataAuth.Any(x => x.EnCode == item.__vModel__ && x.ConditionText == "@organizeId"))
    //         {
    //             authList.Add(new ModuleDataAuthorizeEntity()
    //             {
    //                 Id = SnowflakeIdHelper.NextId(),
    //                 ConditionSymbol = "==", // 条件符号
    //                 Type = "varchar", // 字段类型
    //                 FullName = item.__config__.label, // 字段说明
    //                 ConditionText = "@organizeId", // 条件内容（当前组织）
    //                 EnabledMark = 1,
    //                 SortCode = -9527,
    //                 FieldRule = fRule, // 主表/副表/子表
    //                 EnCode = fRule.Equals(1) ? item.__vModel__.Split("jnpf_").LastOrDefault() : item.__vModel__,
    //                 BindTable = fRule.Equals(2) ? item.__config__.relationTable : item.__config__.tableName,
    //                 ModuleId = menuId
    //             });
    //         }
    //
    //         if (!oldDataAuth.Any(x => x.EnCode == item.__vModel__ && x.ConditionText == "@organizationAndSuborganization"))
    //         {
    //             authList.Add(new ModuleDataAuthorizeEntity()
    //             {
    //                 Id = SnowflakeIdHelper.NextId(),
    //                 ConditionSymbol = "in", // 条件符号
    //                 Type = "varchar", // 字段类型
    //                 FullName = item.__config__.label, // 字段说明
    //                 ConditionText = "@organizationAndSuborganization", // 条件内容（当前组织及组织）
    //                 EnabledMark = 1,
    //                 SortCode = -9527,
    //                 FieldRule = fRule, // 主表/副表/子表
    //                 EnCode = fRule.Equals(1) ? item.__vModel__.Split("jnpf_").LastOrDefault() : item.__vModel__,
    //                 BindTable = fRule.Equals(2) ? item.__config__.relationTable : item.__config__.tableName,
    //                 ModuleId = menuId
    //             });
    //         }
    //
    //         // 删除
    //         List<ModuleDataAuthorizeEntity>? delData = oldDataAuth.Where(x => x.EnCode != item.__vModel__ && (x.ConditionText == "@organizeId" || x.ConditionText == "@organizationAndSuborganization")).ToList();
    //         await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //
    //         noDelData = oldDataAuth.Except(delData).ToList(); // 记录未删除
    //     }
    //     else
    //     {
    //         // 删除
    //         List<ModuleDataAuthorizeEntity>? delData = oldDataAuth.Where(x => x.ConditionText == "@organizeId" || x.ConditionText == "@organizationAndSuborganization").ToList();
    //         await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //     }
    //
    //     // 当前分管组织
    //     item = fields.FirstOrDefault(x => x.__config__.jnpfKey == JnpfKeyConst.CURRORGANIZE);
    //     if (item != null)
    //     {
    //         var fRule = item.__vModel__.Contains("_jnpf_") ? 1 : 0;
    //         fRule = item.__vModel__.ToLower().Contains("tablefield") && item.__vModel__.Contains("-") ? 2 : fRule;
    //
    //         // 新增
    //         if (!oldDataAuth.Any(x => x.EnCode == item.__vModel__ && x.ConditionText == "@branchManageOrganize"))
    //         {
    //             authList.Add(new ModuleDataAuthorizeEntity()
    //             {
    //                 Id = SnowflakeIdHelper.NextId(),
    //                 ConditionSymbol = "in", // 条件符号
    //                 Type = "varchar", // 字段类型
    //                 FullName = item.__config__.label, // 字段说明
    //                 ConditionText = "@branchManageOrganize", // 条件内容（当前分管组织）
    //                 EnabledMark = 1,
    //                 SortCode = -9527,
    //                 FieldRule = fRule, // 主表/副表/子表
    //                 EnCode = fRule.Equals(1) ? item.__vModel__.Split("jnpf_").LastOrDefault() : item.__vModel__,
    //                 BindTable = fRule.Equals(2) ? item.__config__.relationTable : item.__config__.tableName,
    //                 ModuleId = menuId
    //             });
    //         }
    //
    //         // 删除
    //         List<ModuleDataAuthorizeEntity>? delData = oldDataAuth.Where(x => x.EnCode != item.__vModel__ && (x.ConditionText == "@branchManageOrganize")).ToList();
    //         await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //
    //         noDelData = oldDataAuth.Except(delData).ToList(); // 记录未删除
    //     }
    //     else
    //     {
    //         // 删除
    //         List<ModuleDataAuthorizeEntity>? delData = oldDataAuth.Where(x => x.ConditionText == "@branchManageOrganize").ToList();
    //         await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //     }
    //
    //     if (authList.Any()) await _visualDevRepository.AsSugarClient().Insertable(authList).CallEntityMethod(m => m.Create()).ExecuteCommandAsync();
    //     if (noDelData.Any()) authList.AddRange(noDelData);
    //     return authList.Any() ? authList : oldDataAuth;
    // }
    //
    // /// <summary>
    // /// 组装菜单 数据权限 方案管理数据.
    // /// </summary>
    // /// <param name="menuId">菜单ID.</param>
    // /// <param name="authList">字段管理列表.</param>
    // /// <param name="fields">功能模板控件集合.</param>
    // /// <returns></returns>
    // private async Task MenuMergeDataAuthScheme(string menuId, List<ModuleDataAuthorizeEntity> authList, List<FieldsModel> fields)
    // {
    //     // 旧的自动生成的 方案管理
    //     List<ModuleDataAuthorizeSchemeEntity>? oldDataAuthScheme = await _visualDevRepository.AsSugarClient().Queryable<ModuleDataAuthorizeSchemeEntity>()
    //         .Where(x => x.ModuleId == menuId && x.DeleteMark == null && x.SortCode.Equals(-9527))
    //         .Where(x => x.ConditionJson.Contains("\"op\":\"==\",\"value\":\"@userId\"")
    //         || x.ConditionJson.Contains("\"op\":\"in\",\"value\":\"@userAraSubordinates\"")
    //         || x.ConditionJson.Contains("\"op\":\"==\",\"value\":\"@organizeId\"")
    //         || x.ConditionJson.Contains("\"op\":\"in\",\"value\":\"@organizationAndSuborganization\"")
    //         || x.ConditionJson.Contains("\"op\":\"in\",\"value\":\"@branchManageOrganize\""))
    //         .ToListAsync();
    //
    //     List<ModuleDataAuthorizeSchemeEntity>? authSchemeList = new List<ModuleDataAuthorizeSchemeEntity>(); // 方案管理
    //
    //     // 当前用户
    //     FieldsModel? item = fields.FirstOrDefault(x => x.__config__.jnpfKey == JnpfKeyConst.CREATEUSER);
    //     var condJson = new AuthorizeModuleResourceConditionModelInput()
    //     {
    //         logic = "and",
    //         groups = new List<AuthorizeModuleResourceConditionItemModelInput>() { new AuthorizeModuleResourceConditionItemModelInput() { id = "", bindTable = "", field = "", fieldRule = 0, value = "", type = "varchar", op = "==" } }
    //     };
    //
    //     if (item != null)
    //     {
    //         ModuleDataAuthorizeEntity? model = authList.FirstOrDefault(x => x.EnCode == item.__vModel__ && x.ConditionText.Equals("@userId"));
    //
    //         if (model != null)
    //         {
    //             condJson.groups.First().id = model.Id;
    //             condJson.groups.First().bindTable = model.BindTable;
    //             condJson.groups.First().field = item.__vModel__;
    //             condJson.groups.First().fieldRule = model.FieldRule.ParseToInt();
    //             condJson.groups.First().value = "@userId";
    //             condJson.groups.First().conditionText = "@userId";
    //
    //             // 新增
    //             if (!oldDataAuthScheme.Any(x => x.ConditionText == "【{" + item.__config__.label + "} {等于} {@userId}】"))
    //             {
    //                 authSchemeList.Add(new ModuleDataAuthorizeSchemeEntity()
    //                 {
    //                     FullName = "当前用户",
    //                     EnCode = SnowflakeIdHelper.NextId(),
    //                     SortCode = -9527,
    //                     ConditionText = "【{" + item.__config__.label + "} {等于} {@userId}】",
    //                     ConditionJson = new List<AuthorizeModuleResourceConditionModelInput>() { condJson }.ToJsonString(),
    //                     ModuleId = menuId,
    //                     MatchLogic = "and"
    //                 });
    //             }
    //
    //             model = authList.FirstOrDefault(x => x.EnCode == item.__vModel__ && x.ConditionText.Equals("@userAraSubordinates"));
    //             condJson.groups.First().id = model.Id;
    //             condJson.groups.First().op = "in";
    //             condJson.groups.First().value = "@userAraSubordinates";
    //             condJson.groups.First().conditionText = "@userAraSubordinates";
    //             if (!oldDataAuthScheme.Any(x => x.ConditionText == "【{" + item.__config__.label + "} {包含任意一个} {@userAraSubordinates}】"))
    //             {
    //                 authSchemeList.Add(new ModuleDataAuthorizeSchemeEntity()
    //                 {
    //                     FullName = "当前用户及下属",
    //                     EnCode = SnowflakeIdHelper.NextId(),
    //                     SortCode = -9527,
    //                     ConditionText = "【{" + item.__config__.label + "} {包含任意一个} {@userAraSubordinates}】",
    //                     ConditionJson = new List<AuthorizeModuleResourceConditionModelInput>() { condJson }.ToJsonString(),
    //                     ModuleId = menuId,
    //                     MatchLogic = "and"
    //                 });
    //             }
    //
    //             // 删除
    //             //List<ModuleDataAuthorizeSchemeEntity>? delData = oldDataAuthScheme.Where(x => x.EnCode != item.__vModel__
    //             //&& (x.ConditionJson.Contains("\"op\":\"Equal\",\"value\":\"@userId\"") || x.ConditionJson.Contains("\"op\":\"Equal\",\"value\":\"@userAraSubordinates\""))).ToList();
    //             //await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //         }
    //         else
    //         {
    //             // 删除
    //             List<ModuleDataAuthorizeSchemeEntity>? delData = oldDataAuthScheme
    //                 .Where(x => x.ConditionJson.Contains("\"op\":\"==\",\"value\":\"@userId\"") || x.ConditionJson.Contains("\"op\":\"in\",\"value\":\"@userAraSubordinates\"")).ToList();
    //             await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //         }
    //     }
    //     else
    //     {
    //         // 删除
    //         List<ModuleDataAuthorizeSchemeEntity>? delData = oldDataAuthScheme
    //             .Where(x => x.ConditionJson.Contains("\"op\":\"==\",\"value\":\"@userId\"") || x.ConditionJson.Contains("\"op\":\"in\",\"value\":\"@userAraSubordinates\"")).ToList();
    //         await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //     }
    //
    //     // 当前组织
    //     item = fields.FirstOrDefault(x => x.__config__.jnpfKey == JnpfKeyConst.CURRORGANIZE);
    //     if (item != null)
    //     {
    //         ModuleDataAuthorizeEntity? model = authList.FirstOrDefault(x => x.EnCode == item.__vModel__ && x.ConditionText.Equals("@organizeId"));
    //
    //         if (model != null)
    //         {
    //             condJson.groups.First().id = model.Id;
    //             condJson.groups.First().bindTable = model.BindTable;
    //             condJson.groups.First().field = item.__vModel__;
    //             condJson.groups.First().fieldRule = model.FieldRule.ParseToInt();
    //             condJson.groups.First().op = "==";
    //             condJson.groups.First().value = "@organizeId";
    //             condJson.groups.First().conditionText = "@organizeId";
    //
    //             // 新增
    //             if (!oldDataAuthScheme.Any(x => x.ConditionText == "【{" + item.__config__.label + "} {等于} {@organizeId}】"))
    //             {
    //                 authSchemeList.Add(new ModuleDataAuthorizeSchemeEntity()
    //                 {
    //                     FullName = "当前组织",
    //                     EnCode = SnowflakeIdHelper.NextId(),
    //                     SortCode = -9527,
    //                     ConditionText = "【{" + item.__config__.label + "} {等于} {@organizeId}】",
    //                     ConditionJson = new List<AuthorizeModuleResourceConditionModelInput>() { condJson }.ToJsonString(),
    //                     ModuleId = menuId,
    //                     MatchLogic = "and"
    //                 });
    //             }
    //
    //             model = authList.FirstOrDefault(x => x.EnCode == item.__vModel__ && x.ConditionText.Equals("@organizationAndSuborganization"));
    //             condJson.groups.First().id = model.Id;
    //             condJson.groups.First().op = "in";
    //             condJson.groups.First().value = "@organizationAndSuborganization";
    //             condJson.groups.First().conditionText = "@organizationAndSuborganization";
    //             if (!oldDataAuthScheme.Any(x => x.ConditionText == "【{" + item.__config__.label + "} {包含任意一个} {@organizationAndSuborganization}】"))
    //             {
    //                 authSchemeList.Add(new ModuleDataAuthorizeSchemeEntity()
    //                 {
    //                     FullName = "当前组织及子组织",
    //                     EnCode = SnowflakeIdHelper.NextId(),
    //                     SortCode = -9527,
    //                     ConditionText = "【{" + item.__config__.label + "} {包含任意一个} {@organizationAndSuborganization}】",
    //                     ConditionJson = new List<AuthorizeModuleResourceConditionModelInput>() { condJson }.ToJsonString(),
    //                     ModuleId = menuId,
    //                     MatchLogic = "and"
    //                 });
    //             }
    //
    //             // 删除
    //             //List<ModuleDataAuthorizeSchemeEntity>? delData = oldDataAuthScheme.Where(x => x.EnCode != item.__vModel__
    //             //&& (x.ConditionJson.Contains("\"op\":\"Equal\",\"value\":\"@organizeId\"") || x.ConditionJson.Contains("\"op\":\"Equal\",\"value\":\"@organizationAndSuborganization\""))).ToList();
    //             //await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //         }
    //         else
    //         {
    //             // 删除
    //             List<ModuleDataAuthorizeSchemeEntity>? delData = oldDataAuthScheme
    //                 .Where(x => x.ConditionJson.Contains("\"op\":\"==\",\"value\":\"@organizeId\"") || x.ConditionJson.Contains("\"op\":\"in\",\"value\":\"@organizationAndSuborganization\"")).ToList();
    //             await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //         }
    //     }
    //     else
    //     {
    //         // 删除
    //         List<ModuleDataAuthorizeSchemeEntity>? delData = oldDataAuthScheme
    //             .Where(x => x.ConditionJson.Contains("\"op\":\"==\",\"value\":\"@organizeId\"") || x.ConditionJson.Contains("\"op\":\"in\",\"value\":\"@organizationAndSuborganization\"")).ToList();
    //         await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //     }
    //
    //     // 当前分管组织
    //     item = fields.FirstOrDefault(x => x.__config__.jnpfKey == JnpfKeyConst.CURRORGANIZE);
    //     if (item != null)
    //     {
    //         ModuleDataAuthorizeEntity? model = authList.FirstOrDefault(x => x.EnCode == item.__vModel__ && x.ConditionText.Equals("@branchManageOrganize"));
    //
    //         if (model != null)
    //         {
    //             condJson.groups.First().id = model.Id;
    //             condJson.groups.First().bindTable = model.BindTable;
    //             condJson.groups.First().field = item.__vModel__;
    //             condJson.groups.First().fieldRule = model.FieldRule.ParseToInt();
    //             condJson.groups.First().op = "in";
    //             condJson.groups.First().value = "@branchManageOrganize";
    //             condJson.groups.First().conditionText = "@branchManageOrganize";
    //
    //             // 新增
    //             if (!oldDataAuthScheme.Any(x => x.ConditionText == "【{" + item.__config__.label + "} {包含任意一个} {@branchManageOrganize}】"))
    //             {
    //                 authSchemeList.Add(new ModuleDataAuthorizeSchemeEntity()
    //                 {
    //                     FullName = "当前分管组织",
    //                     EnCode = SnowflakeIdHelper.NextId(),
    //                     SortCode = -9527,
    //                     ConditionText = "【{" + item.__config__.label + "} {包含任意一个} {@branchManageOrganize}】",
    //                     ConditionJson = new List<AuthorizeModuleResourceConditionModelInput>() { condJson }.ToJsonString(),
    //                     ModuleId = menuId,
    //                     MatchLogic = "and"
    //                 });
    //             }
    //
    //             // 删除
    //             //List<ModuleDataAuthorizeSchemeEntity>? delData = oldDataAuthScheme.Where(x => x.EnCode != item.__vModel__
    //             //&& (x.ConditionJson.Contains("\"op\":\"Equal\",\"value\":\"@branchManageOrganize\"") || x.ConditionJson.Contains("\"op\":\"Equal\",\"value\":\"@branchManageOrganizeAndSub\""))).ToList();
    //             //await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //         }
    //         else
    //         {
    //             // 删除
    //             List<ModuleDataAuthorizeSchemeEntity>? delData = oldDataAuthScheme
    //                 .Where(x => x.ConditionJson.Contains("\"op\":\"in\",\"value\":\"@branchManageOrganize\"")).ToList();
    //             await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //         }
    //     }
    //     else
    //     {
    //         // 删除
    //         List<ModuleDataAuthorizeSchemeEntity>? delData = oldDataAuthScheme
    //             .Where(x => x.ConditionJson.Contains("\"op\":\"in\",\"value\":\"@branchManageOrganize\"")).ToList();
    //         await _visualDevRepository.AsSugarClient().Deleteable(delData).ExecuteCommandAsync();
    //     }
    //
    //     if (authSchemeList.Any()) await _visualDevRepository.AsSugarClient().Insertable(authSchemeList).CallEntityMethod(m => m.Creator()).ExecuteCommandAsync();
    // }

    /// <summary>
    ///     无限递归 给控件绑定tableName (绕过 布局控件).
    /// </summary>
    private void FieldBindTable(List<Dictionary<string, object>> dicFieldsModelList, Dictionary<string, string> childTableDic, string tableName)
    {
        foreach (Dictionary<string, object> item in dicFieldsModelList)
        {
            Dictionary<string, object> obj = item["__config__"].ToJson().ToObjectOld<Dictionary<string, object>>();

            if (obj.ContainsKey("jnpfKey") && obj["jnpfKey"].Equals(JnpfKeyConst.TABLE))
            {
                obj["tableName"] = childTableDic[item["__vModel__"].ToString()];
            }
            else if (obj.ContainsKey("tableName"))
            {
                obj["tableName"] = tableName;
            }

            // 关联表单属性和弹窗属性
            if (obj.ContainsKey("jnpfKey") && (obj["jnpfKey"].Equals(JnpfKeyConst.RELATIONFORMATTR) || obj["jnpfKey"].Equals(JnpfKeyConst.POPUPATTR)))
            {
                string relationField = Convert.ToString(item["relationField"]);
                string? rField = relationField.ReplaceRegex(@"_jnpfTable_(\w+)", string.Empty);
                item["relationField"] = string.Format("{0}{1}{2}{3}", rField, "_jnpfTable_", tableName, "1");
            }

            // 子表控件
            if (obj.ContainsKey("jnpfKey") && obj["jnpfKey"].Equals(JnpfKeyConst.TABLE))
            {
                List<Dictionary<string, object>> cList = obj["children"].ToJson().ToObjectOld<List<Dictionary<string, object>>>();
                foreach (Dictionary<string, object> child in cList)
                {
                    Dictionary<string, object> cObj = child["__config__"].ToJson().ToObjectOld<Dictionary<string, object>>();
                    if (cObj.ContainsKey("relationTable"))
                    {
                        cObj["relationTable"] = childTableDic[item["__vModel__"].ToString()];
                    }
                    else
                    {
                        cObj.Add("relationTable", childTableDic[item["__vModel__"].ToString()]);
                    }

                    if (cObj.ContainsKey("tableName"))
                    {
                        cObj["tableName"] = obj["tableName"];
                    }

                    // 关联表单属性和弹窗属性
                    if (cObj.ContainsKey("jnpfKey") && (cObj["jnpfKey"].Equals(JnpfKeyConst.RELATIONFORMATTR) || cObj["jnpfKey"].Equals(JnpfKeyConst.POPUPATTR)))
                    {
                        string relationField = Convert.ToString(child["relationField"]);
                        string? rField = relationField.ReplaceRegex(@"_jnpfTable_(\w+)", string.Empty);
                        if (child.ContainsKey("relationField"))
                        {
                            child["relationField"] = string.Format("{0}{1}{2}{3}", rField, "_jnpfTable_", cObj["tableName"], "0");
                        }
                        else
                        {
                            child.Add("relationField", string.Format("{0}{1}{2}{3}", rField, "_jnpfTable_", cObj["tableName"], "0"));
                        }
                    }

                    child["__config__"] = cObj;
                }

                obj["children"] = cList;
            }

            // 递归
            if (obj.ContainsKey("children") && !obj["jnpfKey"].Equals(JnpfKeyConst.TABLE))
            {
                List<Dictionary<string, object>> fmList = obj["children"].ToJson().ToObjectOld<List<Dictionary<string, object>>>();
                FieldBindTable(fmList, childTableDic, tableName);
                obj["children"] = fmList;
            }

            item["__config__"] = obj;
        }
    }

    /// <summary>
    ///     验证主键策略 数据库表是否支持.
    /// </summary>
    /// <param name="tInfo">模板信息.</param>
    /// <param name="dbLinkId">数据库连接id.</param>
    private async Task VerifyPrimaryKeyPolicy(TemplateParsingBase tInfo, long dbLinkId)
    {
        if (tInfo.IsHasTable)
        {
            DbLink link = await _runService.GetDbLink(dbLinkId);
            tInfo.AllTable.ForEach(item =>
            {
                List<DbTableFieldModel>? tableList = _changeDataBase.GetFieldList(link, item.table); // 获取主表所有列
                DbTableFieldModel? mainPrimary = tableList.Find(t => t.primaryKey); // 主表主键
                if (mainPrimary == null)
                {
                    throw Oops.Oh(ErrorCode.D1409, "主键为空", item.table);
                }

                if (tInfo.FormModel.primaryKeyPolicy.Equals(2) && !mainPrimary.identity)
                {
                    throw Oops.Oh(ErrorCode.D1409, "自增长ID,没有自增标识", item.table);
                }

                if (tInfo.FormModel.primaryKeyPolicy.Equals(1) && !(mainPrimary.dataType.ToLower().Equals("string") || mainPrimary.dataType.ToLower().Equals("varchar") ||
                                                                    mainPrimary.dataType.ToLower().Equals("nvarchar")))
                {
                    throw Oops.Oh(ErrorCode.D1409, "雪花ID", item.table);
                }
            });
        }
    }

    // /// <summary>
    // /// 同步到流程相关.
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // private async Task SaveFlowTemplate(VisualDevEntity input)
    // {
    //     if (!(await _visualDevRepository.AsSugarClient().Queryable<FlowTemplateEntity>().AnyAsync(x => x.Id.Equals(input.Id))))
    //     {
    //         if (await _visualDevRepository.AsSugarClient().Queryable<FlowTemplateEntity>().AnyAsync(x => (x.EnCode == input.EnCode || x.FullName == input.FullName) && x.DeleteMark == null))
    //             throw Oops.Oh(ErrorCode.COM1004);
    //         //var dictionaryTypeEntity = await _visualDevRepository.AsSugarClient().Queryable<DictionaryTypeEntity>().FirstAsync(x => x.EnCode == "WorkFlowCategory" && x.DeleteMark == null);
    //         //var dicType = await _visualDevRepository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(x => x.Id.Equals(input.Category)).FirstAsync();
    //         //var flowType = await _visualDevRepository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(x => x.EnCode.Equals(dicType.EnCode) && x.DictionaryTypeId.Equals(dictionaryTypeEntity.Id)).FirstAsync();
    //         //if (flowType == null) flowType = await _visualDevRepository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(x => x.DictionaryTypeId.Equals(dictionaryTypeEntity.Id)).FirstAsync();
    //
    //         var flowTemplateEntity = input.Adapt<FlowTemplateEntity>();
    //         flowTemplateEntity.EnabledMark = 0;
    //         flowTemplateEntity.Type = 1;
    //         flowTemplateEntity.Category = input.Category;
    //         //flowTemplateEntity.IconBackground = "#008cff";.
    //         //flowTemplateEntity.Icon = "icon-ym icon-ym-node";
    //
    //         var result = await _visualDevRepository.AsSugarClient().Insertable(flowTemplateEntity).CallEntityMethod(m => m.Create()).ExecuteReturnEntityAsync();
    //         if (result == null)
    //             throw Oops.Oh(ErrorCode.COM1005);
    //     }
    // }

    /// <summary>
    ///     同步业务字段.
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private async Task SyncField(VisualDevEntity entity)
    {
        if (entity.Tables.IsNotEmptyOrNull() && !entity.Tables.Equals("[]") && entity.FormData.IsNotEmptyOrNull())
        {
            TemplateParsingBase? tInfo = new(entity); // 解析模板
            DbLink link = await _runService.GetDbLink(entity.DbLinkId);
            tInfo.DbLink = link;
            await _runService.SyncField(tInfo);
        }
    }

    #endregion
}