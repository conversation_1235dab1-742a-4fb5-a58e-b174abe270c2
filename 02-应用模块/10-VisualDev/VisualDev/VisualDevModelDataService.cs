using DateTime = System.DateTime;

namespace VisualDev;

/// <summary>
///     可视化开发基础.
/// </summary>
[ApiDescriptionSettings("功能设计", Tag = "VisualDev", Name = "OnlineDev", Order = 172)]
[Route("visualdev/[controller]")]
public class VisualDevModelDataService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<VisualDevEntity> _visualDevRepository; // 在线开发功能实体

    /// <summary>
    ///     可视化开发基础.
    /// </summary>
    private readonly IVisualDevService _visualDevService;

    /// <summary>
    ///     在线开发运行服务.
    /// </summary>
    private readonly RunService _runService;

    /// <summary>
    ///     模板表单列表数据解析.
    /// </summary>
    private readonly FormDataParsing _formDataParsing;

    /// <summary>
    ///     用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    ///     缓存管理.
    /// </summary>
    private readonly SysCacheService _cacheManager;

    /// <summary>
    ///     文件服务.
    /// </summary>
    private readonly IFileManager _fileManager;

    // /// <summary>
    // ///     工作流.
    // /// </summary>
    // private readonly IFlowTaskService _flowTaskService;

    /// <summary>
    ///     数据连接服务.
    /// </summary>
    private readonly DbLinkService _dbLinkService;

    /// <summary>
    ///     切库.
    /// </summary>
    private readonly IDataBaseManager _databaseService;

    // /// <summary>
    // ///     数据接口.
    // /// </summary>
    // private readonly IDataInterfaceService _dataInterfaceService;

    /// <summary>
    ///     事件总线.
    /// </summary>
    private readonly IEventPublisher _eventPublisher;

    /// <summary>
    ///     单据
    /// </summary>
    private readonly BillRuleService _billRuleService;

    /// <summary>
    ///     初始化一个<see cref="VisualDevModelDataService" />类型的新实例.
    /// </summary>
    public VisualDevModelDataService(
        ISqlSugarRepository<VisualDevEntity> visualDevRepository,
        IVisualDevService visualDevService,
        RunService runService,
        FormDataParsing formDataParsing,
        DbLinkService dbLinkService,
        IUserManager userManager,
        IDataBaseManager databaseService,
        SysCacheService cacheManager,
        IFileManager fileManager,
        IEventPublisher eventPublisher, BillRuleService billRuleService)
    {
        _visualDevRepository = visualDevRepository;
        _visualDevService = visualDevService;
        _databaseService = databaseService;
        _dbLinkService = dbLinkService;
        _runService = runService;
        _formDataParsing = formDataParsing;
        _userManager = userManager;
        _cacheManager = cacheManager;
        _fileManager = fileManager;
        _eventPublisher = eventPublisher;
        _billRuleService = billRuleService;
    }

    #region Get

    /// <summary>
    ///     获取列表表单配置JSON.
    /// </summary>
    /// <param name="modelId">主键id.</param>
    /// <param name="type">1 线上版本, 0 草稿版本.</param>
    /// <returns></returns>
    [HttpGet("{modelId}/Config")]
    public async Task<dynamic> GetData(string modelId, string type)
    {
        if (type.IsNullOrEmpty())
        {
            type = "1";
        }

        VisualDevEntity? data = await _visualDevService.GetInfoById(modelId.ToString(), type.Equals("1"));
        if (data == null)
        {
            throw Oops.Bah(ErrorCode.COM1018, "该表单已删除");
        }

        if (data.EnableFlow.Equals(-1) && data.FlowId.IsNotEmptyOrNull())
        {
            throw Oops.Bah(ErrorCode.COM1018, "该功能配置的流程已停用!");
        }

        if (data.EnableFlow.Equals(1) && data.FlowId.IsNullOrWhiteSpace())
        {
            throw Oops.Bah(ErrorCode.COM1018, "该流程功能未绑定流程!");
        }

        if (data.WebType.Equals(1) && data.FormData.IsNullOrWhiteSpace())
        {
            throw Oops.Bah(ErrorCode.COM1018, "该模板内表单内容为空，无法预览!");
        }

        if (data.WebType.Equals(2) && data.ColumnData.IsNullOrWhiteSpace())
        {
            throw Oops.Bah(ErrorCode.COM1018, "该模板内列表内容为空，无法预览!");
        }

        return GetVisualDevModelDataConfig(data);
    }

    /// <summary>
    ///     获取列表配置JSON.
    /// </summary>
    /// <param name="modelId">主键id.</param>
    /// <returns></returns>
    [HttpGet("{modelId}/ColumnData")]
    public async Task<dynamic> GetColumnData(string modelId)
    {
        VisualDevEntity? data = await _visualDevService.GetInfoById(modelId.ToString());
        return new { columnData = data.ColumnData };
    }

    /// <summary>
    ///     获取列表配置JSON.
    /// </summary>
    /// <param name="modelId">主键id.</param>
    /// <returns></returns>
    [HttpGet("{modelId}/FormData")]
    public async Task<dynamic> GetFormData(string modelId)
    {
        VisualDevEntity? data = await _visualDevService.GetInfoById(modelId.ToString());
        return new { formData = data.FormData };
    }

    /// <summary>
    ///     获取数据信息.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="modelId"></param>
    /// <returns></returns>
    [HttpGet("{modelId}/{id}")]
    [AllowAnonymous]
    public async Task<dynamic> GetInfo(string id, string modelId)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true); // 模板实体

        // 有表
        if (!string.IsNullOrEmpty(templateEntity.Tables) && !"[]".Equals(templateEntity.Tables))
        {
            Dictionary<string, object> haveTableInfo = await _runService.GetHaveTableInfo(id.ToString(), templateEntity);
            foreach ((string key, object value) in haveTableInfo)
            {
                haveTableInfo[key] = value.GetJsonElementValue();
            }

            string? data = haveTableInfo.ToJson();
            return new { id, data };
        }

        return null;
    }

    /// <summary>
    ///     获取详情.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="modelId"></param>
    /// <returns></returns>
    [HttpGet("{modelId}/{id}/DataChange")]
    [AllowAnonymous]
    public async Task<dynamic> GetDetails(string id, string modelId)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true); // 模板实体
        // 有表
        if (!string.IsNullOrEmpty(templateEntity.Tables) && !"[]".Equals(templateEntity.Tables))
        {
            return new { id, data = await _runService.GetHaveTableInfoDetails(id.ToString(), templateEntity) };
        }

        return null;
    }

    #endregion

    #region Post

    /// <summary>
    ///     功能导出.
    /// </summary>
    /// <param name="modelId"></param>
    /// <returns></returns>
    [HttpPost("{modelId}/Actions/Export")]
    public async Task<dynamic> ActionsExport(string modelId)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString()); // 模板实体
        if (templateEntity.State.Equals(1))
        {
            VisualDevReleaseEntity? vREntity = await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().FirstAsync(v => v.Id == modelId.ParseToLong());
            templateEntity = vREntity.Adapt<VisualDevEntity>();
            templateEntity.State = 0;
        }

        // 直接返回对象，由前端自行导出  
        return templateEntity;
        // string? jsonStr = templateEntity.ToJson();
        // return await _fileManager.Export(jsonStr, templateEntity.FullName);
    }

    /// <summary>
    ///     导入.
    /// </summary>
    /// <param name="file"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    [HttpPost("Actions/Import")]
    [UnitOfWork]
    public async Task ActionsImport(IFormFile file, int type)
    {
        string fileType = Path.GetExtension(file.FileName).Replace(".", string.Empty);
        if (!fileType.ToLower().Equals("json"))
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        string josn = _fileManager.Import(file);
        VisualDevEntity? templateEntity;
        try
        {
            templateEntity = josn.ToObjectOld<VisualDevEntity>();
        }
        catch
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        if (templateEntity == null || templateEntity.Type.IsNullOrEmpty())
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        if (templateEntity.Type != 1)
        {
            throw Oops.Oh(ErrorCode.D3009);
        }

        await _visualDevService.CreateImportData(templateEntity, type);
    }

    /// <summary>
    ///     获取数据列表.
    /// </summary>
    /// <param name="modelId">主键id.</param>
    /// <param name="input">分页查询条件.</param>
    /// <returns></returns>
    [HttpPost("{modelId}/List")]
    [UnifySerializerSetting("special")]
    [AllowAnonymous]
    public async Task<dynamic> List(string modelId, [FromBody] VisualDevModelListQueryInput input)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true);
        return await _runService.GetListResult(templateEntity, input);
    }

    /// <summary>
    ///     外链获取数据列表.
    /// </summary>
    /// <param name="modelId">主键id.</param>
    /// <param name="input">分页查询条件.</param>
    /// <returns></returns>
    [HttpPost("{modelId}/ListLink")]
    [AllowAnonymous]
    // [IgnoreLog]
    public async Task<dynamic> ListLink(string modelId, [FromBody] VisualDevModelListQueryInput input)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true);
        if (templateEntity == null)
        {
            throw Oops.Oh(ErrorCode.D1420);
        }

        return await _runService.GetListResult(templateEntity, input);
    }

    /// <summary>
    ///     创建数据.
    /// </summary>
    /// <param name="modelId"></param>
    /// <param name="visualDevModelDataCrForm"></param>
    /// <returns></returns>
    [HttpPost("{modelId}")]
    public async Task<string> Create(string modelId, [FromBody] VisualDevModelDataCrInput visualDevModelDataCrForm)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true);
        await CheckRule(visualDevModelDataCrForm, templateEntity);
        return await _runService.Create(templateEntity, visualDevModelDataCrForm);
    }

    /// <summary>
    ///     修改数据.
    /// </summary>
    /// <param name="modelId"></param>
    /// <param name="id"></param>
    /// <param name="visualDevModelDataUpForm"></param>
    /// <returns></returns>
    [HttpPut("{modelId}/{id}")]
    public async Task Update(string modelId, string id, [FromBody] VisualDevModelDataUpInput visualDevModelDataUpForm)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true);
        await CheckRule(visualDevModelDataUpForm, templateEntity);
        await _runService.Update(id.ToString(), templateEntity, visualDevModelDataUpForm);
    }

    /// <summary>
    ///     修改数据（集成助手）.
    /// </summary>
    /// <param name="modelId"></param>
    /// <param name="visualDevModelDataUpForm"></param>
    /// <returns></returns>
    [HttpPut("batchUpdate/{modelId}")]
    public async Task BatchUpdate(string modelId, [FromBody] VisualDevModelDataUpInput visualDevModelDataUpForm)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true);
        await _runService.BatchUpdate(visualDevModelDataUpForm.idList, templateEntity, visualDevModelDataUpForm);
    }

    /// <summary>
    ///     删除数据.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="modelId"></param>
    /// <returns></returns>
    [HttpDelete("{modelId}/{id}")]
    public async Task Delete(string id, string modelId)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true);
        if (!string.IsNullOrEmpty(templateEntity.Tables) && !"[]".Equals(templateEntity.Tables))
        {
            await _runService.DelHaveTableInfo(id, templateEntity);
        }
    }

    /// <summary>
    ///     删除集成助手数据.
    /// </summary>
    /// <param name="modelId"></param>
    /// <returns></returns>
    [HttpDelete("DelInteAssistant/{modelId}")]
    public async Task DelInteAssistant(string modelId)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true);
        if (!string.IsNullOrEmpty(templateEntity.Tables) && !"[]".Equals(templateEntity.Tables))
        {
            await _runService.DelInteAssistant(templateEntity);
        }
    }

    /// <summary>
    ///     批量删除.
    /// </summary>
    /// <param name="modelId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("batchDelete/{modelId}")]
    [UnitOfWork]
    public async Task BatchDelete(string modelId, [FromBody] VisualDevModelDataBatchDelInput input)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true);
        if (!string.IsNullOrEmpty(templateEntity.Tables) && !"[]".Equals(templateEntity.Tables))
        {
            await _runService.BatchDelHaveTableData(input.ids, templateEntity, input);
        }
    }

    /// <summary>
    ///     导出.
    /// </summary>
    /// <param name="modelId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("{modelId}/Actions/ExportData")]
    public async Task<dynamic> ExportData(string modelId, [FromBody] VisualDevModelListQueryInput input)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId.ToString(), true);
        if (input.dataType == "1")
        {
            input.pageSize = 99999999;
            input.currentPage = 1;
        }

        SqlSugarPagedList<Dictionary<string, object>> pageList = await _runService.GetListResult(templateEntity, input);

        // 如果是 分组表格 模板
        ColumnDesignModel? columnData = templateEntity.ColumnData.ToObjectOld<ColumnDesignModel>(); // 列配置模型
        if (columnData.type == 3)
        {
            List<Dictionary<string, object>>? newValueList = new();
            pageList.Rows.ToList().ForEach(item =>
            {
                List<Dictionary<string, object>>? tt = item["children"].ToJson().ToObjectOld<List<Dictionary<string, object>>>();
                newValueList.AddRange(tt);
            });
            pageList.Rows = newValueList;
        }

        // 导出当前选择数据
        List<Dictionary<string, object>> selectList = new();
        if (input.dataType == "2" && input.selectIds.Any())
        {
            foreach (Dictionary<string, object> item in pageList.Rows)
            {
                if (item.ContainsKey("id") && input.selectIds.Contains(item["id"]))
                {
                    selectList.Add(item);
                }
            }

            pageList.Rows = selectList;
            pageList.TotalRows = selectList.Count;
        }

        TemplateParsingBase templateInfo = new(templateEntity);
        templateInfo.AllFieldsModel.Where(x => x.__vModel__.IsNotEmptyOrNull()).ToList().ForEach(item => item.__config__.label = string.Format("{0}({1})", item.__config__.label, item.__vModel__));
        (Dictionary<string, int>, List<Dictionary<string, object>>) res = GetCreateFirstColumnsHeader(input.selectKey, (List<Dictionary<string, object>>)pageList.Rows, templateInfo.AllFieldsModel,
            templateInfo.ColumnData);
        Dictionary<string, int> firstColumns = res.Item1;
        List<Dictionary<string, object>> resultList = res.Item2;
        List<Dictionary<string, object>> newResultList = new();

        // 行内编辑
        if (templateInfo.ColumnData.type.Equals(4))
        {
            resultList.ForEach(row =>
            {
                foreach (KeyValuePair<string, object> data in row)
                {
                    if (data.Key.Contains("_name") && row.ContainsKey(data.Key.Replace("_name", string.Empty)))
                    {
                        row[data.Key.Replace("_name", string.Empty)] = data.Value;
                    }
                }
            });
        }

        resultList.ForEach(row =>
        {
            foreach (string item in input.selectKey)
            {
                if (row[item].IsNotEmptyOrNull())
                {
                    newResultList.Add(row);
                    break;
                }
            }
        });

        if (!newResultList.Any())
        {
            Dictionary<string, object> dic = new();
            dic.Add("id", "id");
            foreach (string item in input.selectKey)
            {
                dic.Add(item, string.Empty);
            }

            newResultList.Add(dic);
        }

        string? menuName = await _visualDevRepository.AsSugarClient().Queryable<SysMenu>().Where(it => it.Id.ToString() == input.menuId).Select(it => it.Name).FirstAsync() ?? templateEntity.FullName;
        string excelName = string.Format("{0}_{1}", menuName, DateTime.Now.ToString("yyyyMMddHHmmss"));
        _cacheManager.Set(excelName + ".xls", string.Empty);
        return firstColumns.Any()
            ? await ExcelCreateModel(templateInfo.AllFieldsModel, newResultList, input.selectKey, false, excelName, firstColumns)
            : await ExcelCreateModel(templateInfo.AllFieldsModel, newResultList, input.selectKey, false, excelName);
    }

    /// <summary>
    ///     模板下载.
    /// </summary>
    /// <returns></returns>
    [HttpGet("{modelId}/TemplateDownload")]
    public async Task<dynamic> TemplateDownload(string modelId, string menuId)
    {
        TemplateParsingBase tInfo = await GetUploaderTemplateInfoAsync(modelId.ToString());

        if (tInfo.selectKey == null || !tInfo.selectKey.Any())
        {
            throw Oops.Oh(ErrorCode.D1411);
        }

        // 初始化 一条空数据
        List<Dictionary<string, object>>? dataList = new();

        // 赋予默认值
        Dictionary<string, object> dicItem = new();
        tInfo.AllFieldsModel.Where(x => tInfo.selectKey.Contains(x.__vModel__)).ToList().ForEach(item =>
        {
            switch (item.__config__.jnpfKey)
            {
                case JnpfKeyConst.COMSELECT:
                    dicItem.Add(item.__vModel__, item.multiple ? "公司名称/部门名称,公司名称/部门名称" : "公司名称/部门名称");
                    break;
                case JnpfKeyConst.DEPSELECT:
                    dicItem.Add(item.__vModel__, item.multiple ? "部门名称/部门编码,部门名称/部门编码" : "部门名称/部门编码");
                    break;
                case JnpfKeyConst.POSSELECT:
                    dicItem.Add(item.__vModel__, item.multiple ? "岗位名称/岗位编码,岗位名称/岗位编码" : "岗位名称/岗位编码");
                    break;
                case JnpfKeyConst.USERSSELECT:
                    dicItem.Add(item.__vModel__, item.multiple ? "姓名/账号,公司名称,部门名称/部门编码,岗位名称/岗位编码,角色名称/角色编码,分组名称/分组编码" : "姓名/账号");
                    break;
                case JnpfKeyConst.USERSELECT:
                    dicItem.Add(item.__vModel__, item.multiple ? "姓名/账号,姓名/账号" : "姓名/账号");
                    break;
                case JnpfKeyConst.ROLESELECT:
                    dicItem.Add(item.__vModel__, item.multiple ? "角色名称/角色编码,角色名称/角色编码" : "角色名称/角色编码");
                    break;
                // case JnpfKeyConst.GROUPSELECT:
                //     dicItem.Add(item.__vModel__, item.multiple ? "分组名称/分组编码,分组名称/分组编码" : "分组名称/分组编码");
                //     break;
                case JnpfKeyConst.DATE:
                case JnpfKeyConst.TIME:
                    dicItem.Add(item.__vModel__, string.Format("{0}", item.format));
                    break;
                case JnpfKeyConst.ADDRESS:
                    switch (item.level)
                    {
                        case 0:
                            dicItem.Add(item.__vModel__, item.multiple ? "省,省" : "省");
                            break;
                        case 1:
                            dicItem.Add(item.__vModel__, item.multiple ? "省/市,省/市" : "省/市");
                            break;
                        case 2:
                            dicItem.Add(item.__vModel__, item.multiple ? "省/市/区,省/市/区" : "省/市/区");
                            break;
                        case 3:
                            dicItem.Add(item.__vModel__, item.multiple ? "省/市/区/街道,省/市/区/街道" : "省/市/区/街道");
                            break;
                    }

                    break;
                case JnpfKeyConst.SELECT:
                    if (item.multiple)
                    {
                        dicItem.Add(item.__vModel__, "选项一,选项二");
                    }

                    break;
                case JnpfKeyConst.CHECKBOX:
                    dicItem.Add(item.__vModel__, "选项一,选项二");
                    break;
                case JnpfKeyConst.CASCADER:
                    dicItem.Add(item.__vModel__, item.multiple ? "选项1/选项1-1,选项2/选项2-1" : "选项1/选项1-1");
                    break;
                case JnpfKeyConst.TREESELECT:
                    dicItem.Add(item.__vModel__, item.multiple ? "选项1,选项2" : "选项1");
                    break;
                default:
                    dicItem.Add(item.__vModel__, string.Empty);
                    break;
            }
        });
        dicItem.Add("id", "id");
        dataList.Add(dicItem);

        string menuName = await _visualDevRepository.AsSugarClient().Queryable<SysMenu>().Where(it => it.Id == menuId.ParseToLong()).Select(it => it.Name).FirstAsync() ?? tInfo.FullName;
        menuName = _fileManager.DetectionSpecialStr(menuName);
        string? excelName = string.Format("{0}导入模板", menuName);
        (Dictionary<string, int>, List<Dictionary<string, object>>) res = GetCreateFirstColumnsHeader(tInfo.selectKey, dataList, tInfo.AllFieldsModel, tInfo.ColumnData);
        Dictionary<string, int> firstColumns = res.Item1;
        List<Dictionary<string, object>> resultList = res.Item2;
        _cacheManager.Set(excelName + ".xls", string.Empty);
        return firstColumns.Any()
            ? await ExcelCreateModel(tInfo.AllFieldsModel, resultList, tInfo.selectKey, true, excelName, firstColumns)
            : await ExcelCreateModel(tInfo.AllFieldsModel, resultList, tInfo.selectKey, true, excelName);
    }

    /// <summary>
    ///     上传文件.
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    [HttpPost("Uploader")]
    public async Task<dynamic> Uploader(IFormFile file)
    {
        string _filePath = _fileManager.GetPathByType(string.Empty);
        string _fileName = DateTime.Now.ToString("yyyyMMdd") + "_" + YitIdHelper.NextId() + Path.GetExtension(file.FileName);
        Stream stream = file.OpenReadStream();
        await _fileManager.UploadFileByType(stream, _filePath, _fileName);
        return new { name = _fileName, url = string.Format("/api/file/Image/{0}/{1}", string.Empty, _fileName) };
    }

    /// <summary>
    ///     导入预览.
    /// </summary>
    /// <returns></returns>
    [HttpGet("{modelId}/ImportPreview")]
    public async Task<dynamic> ImportPreview(string modelId, string fileName)
    {
        TemplateParsingBase tInfo = await GetUploaderTemplateInfoAsync(modelId.ToString());

        List<Dictionary<string, object>>? resData = new();
        List<dynamic> headerRow = new();

        bool isChildTable = tInfo.selectKey.Any(x => tInfo.ChildTableFields.ContainsKey(x));

        // 复杂表头
        if (!tInfo.ColumnData.type.Equals(3) && !tInfo.ColumnData.type.Equals(5) && tInfo.ColumnData.complexHeaderList.Any())
        {
            List<string> complexHeaderField = new();
            foreach (string key in tInfo.selectKey.Select(x => x.Split("-").First()).Distinct().ToList())
            {
                if (!complexHeaderField.Contains(key))
                {
                    foreach (ComplexHeaderModel ch in tInfo.ColumnData.complexHeaderList)
                    {
                        if (ch.childColumns.Contains(key))
                        {
                            List<string> columns = new();
                            foreach (string sk in tInfo.selectKey)
                            {
                                if (ch.childColumns.Contains(sk))
                                {
                                    columns.Add(sk);
                                }
                            }

                            // 调整 selectKey 顺序
                            int index = tInfo.selectKey.IndexOf(key);
                            foreach (string col in columns)
                            {
                                tInfo.selectKey.Remove(col);
                                tInfo.selectKey.Insert(index, col);
                                index++;
                                isChildTable = true;
                            }

                            complexHeaderField.AddRange(columns);
                        }
                    }
                }
            }
        }

        List<FieldsModel> fileEncode = new();
        foreach (string key in tInfo.selectKey)
        {
            FieldsModel? model = tInfo.AllFieldsModel.Find(x => key.Equals(x.__vModel__));
            if (model.IsNotEmptyOrNull())
            {
                fileEncode.Add(model);
            }
        }

        string? savePath = Path.Combine(FileVariable.TemporaryFilePath, fileName);

        // 得到数据
        Stream sr = await _fileManager.GetFileStream(savePath);
        DataTable excelData = new();
        if (isChildTable)
        {
            excelData = ExcelImportHelper.ToDataTable(savePath, sr, 0, 0, 2);
        }
        else
        {
            excelData = ExcelImportHelper.ToDataTable(savePath, sr);
        }

        if (excelData.Columns.Count > tInfo.selectKey.Count)
        {
            excelData.Columns.RemoveAt(tInfo.selectKey.Count);
        }

        if (excelData.DefaultView.Count > 1000)
        {
            throw Oops.Oh(ErrorCode.D1423);
        }

        try
        {
            for (int i = 0; i < fileEncode.Count; i++)
            {
                DataColumn? column = excelData.Columns[i];
                if (!(fileEncode[i].__vModel__ == column.ColumnName && fileEncode[i].__config__.label.Split("(").First() == column.Caption.Replace("*", string.Empty)))
                {
                    throw new Exception();
                }
            }

            resData = excelData.ToObjectOld<List<Dictionary<string, object>>>();
            if (resData.Any())
            {
                if (isChildTable)
                {
                    Dictionary<string, object>? hRow = resData[1].Copy();
                    Dictionary<string, object>? hRow2 = resData[0].Copy();
                    foreach (KeyValuePair<string, object> it in hRow)
                    {
                        if (it.Value.IsNullOrEmpty())
                        {
                            hRow[it.Key] = hRow2[it.Key];
                        }
                    }

                    foreach (KeyValuePair<string, object> item in hRow)
                    {
                        if (item.Key.ToLower().Contains("tablefield") && item.Key.Contains('-'))
                        {
                            string childVModel = item.Key.Split("-").First();
                            if (!headerRow.Any(x => x.id.Equals(childVModel)))
                            {
                                List<dynamic> child = new();
                                hRow.Where(x => x.Key.Contains(childVModel)).ToList().ForEach(x =>
                                {
                                    child.Add(new { id = x.Key.Replace(childVModel + "-", string.Empty), fullName = x.Value.ToString().Replace(string.Format("({0})", x.Key), string.Empty) });
                                });
                                headerRow.Add(new
                                {
                                    id = childVModel,
                                    fullName = tInfo.AllFieldsModel.Find(x => x.__vModel__.Equals(childVModel)).__config__.label.Replace(string.Format("({0})", childVModel), string.Empty),
                                    jnpfKey = "table", children = child
                                });
                            }
                        }
                        else if (tInfo.ColumnData.complexHeaderList.Count > 0 && tInfo.ColumnData.complexHeaderList.Any(it => it.childColumns.Contains(item.Key)))
                        {
                            ComplexHeaderModel? complexHeaderModel = tInfo.ColumnData.complexHeaderList.Find(it => it.childColumns.Contains(item.Key));
                            if (!headerRow.Any(x => x.id.Equals(complexHeaderModel.id)))
                            {
                                List<dynamic> child = new();
                                foreach (string key in tInfo.selectKey)
                                {
                                    if (complexHeaderModel.childColumns.Contains(key) && hRow.ContainsKey(key))
                                    {
                                        child.Add(new { id = key, fullName = hRow[key].ToString() });
                                    }
                                }

                                headerRow.Add(new { complexHeaderModel.id, complexHeaderModel.fullName, jnpfKey = "complexHeader", children = child });
                            }
                        }
                        else
                        {
                            headerRow.Add(new { id = item.Key, fullName = item.Value.ToString() });
                        }
                    }

                    resData.Remove(resData.First());
                    resData.Remove(resData.First());
                }
                else
                {
                    foreach (KeyValuePair<string, object> item in resData.First().Copy())
                    {
                        headerRow.Add(new { id = item.Key, fullName = item.Value.ToString() });
                    }

                    resData.Remove(resData.First());
                }
            }
        }
        catch (Exception)
        {
            throw Oops.Oh(ErrorCode.D1410);
        }

        try
        {
            // 带子表字段数据导入
            if (isChildTable)
            {
                List<Dictionary<string, object>> newData = new();
                List<string> singleForm = tInfo.selectKey.Where(x => !x.Contains("tablefield") && !x.Contains("tableField")).ToList();

                List<string> childTableVModel = tInfo.AllFieldsModel.Where(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.TABLE)).Select(x => x.__vModel__).ToList();

                resData.ForEach(dataItem =>
                {
                    Dictionary<string, object> addItem = new();
                    bool isNextRow = false;
                    foreach (KeyValuePair<string, object> item in dataItem)
                    {
                        if (singleForm.Contains(item.Key) && item.Value.IsNotEmptyOrNull())
                        {
                            isNextRow = true;
                        }
                    }

                    // 单条数据 (多行子表数据合并)
                    if (isNextRow)
                    {
                        singleForm.ForEach(item => addItem.Add(item, dataItem[item]));

                        // 子表数据
                        childTableVModel.ForEach(item =>
                        {
                            Dictionary<string, object> childAddItem = new();
                            tInfo.selectKey.Where(x => x.Contains(item)).ToList().ForEach(it =>
                            {
                                if (dataItem.ContainsKey(it))
                                {
                                    childAddItem.Add(it.Replace(item + "-", string.Empty), dataItem[it]);
                                }
                            });

                            if (childAddItem.Any())
                            {
                                addItem.Add(item, new List<Dictionary<string, object>> { childAddItem });
                            }
                        });

                        newData.Add(addItem);
                    }
                    else
                    {
                        Dictionary<string, object>? item = newData.LastOrDefault();
                        if (item != null)
                        {
                            // 子表数据
                            childTableVModel.ForEach(citem =>
                            {
                                Dictionary<string, object> childAddItem = new();
                                tInfo.selectKey.Where(x => x.Contains(citem)).ToList().ForEach(it => { childAddItem.Add(it.Replace(citem + "-", string.Empty), dataItem[it]); });

                                if (!item.ContainsKey(citem))
                                {
                                    item.Add(citem, new List<Dictionary<string, object>> { childAddItem });
                                }
                                else
                                {
                                    List<Dictionary<string, object>>? childList = item[citem].ToObjectOld<List<Dictionary<string, object>>>();
                                    childList.Add(childAddItem);
                                    item[citem] = childList;
                                }
                            });

                            if (!childTableVModel.Any())
                            {
                                newData.Add(dataItem);
                            }
                        }
                        else
                        {
                            singleForm.ForEach(item => addItem.Add(item, dataItem[item]));

                            // 子表数据
                            childTableVModel.ForEach(item =>
                            {
                                Dictionary<string, object> childAddItem = new();
                                tInfo.selectKey.Where(x => x.Contains(item)).ToList().ForEach(it =>
                                {
                                    if (dataItem.ContainsKey(it))
                                    {
                                        childAddItem.Add(it.Replace(item + "-", string.Empty), dataItem[it]);
                                    }
                                });

                                if (childAddItem.Any())
                                {
                                    addItem.Add(item, new List<Dictionary<string, object>> { childAddItem });
                                }
                            });

                            newData.Add(addItem);
                        }
                    }
                });
                resData = newData;
            }
        }
        catch
        {
            throw Oops.Oh(ErrorCode.D1412);
        }

        resData.ForEach(items =>
        {
            foreach (KeyValuePair<string, object> item in items)
            {
                FieldsModel? vmodel = tInfo.AllFieldsModel.FirstOrDefault(x => x.__vModel__.Equals(item.Key));
                if (vmodel != null && vmodel.__config__.jnpfKey.Equals(JnpfKeyConst.DATE) && item.Value.IsNotEmptyOrNull())
                {
                    items[item.Key] = string.Format("{0:" + vmodel.format + "} ", item.Value);
                }
                else if (vmodel != null && vmodel.__config__.jnpfKey.Equals(JnpfKeyConst.TABLE) && item.Value.IsNotEmptyOrNull())
                {
                    List<Dictionary<string, object>>? ctList = item.Value.ToObjectOld<List<Dictionary<string, object>>>();
                    ctList.ForEach(ctItems =>
                    {
                        foreach (KeyValuePair<string, object> ctItem in ctItems)
                        {
                            FieldsModel? ctVmodel = tInfo.AllFieldsModel.FirstOrDefault(x => x.__vModel__.Equals(vmodel.__vModel__ + "-" + ctItem.Key));
                            if (ctVmodel != null && ctVmodel.__config__.jnpfKey.Equals(JnpfKeyConst.DATE) && ctItem.Value.IsNotEmptyOrNull())
                            {
                                ctItems[ctItem.Key] = string.Format("{0:" + vmodel.format + "} ", ctItem.Value);
                            }
                        }
                    });
                    items[item.Key] = ctList;
                }
            }
        });

// 返回结果
        return new { dataRow = resData, headerRow };
    }

    /// <summary>
    ///     导入数据的错误报告.
    /// </summary>
    /// <param name="modelId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("{modelId}/ImportExceptionData")]
    [UnitOfWork]
    public async Task<dynamic> ExportExceptionData(string modelId, [FromBody] VisualDevImportDataInput input)
    {
        TemplateParsingBase tInfo = await GetUploaderTemplateInfoAsync(modelId.ToString());
        //object[]? res = await ImportMenuData(tInfo, list.list, tInfo.visualDevEntity);

        // 错误数据
        tInfo.selectKey.Insert(0, "errorsInfo");
        tInfo.AllFieldsModel.Add(new FieldsModel { __vModel__ = "errorsInfo", __config__ = new ConfigModel { label = "异常原因" } });
        for (int i = 0; i < input.list.Count(); i++)
        {
            input.list[i].Add("id", i);
        }

        (Dictionary<string, int>, List<Dictionary<string, object>>) result = GetCreateFirstColumnsHeader(tInfo.selectKey, input.list, tInfo.AllFieldsModel, tInfo.ColumnData);
        Dictionary<string, int> firstColumns = result.Item1;
        List<Dictionary<string, object>> resultList = result.Item2;

        string menuName = await _visualDevRepository.AsSugarClient().Queryable<SysMenu>().Where(it => it.Id == input.menuId).Select(it => it.Name).FirstAsync() ?? tInfo.FullName;
        string excelName = string.Format("{0}错误报告_{1}", menuName, DateTime.Now.ToString("yyyyMMddHHmmss"));
        _cacheManager.Set(excelName + ".xls", string.Empty);
        return firstColumns.Any()
            ? await ExcelCreateModel(tInfo.AllFieldsModel, resultList, tInfo.selectKey, true, excelName, firstColumns)
            : await ExcelCreateModel(tInfo.AllFieldsModel, resultList, tInfo.selectKey, true, excelName);
    }

    /// <summary>
    ///     导入数据.
    /// </summary>
    /// <param name="modelId"></param>
    /// <param name="list"></param>
    /// <returns></returns>
    [HttpPost("{modelId}/ImportData")]
    [UnitOfWork]
    public async Task<dynamic> ImportData(string modelId, [FromBody] VisualDevImportDataInput list)
    {
        if (list.flowId.IsNotEmptyOrNull())
        {
            foreach (Dictionary<string, object> item in list.list)
            {
                item.Add("f_flow_id", list.flowId);
            }
        }

        TemplateParsingBase tInfo = await GetUploaderTemplateInfoAsync(modelId.ToString());
        object[]? res = await ImportMenuData(tInfo, list, tInfo.visualDevEntity);
        List<Dictionary<string, object>>? addlist = res.First() as List<Dictionary<string, object>>;
        List<Dictionary<string, object>>? errorlist = res.Last() as List<Dictionary<string, object>>;
        VisualDevImportDataOutput result = new()
        {
            snum = addlist.Count,
            fnum = errorlist.Count,
            failResult = errorlist,
            resultType = errorlist.Count < 1 ? 0 : 1
        };

        return result;
    }

    #endregion

    #region PublicMethod

    /// <summary>
    ///     Excel 转输出 Entity.
    /// </summary>
    /// <param name="fieldList">控件集合.</param>
    /// <param name="realList">数据列表.</param>
    /// <param name="keys"></param>
    /// <param name="isImport"></param>
    /// <param name="excelName">导出文件名称.</param>
    /// <param name="firstColumns">手动输入第一行（合并主表列和各个子表列）.</param>
    /// <returns>VisualDevModelDataExportOutput.</returns>
    public async Task<VisualDevModelDataExportOutput> ExcelCreateModel(List<FieldsModel> fieldList, List<Dictionary<string, object>> realList, List<string> keys, bool isImport,
        string excelName = null, Dictionary<string, int> firstColumns = null)
    {
        VisualDevModelDataExportOutput output = new();
        List<string> columnList = new();
        ExcelConfig excelconfig = new();
        excelconfig.FileName = (excelName.IsNullOrEmpty() ? YitIdHelper.NextId() : excelName) + ".xls";
        excelconfig.HeadFont = "微软雅黑";
        excelconfig.HeadPoint = 10;
        excelconfig.IsAllSizeColumn = true;
        excelconfig.IsBold = true;
        excelconfig.IsAllBorder = true;
        excelconfig.IsImport = isImport;
        excelconfig.ColumnModel = new List<ExcelColumnModel>();
        foreach (string? item in keys)
        {
            FieldsModel? excelColumn = fieldList.Find(t => t.__vModel__ == item);
            if (excelColumn != null)
            {
                // Excel下拉的数据
                List<string> selectList = new();
                if (isImport)
                {
                    switch (excelColumn.__config__.jnpfKey)
                    {
                        case JnpfKeyConst.RADIO:
                        case JnpfKeyConst.SELECT:
                            if (!excelColumn.multiple)
                            {
                                string propsLabel = excelColumn.props != null ? excelColumn.props.label : string.Empty;

                                if (excelColumn.__config__.dataType.Equals("static") && excelColumn.options != null)
                                {
                                    foreach (Dictionary<string, object> option in excelColumn.options)
                                    {
                                        selectList.Add(option[propsLabel].ToString());
                                    }
                                }
                                // else if (excelColumn.__config__.dataType.Equals("dictionary"))
                                // {
                                //     var dictionaryDataList = await _visualDevRepository.AsSugarClient().Queryable<DictionaryDataEntity, DictionaryTypeEntity>((a, b) => new JoinQueryInfos(JoinType.Left, b.Id == a.DictionaryTypeId))
                                //         .WhereIF(excelColumn.__config__.dictionaryType.IsNotEmptyOrNull(), (a, b) => b.Id == excelColumn.__config__.dictionaryType || b.EnCode == excelColumn.__config__.dictionaryType)
                                //         .Where(a => a.DeleteMark == null && a.EnabledMark == 1).Select(a => new { a.Id, a.EnCode, a.FullName }).ToListAsync();
                                //     foreach (var it in dictionaryDataList) selectList.Add(it.FullName);
                                // }
                                else if (excelColumn.__config__.dataType.Equals("dynamic"))
                                {
                                    List<Dictionary<string, string>> dataList = await _formDataParsing.GetDynamicList(excelColumn);
                                    foreach (Dictionary<string, string> data in dataList)
                                    {
                                        selectList.Add(data.FirstOrDefault().Value);
                                    }
                                }
                            }

                            break;
                        case JnpfKeyConst.SWITCH:
                            if (excelColumn.activeTxt.IsNotEmptyOrNull())
                            {
                                selectList.Add(excelColumn.activeTxt);
                            }
                            else
                            {
                                selectList.Add("开");
                            }

                            if (excelColumn.inactiveTxt.IsNotEmptyOrNull())
                            {
                                selectList.Add(excelColumn.inactiveTxt);
                            }
                            else
                            {
                                selectList.Add("关");
                            }

                            break;
                    }
                }

                excelconfig.ColumnModel.Add(new ExcelColumnModel { Column = item, ExcelColumn = excelColumn.__config__.label, Required = excelColumn.__config__.required, SelectList = selectList });
                columnList.Add(excelColumn.__config__.label);
            }
        }

        string? addPath = Path.Combine(FileVariable.TemporaryFilePath, excelconfig.FileName);
        MemoryStream fs = firstColumns == null
            ? ExcelExportHelper<Dictionary<string, object>>.ExportMemoryStream(realList, excelconfig, columnList)
            : ExcelExportHelper<Dictionary<string, object>>.ExportMemoryStream(realList, excelconfig, columnList, firstColumns);
        bool flag = await _fileManager.UploadFileByType(fs, FileVariable.TemporaryFilePath, excelconfig.FileName);
        if (flag)
        {
            fs.Flush();
            fs.Close();
        }

        output.name = excelconfig.FileName;
        output.url = "/api/file/Download?encryption=" + DESEncryption.Encrypt(_userManager?.UserId + "|" + excelconfig.FileName + "|" + addPath, "JNPF");
        return output;
    }

    /// <summary>
    ///     组装导出带子表得数据,返回 第一个合并行标头,第二个导出数据.
    /// </summary>
    /// <param name="selectKey">导出选择列.</param>
    /// <param name="realList">原数据集合.</param>
    /// <param name="fieldList">控件列表.</param>
    /// <param name="columnDesignModel"></param>
    /// <returns>第一行标头 , 导出数据.</returns>
    public (Dictionary<string, int>, List<Dictionary<string, object>>) GetCreateFirstColumnsHeader(List<string> selectKey, List<Dictionary<string, object>> realList, List<FieldsModel> fieldList,
        ColumnDesignModel columnDesignModel = null)
    {
        // 是否有复杂表头
        bool isComplexHeader = false;
        if (!columnDesignModel.type.Equals(3) && !columnDesignModel.type.Equals(5) && columnDesignModel.complexHeaderList.Any())
        {
            foreach (ComplexHeaderModel item in columnDesignModel.complexHeaderList)
            {
                foreach (string subItem in item.childColumns)
                {
                    if (selectKey.Contains(subItem))
                    {
                        isComplexHeader = true;
                        break;
                    }
                }
            }
        }

        selectKey.ForEach(item =>
        {
            realList.ForEach(it =>
            {
                if (!it.ContainsKey(item))
                {
                    it.Add(item, string.Empty);
                }
            });
        });

        List<Dictionary<int, Dictionary<string, object>>> addItemList = new();
        int num = 0;
        realList.ForEach(items =>
        {
            Dictionary<string, List<Dictionary<string, object>>> rowChildDatas = new();
            foreach (KeyValuePair<string, object> item in items)
            {
                if (item.Value != null && item.Key.ToLower().Contains("tablefield") && (item.Value is List<Dictionary<string, object>> || item.Value.GetType().Name.Equals("JArray")))
                {
                    List<Dictionary<string, object>>? ctList = item.Value.ToObjectOld<List<Dictionary<string, object>>>();
                    rowChildDatas.Add(item.Key, ctList);
                }
            }

            int len = rowChildDatas.Select(x => x.Value.Count()).OrderByDescending(x => x).FirstOrDefault();

            if (len != null && len > 0)
            {
                for (int i = 0; i < len; i++)
                {
                    if (i == 0)
                    {
                        Dictionary<string, object>? newRealItem = realList.Find(x => x["id"].Equals(items["id"]));
                        foreach (KeyValuePair<string, List<Dictionary<string, object>>> cData in rowChildDatas)
                        {
                            Dictionary<string, object>? itemData = cData.Value.FirstOrDefault();
                            if (itemData != null)
                            {
                                foreach (KeyValuePair<string, object> key in itemData)
                                {
                                    if (newRealItem.ContainsKey(cData.Key + "-" + key.Key))
                                    {
                                        newRealItem[cData.Key + "-" + key.Key] = key.Value;
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        Dictionary<string, object> newRealItem = new();
                        foreach (KeyValuePair<string, object> it in items)
                        {
                            if (it.Key.Equals("id"))
                            {
                                newRealItem.Add(it.Key, it.Value);
                            }
                            else
                            {
                                newRealItem.Add(it.Key, string.Empty);
                            }
                        }

                        foreach (KeyValuePair<string, List<Dictionary<string, object>>> cData in rowChildDatas)
                        {
                            if (cData.Value.Count > i)
                            {
                                foreach (KeyValuePair<string, object> it in cData.Value[i])
                                {
                                    if (newRealItem.ContainsKey(cData.Key + "-" + it.Key))
                                    {
                                        newRealItem[cData.Key + "-" + it.Key] = it.Value;
                                    }
                                }
                            }
                        }

                        Dictionary<int, Dictionary<string, object>> dicItem = new();
                        dicItem.Add(num + 1, newRealItem);
                        addItemList.Add(dicItem);
                    }
                }
            }

            num++;
        });
        for (int i = 0; i < addItemList.Count; i++)
        {
            KeyValuePair<int, Dictionary<string, object>> dic = addItemList[i].FirstOrDefault();
            realList.Insert(dic.Key + i, dic.Value);
        }

        List<Dictionary<string, object>> resultList = new();

        if (isComplexHeader)
        {
            realList.ForEach(newRealItem =>
            {
                Dictionary<string, object> complexHeaderDic = new();

                // 添加复杂表单字段
                int i = 0;
                foreach (ComplexHeaderModel item in columnDesignModel.complexHeaderList)
                {
                    foreach (string subItem in item.childColumns)
                    {
                        if (newRealItem.ContainsKey(subItem) && selectKey.Contains(subItem))
                        {
                            complexHeaderDic.Add(subItem, newRealItem[subItem]);
                            selectKey.Remove(subItem);
                            selectKey.Insert(i, subItem);
                            i++;
                        }
                    }
                }

                // 添加其他字段
                foreach (KeyValuePair<string, object> item in newRealItem)
                {
                    if ((!complexHeaderDic.ContainsKey(item.Key) && selectKey.Contains(item.Key)) || item.Key.Equals("id"))
                    {
                        complexHeaderDic.Add(item.Key, item.Value);
                    }
                }

                resultList.Add(complexHeaderDic);
            });
        }
        else
        {
            resultList = realList;
        }

        Dictionary<string, int> firstColumns = new();
        if (selectKey.Any(x => x.Contains("-") && x.ToLower().Contains("tablefield")) || isComplexHeader)
        {
            string? empty = string.Empty;
            List<string> keyList = selectKey.Select(x => x.Split("-").First()).Distinct().ToList();
            int mainFieldIndex = 1;

            if (isComplexHeader)
            {
                foreach (ComplexHeaderModel item in columnDesignModel.complexHeaderList)
                {
                    int count = 0;
                    foreach (string subItem in item.childColumns)
                    {
                        if (keyList.Contains(subItem))
                        {
                            keyList.Remove(subItem);
                            count++;
                        }
                    }

                    if (firstColumns.ContainsKey(item.fullName))
                    {
                        firstColumns[item.fullName] += count;
                    }
                    else
                    {
                        firstColumns.Add(item.fullName, count);
                    }
                }
            }

            keyList.ForEach(item =>
            {
                if (item.ToLower().Contains("tablefield"))
                {
                    string? title = fieldList.FirstOrDefault(x => x.__vModel__.Equals(item))?.__config__.label;
                    firstColumns.Add(title + empty, selectKey.Count(x => x.Contains(item)));
                    empty += " ";
                    mainFieldIndex = 1;
                }
                else
                {
                    if (mainFieldIndex == 1)
                    {
                        empty += " ";
                    }

                    if (!firstColumns.ContainsKey(empty))
                    {
                        firstColumns.Add(empty, mainFieldIndex);
                    }
                    else
                    {
                        firstColumns[empty] = mainFieldIndex;
                    }

                    mainFieldIndex++;
                }
            });
        }

        return (firstColumns, resultList);
    }

    #endregion

    #region PrivateMethod

    /// <summary>
    ///     表单校验规则
    /// </summary>
    /// <returns></returns>
    private async Task CheckRule(VisualDevModelDataCrInput dataInput, VisualDevEntity? entity)
    {
        if (entity == null)
        {
            throw Oops.Oh("表单已经被删除！");
        }

        if (entity.ColumnData == null)
        {
            throw Oops.Oh("列配置已经被删除！");
        }

        FormDataModel formModel = entity.FormData.ToObjectOld<FormDataModel>();
        List<FieldsModel> fieldsModelList = TemplateAnalysis.AnalysisTemplateData(formModel.fields); // 已剔除布局控件集合
        List<FieldsModel> childTableFieldsModelList = fieldsModelList.Where(x => x.__config__.jnpfKey == JnpfKeyConst.TABLE).ToList(); // 子表集合
        Dictionary<string, object>? allDataMap = dataInput.data.ToObjectOld<Dictionary<string, object>>();
        Interpreter interpreter = new();

        // // 当使用中文规则匹配时，需将字段做映射 map.key = id, map.value = 中文含义
        // var columnData = entity.ColumnData.ToObjectOld<ColumnDesignModel>(); // 列配置模型
        // foreach (var columnOption in columnData.columnOptions)
        // {
        //     interpreter.SetVariable(columnOption.id, columnOption.id);
        // }

        foreach ((string key, object value) in allDataMap)
        {
            // Log.Information($"key：{key},value:{value}");
            if (value == null)
            {
                interpreter.SetVariable(key, null);
                continue;
            }

            if (value.GetType() == typeof(JArray))
            {
                interpreter.SetVariable(key, value.ToObjectOld<List<Dictionary<string, object>>>());
            }

            interpreter.SetVariable(key, value);
        }

        if (childTableFieldsModelList.Count != 0)
        {
            // 处理子表
            foreach (FieldsModel childTableFieldsModel in childTableFieldsModelList.Where(childTableFieldsModel => allDataMap.ContainsKey(childTableFieldsModel.__vModel__)))
            {
                interpreter.DetectIdentifiers(childTableFieldsModel.__vModel__);
                List<Dictionary<string, object>> childrenValues = allDataMap[childTableFieldsModel.__vModel__].ToObjectOld<List<Dictionary<string, object>>>();
                foreach (dynamic? dynamicData in childrenValues.Select(childrenValueDic => childrenValueDic.ConvertDictionaryToDynamic()))
                {
                    interpreter.SetVariable(childTableFieldsModel.__vModel__, dynamicData);
                    CheckRuleEx(formModel.checkRules, interpreter);
                }
            }
        }
        else
        {
            CheckRuleEx(formModel.checkRules, interpreter);
        }
    }

    /// <summary>
    ///     校验规则
    /// </summary>
    /// <param name="checkRules"></param>
    /// <param name="interpreter"></param>
    /// 、
    /// <exception cref="AppFriendlyException"></exception>
    private void CheckRuleEx(List<FieldCheckRule> checkRules, Interpreter interpreter, params Parameter[] parameters)
    {
        foreach (FieldCheckRule checkRule in checkRules)
        {
            string expressionNew = checkRule.expression;
            string pattern = @"\${(.*?)}";
            MatchCollection matches = Regex.Matches(expressionNew, pattern);
            foreach (Match match in matches)
            {
                expressionNew = expressionNew.Replace(match.Groups[0].Value, match.Groups[1].Value);
            }

            if (interpreter.Eval<bool>(expressionNew, parameters))
            {
                // Log.Information($"表达式：{expressionNew},成立");
                throw Oops.Oh(checkRule.errorMessage);
            }
            // Log.Information($"表达式：{expressionNew},不成立");
        }
    }

    /// <summary>
    ///     获取导出模板信息.
    /// </summary>
    /// <param name="modelId"></param>
    /// <returns></returns>
    private async Task<TemplateParsingBase> GetUploaderTemplateInfoAsync(string modelId)
    {
        VisualDevEntity? templateEntity = await _visualDevService.GetInfoById(modelId, true);
        TemplateParsingBase tInfo = new(templateEntity);
        tInfo.DbLink = await _dbLinkService.GetInfo(templateEntity.DbLinkId);
        if (tInfo.DbLink == null)
        {
            tInfo.DbLink = _databaseService.GetTenantDbLink(_userManager.TenantId, _userManager.TenantDbName); // 当前数据库连接
        }

        List<DbTableFieldModel> tableList = _databaseService.GetFieldList(tInfo.DbLink, tInfo.MainTableName); // 获取主表所有列
        DbTableFieldModel? mainPrimary = tableList.Find(t => t.primaryKey); // 主表主键
        if (mainPrimary == null || mainPrimary.IsNullOrEmpty())
        {
            throw Oops.Oh(ErrorCode.D1402); // 主表未设置主键
        }

        tInfo.MainPrimary = mainPrimary.field;
        tInfo.AllFieldsModel = tInfo.AllFieldsModel.Where(x => !x.__config__.jnpfKey.Equals(JnpfKeyConst.UPLOADFZ)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.UPLOADIMG)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.COLORPICKER)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPTABLESELECT)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.RELATIONFORM)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPSELECT)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.RELATIONFORMATTR)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPATTR)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.QRCODE)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.BARCODE)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.CALCULATE)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.SIGN)
                                                               && !x.__config__.jnpfKey.Equals(JnpfKeyConst.LOCATION)).ToList();
        tInfo.AllFieldsModel.Where(x => x.__vModel__.IsNotEmptyOrNull()).ToList().ForEach(item => item.__config__.label = string.Format("{0}({1})", item.__config__.label, item.__vModel__));
        return tInfo;
    }

    /// <summary>
    ///     导入数据.
    /// </summary>
    /// <param name="tInfo">模板信息.</param>
    /// <param name="input"></param>
    /// <param name="tEntity">开发实体.</param>
    /// <returns>[成功列表,失败列表].</returns>
    private async Task<object[]> ImportMenuData(TemplateParsingBase tInfo, VisualDevImportDataInput input, VisualDevEntity tEntity = null)
    {
        if (tInfo.ColumnData.complexHeaderList.Count > 0 && !tInfo.ColumnData.type.Equals(3) && !tInfo.ColumnData.type.Equals(5))
        {
            List<string> complexHeaderIdList = tInfo.ColumnData.complexHeaderList.Select(it => it.id).ToList();
            foreach (Dictionary<string, object> item in input.list)
            {
                Dictionary<string, object> addValue = new();
                foreach (KeyValuePair<string, object> subItem in item)
                {
                    if (complexHeaderIdList.Contains(subItem.Key))
                    {
                        foreach (Dictionary<string, object> newItem in subItem.Value.ToObjectOld<List<Dictionary<string, object>>>())
                        {
                            foreach (KeyValuePair<string, object> dicItem in newItem)
                            {
                                addValue[dicItem.Key] = dicItem.Value;
                            }
                        }
                    }
                }

                if (addValue.Count > 0)
                {
                    foreach (KeyValuePair<string, object> addItem in addValue)
                    {
                        item[addItem.Key] = addItem.Value;
                    }
                }
            }
        }

        List<Dictionary<string, object>> userInputList = ImportFirstVerify(tInfo, input.list);
        List<FieldsModel> fieldsModelList = tInfo.AllFieldsModel.Where(x => tInfo.selectKey.Contains(x.__vModel__)).ToList();

        List<Dictionary<string, object>> successList = new();
        List<Dictionary<string, object>> errorsList = new();

        // 捞取控件解析数据
        Dictionary<string, List<Dictionary<string, string>>> cData = await GetCDataList(tInfo.AllFieldsModel, new Dictionary<string, List<Dictionary<string, string>>>());
        List<Dictionary<string, object>> res = await ImportDataAssemble(tInfo.AllFieldsModel, userInputList, cData);
        res.Where(x => x.ContainsKey("errorsInfo")).ToList().ForEach(item => errorsList.Add(item));
        res.Where(x => !x.ContainsKey("errorsInfo")).ToList().ForEach(item => successList.Add(item));

        // 唯一验证已处理，入库前去掉.
        tInfo.AllFieldsModel.Where(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.COMINPUT) && x.__config__.unique).ToList().ForEach(item => item.__config__.unique = false);

        List<object> eventList = new();
        foreach (Dictionary<string, object> item in successList)
        {
            if (item.ContainsKey("Update_MainTablePrimary_Id"))
            {
                string? mainId = item["Update_MainTablePrimary_Id"].ToString();
                List<string> haveTableSql = await _runService.GetUpdateSqlByTemplate(tInfo, new VisualDevModelDataUpInput { data = item.ToJson() }, mainId);
                foreach (string it in haveTableSql)
                {
                    await _databaseService.ExecuteSql(tInfo.DbLink, it); // 修改功能数据
                }

                Dictionary<string, object>? eventData = item.Copy();
                eventData.Remove("Update_MainTablePrimary_Id");
                eventList.Add(new { id = mainId, data = eventData });
            }
            else
            {
                if (tInfo.visualDevEntity.EnableFlow.Equals(1))
                {
                    // await _flowTaskService.Create(new FlowTaskSubmitModel {formData = item, flowId = input.flowId, flowUrgent = 1, status = 1});
                }
                else
                {
                    long mainId = YitIdHelper.NextId();
                    Dictionary<string, List<Dictionary<string, object>>> haveTableSql = await _runService.GetCreateSqlByTemplate(tInfo, new VisualDevModelDataCrInput { data = item.ToJson() }, mainId.ToString());

                    // 主表自增长Id.
                    if (haveTableSql.ContainsKey("MainTableReturnIdentity"))
                    {
                        haveTableSql.Remove("MainTableReturnIdentity");
                    }

                    foreach (KeyValuePair<string, List<Dictionary<string, object>>> it in haveTableSql)
                    {
                        await _databaseService.ExecuteSql(tInfo.DbLink, it.Key, it.Value); // 新增功能数据
                    }

                    eventList.Add(new { id = mainId, data = item });
                }
            }
        }

        errorsList.ForEach(item =>
        {
            if (item.ContainsKey("errorsInfo") && item["errorsInfo"].IsNotEmptyOrNull())
            {
                item["errorsInfo"] = item["errorsInfo"].ToString().TrimStart(',').TrimEnd(',');
            }
        });

        // 添加集成助手`事件触发`导入事件
        if (input.isInteAssis)
        {
            await _eventPublisher.PublishAsync(new InteEventSource("Inte:CreateInte", _userManager.UserId.ToString(), _userManager.TenantId.ToString(), new InteAssiEventModel
            {
                ModelId = tEntity.Id.ToString(),
                Data = eventList.ToJson(),
                TriggerType = 4
            }));
        }

        return new object[] { successList, errorsList };
    }

    /// <summary>
    ///     导入功能数据初步验证.
    /// </summary>
    private List<Dictionary<string, object>> ImportFirstVerify(TemplateParsingBase tInfo, List<Dictionary<string, object>> list)
    {
        string errorKey = "errorsInfo";
        List<Dictionary<string, object>> resList = new();
        list.ForEach(item =>
        {
            Dictionary<string, object>? addItem = item.Copy();
            addItem.Add(errorKey, string.Empty);
            resList.Add(addItem);
        });

        #region 验证必填控件

        List<string> childTableList = tInfo.AllFieldsModel.Where(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.TABLE)).Select(x => x.__vModel__).ToList();
        List<FieldsModel> requiredList = tInfo.AllFieldsModel.Where(x => x.__config__.required).ToList();
        List<string> VModelList = requiredList.Select(x => x.__vModel__).ToList();

        if (VModelList.Any())
        {
            List<Dictionary<string, object>> newResList = new();
            resList.ForEach(items =>
            {
                Dictionary<string, object>? newItems = items.Copy();
                foreach (KeyValuePair<string, object> item in items)
                {
                    if (item.Value.IsNullOrEmpty() && VModelList.Contains(item.Key))
                    {
                        string errorInfo = requiredList.Find(x => x.__vModel__.Equals(item.Key)).__config__.label + ": 值不能为空";
                        if (newItems.ContainsKey(errorKey))
                        {
                            newItems[errorKey] = newItems[errorKey] + "," + errorInfo;
                        }
                        else
                        {
                            newItems.Add(errorKey, errorInfo);
                        }
                    }

                    // 子表
                    if (childTableList.Contains(item.Key))
                    {
                        item.Value.ToObjectOld<List<Dictionary<string, object>>>().ForEach(childItems =>
                        {
                            foreach (KeyValuePair<string, object> childItem in childItems)
                            {
                                if (childItem.Value.IsNullOrEmpty() && VModelList.Contains(item.Key + "-" + childItem.Key))
                                {
                                    string errorInfo = tInfo.AllFieldsModel.Find(x => x.__vModel__.Equals(item.Key)).__config__.children.Find(x => x.__vModel__.Equals(item.Key + "-" + childItem.Key))
                                        .__config__.label + ": 值不能为空";
                                    if (newItems.ContainsKey(errorKey))
                                    {
                                        newItems[errorKey] = newItems[errorKey] + "," + errorInfo;
                                    }
                                    else
                                    {
                                        newItems.Add(errorKey, errorInfo);
                                    }
                                }
                            }
                        });
                    }
                }

                newResList.Add(newItems);
            });
            resList = newResList;
        }

        #endregion

        #region 验证唯一

        List<FieldsModel> uniqueList = tInfo.AllFieldsModel.Where(x => x.__config__.unique).ToList();
        VModelList = uniqueList.Select(x => x.__vModel__).ToList();

        if (uniqueList.Any())
        {
            resList.ForEach(items =>
            {
                foreach (KeyValuePair<string, object> item in items)
                {
                    if (VModelList.Contains(item.Key))
                    {
                        List<Dictionary<string, object>> vlist = new();
                        resList.Where(x => x.ContainsKey(item.Key) && x.ContainsValue(item.Value)).ToList().ForEach(it =>
                        {
                            foreach (KeyValuePair<string, object> dic in it)
                            {
                                if (dic.Value != null && item.Value != null && dic.Key.Equals(item.Key) && dic.Value.Equals(item.Value))
                                {
                                    vlist.Add(it);
                                    break;
                                }
                            }
                        });

                        if (vlist.Count > 1)
                        {
                            for (int i = 1; i < vlist.Count; i++)
                            {
                                string errorInfo = tInfo.AllFieldsModel.Find(x => x.__vModel__.Equals(item.Key)).__config__.label + ": 值不能重复";
                                items[errorKey] = items[errorKey] + "," + errorInfo;
                            }
                        }
                    }

                    // 子表
                    List<Dictionary<string, object>> updateItemCList = new();
                    List<string> ctItemErrors = new();
                    if (childTableList.Contains(item.Key))
                    {
                        List<Dictionary<string, object>>? itemCList = item.Value.ToObjectOld<List<Dictionary<string, object>>>();
                        itemCList.ForEach(childItems =>
                        {
                            if (tInfo.dataType.Equals("2"))
                            {
                                foreach (KeyValuePair<string, object> childItem in childItems)
                                {
                                    string uniqueKey = item.Key + "-" + childItem.Key;
                                    if (VModelList.Contains(uniqueKey))
                                    {
                                        List<Dictionary<string, object>> vlist = itemCList.Where(x => x.ContainsKey(childItem.Key) && x.ContainsValue(childItem.Value)).ToList();
                                        if (!updateItemCList.Any(x => x.ContainsKey(childItem.Key) && x.ContainsValue(childItem.Value)))
                                        {
                                            updateItemCList.Add(vlist.Last());
                                        }
                                    }
                                }
                            }
                            else
                            {
                                foreach (KeyValuePair<string, object> childItem in childItems)
                                {
                                    string uniqueKey = item.Key + "-" + childItem.Key;
                                    if (VModelList.Contains(uniqueKey) && childItem.Value != null)
                                    {
                                        List<Dictionary<string, object>> vlist = itemCList.Where(x => x.ContainsKey(childItem.Key) && x.ContainsValue(childItem.Value)).ToList();
                                        if (vlist.Count > 1)
                                        {
                                            for (int i = 1; i < vlist.Count; i++)
                                            {
                                                string errorTxt = tInfo.AllFieldsModel.Find(x => x.__vModel__.Equals(uniqueKey)).__config__.label + ": 值不能重复";
                                                if (!ctItemErrors.Any(x => x.Equals(errorTxt)))
                                                {
                                                    ctItemErrors.Add(errorTxt);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        });
                    }

                    if (tInfo.dataType.Equals("2") && updateItemCList.Any())
                    {
                        items[item.Key] = updateItemCList;
                    }

                    if (ctItemErrors.Any())
                    {
                        items[errorKey] = items[errorKey].IsNullOrEmpty() ? string.Join(",", ctItemErrors) : items[errorKey] + "," + string.Join(",", ctItemErrors);
                    }
                }
            });

            // 表里的数据验证唯一
            List<string>? relationKey = new();
            List<string>? auxiliaryFieldList = tInfo.AuxiliaryTableFieldsModelList.Select(x => x.__config__.tableName).Distinct().ToList();
            auxiliaryFieldList.ForEach(tName =>
            {
                string? tableField = tInfo.AllTable.Find(tf => tf.table == tName)?.tableField;
                relationKey.Add(tInfo.MainTableName + "." + tInfo.MainPrimary + "=" + tName + "." + tableField);
            });

            resList.ForEach(allDataMap =>
            {
                List<string>? fieldList = new();
                List<IConditionalModel> whereList = new();
                fieldList.Add(string.Format("{0}.{1}", tInfo.MainTableName, tInfo.MainPrimary));
                List<string> uniqueList = new();
                tInfo.SingleFormData.Where(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.COMINPUT) && x.__config__.unique).ToList().ForEach(item =>
                {
                    uniqueList.Add(item.__vModel__);
                    fieldList.Add(string.Format("{0}.{1} {2}", item.__config__.tableName, item.__vModel__.Split("_jnpf_").Last(), item.__vModel__));
                    if (allDataMap.ContainsKey(item.__vModel__) && allDataMap[item.__vModel__] != null)
                    {
                        whereList.Add(new ConditionalCollections
                        {
                            ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                            {
                                new(WhereType.Or, new ConditionalModel
                                {
                                    FieldName = string.Format("{0}.{1}", item.__config__.tableName, item.__vModel__.Split("_jnpf_").Last()),
                                    ConditionalType = allDataMap.ContainsKey(item.__vModel__) ? ConditionalType.Equal : ConditionalType.IsNullOrEmpty,
                                    FieldValue = allDataMap.ContainsKey(item.__vModel__) ? allDataMap[item.__vModel__].ToString() : string.Empty
                                })
                            }
                        });
                    }
                });

                string? itemWhere = _visualDevRepository.AsSugarClient().SqlQueryable<dynamic>("@").Where(whereList).ToSqlString();
                if (!itemWhere.Equals("@"))
                {
                    List<string> relationList = new();
                    string? whereStr = string.Empty;
                    relationList.AddRange(relationKey);
                    if (relationList.Count > 0)
                    {
                        string whereRelation = string.Join(" and ", relationList);
                        whereStr = string.Format("({0}) and {1}", whereRelation, itemWhere.Split("WHERE").Last());
                    }
                    else
                    {
                        whereStr = itemWhere.Split("WHERE").Last();
                    }

                    string querStr = string.Format(
                        "select {0} from {1} where {2}",
                        string.Join(",", fieldList),
                        auxiliaryFieldList.Any() ? tInfo.MainTableName + "," + string.Join(",", auxiliaryFieldList) : tInfo.MainTableName,
                        whereStr); // 多表， 联合查询
                    List<Dictionary<string, string>>? res = _databaseService.GetSqlData(tInfo.DbLink, querStr).ToObjectOld<List<Dictionary<string, string>>>();

                    if (res.Any())
                    {
                        List<string> errorList = new();
                        string? mainId = string.Empty;
                        string uniqueKey = string.Empty;
                        foreach (string item in uniqueList)
                        {
                            List<Dictionary<string, string>> list = res.FindAll(x => x[item].IsNotEmptyOrNull());
                            if (list.Any(x => x.Any(xx => xx.Key.Equals(item) && xx.Value.Equals(allDataMap[item].ToString()))))
                            {
                                if (mainId.IsNotEmptyOrNull() && !mainId.Equals(list.Find(x => x.Any(xx => xx.Key.Equals(item) && xx.Value.Equals(allDataMap[item].ToString())))[tInfo.MainPrimary]))
                                {
                                    allDataMap[errorKey] = "存在重复数据";
                                }
                                else
                                {
                                    mainId = list.Find(x => x.Any(xx => xx.Key.Equals(item) && xx.Value.Equals(allDataMap[item].ToString())))[tInfo.MainPrimary];
                                    uniqueKey = item;
                                }
                            }
                        }

                        if (tInfo.dataType.Equals("2"))
                        {
                            if (mainId.IsNotEmptyOrNull() && !allDataMap.ContainsKey("Update_MainTablePrimary_Id"))
                            {
                                allDataMap.Add("Update_MainTablePrimary_Id", mainId);
                            }
                        }
                        else
                        {
                            if (mainId.IsNotEmptyOrNull())
                            {
                                string errorInfo = tInfo.SingleFormData.First(x => x.__vModel__.Equals(uniqueKey))?.__config__.label + ": 值不能重复";
                                if (allDataMap.ContainsKey(errorKey))
                                {
                                    if (!allDataMap[errorKey].ToString().Contains(errorInfo))
                                    {
                                        allDataMap[errorKey] = allDataMap[errorKey] + "," + errorInfo;
                                    }
                                }
                                else
                                {
                                    allDataMap.Add(errorKey, errorInfo);
                                }
                            }
                        }
                    }
                }
            });
        }

        #endregion

        resList.ForEach(item =>
        {
            if (item[errorKey].IsNullOrEmpty())
            {
                item.Remove(errorKey);
            }
        });
        return resList;
    }

    /// <summary>
    ///     获取模板控件解析数据.
    /// </summary>
    /// <param name="tInfo"></param>
    /// <param name="resData"></param>
    /// <returns></returns>
    private async Task<Dictionary<string, List<Dictionary<string, string>>>> GetCDataList(List<FieldsModel> listFieldsModel, Dictionary<string, List<Dictionary<string, string>>> resData)
    {
        foreach (var item in listFieldsModel.Where(x => !x.__config__.jnpfKey.Equals(JnpfKeyConst.TABLE)).ToList())
        {
            List<Dictionary<string, string>>? addItem = new();
            switch (item.__config__.jnpfKey)
            {
                case JnpfKeyConst.COMSELECT:
                {
                    if (!resData.ContainsKey(item.__vModel__))
                    {
                        List<SysOrg>? allDataList = await _visualDevRepository.AsSugarClient().Queryable<SysOrg>().Where(x => x.Status == true)
                            .Select(x => new SysOrg { Id = x.Id, OrganizeIdTree = x.OrganizeIdTree, Name = x.Name }).ToListAsync();
                        List<SysOrg>? dataList = new();
                        if (item.selectType.Equals("custom"))
                        {
                            item.ableIds = DynamicParameterConversion(item.ableIds);
                            dataList = allDataList.Where(it => item.ableIds.Contains(it.Id)).ToList();
                        }
                        else
                        {
                            dataList = allDataList;
                        }

                        dataList.ForEach(item =>
                        {
                            if (item.OrganizeIdTree.IsNullOrEmpty())
                            {
                                item.OrganizeIdTree = item.Id.ToString();
                            }

                            List<string> orgNameList = new();
                            item.OrganizeIdTree.Split(",").ToList().ForEach(it =>
                            {
                                SysOrg? org = allDataList.Find(x => x.Id == it.ParseToLong());
                                if (org != null)
                                {
                                    orgNameList.Add(org.Name);
                                }
                            });
                            Dictionary<string, string> dictionary = new();
                            dictionary.Add(item.OrganizeIdTree, string.Join("/", orgNameList));
                            addItem.Add(dictionary);
                        });

                        resData.Add(item.__vModel__, addItem);
                    }
                }

                    break;
                case JnpfKeyConst.ADDRESS:
                {
                    string? addCacheKey = "Import_Address";

                    if (!resData.ContainsKey(JnpfKeyConst.ADDRESS))
                    {
                        if (_cacheManager.ExistKey(addCacheKey))
                        {
                            addItem = _cacheManager.Get<string>(addCacheKey).ToObjectOld<List<Dictionary<string, string>>>();
                            resData.Add(JnpfKeyConst.ADDRESS, addItem);
                        }
                        // var dataList = await _visualDevRepository.AsSugarClient().Queryable<ProvinceEntity>()
                        //     .Select(x => new ProvinceEntity {Id = x.Id, ParentId = x.ParentId, Type = x.Type, FullName = x.FullName}).ToListAsync();
                        //
                        // // 处理省市区树
                        // dataList.Where(x => x.Type == "1").ToList().ForEach(item =>
                        // {
                        //     item.QuickQuery = item.FullName;
                        //     item.Description = item.Id;
                        //     Dictionary<string, string> address = new();
                        //     address.Add(item.Description, item.QuickQuery);
                        //     addItem.Add(address);
                        // });
                        // dataList.Where(x => x.Type == "2").ToList().ForEach(item =>
                        // {
                        //     item.QuickQuery = dataList.Find(x => x.Id == item.ParentId).QuickQuery + "/" + item.FullName;
                        //     item.Description = dataList.Find(x => x.Id == item.ParentId).Description + "," + item.Id;
                        //     Dictionary<string, string> address = new();
                        //     address.Add(item.Description, item.QuickQuery);
                        //     addItem.Add(address);
                        // });
                        // dataList.Where(x => x.Type == "3").ToList().ForEach(item =>
                        // {
                        //     item.QuickQuery = dataList.Find(x => x.Id == item.ParentId).QuickQuery + "/" + item.FullName;
                        //     item.Description = dataList.Find(x => x.Id == item.ParentId).Description + "," + item.Id;
                        //     Dictionary<string, string> address = new();
                        //     address.Add(item.Description, item.QuickQuery);
                        //     addItem.Add(address);
                        // });
                        // dataList.Where(x => x.Type == "4").ToList().ForEach(item =>
                        // {
                        //     ProvinceEntity? it = dataList.Find(x => x.Id == item.ParentId);
                        //     if (it != null)
                        //     {
                        //         item.QuickQuery = it.QuickQuery + "/" + item.FullName;
                        //         item.Description = it.Description + "," + item.Id;
                        //         Dictionary<string, string> address = new();
                        //         address.Add(item.Description, item.QuickQuery);
                        //         addItem.Add(address);
                        //     }
                        // });
                        // dataList.ForEach(it =>
                        // {
                        //     if (it.Description.IsNotEmptyOrNull())
                        //     {
                        //         Dictionary<string, string> dictionary = new();
                        //         dictionary.Add(it.Description, it.QuickQuery);
                        //         addItem.Add(dictionary);
                        //     }
                        // });
                        //
                        // var noTypeList = dataList.Where(x => x.Type.IsNullOrWhiteSpace()).ToList();
                        // foreach (var it in noTypeList)
                        // {
                        //     it.QuickQuery = GetAddressByPList(noTypeList, it);
                        //     it.Description = GetAddressIdByPList(noTypeList, it);
                        // }
                        //
                        // foreach (var it in noTypeList)
                        // {
                        //     Dictionary<string, string> address = new();
                        //     address.Add(it.Description, it.QuickQuery);
                        //     addItem.Add(address);
                        // }
                        //
                        // _cacheManager.Set(addCacheKey, addItem, TimeSpan.FromDays(7)); // 缓存七天
                        // resData.Add(JnpfKeyConst.ADDRESS, addItem);
                    }
                }

                    break;
                // case JnpfKeyConst.GROUPSELECT:
                // {
                //     if (!resData.ContainsKey(item.__vModel__))
                //     {
                //         var dataList = await _visualDevRepository.AsSugarClient().Queryable<GroupEntity>().Where(x => x.DeleteMark == null).Select(x => new GroupEntity {Id = x.Id, EnCode = x.EnCode})
                //             .ToListAsync();
                //         if (item.selectType.Equals("custom"))
                //         {
                //             dataList = dataList.Where(it => item.ableIds.Contains(it.Id)).ToList();
                //         }
                //
                //         dataList.ForEach(item =>
                //         {
                //             Dictionary<string, string> dictionary = new();
                //             dictionary.Add(item.Id, item.EnCode);
                //             addItem.Add(dictionary);
                //         });
                //         resData.Add(item.__vModel__, addItem);
                //     }
                // }

                // break;
                case JnpfKeyConst.ROLESELECT:
                {
                    if (!resData.ContainsKey(item.__vModel__))
                    {
                        List<SysRole>? dataList = await _visualDevRepository.AsSugarClient().Queryable<SysRole>().Select(x => new SysRole { Id = x.Id, Code = x.Code })
                            .ToListAsync();
                        if (item.selectType.Equals("custom"))
                        {
                            item.ableIds = DynamicParameterConversion(item.ableIds);
                            // var relationIds = await _visualDevRepository.AsSugarClient().Queryable<OrganizeRelationEntity>()
                            //     .Where(it => item.ableIds.Contains(it.OrganizeId) && it.ObjectType.Equals("Role"))
                            //     .Select(it => it.ObjectId).ToListAsync();
                            // item.ableIds.AddRange(relationIds);
                            dataList = dataList.Where(it => item.ableIds.Contains(it.Id)).ToList();
                        }

                        dataList.ForEach(item =>
                        {
                            Dictionary<string, string> dictionary = new();
                            dictionary.Add(item.Id.ToString(), item.Code);
                            addItem.Add(dictionary);
                        });
                        resData.Add(item.__vModel__, addItem);
                    }
                }

                    break;
                case JnpfKeyConst.SWITCH:
                {
                    if (!resData.ContainsKey(item.__vModel__))
                    {
                        Dictionary<string, string> dictionary = new();
                        dictionary.Add("1", item.activeTxt);
                        addItem.Add(dictionary);
                        Dictionary<string, string> dictionary2 = new();
                        dictionary2.Add("0", item.inactiveTxt);
                        addItem.Add(dictionary2);
                        resData.Add(item.__vModel__, addItem);
                    }
                }

                    break;
                case JnpfKeyConst.CHECKBOX:
                case JnpfKeyConst.SELECT:
                case JnpfKeyConst.RADIO:
                {
                    if (!resData.ContainsKey(item.__vModel__))
                    {
                        string? propsValue = string.Empty;
                        string? propsLabel = string.Empty;
                        string? children = string.Empty;
                        if (item.props != null)
                        {
                            propsValue = item.props.value;
                            propsLabel = item.props.label;
                            children = item.props.children;
                        }

                        if (item.__config__.dataType.Equals("static"))
                        {
                            if (item != null && item.options != null)
                            {
                                item.options.ForEach(option =>
                                {
                                    Dictionary<string, string> dictionary = new();
                                    dictionary.Add(option[propsValue].ToString(), option[propsLabel].ToString());
                                    addItem.Add(dictionary);
                                });
                                resData.Add(item.__vModel__, addItem);
                            }
                        }
                        else if (item.__config__.dataType.Equals("dictionary"))
                        {
                            // var dictionaryDataList = await _visualDevRepository.AsSugarClient()
                            //     .Queryable<DictionaryDataEntity, DictionaryTypeEntity>((a, b) => new JoinQueryInfos(JoinType.Left, b.Id == a.DictionaryTypeId))
                            //     .WhereIF(item.__config__.dictionaryType.IsNotEmptyOrNull(), (a, b) => b.Id == item.__config__.dictionaryType || b.EnCode == item.__config__.dictionaryType)
                            //     .Where(a => a.DeleteMark == null).Select(a => new {a.Id, a.EnCode, a.FullName}).ToListAsync();
                            //
                            // foreach (var it in dictionaryDataList)
                            // {
                            //     Dictionary<string, string> dictionary = new();
                            //     if (propsValue.Equals("id"))
                            //     {
                            //         dictionary.Add(it.Id, it.FullName);
                            //     }
                            //
                            //     if (propsValue.Equals("enCode"))
                            //     {
                            //         dictionary.Add(it.EnCode, it.FullName);
                            //     }
                            //
                            //     addItem.Add(dictionary);
                            // }
                            //
                            // resData.Add(item.__vModel__, addItem);
                        }
                        else if (item.__config__.dataType.Equals("dynamic"))
                        {
                            List<Dictionary<string, string>> popDataList = await _formDataParsing.GetDynamicList(item);
                            resData.Add(item.__vModel__, popDataList);
                        }
                    }
                }
                    break;
                case JnpfKeyConst.TREESELECT:
                case JnpfKeyConst.CASCADER:
                {
                    if (!resData.ContainsKey(item.__vModel__))
                    {
                        if (item.__config__.dataType.Equals("static"))
                        {
                            if (item.options != null)
                            {
                                resData.Add(item.__vModel__, GetStaticList(item));
                            }
                        }
                        else if (item.__config__.dataType.Equals("dictionary"))
                        {
                            // var dictionaryDataList = await _visualDevRepository.AsSugarClient()
                            //     .Queryable<DictionaryDataEntity, DictionaryTypeEntity>((a, b) => new JoinQueryInfos(JoinType.Left, b.Id == a.DictionaryTypeId))
                            //     .WhereIF(item.__config__.dictionaryType.IsNotEmptyOrNull(), (a, b) => b.Id == item.__config__.dictionaryType || b.EnCode == item.__config__.dictionaryType)
                            //     .Where(a => a.DeleteMark == null).Select(a => new {a.Id, a.EnCode, a.FullName}).ToListAsync();
                            // if (item.props.value.ToLower().Equals("encode"))
                            // {
                            //     foreach (var it in dictionaryDataList)
                            //     {
                            //         Dictionary<string, string> dictionary = new();
                            //         dictionary.Add(it.EnCode, it.FullName);
                            //         addItem.Add(dictionary);
                            //     }
                            // }
                            // else
                            // {
                            //     foreach (var it in dictionaryDataList)
                            //     {
                            //         Dictionary<string, string> dictionary = new();
                            //         dictionary.Add(it.Id, it.FullName);
                            //         addItem.Add(dictionary);
                            //     }
                            // }
                            //
                            // resData.Add(item.__vModel__, addItem);
                        }
                        else if (item.__config__.dataType.Equals("dynamic"))
                        {
                            List<Dictionary<string, string>> popDataList = await _formDataParsing.GetDynamicList(item);
                            resData.Add(item.__vModel__, popDataList);
                        }
                    }
                }

                    break;
                case JnpfKeyConst.POPUPTABLESELECT:
                {
                    if (!resData.ContainsKey(item.__vModel__))
                    {
                        List<Dictionary<string, string>> popDataList = await _formDataParsing.GetDynamicList(item);
                        resData.Add(item.__vModel__, popDataList);
                    }
                }
                    break;

                case JnpfKeyConst.USERSELECT:
                {
                    if (!resData.ContainsKey(item.__vModel__))
                    {
                        if (item.selectType.Equals("all"))
                        {
                            List<SysUser>? dataList = await _visualDevRepository.AsSugarClient().Queryable<SysUser>()
                                .Select(x => new SysUser { Id = x.Id, Account = x.Account }).ToListAsync();
                            dataList.ForEach(item =>
                            {
                                Dictionary<string, string> dictionary = new();
                                dictionary.Add(item.Id.ToString(), item.Account);
                                addItem.Add(dictionary);
                            });
                            resData.Add(item.__vModel__, addItem);
                        }
                        else if (item.selectType.Equals("custom"))
                        {
                            List<object> newAbleIds = new();
                            item.ableIds.ForEach(x => newAbleIds.Add(x.ParseToString().Split("--").FirstOrDefault()));
                            newAbleIds = DynamicParameterConversion(newAbleIds);
                            // var userIdList = await _visualDevRepository.AsSugarClient().Queryable<UserRelationEntity>()
                            //     .WhereIF(item.ableIds.Any(), x => newAbleIds.Contains(x.UserId) || newAbleIds.Contains(x.ObjectId)).Select(x => x.UserId).ToListAsync();
                            // var dataList = await _visualDevRepository.AsSugarClient().Queryable<UserEntity>().Where(x => x.DeleteMark == null && userIdList.Contains(x.Id))
                            //     .Select(x => new UserEntity {Id = x.Id, Account = x.Account}).ToListAsync();
                            // dataList.ForEach(item =>
                            // {
                            //     Dictionary<string, string> dictionary = new();
                            //     dictionary.Add(item.Id, item.Account);
                            //     if (!addItem.Any(x => x.ContainsKey(item.Id)))
                            //     {
                            //         addItem.Add(dictionary);
                            //     }
                            // });
                            // resData.Add(item.__vModel__, addItem);
                        }
                    }
                }

                    break;
                case JnpfKeyConst.USERSSELECT:
                {
                    if (!resData.ContainsKey(item.__vModel__))
                    {
                        if (item.selectType.Equals("all"))
                        {
                            if (item.multiple)
                            {
                                (await _visualDevRepository.AsSugarClient().Queryable<SysUser>().Select(x => new { x.Id, x.Name, x.Account }).ToListAsync())
                                    .ForEach(item =>
                                    {
                                        Dictionary<string, string> user = new();
                                        user.Add(item.Id + "--user", item.Name + "/" + item.Account);
                                        addItem.Add(user);
                                    });
                                List<SysOrg>? dataList = await _visualDevRepository.AsSugarClient().Queryable<SysOrg>()
                                    .Select(x => new SysOrg { Id = x.Id, OrganizeIdTree = x.OrganizeIdTree, Name = x.Name, Code = x.Code }).ToListAsync();
                                dataList.ForEach(item =>
                                {
                                    Dictionary<string, string> user = new();
                                    user.Add(item.Id + "--department", item.Name + "/" + item.Code);
                                    addItem.Add(user);

                                    if (item.OrganizeIdTree.IsNullOrEmpty())
                                    {
                                        item.OrganizeIdTree = item.Id.ToString();
                                    }

                                    List<string> orgNameList = new();
                                    item.OrganizeIdTree.Split(",").ToList().ForEach(it =>
                                    {
                                        SysOrg? org = dataList.Find(x => x.Id == it.ParseToLong());
                                        if (org != null)
                                        {
                                            orgNameList.Add(org.Name);
                                        }
                                    });
                                    Dictionary<string, string> dictionary = new();
                                    dictionary.Add(item.Id + "--company", string.Join("/", orgNameList));
                                    addItem.Add(dictionary);
                                });
                                (await _visualDevRepository.AsSugarClient().Queryable<SysRole>().Select(x => new { x.Id, x.Name, x.Code }).ToListAsync())
                                    .ForEach(item =>
                                    {
                                        Dictionary<string, string> user = new();
                                        user.Add(item.Id + "--role", item.Name + "/" + item.Code);
                                        addItem.Add(user);
                                    });
                                // (await _visualDevRepository.AsSugarClient().Queryable<PositionEntity>().Where(x => x.DeleteMark == null).Select(x => new {x.Id, x.FullName, x.EnCode}).ToListAsync())
                                //     .ForEach(item =>
                                //     {
                                //         Dictionary<string, string> user = new();
                                //         user.Add(item.Id + "--position", item.FullName + "/" + item.EnCode);
                                //         addItem.Add(user);
                                //     });
                                // (await _visualDevRepository.AsSugarClient().Queryable<GroupEntity>().Where(x => x.DeleteMark == null).Select(x => new {x.Id, x.FullName, x.EnCode}).ToListAsync())
                                //     .ForEach(item =>
                                //     {
                                //         Dictionary<string, string> user = new();
                                //         user.Add(item.Id + "--group", item.FullName + "/" + item.EnCode);
                                //         addItem.Add(user);
                                //     });
                            }
                            else
                            {
                                List<SysUser>? dataList = await _visualDevRepository.AsSugarClient().Queryable<SysUser>()
                                    .Select(x => new SysUser { Id = x.Id, Account = x.Account }).ToListAsync();
                                dataList.ForEach(item =>
                                {
                                    Dictionary<string, string> dictionary = new();
                                    dictionary.Add(item.Id + "--user", item.Account);
                                    if (!addItem.Any(x => x.ContainsKey(item.Id.ToString())))
                                    {
                                        addItem.Add(dictionary);
                                    }
                                });
                            }

                            resData.Add(item.__vModel__, addItem);
                        }
                        else if (item.selectType.Equals("custom"))
                        {
                            if (item.ableIds.Any())
                            {
                                List<object> newAbleIds = new();
                                item.ableIds.ForEach(x => newAbleIds.Add(x.ParseToString().Split("--").FirstOrDefault()));
                                newAbleIds = DynamicParameterConversion(newAbleIds);
                                // var userIdList = await _visualDevRepository.AsSugarClient().Queryable<UserRelationEntity>().Where(x => newAbleIds.Contains(x.UserId) || newAbleIds.Contains(x.ObjectId))
                                //     .Select(x => x.UserId).ToListAsync();
                                // var dataList = await _visualDevRepository.AsSugarClient().Queryable<SysUser>().Where(x => userIdList.Contains(x.Id))
                                //     .Select(x => new UserEntity {Id = x.Id, Account = x.Account}).ToListAsync();
                                // dataList.ForEach(item =>
                                // {
                                //     Dictionary<string, string> dictionary = new();
                                //     dictionary.Add(item.Id + "--user", item.Account);
                                //     if (!addItem.Any(x => x.ContainsKey(item.Id)))
                                //     {
                                //         addItem.Add(dictionary);
                                //     }
                                // });
                                // resData.Add(item.__vModel__, addItem);
                            }
                        }
                    }
                }
                    break;
            }
        }

        listFieldsModel.Where(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.TABLE)).ToList().ForEach(async item =>
        {
            Dictionary<string, List<Dictionary<string, string>>> res = await GetCDataList(item.__config__.children, resData);
            if (res.Any())
            {
                foreach (KeyValuePair<string, List<Dictionary<string, string>>> it in res)
                {
                    if (!resData.ContainsKey(it.Key))
                    {
                        resData.Add(it.Key, it.Value);
                    }
                }
            }
        });

        return resData;
    }

    /// <summary>
    ///     导入数据组装.
    /// </summary>
    /// <param name="fieldsModelList">控件列表.</param>
    /// <param name="dataList">导入数据列表.</param>
    /// <param name="cDataList">控件解析缓存数据.</param>
    /// <returns></returns>
    private async Task<List<Dictionary<string, object>>> ImportDataAssemble(List<FieldsModel> fieldsModelList, List<Dictionary<string, object>> dataList,
        Dictionary<string, List<Dictionary<string, string>>> cDataList)
    {
        string errorKey = "errorsInfo";
        SysUser? userInfo = _userManager.User;

        List<Dictionary<string, object>> resList = new();
        foreach (Dictionary<string, object> dataItems in dataList)
        {
            Dictionary<string, object>? newDataItems = dataItems.Copy();
            foreach (KeyValuePair<string, object> item in dataItems)
            {
                FieldsModel? vModel = fieldsModelList.Find(x => x.__vModel__.Equals(item.Key));
                if (vModel == null)
                {
                    continue;
                }

                List<Dictionary<string, string>>? dicList = new();
                if (cDataList.ContainsKey(vModel.__config__.jnpfKey))
                {
                    dicList = cDataList[vModel.__config__.jnpfKey];
                }

                if ((dicList == null || !dicList.Any()) && cDataList.ContainsKey(vModel.__vModel__))
                {
                    dicList = cDataList[vModel.__vModel__];
                }

                switch (vModel.__config__.jnpfKey)
                {
                    case JnpfKeyConst.DATE:
                        try
                        {
                            if (item.Value.IsNotEmptyOrNull())
                            {
                                // 判断格式是否正确
                                DateTime value = DateTime.ParseExact(item.Value.ToString().TrimEnd(), vModel.format, CultureInfo.CurrentCulture);
                                if (vModel.__config__.startTimeRule)
                                {
                                    DateTime minDate = string.Format("{0:" + vModel.format + "}", DateTime.Now).ParseToDateTime();
                                    switch (vModel.__config__.startTimeType)
                                    {
                                        case 1:
                                        {
                                            if (vModel.__config__.startTimeValue.IsNotEmptyOrNull())
                                            {
                                                minDate = vModel.__config__.startTimeValue.ParseToDateTime();
                                            }
                                        }

                                            break;
                                        case 2:
                                        {
                                            if (vModel.__config__.startRelationField.IsNotEmptyOrNull() && dataItems.ContainsKey(vModel.__config__.startRelationField))
                                            {
                                                if (dataItems[vModel.__config__.startRelationField] == null)
                                                {
                                                    minDate = DateTime.MinValue;
                                                }
                                                else
                                                {
                                                    string? data = dataItems[vModel.__config__.startRelationField].ToString();
                                                    minDate = data.TrimEnd().ParseToDateTime();
                                                }
                                            }
                                        }

                                            break;
                                        case 3:
                                            break;
                                        case 4:
                                        {
                                            switch (vModel.__config__.startTimeTarget)
                                            {
                                                case 1:
                                                    minDate = minDate.AddYears(-vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                                case 2:
                                                    minDate = minDate.AddMonths(-vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                                case 3:
                                                    minDate = minDate.AddDays(-vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                            }
                                        }

                                            break;
                                        case 5:
                                        {
                                            switch (vModel.__config__.startTimeTarget)
                                            {
                                                case 1:
                                                    minDate = minDate.AddYears(vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                                case 2:
                                                    minDate = minDate.AddMonths(vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                                case 3:
                                                    minDate = minDate.AddDays(vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                            }
                                        }

                                            break;
                                    }

                                    if (minDate > value && !minDate.Equals(DateTime.MinValue))
                                    {
                                        string errorInfo = vModel.__config__.label + ": 日期选择值不在范围内";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }

                                if (vModel.__config__.endTimeRule)
                                {
                                    DateTime maxDate = string.Format("{0:" + vModel.format + "}", DateTime.Now).ParseToDateTime();
                                    switch (vModel.__config__.endTimeType)
                                    {
                                        case 1:
                                        {
                                            if (vModel.__config__.endTimeValue.IsNotEmptyOrNull())
                                            {
                                                maxDate = vModel.__config__.endTimeValue.ParseToDateTime();
                                            }
                                        }

                                            break;
                                        case 2:
                                        {
                                            if (vModel.__config__.endRelationField.IsNotEmptyOrNull() && dataItems.ContainsKey(vModel.__config__.endRelationField))
                                            {
                                                if (dataItems[vModel.__config__.endRelationField] == null)
                                                {
                                                    maxDate = DateTime.MinValue;
                                                }
                                                else
                                                {
                                                    string? data = dataItems[vModel.__config__.endRelationField].ToString();
                                                    maxDate = data.TrimEnd().ParseToDateTime();
                                                }
                                            }
                                        }

                                            break;
                                        case 3:
                                            break;
                                        case 4:
                                        {
                                            switch (vModel.__config__.startTimeTarget)
                                            {
                                                case 1:
                                                    maxDate = maxDate.AddYears(-vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                                case 2:
                                                    maxDate = maxDate.AddMonths(-vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                                case 3:
                                                    maxDate = maxDate.AddDays(-vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                            }
                                        }

                                            break;
                                        case 5:
                                        {
                                            switch (vModel.__config__.startTimeTarget)
                                            {
                                                case 1:
                                                    maxDate = maxDate.AddYears(vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                                case 2:
                                                    maxDate = maxDate.AddMonths(vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                                case 3:
                                                    maxDate = maxDate.AddDays(vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                            }
                                        }

                                            break;
                                    }

                                    if (maxDate < value && !maxDate.Equals(DateTime.MinValue))
                                    {
                                        string errorInfo = vModel.__config__.label + ": 日期选择值不在范围内";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }

                                newDataItems[item.Key] = value.ParseToUnixTime();
                            }
                        }
                        catch
                        {
                            string errorInfo = vModel.__config__.label + ": 值不正确";
                            if (newDataItems.ContainsKey(errorKey))
                            {
                                newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                            }
                            else
                            {
                                newDataItems.Add(errorKey, errorInfo);
                            }
                        }

                        break;
                    case JnpfKeyConst.TIME: // 时间选择
                        try
                        {
                            if (item.Value.IsNotEmptyOrNull())
                            {
                                DateTime value = DateTime.ParseExact(item.Value.ToString().TrimEnd(), vModel.format, CultureInfo.CurrentCulture);
                                if (vModel.__config__.startTimeRule)
                                {
                                    DateTime minTime = value;
                                    switch (vModel.__config__.startTimeType)
                                    {
                                        case 1:
                                        {
                                            if (vModel.__config__.startTimeValue.IsNotEmptyOrNull())
                                            {
                                                minTime = DateTime.Parse(vModel.__config__.startTimeValue);
                                            }
                                        }

                                            break;
                                        case 2:
                                        {
                                            if (vModel.__config__.startRelationField.IsNotEmptyOrNull() && dataItems.ContainsKey(vModel.__config__.startRelationField))
                                            {
                                                if (dataItems[vModel.__config__.startRelationField] == null)
                                                {
                                                    minTime = DateTime.MinValue;
                                                }
                                                else
                                                {
                                                    minTime = dataItems[vModel.__config__.startRelationField].ToString().ParseToDateTime();
                                                }
                                            }
                                        }

                                            break;
                                        case 3:
                                            break;
                                        case 4:
                                        {
                                            switch (vModel.__config__.startTimeTarget)
                                            {
                                                case 1:
                                                    minTime = minTime.AddHours(-vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                                case 2:
                                                    minTime = minTime.AddMinutes(-vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                                case 3:
                                                    minTime = minTime.AddSeconds(-vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                            }
                                        }

                                            break;
                                        case 5:
                                        {
                                            switch (vModel.__config__.startTimeTarget)
                                            {
                                                case 1:
                                                    minTime = minTime.AddHours(vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                                case 2:
                                                    minTime = minTime.AddMinutes(vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                                case 3:
                                                    minTime = minTime.AddSeconds(vModel.__config__.startTimeValue.ParseToInt());
                                                    break;
                                            }
                                        }

                                            break;
                                    }

                                    if (minTime > value && !minTime.Equals(DateTime.MinValue))
                                    {
                                        string errorInfo = vModel.__config__.label + ": 时间选择值不在范围内";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }

                                if (vModel.__config__.endTimeRule)
                                {
                                    DateTime maxTime = value;
                                    switch (vModel.__config__.endTimeType)
                                    {
                                        case 1:
                                        {
                                            if (vModel.__config__.endTimeValue.IsNotEmptyOrNull())
                                            {
                                                maxTime = DateTime.Parse(vModel.__config__.endTimeValue);
                                            }
                                        }

                                            break;
                                        case 2:
                                        {
                                            if (vModel.__config__.endRelationField.IsNotEmptyOrNull() && dataItems.ContainsKey(vModel.__config__.endRelationField))
                                            {
                                                if (dataItems[vModel.__config__.endRelationField] == null)
                                                {
                                                    maxTime = DateTime.MinValue;
                                                }
                                                else
                                                {
                                                    maxTime = dataItems[vModel.__config__.endRelationField].ToString().ParseToDateTime();
                                                }
                                            }
                                        }

                                            break;
                                        case 3:
                                            break;
                                        case 4:
                                        {
                                            switch (vModel.__config__.startTimeTarget)
                                            {
                                                case 1:
                                                    maxTime = maxTime.AddHours(-vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                                case 2:
                                                    maxTime = maxTime.AddMinutes(-vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                                case 3:
                                                    maxTime = maxTime.AddSeconds(-vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                            }
                                        }

                                            break;
                                        case 5:
                                        {
                                            switch (vModel.__config__.startTimeTarget)
                                            {
                                                case 1:
                                                    maxTime = maxTime.AddHours(vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                                case 2:
                                                    maxTime = maxTime.AddMinutes(vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                                case 3:
                                                    maxTime = maxTime.AddSeconds(vModel.__config__.endTimeValue.ParseToInt());
                                                    break;
                                            }
                                        }

                                            break;
                                    }

                                    if (maxTime < value && !maxTime.Equals(DateTime.MinValue))
                                    {
                                        string errorInfo = vModel.__config__.label + ": 时间选择值不在范围内";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }
                            }
                        }
                        catch
                        {
                            string errorInfo = vModel.__config__.label + ": 值不正确";
                            if (newDataItems.ContainsKey(errorKey))
                            {
                                newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                            }
                            else
                            {
                                newDataItems.Add(errorKey, errorInfo);
                            }
                        }

                        break;
                    case JnpfKeyConst.COMSELECT:
                    case JnpfKeyConst.ADDRESS:
                    {
                        if (item.Value.IsNotEmptyOrNull())
                        {
                            if (vModel.multiple)
                            {
                                List<object> addList = new();
                                item.Value.ToString().Split(",").ToList().ForEach(it =>
                                {
                                    if (vModel.__config__.jnpfKey.Equals(JnpfKeyConst.COMSELECT) || it.Count(x => x == '/') == vModel.level)
                                    {
                                        if (dicList.Where(x => x.ContainsValue(it)).Any())
                                        {
                                            KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(it)).FirstOrDefault().FirstOrDefault();
                                            addList.Add(value.Key.Split(",").ToList());
                                        }
                                        else
                                        {
                                            string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                            if (newDataItems.ContainsKey(errorKey))
                                            {
                                                newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                            }
                                            else
                                            {
                                                newDataItems.Add(errorKey, errorInfo);
                                            }
                                        }
                                    }
                                    else
                                    {
                                        string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                });
                                newDataItems[item.Key] = addList;
                            }
                            else
                            {
                                if (vModel.__config__.jnpfKey.Equals(JnpfKeyConst.COMSELECT) || item.Value?.ToString().Count(x => x == '/') == vModel.level)
                                {
                                    if (dicList.Where(x => x.ContainsValue(item.Value?.ToString())).Any())
                                    {
                                        KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(item.Value?.ToString())).FirstOrDefault().FirstOrDefault();
                                        newDataItems[item.Key] = value.Key.Split(",").ToList();
                                    }
                                    else
                                    {
                                        string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }
                                else
                                {
                                    string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                    if (newDataItems.ContainsKey(errorKey))
                                    {
                                        newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                    }
                                    else
                                    {
                                        newDataItems.Add(errorKey, errorInfo);
                                    }
                                }
                            }
                        }
                    }

                        break;
                    case JnpfKeyConst.CHECKBOX:
                    case JnpfKeyConst.SWITCH:
                    case JnpfKeyConst.SELECT:
                    case JnpfKeyConst.RADIO:
                    {
                        if (item.Value.IsNotEmptyOrNull())
                        {
                            if (vModel.multiple || vModel.__config__.jnpfKey.Equals(JnpfKeyConst.CHECKBOX))
                            {
                                List<object> addList = new();
                                item.Value.ToString().Split(",").ToList().ForEach(it =>
                                {
                                    if (dicList.Where(x => x.ContainsValue(it)).Any())
                                    {
                                        KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(it)).FirstOrDefault().LastOrDefault();
                                        addList.Add(value.Key);
                                    }
                                    else
                                    {
                                        string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                });
                                newDataItems[item.Key] = addList;
                            }
                            else
                            {
                                if (dicList.Where(x => x.ContainsValue(item.Value.ToString())).Any())
                                {
                                    KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(item.Value?.ToString())).FirstOrDefault().LastOrDefault();
                                    newDataItems[item.Key] = value.Key;
                                }
                                else
                                {
                                    string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                    if (newDataItems.ContainsKey(errorKey))
                                    {
                                        newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                    }
                                    else
                                    {
                                        newDataItems.Add(errorKey, errorInfo);
                                    }
                                }
                            }
                        }
                    }

                        break;
                    case JnpfKeyConst.ROLESELECT:
                    case JnpfKeyConst.USERSELECT:
                    {
                        if (item.Value.IsNotEmptyOrNull() && (vModel.selectType.IsNullOrEmpty() || vModel.selectType.Equals("all") || vModel.selectType.Equals("custom")))
                        {
                            if (vModel.multiple)
                            {
                                List<object> addList = new();
                                item.Value.ToString().Split(",").ToList().ForEach(it =>
                                {
                                    if (dicList.Where(x => x.ContainsValue(it.Split("/").Last())).Any())
                                    {
                                        KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(it.Split("/").Last())).FirstOrDefault().LastOrDefault();
                                        addList.Add(value.Key);
                                    }
                                    else
                                    {
                                        string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                });
                                newDataItems[item.Key] = addList;
                            }
                            else
                            {
                                if (dicList.Where(x => x.ContainsValue(item.Value.ToString().Split("/").Last())).Any())
                                {
                                    KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(item.Value?.ToString().Split("/").Last())).FirstOrDefault().LastOrDefault();
                                    newDataItems[item.Key] = value.Key;
                                }
                                else
                                {
                                    string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                    if (newDataItems.ContainsKey(errorKey))
                                    {
                                        newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                    }
                                    else
                                    {
                                        newDataItems.Add(errorKey, errorInfo);
                                    }
                                }
                            }
                        }
                        else
                        {
                            newDataItems[item.Key] = null;
                        }
                    }

                        break;
                    case JnpfKeyConst.USERSSELECT:
                    {
                        if (item.Value.IsNotEmptyOrNull() && (vModel.selectType.IsNullOrEmpty() || vModel.selectType.Equals("all") || vModel.selectType.Equals("custom")))
                        {
                            if (vModel.multiple)
                            {
                                List<object> addList = new();
                                item.Value.ToString().Split(",").ToList().ForEach(it =>
                                {
                                    if (dicList.Where(x => x.ContainsValue(it)).Any())
                                    {
                                        KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(it)).FirstOrDefault().LastOrDefault();
                                        addList.Add(value.Key);
                                    }
                                    else
                                    {
                                        if (dicList.Where(x => x.ContainsValue(it.Split("/").Last())).Any())
                                        {
                                            KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(it.Split("/").Last())).FirstOrDefault().LastOrDefault();
                                            addList.Add(value.Key);
                                        }
                                        else
                                        {
                                            string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                            if (newDataItems.ContainsKey(errorKey))
                                            {
                                                newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                            }
                                            else
                                            {
                                                newDataItems.Add(errorKey, errorInfo);
                                            }
                                        }
                                    }
                                });
                                newDataItems[item.Key] = addList;
                            }
                            else
                            {
                                if (dicList.Where(x => x.ContainsValue(item.Value.ToString())).Any())
                                {
                                    KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(item.Value?.ToString())).FirstOrDefault().LastOrDefault();
                                    newDataItems[item.Key] = value.Key;
                                }
                                else
                                {
                                    if (dicList.Where(x => x.ContainsValue(item.Value.ToString().Split("/").Last())).Any())
                                    {
                                        KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(item.Value?.ToString().Split("/").Last())).FirstOrDefault().LastOrDefault();
                                        newDataItems[item.Key] = value.Key;
                                    }
                                    else
                                    {
                                        string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            newDataItems[item.Key] = null;
                        }
                    }

                        break;
                    case JnpfKeyConst.TREESELECT:
                    {
                        if (item.Value.IsNotEmptyOrNull())
                        {
                            if (vModel.multiple)
                            {
                                List<object> addList = new();
                                item.Value.ToString().Split(",").ToList().ForEach(it =>
                                {
                                    if (dicList.Where(x => x.ContainsValue(it)).Any())
                                    {
                                        KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(it)).FirstOrDefault().LastOrDefault();
                                        addList.Add(value.Key);
                                    }
                                    else
                                    {
                                        string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                });
                                newDataItems[item.Key] = addList;
                            }
                            else
                            {
                                if (dicList.Where(x => x.ContainsValue(item.Value.ToString())).Any())
                                {
                                    KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(item.Value?.ToString())).FirstOrDefault().LastOrDefault();
                                    newDataItems[item.Key] = value.Key;
                                }
                                else
                                {
                                    string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                    if (newDataItems.ContainsKey(errorKey))
                                    {
                                        newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                    }
                                    else
                                    {
                                        newDataItems.Add(errorKey, errorInfo);
                                    }
                                }
                            }
                        }
                    }

                        break;
                    case JnpfKeyConst.CASCADER:
                    {
                        if (item.Value.IsNotEmptyOrNull())
                        {
                            if (vModel.multiple)
                            {
                                List<object> addsList = new();
                                item.Value.ToString().Split(",").ToList().ForEach(its =>
                                {
                                    List<string> txtList = its.Split(vModel.separator).ToList();

                                    List<object> add = new();
                                    txtList.ForEach(it =>
                                    {
                                        if (dicList.Where(x => x.ContainsValue(it)).Any())
                                        {
                                            KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(it)).FirstOrDefault().LastOrDefault();
                                            add.Add(value.Key);
                                        }
                                        else
                                        {
                                            string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                            if (newDataItems.ContainsKey(errorKey))
                                            {
                                                newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                            }
                                            else
                                            {
                                                newDataItems.Add(errorKey, errorInfo);
                                            }
                                        }
                                    });
                                    addsList.Add(add);
                                });
                                newDataItems[item.Key] = addsList;
                            }
                            else
                            {
                                List<string> txtList = item.Value.ToString().Split(vModel.separator).ToList();

                                List<object> addList = new();
                                txtList.ForEach(it =>
                                {
                                    if (dicList.Where(x => x.ContainsValue(it)).Any())
                                    {
                                        KeyValuePair<string, string> value = dicList.Where(x => x.ContainsValue(it)).FirstOrDefault().LastOrDefault();
                                        addList.Add(value.Key);
                                    }
                                    else
                                    {
                                        string errorInfo = vModel.__config__.label + ": 值无法匹配";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                });
                                newDataItems[item.Key] = addList;
                            }
                        }
                    }

                        break;
                    case JnpfKeyConst.TABLE:
                    {
                        if (item.Value != null)
                        {
                            List<Dictionary<string, object>>? valueList = item.Value.ToObjectOld<List<Dictionary<string, object>>>();
                            List<Dictionary<string, object>> newValueList = new();
                            valueList.ForEach(it =>
                            {
                                Dictionary<string, object> addValue = new();
                                foreach (KeyValuePair<string, object> value in it)
                                {
                                    addValue.Add(vModel.__vModel__ + "-" + value.Key, value.Value);
                                }

                                newValueList.Add(addValue);
                            });

                            List<Dictionary<string, object>> res = await ImportDataAssemble(vModel.__config__.children, newValueList, cDataList);
                            if (res.Any(x => x.ContainsKey(errorKey)))
                            {
                                if (newDataItems.ContainsKey(errorKey))
                                {
                                    newDataItems[errorKey] = newDataItems[errorKey] + "," + res.FirstOrDefault(x => x.ContainsKey(errorKey))[errorKey];
                                }
                                else
                                {
                                    newDataItems.Add(errorKey, res.FirstOrDefault(x => x.ContainsKey(errorKey))[errorKey].ToString());
                                }

                                res.Remove(res.FirstOrDefault(x => x.ContainsKey(errorKey)));
                            }

                            List<Dictionary<string, object>> result = new();
                            res.ForEach(it =>
                            {
                                Dictionary<string, object> addValue = new();
                                foreach (KeyValuePair<string, object> value in it)
                                {
                                    addValue.Add(value.Key.Replace(vModel.__vModel__ + "-", string.Empty), value.Value);
                                }

                                result.Add(addValue);
                            });
                            newDataItems[item.Key] = result;
                        }
                    }
                        break;
                    case JnpfKeyConst.RATE:
                        if (item.Value.IsNotEmptyOrNull())
                        {
                            try
                            {
                                double value = double.Parse(item.Value.ToString());

                                if (value < 0)
                                {
                                    throw new Exception(string.Empty);
                                }

                                if (vModel.allowHalf)
                                {
                                    if (value % 0.5 != 0)
                                    {
                                        throw new Exception(string.Empty);
                                    }
                                }
                                else
                                {
                                    if (value % 1 != 0)
                                    {
                                        throw new Exception(string.Empty);
                                    }
                                }

                                if (vModel.count != null && vModel.count < value)
                                {
                                    string errorInfo = vModel.__config__.label + ": 评分超过设置的最大值";
                                    if (newDataItems.ContainsKey(errorKey))
                                    {
                                        newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                    }
                                    else
                                    {
                                        newDataItems.Add(errorKey, errorInfo);
                                    }
                                }
                            }
                            catch
                            {
                                string errorInfo = vModel.__config__.label + ": 值不正确";
                                if (newDataItems.ContainsKey(errorKey))
                                {
                                    newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                }
                                else
                                {
                                    newDataItems.Add(errorKey, errorInfo);
                                }
                            }
                        }

                        break;
                    case JnpfKeyConst.SLIDER:
                        if (item.Value.IsNotEmptyOrNull())
                        {
                            try
                            {
                                decimal value = decimal.Parse(item.Value.ToString());
                                if (vModel.max != null)
                                {
                                    if (vModel.max < value)
                                    {
                                        string errorInfo = vModel.__config__.label + ": 滑块超过设置的最大值";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }

                                if (vModel.min != null)
                                {
                                    if (vModel.min > value)
                                    {
                                        string errorInfo = vModel.__config__.label + ": 滑块超过设置的最小值";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }
                            }
                            catch
                            {
                                string errorInfo = vModel.__config__.label + ": 值不正确";
                                if (newDataItems.ContainsKey(errorKey))
                                {
                                    newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                }
                                else
                                {
                                    newDataItems.Add(errorKey, errorInfo);
                                }
                            }
                        }

                        break;
                    case JnpfKeyConst.NUMINPUT:
                        if (item.Value.IsNotEmptyOrNull())
                        {
                            try
                            {
                                decimal value = decimal.Parse(item.Value.ToString());
                                if (vModel.max != null)
                                {
                                    if (vModel.max < value)
                                    {
                                        string errorInfo = vModel.__config__.label + ": 数字输入超过设置的最大值";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }

                                if (vModel.min != null)
                                {
                                    if (vModel.min > value)
                                    {
                                        string errorInfo = vModel.__config__.label + ": 数字输入超过设置的最小值";
                                        if (newDataItems.ContainsKey(errorKey))
                                        {
                                            newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                        }
                                        else
                                        {
                                            newDataItems.Add(errorKey, errorInfo);
                                        }
                                    }
                                }
                            }
                            catch
                            {
                                string errorInfo = vModel.__config__.label + ": 值不正确";
                                if (newDataItems.ContainsKey(errorKey))
                                {
                                    newDataItems[errorKey] = newDataItems[errorKey] + "," + errorInfo;
                                }
                                else
                                {
                                    newDataItems.Add(errorKey, errorInfo);
                                }
                            }
                        }

                        break;
                }
            }

            // 系统自动生成控件
            foreach (KeyValuePair<string, object> item in dataItems)
            {
                if (newDataItems.ContainsKey(errorKey))
                {
                    continue; // 如果存在错误信息 则 不生成
                }

                FieldsModel? vModel = fieldsModelList.Find(x => x.__vModel__.Equals(item.Key));
                if (vModel == null)
                {
                    continue;
                }

                switch (vModel.__config__.jnpfKey)
                {
                    case JnpfKeyConst.BILLRULE:
                        string billNumber = await _billRuleService.GetBillNumber(vModel.__config__.rule, false);
                        if (!"单据规则不存在".Equals(billNumber))
                        {
                            newDataItems[item.Key] = billNumber;
                        }
                        else
                        {
                            newDataItems[item.Key] = string.Empty;
                        }

                        break;
                    case JnpfKeyConst.MODIFYUSER:
                        newDataItems[item.Key] = string.Empty;
                        break;
                    case JnpfKeyConst.CREATEUSER:
                        newDataItems[item.Key] = userInfo?.Id;
                        break;
                    case JnpfKeyConst.MODIFYTIME:
                        newDataItems[item.Key] = string.Empty;
                        break;
                    case JnpfKeyConst.CREATETIME:
                        newDataItems[item.Key] = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now);
                        break;
                    case JnpfKeyConst.CURRORGANIZE:
                        if (userInfo?.OrgId != null)
                        {
                            newDataItems[item.Key] = userInfo?.OrgId;
                        }
                        else
                        {
                            newDataItems[item.Key] = string.Empty;
                        }

                        break;
                }
            }

            if (fieldsModelList.Any(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.COMINPUT) && x.__config__.unique) && dataItems.ContainsKey("f_flow_id") &&
                dataItems.ContainsKey("Update_MainTablePrimary_Id"))
            {
                string? mainId = dataItems["Update_MainTablePrimary_Id"].ToString();
                // var taskFlowStatus = await _visualDevRepository.AsSugarClient().Queryable<FlowTaskEntity>().Where(it => it.Id.Equals(mainId)).Select(it => it.Status).FirstAsync();
                // if (taskFlowStatus.IsNotEmptyOrNull() && !taskFlowStatus.Equals(0))
                // {
                //     dataItems.Add(errorKey, "已发起流程，导入失败");
                //     resList.Add(dataItems);
                //     continue;
                // }
            }

            if (newDataItems.ContainsKey(errorKey))
            {
                if (dataItems.ContainsKey(errorKey))
                {
                    dataItems[errorKey] = newDataItems[errorKey].ToString();
                }
                else
                {
                    dataItems.Add(errorKey, newDataItems[errorKey]);
                }

                resList.Add(dataItems);
            }
            else
            {
                resList.Add(newDataItems);
            }
        }

        return resList;
    }

    /// <summary>
    ///     处理静态数据.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private List<Dictionary<string, string>> GetStaticList(FieldsModel model)
    {
        PropsBeanModel? props = model.props;
        List<OptionsModel>? optionList = GetTreeOptions(model.options, props);
        List<Dictionary<string, string>> list = new();
        foreach (OptionsModel? item in optionList)
        {
            Dictionary<string, string> option = new();
            option.Add(item.value, item.label);
            list.Add(option);
        }

        return list;
    }

    /// <summary>
    ///     options无限级.
    /// </summary>
    /// <returns></returns>
    private List<OptionsModel> GetTreeOptions(List<Dictionary<string, object>> model, PropsBeanModel props)
    {
        List<OptionsModel> options = new();
        foreach (object? item in model)
        {
            OptionsModel option = new();
            Dictionary<string, object>? dicObject = item.ToObjectOld<Dictionary<string, object>>();
            option.label = dicObject[props.label].ToString();
            option.value = dicObject[props.value].ToString();
            if (dicObject.ContainsKey(props.children))
            {
                List<Dictionary<string, object>>? children = dicObject[props.children].ToObjectOld<List<Dictionary<string, object>>>();
                options.AddRange(GetTreeOptions(children, props));
            }

            options.Add(option);
        }

        return options;
    }

    /// <summary>
    ///     获取动态无限级数据.
    /// </summary>
    /// <param name="data"></param>
    /// <param name="props"></param>
    /// <returns></returns>
    private List<Dictionary<string, string>> GetDynamicInfiniteData(string data, PropsBeanModel props)
    {
        List<Dictionary<string, string>> list = new();
        string? value = props.value;
        string? label = props.label;
        string? children = props.children;
        foreach (JToken? info in JToken.Parse(data))
        {
            Dictionary<string, string> dic = new();
            dic[info.Value<string>(value)] = info.Value<string>(label);
            list.Add(dic);
            if (info.Value<object>(children) != null && info.Value<object>(children).ToString() != string.Empty)
            {
                list.AddRange(GetDynamicInfiniteData(info.Value<object>(children).ToString(), props));
            }
        }

        return list;
    }

    /// <summary>
    ///     处理模板默认值.
    ///     用户选择 , 部门选择 , 岗位选择 , 角色选择 , 分组选择 ， 用户组件.
    /// </summary>
    /// <param name="config">模板.</param>
    /// <returns></returns>
    private VisualDevModelDataConfigOutput GetVisualDevModelDataConfig(VisualDevEntity config)
    {
        if (config.WebType.Equals(4))
        {
            return config.Adapt<VisualDevModelDataConfigOutput>();
        }

        TemplateParsingBase tInfo = new(config);
        if (tInfo.AllFieldsModel.Any(x =>
                x.__config__.defaultCurrent && (x.__config__.jnpfKey.Equals(JnpfKeyConst.USERSELECT) || x.__config__.jnpfKey.Equals(JnpfKeyConst.ROLESELECT) ||
                                                x.__config__.jnpfKey.Equals(JnpfKeyConst.USERSSELECT))))
        {
            // var userId = _userManager.UserId;
            // var depId = _visualDevRepository.AsSugarClient().Queryable<UserEntity, OrganizeEntity>((a, b) => new JoinQueryInfos(JoinType.Left, b.Id == a.OrganizeId))
            //     .Where((a, b) => a.Id.Equals(_userManager.UserId) && b.Category.Equals("department")).Select((a, b) => a.OrganizeId).First();
            // var posIds = _visualDevRepository.AsSugarClient()
            //     .Queryable<PositionEntity, UserRelationEntity>((a, b) => new JoinQueryInfos(JoinType.Left, a.Id == b.ObjectId && b.ObjectType.Equals("Position")))
            //     .Where((a, b) => b.UserId.Equals(_userManager.UserId) && a.OrganizeId.Equals(_userManager.User.OrganizeId)).Select(a => a.Id).ToList();
            // var roleIds = _visualDevRepository.AsSugarClient().Queryable<UserRelationEntity>()
            //     .Where(it => it.UserId.Equals(_userManager.UserId) && it.ObjectType.Equals("Role")).Select(it => it.ObjectId).ToList();
            // var groupIds = _visualDevRepository.AsSugarClient().Queryable<UserRelationEntity>()
            //     .Where(it => it.UserId.Equals(_userManager.UserId) && it.ObjectType.Equals("Group")).Select(it => it.ObjectId).ToList();
            //
            // var allUserRelationList = _visualDevRepository.AsSugarClient().Queryable<UserRelationEntity>().Select(x => new UserRelationEntity {UserId = x.UserId, ObjectId = x.ObjectId}).ToList();
            //
            // var configData = config.FormData.ToObject<Dictionary<string, object>>();
            // var columnList = configData["fields"].ToObject<List<Dictionary<string, object>>>();
            // _runService.FieldBindDefaultValue(ref columnList, userId, depId, posIds, roleIds, groupIds, allUserRelationList);
            // configData["fields"] = columnList;
            // config.FormData = configData.ToJson();
            //
            // configData = config.ColumnData.ToObject<Dictionary<string, object>>();
            // var searchList = configData["searchList"].ToObject<List<Dictionary<string, object>>>();
            // columnList = configData["columnList"].ToObject<List<Dictionary<string, object>>>();
            // _runService.FieldBindDefaultValue(ref searchList, userId, depId, posIds, roleIds, groupIds, allUserRelationList);
            // _runService.FieldBindDefaultValue(ref columnList, userId, depId, posIds, roleIds, groupIds, allUserRelationList);
            // configData["searchList"] = searchList;
            // configData["columnList"] = columnList;
            // config.ColumnData = configData.ToJson();
            //
            // configData = config.AppColumnData.ToObject<Dictionary<string, object>>();
            // searchList = configData["searchList"].ToObject<List<Dictionary<string, object>>>();
            // columnList = configData["columnList"].ToObject<List<Dictionary<string, object>>>();
            // _runService.FieldBindDefaultValue(ref searchList, userId, depId, posIds, roleIds, groupIds, allUserRelationList);
            // _runService.FieldBindDefaultValue(ref columnList, userId, depId, posIds, roleIds, groupIds, allUserRelationList);
            // configData["searchList"] = searchList;
            // configData["columnList"] = columnList;
            // config.AppColumnData = configData.ToJson();
        }

        return config.Adapt<VisualDevModelDataConfigOutput>();
    }

    /// <summary>
    ///     动态参数的转换.
    /// </summary>
    /// <param name="dynamicParameter"></param>
    /// <returns></returns>
    private List<object> DynamicParameterConversion(List<object> dynamicParameter)
    {
        List<object> list = new();
        foreach (object item in dynamicParameter)
        {
            if (item.ToString().Contains("["))
            {
                string? str = item.ToObjectOld<List<string>>().LastOrDefault();
                list.AddRange(ReplaceParameter(str));
            }
            else
            {
                list.AddRange(ReplaceParameter(item.ToString()));
            }
        }

        return list;
    }

    /// <summary>
    ///     替换参数.
    /// </summary>
    /// <param name="parameter"></param>
    /// <returns></returns>
    private List<string> ReplaceParameter(string parameter)
    {
        List<string> result = new();

        // // 获取所有组织
        // List<SysOrg>? allOrgList = _organizeService.GetOrgListTreeName();
        // switch (parameter)
        // {
        //     case "@currentOrg":
        //         result.Add(_userManager.User.OrganizeId);
        //         break;
        //     case "@currentOrgAndSubOrg":
        //         result.AddRange(allOrgList.TreeChildNode(_userManager.User.OrganizeId, t => t.Id, t => t.ParentId).Select(it => it.Id).ToList());
        //         break;
        //     case "@currentGradeOrg":
        //         if (_userManager.IsAdministrator)
        //         {
        //             result.AddRange(allOrgList.Select(it => it.Id).ToList());
        //         }
        //         else
        //         {
        //             result.AddRange(_userManager.DataScope.Select(x => x.organizeId).ToList());
        //         }
        //
        //         break;
        //     default:
        //         result.Add(parameter);
        //         break;
        // }

        return result;
    }

    #endregion
}