using System.ComponentModel.DataAnnotations;
using Common.Models;

namespace IotPlatform.ProgramBlock.Entity;

/// <summary>
///     程序块-下拉输出
/// </summary>
public class ProgramBlockSelectOutput
{
    /// <summary>
    ///     Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string Remark { get; set; }
}