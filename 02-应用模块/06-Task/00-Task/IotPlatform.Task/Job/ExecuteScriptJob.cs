using System.Diagnostics;
using IotPlatform.ProgramBlock.Entity;
using DateTime = System.DateTime;

namespace IotPlatform.Task.Job;

/// <summary>
///     任务中心-定时任务
/// </summary>
public class ExecuteScriptJob : IJob
{
    /// <summary>
    ///     日志
    /// </summary>
    private readonly ILogger<ExecuteScriptJob> _logger;
    /// <summary>
    ///     服务提供者
    /// </summary>
    private readonly IServiceProvider _scope;
    /// <summary>
    ///     数据库
    /// </summary>
    private readonly ISqlSugarClient _db;
    /// <summary>
    ///     引擎池
    /// </summary>
    private readonly JsScriptEnginePool _enginePool;
    /// <summary>
    ///     计时器
    /// </summary>
    private readonly Stopwatch _stopwatch = new();

    /// <summary>
    ///     构造函数    
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="scopeFactory"></param>
    /// <param name="db"></param>
    /// <param name="enginePool"></param>
    public ExecuteScriptJob(
        ILogger<ExecuteScriptJob> logger,
        IServiceScopeFactory scopeFactory,
        ISqlSugarClient db,
        JsScriptEnginePool enginePool)
    {
        // 初始化日志
        _logger = logger;
        // 初始化服务提供者
        _scope = scopeFactory.CreateScope().ServiceProvider;
        // 初始化数据库
        _db = db;
        // 初始化引擎池
        _enginePool = enginePool;
    }

    /// <summary>
    ///     执行任务
    /// </summary>
    /// <param name="context"></param>
    /// <param name="stoppingToken"></param>
    /// <returns></returns>
    public async System.Threading.Tasks.Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        // 创建一个30秒超时的取消令牌
        using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
        using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, stoppingToken);

        // 获取当前时间
        DateTime dateTime = DateTime.Now;

        // 获取任务详情
        var definitionDetail = GetTaskDefinition(context);
        if (definitionDetail == null) return;

        // 创建执行记录
        var log = CreateExecutionLog(definitionDetail, dateTime);
        // 重启计时器
        _stopwatch.Restart();
        try
        {
            // 执行任务，使用带超时的令牌
            log.Result = await ExecuteTaskAsync(definitionDetail, linkedCts.Token);
            // 设置执行成功
            log.Success = true;
        }
        catch (OperationCanceledException)
        {
            // 处理超时情况
            string errorMsg = ActionErrorEx("任务执行超时(30秒)", definitionDetail.Id, "", definitionDetail.ExecuteContent);
            log.Result = errorMsg;
            _logger.LogWarning($"【任务中心-定时任务】 任务Id:{definitionDetail.Id} 执行超时");
        }
        catch (Exception ex)
        {
            // 设置执行错误
            string errorMsg = ActionErrorEx(ex.Message, definitionDetail.Id, "", definitionDetail.ExecuteContent, ex.InnerException?.Message);
            log.Result = errorMsg;
            // 记录错误日志
            _logger.LogError($"【任务中心-定时任务】 任务Id:{definitionDetail.Id} 执行Error:【{ex.Message}】");
        }
        // 停止计时器
        _stopwatch.Stop();
        // 设置执行时间
        log.ElapsedMilliseconds = _stopwatch.ElapsedMilliseconds;

        // 保存执行记录
        await SaveExecutionLog(log, context);
    }

    /// <summary>
    ///     执行任务
    /// </summary>
    /// <param name="detail"></param>
    /// <param name="stoppingToken"></param>
    /// <returns></returns>
    /// <exception cref="NotSupportedException"></exception>
    private async Task<object> ExecuteTaskAsync(TaskDefinitionDetail detail, CancellationToken stoppingToken)
    {
        // 根据任务类型执行任务
        switch (detail.ExecuteType)
        {
            // 执行脚本
            case ExecuteTypeEnum.Script:
                return await ExecuteScriptTaskAsync(detail.ExecuteContent);

            // 执行程序块
            case ExecuteTypeEnum.Task:
                return await ExecuteProgramBlockTaskAsync(detail.ExecuteContent, stoppingToken);

            // 执行消息推送
            case ExecuteTypeEnum.Messgae:
                return await ExecuteMessageTaskAsync(detail.ExecuteContent);

            // 不支持的执行类型
            default:
                throw new NotSupportedException($"不支持的执行类型: {detail.ExecuteType}");
        }
    }

    /// <summary>
    ///     执行脚本
    /// </summary>
    /// <param name="script"></param>
    /// <returns></returns>
    private async Task<object> ExecuteScriptTaskAsync(string script)
    {
        // 执行脚本并返回结果和日志
        var (result, log) = await _enginePool.ExecuteScriptWithLogAsync(
            "task-script",
            script,
            new { timestamp = DateTime.Now }
        );
        return result;
    }

    /// <summary>
    ///     执行程序块
    /// </summary>
    /// <param name="content"></param>
    /// <param name="stoppingToken"></param>
    /// <returns></returns>
    private async Task<object> ExecuteProgramBlockTaskAsync(string content, CancellationToken stoppingToken)
    {
        // 解析执行参数
        var runTaskTemplate = content.ToObjectOld<RunProgramBlock>();
        // 如果解析失败，则抛出异常
        if (runTaskTemplate == null)
        {
            throw Oops.Oh("配置参数异常！");
        }

        // 获取程序块
        var programBlock = await _db.CopyNew()
            .Queryable<ProgramBlock.Entity.ProgramBlock>()
            .FirstAsync(f => f.Id == runTaskTemplate.Id, stoppingToken);

        // 如果程序块不存在，则抛出异常
        if (programBlock == null)
        {
            throw Oops.Oh("程序块已经被删除！");
        }

        // 如果程序块内容为空，则返回null   
        if (string.IsNullOrEmpty(programBlock.Content))
        {
            return null;
        }

        // 使用脚本上下文执行
        await using var context = await _enginePool.CreateContextAsync();

        // 设置变量
        foreach (var (key, value) in runTaskTemplate.Values)
        {
            try
            {
                // 设置变量
                context.SetVariable(key, value);
            }
            catch (Exception e)
            {
                throw Oops.Oh($"传入参数：{key}，值：{value}，Error：{e.Message}");
            }
        }

        // 执行程序块脚本
        var (result, log) = await context.ExecuteAsync(
            $"program-block-{runTaskTemplate.Id}",
            programBlock.Content,
            new { timestamp = DateTime.Now }
        );

        return result;
    }

    /// <summary>
    ///     执行消息推送    
    /// </summary>
    /// <param name="content"></param>
    /// <returns></returns>
    private async Task<object> ExecuteMessageTaskAsync(string content)
    {
        // 解析执行参数
        var sendStrategy = JSON.Deserialize<StandardSelectOutput>(content, _scope);
        // 执行消息推送
        return await _scope.GetRequiredService<ExecuteMessagePushStrategy>()
            .ExecuteMessagePushStrategyTask(new BaseId { Id = sendStrategy.Id });
    }

    /// <summary>
    ///     获取任务详情
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    private static TaskDefinitionDetail GetTaskDefinition(JobExecutingContext context)
    {
        // 获取任务详情
        var properties = context.JobDetail.GetProperties();
        // 如果任务详情存在，则返回任务详情
        if (properties.TryGetValue("TaskDefinitionDetail", out object? property))
        {
            return property.ToString().ToObjectOld<TaskDefinitionDetail>();
        }
        return null;
    }

    /// <summary>
    ///     创建执行记录
    /// </summary>
    /// <param name="detail"></param>
    /// <param name="dateTime"></param>
    /// <returns></returns>
    private static TaskDefinitionRecord CreateExecutionLog(TaskDefinitionDetail detail, DateTime dateTime)
    {
        // 创建执行记录
        return new TaskDefinitionRecord
        {
            // 设置执行时间
            ActionTime = dateTime,
            // 设置任务详情Id
            TaskDetailId = detail.Id,
            // 设置租户Id
            TenantId = detail.TenantId,
            // 设置系统任务Id
            SysJobTaskId = detail.SysJobTaskId
        };
    }

    /// <summary>
    ///     保存执行记录
    /// </summary>
    /// <param name="log"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    private async System.Threading.Tasks.Task SaveExecutionLog(TaskDefinitionRecord log, JobExecutingContext context)
    {
        // 保存执行记录
        await _db.CopyNew().Insertable(log).SplitTable().ExecuteCommandAsync();
        // 设置执行结果
        context.Result = log.Result.ToJson();
    }

    /// <summary>
    ///     处理错误    
    /// </summary>
    /// <param name="ex"></param>
    /// <param name="jobDetailId"></param>
    /// <param name="cron"></param>
    /// <param name="content"></param>
    /// <param name="innerEx"></param>
    /// <returns></returns>
    private static string ActionErrorEx(string ex, long jobDetailId, string cron, string content, string? innerEx = "")
    {
        // 返回错误信息
        return TP.Wrapper(
            "任务执行时发生异常",
            $"报错原因:【{ex}】",
            $"执行Id:【{jobDetailId}】",
            $"执行周期:【{cron}】",
            $"执行参数:【{content}】",
            $"堆栈异常:【{innerEx}】"
        );
    }
}