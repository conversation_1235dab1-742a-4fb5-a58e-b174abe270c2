using IotPlatform.Core.Entity;
using SqlSugar;

namespace IotPlatform.WorkFlow.Entity;

/// <summary>
///     工作流模板实例变量值
/// </summary>
[SugarTable("business_flowProjectVariableValue", "工作流模板实例变量值")]
public class FlowProjectVariableValue : EntityTenantId
{
    /// <summary>
    ///     所属实例ID
    /// </summary>
    [SugarColumn(ColumnDescription = "所属实例ID")]
    public long InstanceId { get; set; }

    /// <summary>
    ///     关联实例
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(InstanceId))]
    public FlowProject Instance { get; set; }

    /// <summary>
    ///     变量ID
    /// </summary>
    [SugarColumn(ColumnDescription = "变量ID")]
    public long VariableId { get; set; }

    /// <summary>
    ///     关联变量
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(VariableId))]
    public FlowProjectVariable Variable { get; set; }

    /// <summary>
    ///     变量值
    /// </summary>
    [SugarColumn(ColumnDescription = "变量值", IsNullable = true)]
    public string? Value { get; set; }
}