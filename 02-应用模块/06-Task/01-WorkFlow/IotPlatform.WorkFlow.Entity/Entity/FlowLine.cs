namespace IotPlatform.WorkFlow.Entity;

/// <summary>
///     工作流连接节点
/// </summary>
[SugarTable("business_flowLine", "工作流连接节点")]
public class FlowLine
{
    /// <summary>
    ///     雪花Id
    /// </summary>
    [SugarColumn(ColumnDescription = "Id", IsPrimaryKey = true)]
    public Guid Id { get; set; }

    /// <summary>
    ///     项目Id
    /// </summary>
    [SugarColumn(ColumnDescription = "项目Id")]
    public long ProjectId { get; set; }

    /// <summary>
    ///     开始节点ID
    /// </summary>
    [SugarColumn(ColumnDescription = "开始节点ID")]
    public Guid From { get; set; }

    /// <summary>
    ///     结束节点ID
    /// </summary>
    [SugarColumn(ColumnDescription = "结束节点ID")]
    public Guid To { get; set; }

    /// <summary>
    ///     条件
    /// </summary>
    [SugarColumn(ColumnDescription = "条件", IsNullable = true)]
    public string? Label { get; set; }

    #region 关联表

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ProjectId))]
    public FlowProject FlowProject { get; set; }

    /// <summary>
    ///     开始节点
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(From))]
    public FlowNode FromFlowNode { get; set; }

    /// <summary>
    ///     结束节点
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(To))]
    public FlowNode ToFlowNode { get; set; }

    #endregion
}