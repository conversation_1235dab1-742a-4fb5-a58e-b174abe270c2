using Extras.DatabaseAccessor.SqlSugar.Extensions;

namespace IotPlatform.WorkFlow.Entity.SeedData;

/// <summary>
///     任务流种子数据
/// </summary>
public class FlowProjectSeedData : ISqlSugarEntitySeedData<FlowProject>
{
    public IEnumerable<FlowProject> HasData()
    {
        yield return new FlowProject
        {
            Pid = 0,
            Name = "流程设计",
            Status = FlowProjectStatusEnum.Publish,
            FlowLine = new List<FlowLine>(),
            FlowNode = new List<FlowNode>(),
            Id = 311851256283205
        };
    }
}