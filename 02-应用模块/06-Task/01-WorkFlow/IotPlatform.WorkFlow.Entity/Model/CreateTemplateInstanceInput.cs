namespace IotPlatform.WorkFlow.Entity;

/// <summary>
///     创建模板实例输入
/// </summary>
public class CreateTemplateInstanceInput
{
    /// <summary>
    ///     模板ID
    /// </summary>
    public long TemplateId { get; set; }

    /// <summary>
    ///     实例名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     实例描述
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    ///     变量值列表
    /// </summary>
    public List<TemplateVariableValue> Variables { get; set; } = new List<TemplateVariableValue>();
}

/// <summary>
///     模板变量值
/// </summary>
public class TemplateVariableValue
{
    /// <summary>
    ///     变量ID
    /// </summary>
    public long VariableId { get; set; }

    /// <summary>
    ///     变量值
    /// </summary>
    public string? Value { get; set; }
}