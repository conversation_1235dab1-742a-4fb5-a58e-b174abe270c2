using Common.Configuration;
using Common.Core.Manager.Files;
using Common.Models;
using Common.Options;
using Common.Security;
using Furion.VirtualFileServer;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Microsoft.AspNetCore.StaticFiles;
using DateTime = System.DateTime;
using Extensions = IotPlatform.Core.Extension.Extensions;

namespace Systems.Core.File;

/// <summary>
///     系统文件服务
/// </summary>
[ApiDescriptionSettings("系统服务", Order = 410)]
public class FileService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SysFile> _sysFileRep;
    private readonly UploadOptions _uploadOptions;
    private readonly IFileManager _fileManager;

    /// <summary>
    ///     用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    ///     缓存服务.
    /// </summary>
    private readonly SysCacheService _cacheManager;

    public FileService(ISqlSugarRepository<SysFile> sysFileRep, IOptions<UploadOptions> uploadOptions, SysCacheService cacheManager, IFileManager fileManager, IUserManager userManager)
    {
        _sysFileRep = sysFileRep;
        _cacheManager = cacheManager;
        _fileManager = fileManager;
        _userManager = userManager;
        _uploadOptions = uploadOptions.Value;
    }

    #region v1

    /// <summary>
    ///     获取文件分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取文件分页列表")]
    [HttpGet("/sysFileInfo/page")]
    public async Task<SqlSugarPagedList<SysFile>> Page([FromQuery] PageFileInput input)
    {
        return await _sysFileRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.FileName), u => u.FileName.Contains(input.FileName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StartTime.ToString()) && !string.IsNullOrWhiteSpace(input.EndTime.ToString()),
                u => u.CreatedTime >= input.StartTime && u.CreatedTime <= input.EndTime)
            .OrderBy(u => u.CreatedTime, OrderByType.Desc)
            .ToPagedListAsync(input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     上传文件
    /// </summary>
    /// <param name="file"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    [DisplayName("上传文件")]
    [HttpPost("/sysFileInfo/uploadFile")]
    public async Task<FileOutput> UploadFile([Required] IFormFile file, [FromQuery] string? path)
    {
        SysFile sysFile = await HandleUploadFile(file, path);
        sysFile.FilePath = sysFile.FilePath?.TrimStart('.').TrimStart('/').TrimEnd('/');
        // 生成外链
        sysFile.Url = $"{Extensions.GetLocalhost()}/{sysFile.FilePath}/{sysFile.Id + sysFile.Suffix}";
        await _sysFileRep.AsInsertable(sysFile).ExecuteCommandAsync();
        return new FileOutput
        {
            Id = sysFile.Id,
            Url = sysFile.Url, // string.IsNullOrWhiteSpace(sysFile.Url) ? _commonService.GetFileUrl(sysFile) : sysFile.Url,
            SizeKb = sysFile.SizeKb,
            Suffix = sysFile.Suffix,
            FilePath = sysFile.FilePath,
            FileName = sysFile.FileName
        };
    }

    /// <summary>
    ///     远程控制-固件管理-上传文件
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    [DisplayName("远程控制-固件管理-上传文件")]
    [HttpPost("/sysFileInfo/firmwareManage/uploadFile")]
    [DisableRequestSizeLimit]
    public async Task<FileOutput> UploadFile([Required] IFormFile file)
    {
        SysFile sysFile = await HandleUploadFile(file, "");
        // 生成外链
        sysFile.Url = $"{Extensions.GetLocalhost()}/{sysFile.FilePath}/{sysFile.Id + sysFile.Suffix}";
        await _sysFileRep.AsInsertable(sysFile).ExecuteCommandAsync();
        return new FileOutput
        {
            Id = sysFile.Id,
            Url = sysFile.Url, // string.IsNullOrWhiteSpace(sysFile.Url) ? _commonService.GetFileUrl(sysFile) : sysFile.Url,
            SizeKb = sysFile.SizeKb,
            Suffix = sysFile.Suffix,
            FilePath = sysFile.FilePath,
            FileName = sysFile.FileName
        };
    }

    /// <summary>
    ///     上传文件Base64
    /// </summary>
    /// <param name="strBase64"></param>
    /// <param name="fileName"></param>
    /// <param name="contentType"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    private async Task<FileOutput> UploadFileFromBase64(string strBase64, string fileName, string contentType, string? path)
    {
        byte[] fileData = Convert.FromBase64String(strBase64);
        MemoryStream ms = new();
        ms.Write(fileData);
        ms.Seek(0, SeekOrigin.Begin);
        if (string.IsNullOrEmpty(fileName))
        {
            fileName = $"{YitIdHelper.NextId()}.jpg";
        }

        if (string.IsNullOrEmpty(contentType))
        {
            contentType = "image/jpg";
        }

        IFormFile formFile = new FormFile(ms, 0, fileData.Length, "file", fileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = contentType
        };
        return await UploadFile(formFile, path);
    }

    /// <summary>
    ///     上传文件Base64
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("上传文件Base64")]
    [HttpPost("/sysFileInfo/uploadFileFromBase64")]
    public async Task<FileOutput> UploadFileFromBase64(UploadFileFromBase64Input input)
    {
        return await UploadFileFromBase64(input.FileDataBase64, input.FileName, input.ContentType, input.Path);
    }

    /// <summary>
    ///     上传多文件
    /// </summary>
    /// <param name="files"></param>
    /// <returns></returns>
    [DisplayName("上传多文件")]
    [HttpPost("/sysFileInfo/uploadFiles")]
    public async Task<List<FileOutput>> UploadFiles([Required] List<IFormFile> files)
    {
        List<FileOutput> filelist = new();
        foreach (IFormFile file in files)
        {
            filelist.Add(await UploadFile(file, ""));
        }

        return filelist;
    }

    /// <summary>
    ///     根据文件Id或Url下载
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("根据文件Id或Url下载")]
    [HttpPost("/sysFileInfo/downloadFile")]
    public async Task<IActionResult> DownloadFile(FileInput input)
    {
        SysFile file = input.Id > 0 ? await GetFile(input) : await _sysFileRep.GetFirstAsync(u => u.Url == input.Url);
        string fileName = HttpUtility.UrlEncode(file.FileName, Encoding.GetEncoding("UTF-8"));
        string filePath = Path.Combine(file.FilePath, file.Id + file.Suffix);
        string path = Path.Combine(App.WebHostEnvironment.ContentRootPath, filePath);
        return new FileStreamResult(new FileStream(path, FileMode.Open), "application/octet-stream") { FileDownloadName = fileName + file.Suffix };
    }

    /// <summary>
    ///     删除文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysFileInfo/delete")]
    [DisplayName("删除文件")]
    public async Task DeleteFile(DeleteFileInput input)
    {
        SysFile file = await _sysFileRep.GetFirstAsync(u => u.Id == input.Id);
        if (file != null)
        {
            await _sysFileRep.DeleteAsync(file);
            string filePath = Path.Combine(App.WebHostEnvironment.ContentRootPath, file.FilePath, input.Id + file.Suffix);
            if (System.IO.File.Exists(filePath))
            {
                System.IO.File.Delete(filePath);
            }
        }
    }

    /// <summary>
    ///     更新文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysFileInfo/update")]
    [DisplayName("更新文件")]
    public async Task UpdateFile(FileInput input)
    {
        bool isExist = await _sysFileRep.IsAnyAsync(u => u.Id == input.Id);
        if (!isExist)
        {
            throw Oops.Oh(ErrorCode.D8000);
        }

        await _sysFileRep.UpdateAsync(u => new SysFile { FileName = input.FileName }, u => u.Id == input.Id);
    }

    /// <summary>
    ///     获取文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<SysFile> GetFile([FromQuery] FileInput input)
    {
        SysFile file = await _sysFileRep.GetFirstAsync(u => u.Id == input.Id);
        return file ?? throw Oops.Oh(ErrorCode.D8000);
    }

    /// <summary>
    ///     上传文件
    /// </summary>
    /// <param name="file">文件</param>
    /// <param name="savePath">路径</param>
    /// <returns></returns>
    private async Task<SysFile> HandleUploadFile(IFormFile file, string savePath)
    {
        if (file == null)
        {
            throw Oops.Oh(ErrorCode.D8000);
        }

        // 是否重复上传的文件
        long sizeKb = (long)(file.Length / 1024.0); // 大小KB
        string fileMd5 = string.Empty;
        string path = savePath;
        if (string.IsNullOrWhiteSpace(savePath))
        {
            path = _uploadOptions.Path;
            Regex reg = new(@"(\{.+?})");
            MatchCollection match = reg.Matches(path);
            match.ToList().ForEach(a =>
            {
                string str = DateTime.Now.ToString(a.ToString().Substring(1, a.Length - 2)); // 每天一个目录
                path = path.Replace(a.ToString(), str);
            });
        }
        //
        // if (!_uploadOptions.ContentType.Contains(file.ContentType))
        // {
        //     throw Oops.Oh(ErrorCode.D8001);
        // }

        string suffix = Path.GetExtension(file.FileName).ToLower(); // 后缀
        if (string.IsNullOrWhiteSpace(suffix))
        {
            FileExtensionContentTypeProvider contentTypeProvider = FS.GetFileExtensionContentTypeProvider();
            suffix = contentTypeProvider.Mappings.FirstOrDefault(u => u.Value == file.ContentType).Key;
            // 修改 image/jpeg 类型返回的 .jpe 后缀
            if (suffix == ".jpe")
            {
                suffix = ".jpg";
            }
        }

        if (string.IsNullOrWhiteSpace(suffix))
        {
            throw Oops.Oh(ErrorCode.D8003);
        }

        SysFile newFile = new()
        {
            Id = YitIdHelper.NextId(),
            // BucketName = _OSSProviderOptions.IsEnable ? _OSSProviderOptions.Provider.ToString() : "Local",
            // 阿里云对bucket名称有要求，1.只能包括小写字母，数字，短横线（-）2.必须以小写字母或者数字开头  3.长度必须在3-63字节之间
            // 无法使用Provider
            BucketName = "Local",
            FileName = Path.GetFileNameWithoutExtension(file.FileName),
            Suffix = suffix,
            SizeKb = sizeKb.ToString(),
            FilePath = path,
            FileMd5 = fileMd5
        };

        string finalName = newFile.Id + suffix; // 文件最终名称
        newFile.Provider = ""; // 本地存储 Provider 显示为空
        string filePath = path;
        if (!Directory.Exists(filePath))
        {
            Directory.CreateDirectory(filePath);
        }

        string realFile = Path.Combine(filePath, finalName);
        //IDetector detector;
        using (FileStream stream = System.IO.File.Create(realFile))
        {
            await file.CopyToAsync(stream);
            //detector = stream.DetectFiletype();
        }

        return newFile;
    }

    /// <summary>
    ///     上传头像
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    [HttpPost("/sysFileInfo/uploadAvatar")]
    public async Task<string> UploadAvatar(IFormFile file)
    {
        FileOutput res = await UploadFile(file, "Upload/Avatar");
        string[] end = file.FileName.Split('.');
        return res.Id + "." + end[^1];
    }

    #endregion

    #region v2

    /// <summary>
    ///     获取下载文件链接.
    /// </summary>
    /// <param name="type">图片类型.</param>
    /// <param name="fileName">文件名称.</param>
    /// <returns></returns>
    [HttpGet("/file/Download/{type}/{fileName}")]
    [AllowAnonymous]
    public dynamic DownloadUrl(string type, string fileName)
    {
        string? url = string.Format("{0}|{1}|{2}", _userManager.UserId, fileName, type);
        string? encryptStr = DESEncryption.Encrypt(url, "JNPF");
        _cacheManager.Set(fileName, string.Empty);
        return new { name = fileName, url = string.Format("/api/file/Download?encryption={0}", encryptStr) };
    }

    /// <summary>
    ///     下载文件链接.
    /// </summary>
    [HttpGet("/file/Download")]
    [AllowAnonymous]
    public async Task<dynamic> DownloadFile([FromQuery] string encryption, [FromQuery] string name)
    {
        string decryptStr = DESEncryption.Decrypt(encryption, "JNPF");
        List<string> paramsList = decryptStr.Split("|").ToList();
        if (paramsList.Count > 0)
        {
            string fileName = paramsList.Count > 1 ? paramsList[1] : string.Empty;
            if (_cacheManager.ExistKey(fileName))
            {
                _cacheManager.Remove(fileName);
            }
            else
            {
                throw Oops.Oh(ErrorCode.D1805);
            }

            string type = paramsList.Count > 2 ? paramsList[2] : string.Empty;
            string filePath = Path.Combine(_fileManager.GetPathByType(type), fileName.Replace("@", "."));
            string fileDownloadName = name.IsNullOrEmpty() ? fileName : name;
            return await _fileManager.DownloadFileByType(filePath, fileDownloadName);
        }

        throw Oops.Oh(ErrorCode.D8000);
    }

    /// <summary>
    ///     分片上传获取.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("/file/chunk")]
    [AllowAnonymous]
    public async Task<dynamic> CheckChunk([FromQuery] ChunkModel input)
    {
        if (!AllowFileType(input.extension, input.extension))
        {
            throw Oops.Oh(ErrorCode.D1800);
        }

        string path = GetPathByType(string.Empty);
        string filePath = Path.Combine(path, input.identifier);
        List<FileInfo> chunkFiles = FileHelper.GetAllFiles(filePath);
        List<int> existsChunk = chunkFiles.FindAll(x => !FileHelper.GetFileType(x).Equals("tmp"))
            .Select(x => x.FullName.Replace(input.identifier + "-", string.Empty).ParseToInt()).ToList();
        return new { chunkNumbers = existsChunk, merge = false };
    }

    /// <summary>
    ///     分片上传附件.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/file/chunk")]
    [AllowAnonymous]
    public async Task<dynamic> UploadChunk([FromForm] ChunkModel input)
    {
        if (!AllowFileType(input.extension, input.extension))
        {
            throw Oops.Oh(ErrorCode.D1800);
        }

        return await _fileManager.UploadChunk(input);
    }

    /// <summary>
    ///     分片组装.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/file/merge")]
    [AllowAnonymous]
    public async Task<dynamic> Merge([FromForm] ChunkModel input)
    {
        return await _fileManager.Merge(input);
    }

    /// <summary>
    ///     上传文件/图片.
    /// </summary>
    /// <returns></returns>
    [HttpPost("/file/Uploader/{type}")]
    [AllowAnonymous]
    public async Task<dynamic> Uploader(string type, [FromForm] ChunkModel input)
    {
        string? fileType = Path.GetExtension(input.file.FileName).Replace(".", string.Empty);
        if (!AllowFileType(fileType, type))
        {
            throw Oops.Oh(ErrorCode.D1800);
        }

        string saveFileName = string.Format("{0}{1}{2}", DateTime.Now.ToString("yyyyMMdd"), new Random().NextLetterAndNumberString(5), Path.GetExtension(input.file.FileName));
        Stream stream = input.file.OpenReadStream();
        input.type = type;
        _fileManager.GetChunkModel(input, saveFileName);
        await _fileManager.UploadFileByType(stream, input.folder, saveFileName);
        if (AllowImageType(fileType) && type.Equals("annexpic"))
        {
            Stream slStram = await _fileManager.GetFileStream(Path.Combine(input.folder, saveFileName));
            await _fileManager.MakeThumbnail(slStram, saveFileName, input.folder);
            return new FileControlsModel
            {
                name = input.fileName, url = string.Format("/api/file/Image/{0}/{1}", type, input.fileName), thumbUrl = string.Format("/api/file/Image/{0}/{1}", type, input.slImgName),
                fileExtension = fileType, fileSize = input.file.Length, fileName = input.slImgName
            };
        }

        return new FileControlsModel
            { name = input.fileName, url = string.Format("/api/file/Image/{0}/{1}", type, input.fileName), fileExtension = fileType, fileSize = input.file.Length, fileName = input.fileName };
    }

    /// <summary>
    ///     生成图片链接.
    /// </summary>
    /// <param name="type">图片类型.</param>
    /// <param name="fileName">注意 后缀名前端故意把 .替换@ .</param>
    /// <returns></returns>
    [HttpGet("/file/Image/{type}/{fileName}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetImg(string type, string fileName)
    {
        string? filePath = Path.Combine(GetPathByType(type), fileName.Replace("@", "."));
        return await _fileManager.DownloadFileByType(filePath, fileName);
    }

    /// <summary>
    /// 生成大屏图片链接.
    /// </summary>
    /// <param name="type">图片类型.</param>
    /// <param name="fileName">注意 后缀名前端故意把 .替换@ .</param>
    /// <returns></returns>
    [HttpGet("/file/VisusalImg/BiVisualPath/{type}/{fileName}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetScreenImg(string type, string fileName)
    {
        string filePath = Path.Combine(GetPathByType(type), type, fileName.Replace("@", "."));
        return await _fileManager.DownloadFileByType(filePath, fileName);
    }
    
    /// <summary>
    ///     下载json.txt等文本内容值.
    /// </summary>
    /// <returns></returns>
    [HttpPost("/file/getTextContent/{type}")]
    public async Task<dynamic> GetTextContent(string type ,GetTextContentInput input)
    {
        string filePath = Path.Combine(GetPathByType(type), input.FileName.Replace("@", "."));
        filePath = filePath.Replace(@",", "/");
        // 传入字符串格式encoding 转成encoding
        if (input.Encoding == "auto")
        {
            return await System.IO.File.ReadAllTextAsync(filePath);
        }
        Encoding encoding = Encoding.GetEncoding(input.Encoding);
        return await System.IO.File.ReadAllTextAsync(filePath, encoding);
    }
    
    /// <summary>
    ///     保存json.txt等文本内容值.
    /// </summary>
    /// <returns></returns>
    [HttpPost("/file/saveTextContent/{type}")]
    public async Task SaveTextContent(string type ,SaveTextContentInput input)
    {
        string filePath = Path.Combine(GetPathByType(type), input.FileName.Replace("@", "."));
        filePath = filePath.Replace(",", "/");
        await System.IO.File.WriteAllTextAsync(filePath, input.Content);
    }
    
    /// <summary>
    ///     上传文件预览.
    /// </summary>
    /// <returns></returns>
    [HttpPost("/file/Uploader/Preview")]
    public async Task<dynamic> Preview([FromBody] FilePreviewInput input)
    {
        string[]? typeList = { "doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "jpg", "jpeg", "gif", "png", "bmp" };
        string? type = input.fileName.Split('.').LastOrDefault();
        if (typeList.Contains(type))
        {
            if (input.fileName.IsNotEmptyOrNull())
            {
                string previewUrl = string.Empty;
                previewUrl =_fileManager.KkFileUploaderPreview(input.fileName, input.fileDownloadUrl);
                return previewUrl;
            }
    
            throw Oops.Oh(ErrorCode.D8000);
        }
    
        throw Oops.Oh(ErrorCode.D1802);
    }

    /// <summary>
    /// 下载.
    /// </summary>
    /// <param name="fileName"></param>
    /// <param name="type"></param>
    [HttpGet("/file/down/{fileName}")]
    [AllowAnonymous]
    public async Task FileDown(string fileName, [FromQuery] string type)
    {
        string? systemFilePath = Path.Combine(FileVariable.SystemFilePath, fileName);
        if (type.IsNotEmptyOrNull())
        {
            systemFilePath = Path.Combine(_fileManager.GetPathByType(type), fileName);
        }
        var fileStreamResult = await _fileManager.DownloadFileByType(systemFilePath, fileName);
        byte[] bytes = new byte[fileStreamResult.FileStream.Length];

        await fileStreamResult.FileStream.ReadAsync(bytes, 0, bytes.Length);

        fileStreamResult.FileStream.Close();
        var httpContext = App.HttpContext;
        httpContext.Response.ContentType = "application/octet-stream";
        httpContext.Response.Headers.Add("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode(fileName, Encoding.UTF8));
        httpContext.Response.Headers.Add("Content-Length", bytes.Length.ToString());
        await httpContext.Response.Body.WriteAsync(bytes);
        await httpContext.Response.Body.FlushAsync();
        httpContext.Response.Body.Close();
    }
    
    #endregion

    #region PublicMethod

    #region 多种存储文件

    /// <summary>
    ///     根据存储类型上传文件.
    /// </summary>
    /// <param name="uploadFilePath">上传文件地址.</param>
    /// <param name="directoryPath">保存文件夹.</param>
    /// <param name="fileName">新文件名.</param>
    /// <returns></returns>
    [NonAction]
    public async Task UploadFileByType(string uploadFilePath, string directoryPath, string fileName)
    {
        FileStream? file = new(uploadFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
        await _fileManager.UploadFileByType(file, directoryPath, fileName);
    }

    #endregion

    /// <summary>
    ///     根据类型获取文件存储路径.
    /// </summary>
    /// <param name="type">文件类型.</param>
    /// <returns></returns>
    [NonAction]
    public string GetPathByType(string type)
    {
        return _fileManager.GetPathByType(type);
    }

    #endregion

    #region PrivateMethod

    /// <summary>
    ///     允许文件类型.
    /// </summary>
    /// <param name="fileExtension">文件后缀名.</param>
    /// <param name="type">文件类型.</param>
    /// <returns></returns>
    private bool AllowFileType(string fileExtension, string type)
    {
        List<string> allowExtension = KeyVariable.AllowUploadFileType.Distinct().ToList();
        if (fileExtension.IsNullOrEmpty() || type.IsNullOrEmpty())
        {
            return false;
        }

        foreach (object? item in Enum.GetValues(typeof(ExportFileType)))
        {
            allowExtension.Add(item.ToString());
        }

        return allowExtension.Any(a => a == fileExtension.ToLower());
    }

    /// <summary>
    ///     允许文件类型.
    /// </summary>
    /// <param name="fileExtension">文件后缀名.</param>
    /// <returns></returns>
    private bool AllowImageType(string fileExtension)
    {
        return KeyVariable.AllowImageType.Any(a => a == fileExtension.ToLower());
    }

    #endregion
}