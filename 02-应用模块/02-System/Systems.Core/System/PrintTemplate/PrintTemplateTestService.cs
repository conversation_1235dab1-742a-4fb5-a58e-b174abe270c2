using Systems.Entity;
using Systems.Entity.Dto;
using SqlSugar;

namespace Systems.Core.PrintTemplate;

/// <summary>
/// 打印模板测试服务
/// </summary>
[ApiDescriptionSettings("系统服务", Order = 501)]
public class PrintTemplateTestService : IDynamicApiController, ITransient
{
    private readonly PrintTemplateService _printTemplateService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="printTemplateService">打印模板服务</param>
    public PrintTemplateTestService(
        PrintTemplateService printTemplateService)
    {
        _printTemplateService = printTemplateService;
    }

    /// <summary>
    /// 获取测试模板
    /// </summary>
    /// <returns>测试模板</returns>
    [HttpGet("/printTemplate/test/template")]
    [DisplayName("获取测试模板")]
    public async Task<PrintTemplateDto> GetTestTemplate()
    {

        // 创建测试模板
        var input = new PrintTemplateInput
        {
            TemplateName = "测试模板",
            TemplateContent = @"^XA
~TA000
~JSN
^LT0
^MNW
^MTT
^PON
^PMN
^LH0,0
^JMA
^PR6,6
~SD15
^JUS
^LRN
^CI27
^PA0,1,1,0
^XZ
^XA
^MMT
^PW799
^LL237
^LS0
^FT416,84^A0N,28,28^FH\^CI28^FD{产品号}^FS^CI27
^FT416,128^A0N,28,28^FH\^CI28^FD{产品名称}^FS^CI27
^FT74,107^A0N,54,53^FH\^CI28^{编码}^FS^CI27
^FT16,201^A0N,39,33^FH\^CI28^{型号}^FS^CI27
^FT269,201^A0N,39,38^FH\^CI28^{批次}^FS^CI27
^FT499,194^A0N,34,33^FH\^CI28^FD ^FS^CI27
^FT712,67^A0N,28,28^FH\^CI28^{规格}^FS^CI27
^FT353,183^A0N,20,20^FH\^CI28^FD{产品说明}^FS^CI27
^FO673,75^BQN,2,5^FD{二维码内容}^FS
^PQ1,0,1,Y
^XZ",
            Description = "测试用打印模板"
        };

        await _printTemplateService.Add(input);

        // 重新获取刚创建的模板
        var template = await _printTemplateService.GetPage();

        return template.First();
    }
}