using Common.Models;
using IotPlatform.Core.Enum;
using Systems.Core.Config;
using Systems.Entity.Dto.Role;

namespace Systems.Core.Tenant;

/// <summary>
///     系统租户管理服务
/// </summary>
[ApiDescriptionSettings("系统服务",Order = 390)]
public class SysTenantService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SysTenant> _sysTenantRep;
    private readonly ISqlSugarRepository<SysOrg> _sysOrgRep;
    private readonly ISqlSugarRepository<SysRole> _sysRoleRep;
    private readonly ISqlSugarRepository<SysUser> _sysUserRep;
    private readonly ISqlSugarRepository<SysRoleMenu> _sysRoleMenuRep;
    private readonly ISqlSugarRepository<SysUserRole> _userRoleRep;
    private readonly SysUserRoleService _sysUserRoleService;
    private readonly SysRoleMenuService _sysRoleMenuService;
    private readonly SysConfigService _sysConfigService;
    private readonly IUserManager _userManager;

    public SysTenantService(ISqlSugarRepository<SysTenant> sysTenantRep,
        ISqlSugarRepository<SysOrg> sysOrgRep,
        ISqlSugarRepository<SysRole> sysRoleRep,
        ISqlSugarRepository<SysUser> sysUserRep,
        ISqlSugarRepository<SysRoleMenu> sysRoleMenuRep,
        ISqlSugarRepository<SysUserRole> userRoleRep,
        SysUserRoleService sysUserRoleService,
        SysRoleMenuService sysRoleMenuService,
        SysConfigService sysConfigService, IUserManager userManager)
    {
        _sysTenantRep = sysTenantRep;
        _sysOrgRep = sysOrgRep;
        _sysRoleRep = sysRoleRep;
        _sysUserRep = sysUserRep;
        _sysRoleMenuRep = sysRoleMenuRep;
        _userRoleRep = userRoleRep;
        _sysUserRoleService = sysUserRoleService;
        _sysRoleMenuService = sysRoleMenuService;
        _sysConfigService = sysConfigService;
        _userManager = userManager;
    }

    /// <summary>
    ///     获取租户分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/tenant/page")]
    public async Task<SqlSugarPagedList<TenantOutput>> Page([FromQuery] PageTenantInput input)
    {
        return await _sysTenantRep.AsQueryable()
            .WhereIF(_userManager.IsAdministrator == false, w => w.Id == _userManager.TenantId)
            .LeftJoin<SysUser>((u, a) => u.UserId == a.Id)
            .LeftJoin<SysOrg>((u, a, b) => u.OrgId == b.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), (u, a) => a.Phone.Contains(input.Phone.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), (u, a, b) => b.Name.Contains(input.Name.Trim()))
            .OrderBy(u => u.OrderNo)
            .Select((u, a, b) => new TenantOutput
            {
                Id = u.Id,
                OrgId = b.Id,
                Name = b.Name,
                UserId = a.Id,
                AdminAccount = a.Account,
                Phone = a.Phone,
                TenantType = u.TenantType,
                DbType = u.DbType,
                Connection = u.Connection,
                ConfigId = u.ConfigId,
                OrderNo = u.OrderNo,
                Remark = u.Remark,
                Status = u.Status
            })
            .ToPagedListAsync(input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     获取库隔离的租户列表
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task<List<SysTenant>> GetTenantDbList()
    {
        return await _sysTenantRep.GetListAsync(u => u.TenantType == TenantTypeEnum.Db && u.Status == true);
    }

    /// <summary>
    ///     增加租户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/tenant/add")]
    public async Task AddTenant(AddTenantInput input)
    {
        bool isExist = await _sysOrgRep.IsAnyAsync(u => u.Name == input.Name);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D1300);
        }

        isExist = await _sysUserRep.AsQueryable().ClearFilter().AnyAsync(u => u.Account == input.AdminAccount);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D1301);
        }

        TenantOutput tenant = input.Adapt<TenantOutput>();

        // ID隔离时设置与主库一致
        if (tenant.TenantType == TenantTypeEnum.Id)
        {
            ConnectionConfig config = _sysTenantRep.AsSugarClient().CurrentConnectionConfig;
            tenant.DbType = config.DbType;
            tenant.Connection = config.ConnectionString;
        }

        await InitNewTenant(tenant);
        await _sysTenantRep.InsertAsync(tenant);

        await CacheTenant();
    }

    /// <summary>
    ///     设置租户状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/tenant/enable")]
    public async Task<int> SetStatus(TenantInput input)
    {
        SysTenant tenant = await _sysTenantRep.GetFirstAsync(u => u.Id == input.Id);
        if (tenant == null || tenant.ConfigId == SqlSugarConst.MainConfigId)
        {
            throw Oops.Oh(ErrorCode.Z1001);
        }

        tenant.Status = input.Status;
        return await _sysTenantRep.AsUpdateable(tenant).UpdateColumns(u => new {u.Status}).ExecuteCommandAsync();
    }

    /// <summary>
    ///     新增租户初始化
    /// </summary>
    /// <param name="tenant"></param>
    private async Task InitNewTenant(TenantOutput tenant)
    {
        long tenantId = YitIdHelper.NextId();
        string tenantName = tenant.Name;
        long orgId = YitIdHelper.NextId();
        long userId = YitIdHelper.NextId();
        tenant.OrgId = orgId;
        tenant.Id = tenantId;
        tenant.UserId = userId;
        // 初始化机构
        SysOrg newOrg = new()
        {
            Id = orgId,
            TenantId = tenantId,
            Pid = 0,
            OrganizeIdTree = "0",
            Name = tenantName,
            Code = tenantName,
            Category = "company",
            Remark = tenantName
        };
        await _sysOrgRep.InsertAsync(newOrg);

        // 初始化角色
        SysRole newRole = new()
        {
            TenantId = tenantId,
            Name = "租管-" + tenantName,
            Code = CommonConst.SysAdminRole,
            DataScope = DataScopeEnum.All,
            Remark = tenantName
        };
        await _sysRoleRep.InsertAsync(newRole);

        // 初始化系统账号
        string? password = await _sysConfigService.GetConfigValue<string>(CommonConst.SysPassword);
        SysUser newUser = new()
        {
            Id = userId,
            TenantId = tenantId,
            Account = tenant.AdminAccount,
            Password = MD5Encryption.Encrypt(password),
            NickName = tenant.AdminName ?? "租管",
            Phone = tenant.Phone,
            AdminType = AdminTypeEnum.Admin,
            OrgId = newOrg.Id,
            Name = tenant.AdminName ?? "租管",
            Remark = tenant.AdminName ?? "租管" + tenantName
        };
        await _sysUserRep.InsertAsync(newUser);

        // 关联用户及角色
        SysUserRole newUserRole = new()
        {
            SysRoleId = newRole.Id,
            SysUserId = newUser.Id
        };
        await _userRoleRep.InsertAsync(newUserRole);

        // 默认租户管理员角色菜单集合
        List<long> menuIdList = new();
        await _sysRoleMenuService.GrantRoleMenu(new RoleMenuInput {Id = newRole.Id, GrantMenuIdList = menuIdList});
    }

    /// <summary>
    ///     删除租户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/tenant/delete")]
    public async Task DeleteTenant(BaseId input)
    {
        // 禁止删除默认租户
        if (input.Id.ToString() == SqlSugarConst.MainConfigId)
        {
            throw Oops.Oh(ErrorCode.D1023);
        }

        await _sysTenantRep.DeleteAsync(u => u.Id == input.Id);

        await CacheTenant(input.Id);

        // 删除与租户相关的表数据
        List<SysUser> users = await _sysUserRep.AsQueryable().ClearFilter().Where(u => u.TenantId == input.Id).ToListAsync();
        List<long> userIds = users.Select(u => u.Id).ToList();
        await _sysUserRep.AsDeleteable().Where(u => userIds.Contains(u.Id)).ExecuteCommandAsync();

        await _userRoleRep.AsDeleteable().Where(u => userIds.Contains(u.SysUserId)).ExecuteCommandAsync();

        await _sysRoleRep.AsDeleteable().Where(u => u.TenantId == input.Id).ExecuteCommandAsync();

        List<long> roleIds = await _sysRoleRep.AsQueryable().ClearFilter()
            .Where(u => u.TenantId == input.Id).Select(u => u.Id).ToListAsync();
        await _sysRoleMenuRep.AsDeleteable().Where(u => roleIds.Contains(u.RoleId)).ExecuteCommandAsync();

        await _sysOrgRep.AsDeleteable().Where(u => u.TenantId == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    ///     更新租户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/tenant/update")]
    public async Task UpdateTenant(UpdateTenantInput input)
    {
        bool isExist = await _sysOrgRep.IsAnyAsync(u => u.Name == input.Name && u.Id != input.OrgId);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D1300);
        }

        isExist = await _sysUserRep.IsAnyAsync(u => u.Account == input.AdminAccount && u.Id != input.UserId);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D1301);
        }

        await _sysTenantRep.AsUpdateable(input.Adapt<TenantOutput>()).IgnoreColumns(true).ExecuteCommandAsync();

        // 更新系统机构
        await _sysOrgRep.UpdateAsync(u => new SysOrg {Name = input.Name}, u => u.Id == input.OrgId);

        // 更新系统用户
        await _sysUserRep.UpdateAsync(u => new SysUser {Account = input.AdminAccount, Phone = input.Phone}, u => u.Id == input.UserId);

        await CacheTenant(input.Id);
    }

    /// <summary>
    ///     授权租户管理员角色菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/tenant/grantMenu")]
    public async Task GrantMenu(RoleMenuInput input)
    {
        SysUser tenantAdminUser = await _sysUserRep.GetFirstAsync(u => u.TenantId == input.Id && u.AdminType == AdminTypeEnum.Admin);
        if (tenantAdminUser == null)
        {
            return;
        }

        List<long> roleIds = await _sysUserRoleService.GetUserRoleIdList(tenantAdminUser.Id);
        input.Id = roleIds[0]; // 重置租户管理员角色Id
        await _sysRoleMenuService.GrantRoleMenu(input);
    }

    /// <summary>
    ///     获取租户管理员角色拥有菜单Id集合
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/tenant/getOwnMenuList")]
    public async Task<List<long>> GetOwnMenuList([FromQuery] TenantUserInput input)
    {
        List<long> roleIds = await _sysUserRoleService.GetUserRoleIdList(input.UserId);
        return await _sysRoleMenuService.GetRoleMenuIdList(new List<long> {roleIds[0]});
    }

    /// <summary>
    ///     重置租户管理员密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("重置租户管理员密码")]
    public async Task<string?> ResetPwd(TenantUserInput input)
    {
        string? password = await _sysConfigService.GetConfigValue<string>(CommonConst.SysPassword);
        string encryptPassword = MD5Encryption.Encrypt(password);
        await _sysUserRep.UpdateAsync(u => new SysUser {Password = encryptPassword}, u => u.Id == input.UserId);
        return password;
    }

    /// <summary>
    ///     缓存所有租户
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    [NonAction]
    public async Task CacheTenant(long tenantId = 0)
    {
        // 移除 ISqlSugarClient 中的库连接并排除默认主库
        if (tenantId > 0 && tenantId.ToString() != SqlSugarConst.MainConfigId)
        {
            _sysTenantRep.AsTenant().RemoveConnection(tenantId);
        }

        List<SysTenant> tenantList = await _sysTenantRep.GetListAsync();
        // 对租户库连接进行SM2加密
        foreach (SysTenant tenant in tenantList)
        {
            tenant.Connection = tenant.Connection;
        }
    }
}