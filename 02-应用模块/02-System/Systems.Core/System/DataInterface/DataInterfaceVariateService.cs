using Common.Core.Manager.Files;
using Common.Models;
using Common.Security;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Systems.Entity.Dto.DataInterfaceVariate;
using DateTime = System.DateTime;

namespace Systems.Core;

/// <summary>
///     数据接口变量
/// </summary>
[ApiDescriptionSettings("数据应用")]
public class DataInterfaceVariateService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<DataInterfaceVariateEntity> _repository;
    private readonly IFileManager _fileManager;
    private readonly IUserManager _userManager;

    public DataInterfaceVariateService(ISqlSugarRepository<DataInterfaceVariateEntity> repository,
        IFileManager fileManager, IUserManager userManager)
    {
        _repository = repository;
        _fileManager = fileManager;
        _userManager = userManager;
    }

    #region Get

    /// <summary>
    ///     列表.
    /// </summary>
    /// <param name="interfaceId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/dataInterfaceVariate/{interfaceId}")]
    public async Task<dynamic> GetList(long interfaceId, [FromQuery] KeywordInput input)
    {
        List<DataInterfaceVariateOutput>? list = await _repository.AsSugarClient()
            .Queryable<DataInterfaceVariateEntity>()
            .Where(a => a.InterfaceId == interfaceId)
            .WhereIF(input.keyword.IsNotEmptyOrNull(), a => a.FullName.Contains(input.keyword))
            .OrderBy(a => a.SortCode).OrderByDescending(a => a.CreatedTime).OrderByIF(input.keyword.IsNotEmptyOrNull(),
                a => a.UpdatedTime, OrderByType.Desc)
            .Select(a => new DataInterfaceVariateOutput
            {
                id = a.Id,
                interfaceId = a.InterfaceId,
                fullName = a.FullName,
                value = a.Value,
                creatorUser = SqlFunc.Subqueryable<SysUser>().Where(u => u.Id == a.CreatedUserId)
                    .Select(u => u.Name),
                creatorTime = a.CreatedTime,
                lastModifyTime = a.UpdatedTime
            }).ToListAsync();
        return new { list };
    }

    /// <summary>
    ///     下拉列表.
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dataInterfaceVariate/Selector")]
    public async Task<dynamic> GetSelector()
    {
        List<DataInterfaceVariateTreeOutput> output = new();
        List<DataInterfaceVariateTreeOutput>? dataInterfaceList = await _repository.AsSugarClient()
            .Queryable<DataInterfaceEntity>()
            .Where(x => x.IsPostposition == 1 && x.EnabledMark == 1)
            .Select(x => new DataInterfaceVariateTreeOutput
            {
                id = x.Id.ToString(),
                fullName = x.FullName,
                type = 0,
                parentId = "0"
            }).ToListAsync();
        List<DataInterfaceVariateTreeOutput>? dataInterfaceVariateList = await _repository.AsQueryable()
            .Select(x => new DataInterfaceVariateTreeOutput
            {
                id = x.Id.ToString(),
                fullName = x.FullName,
                type = 1,
                parentId = x.InterfaceId.ToString()
            }).ToListAsync();
        output = dataInterfaceList.Union(dataInterfaceVariateList).ToList().ToTree();
        return output;
    }

    /// <summary>
    ///     详情.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("/dataInterfaceVariate/{id}/Info")]
    public async Task<dynamic> Info(long id)
    {
        return (await _repository.GetFirstAsync(x => x.Id == id)).Adapt<DataInterfaceVariateInput>();
    }

    /// <summary>
    ///     导出.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("/dataInterfaceVariate/{id}/Actions/Export")]
    public async Task<dynamic> ActionsExport(long id)
    {
        DataInterfaceVariateEntity? entity = await _repository.GetFirstAsync(x => x.Id == id);
        if (entity == null)
        {
            throw Oops.Oh("数据接口变量已经被删除！");
        }

        return entity;
        // return await _fileManager.Export(jsonStr, entity.FullName, ExportFileType.ffa);
    }

    #endregion

    #region Post

    /// <summary>
    ///     添加接口.
    /// </summary>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("/dataInterfaceVariate/add")]
    public async Task Create([FromBody] DataInterfaceVariateInput input)
    {
        if (input.fullName.Contains("@"))
        {
            throw Oops.Oh("变量名不能包含敏感字符");
        }

        if (await _repository.IsAnyAsync(x => x.FullName == input.fullName))
        {
            throw Oops.Oh("变量名已存在");
        }

        DataInterfaceVariateEntity entity = input.Adapt<DataInterfaceVariateEntity>();
        int isOk = await _repository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh("新增数据失败");
        }
    }

    /// <summary>
    ///     修改接口.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("/dataInterfaceVariate/{id}/update")]
    public async Task Update(long id, [FromBody] DataInterfaceVariateInput input)
    {
        if (input.fullName.Contains("@"))
        {
            throw Oops.Oh("变量名不能包含敏感字符");
        }

        if (await _repository.IsAnyAsync(x => x.Id != id && x.FullName == input.fullName))
        {
            throw Oops.Oh("变量名已存在");
        }

        DataInterfaceVariateEntity entity = input.Adapt<DataInterfaceVariateEntity>();
        bool isOk = await _repository.AsUpdateable(entity).IgnoreColumns(true).ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh("修改数据失败");
        }
    }

    /// <summary>
    ///     删除接口.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpPost("/dataInterfaceVariate/{id}/delete")]
    public async Task Delete(long id)
    {
        DataInterfaceVariateEntity? entity = await _repository.GetFirstAsync(x => x.Id == id);
        if (entity == null)
        {
            throw Oops.Oh("检测数据不存在");
        }

        bool isOk = await _repository.DeleteAsync(entity);
        if (!isOk)
        {
            throw Oops.Oh("删除数据失败");
        }
    }

    /// <summary>
    ///     导入.
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    [HttpPost("/dataInterfaceVariate/Actions/Import")]
    public async Task ActionsImport(IFormFile file)
    {
        string fileType = Path.GetExtension(file.FileName).Replace(".", string.Empty);
        if (!fileType.ToLower().Equals(ExportFileType.json.ToString()))
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        string josn = _fileManager.Import(file);
        DataInterfaceVariateEntity? data = josn.ToObjectOld<DataInterfaceVariateEntity>();
        if (data == null)
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        data.CreatedTime = DateTime.Now;
        data.CreatedUserId = _userManager.UserId;
        data.UpdatedUserId = null;
        data.UpdatedTime = null;
        int isOk = await _repository.AsSugarClient().Storageable(data).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh(ErrorCode.D3008);
        }
    }

    /// <summary>
    ///     复制.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpPost("/dataInterfaceVariate/{id}/Actions/Copy")]
    public async Task ActionsCopy(long id)
    {
        DataInterfaceVariateEntity? entity = await _repository.GetFirstAsync(x => x.Id == id);
        if (entity == null)
        {
            throw Oops.Oh("检测数据不存在");
        }

        string random = new Random().NextLetterAndNumberString(5).ToLower();
        entity.FullName = string.Format("{0}.副本{1}", entity.FullName, random);
        entity.UpdatedTime = null;
        entity.UpdatedUserId = null;
        entity.Id = YitIdHelper.NextId();
        if (entity.FullName.Length >= 50)
        {
            throw Oops.Oh("已到达该模板复制上限，请复制源模板");
        }

        int isOk = await _repository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh("新增数据失败");
        }
    }

    #endregion
}