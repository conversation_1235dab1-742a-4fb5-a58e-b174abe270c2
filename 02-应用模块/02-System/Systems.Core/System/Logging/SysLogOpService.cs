namespace Systems.Core;

/// <summary>
///     系统操作日志服务
/// </summary>
[ApiDescriptionSettings("系统服务",Order = 360)]
public class SysLogOpService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SysLogOp> _sysLogOpRep;

    public SysLogOpService(ISqlSugarRepository<SysLogOp> sysLogOpRep)
    {
        _sysLogOpRep = sysLogOpRep;
    }

    /// <summary>
    ///     获取操作日志分页列表
    /// </summary>
    /// <returns></returns>
    [SuppressMonitor]
    [HttpGet("/sysOplog/page")]
    [DisplayName("获取操作日志分页列表")]
    public async Task<SqlSugarPagedList<SysLogOp>> Page([FromQuery] PageLogInput input)
    {
        return await _sysLogOpRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchBeginTime), u => u.CreatedTime >= Convert.ToDateTime(input.SearchBeginTime))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchEndTime), u => u.CreatedTime <= Convert.ToDateTime(input.SearchEndTime))
            .OrderBy(u => u.CreatedTime, OrderByType.Desc)
            .ToPagedListAsync(input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     清空操作日志
    /// </summary>
    /// <returns></returns>
    [HttpPost("/sysOplog/delete")]
    [DisplayName("清空操作日志")]
    public async Task<bool> Clear()
    {
        return await _sysLogOpRep.DeleteAsync(u => u.Id > 0);
    }

    /// <summary>
    ///     导出操作日志
    /// </summary>
    /// <returns></returns>
    [NonUnify]
    [HttpPost("/sysOplog/export")]
    [DisplayName("导出操作日志")]
    public async Task<IActionResult> ExportLogOp(LogInput input)
    {
        List<ExportLogDto> logOpList = await _sysLogOpRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchBeginTime) && !string.IsNullOrWhiteSpace(input.SearchEndTime),
                u => u.CreatedTime >= Convert.ToDateTime(input.SearchBeginTime) && u.CreatedTime <= Convert.ToDateTime(input.SearchEndTime))
            .OrderBy(u => u.CreatedTime, OrderByType.Desc)
            .Select<ExportLogDto>().ToListAsync();

        IExcelExporter excelExporter = new ExcelExporter();
        byte[] res = await excelExporter.ExportAsByteArray(logOpList);
        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") {FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + "操作日志.xlsx"};
    }
}