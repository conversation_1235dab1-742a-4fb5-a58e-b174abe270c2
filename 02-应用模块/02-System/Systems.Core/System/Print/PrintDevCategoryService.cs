namespace Systems.Core;

/// <summary>
///     打印模板分类
/// </summary>
[ApiDescriptionSettings("应用开发")]
public class PrintDevCategoryService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<PrintDevCategory> _repository;
    private readonly IUserManager _userManager;

    /// <summary>
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="userManager"></param>
    public PrintDevCategoryService(ISqlSugarRepository<PrintDevCategory> repository, IUserManager userManager)
    {
        _repository = repository;
        _userManager = userManager;
    }

    /// <summary>
    ///     获取打印模板列表树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/printDevCategory/tree")]
    public async Task<dynamic> GetSelector()
    {
        return await _repository.AsQueryable()
            .ToTreeAsync(u => u.Children, u => u.Pid, 0);
    }

    /// <summary>
    ///     添加打印模板分类
    /// </summary>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("/printDevCategory/add")]
    public async Task Create([FromBody] PrintDevCategoryAddInput input)
    {
        if (await _repository.IsAnyAsync(x => x.Name == input.Name && x.TenantId == _userManager.TenantId))
        {
            throw Oops.Oh("分类名称已存在！");
        }

        PrintDevCategory entity = input.Adapt<PrintDevCategory>();
        int isOk = await _repository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh("新增数据失败");
        }
    }

    /// <summary>
    ///     修改打印模板分类
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("/printDevCategory/{id}/update")]
    public async Task Update(long id, [FromBody] PrintDevCategoryUpdateInput input)
    {
        if (await _repository.IsAnyAsync(x => x.Name == input.Name
                                              && x.Id == input.Id && x.TenantId == _userManager.TenantId))
        {
            throw Oops.Oh("分类名称已存在！");
        }

        PrintDevCategory entity = input.Adapt<PrintDevCategory>();
        bool isOk = await _repository.AsUpdateable(entity).IgnoreColumns(true).ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh("修改数据失败");
        }
    }

    /// <summary>
    ///     删除打印模板分类
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpPost("/printDevCategory/{id}/delete")]
    public async Task Delete(long id)
    {
        bool isOk = await _repository.AsDeleteable()
            .Where(it => it.Id == id)
            .ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh("删除数据失败");
        }
    }
}