using Common.Core.Manager.DataBase;
using Common.Core.Manager.Files;
using Common.Security;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Systems.Entity.Model.System;
using DateTime = System.DateTime;

namespace Systems.Core;

/// <summary>
/// 打印模板配置
/// </summary>
[ApiDescriptionSettings( "应用开发", Order = 200)]
public class PrintDevService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<PrintDevEntity> _repository;

    /// <summary>
    /// 数据连接服务.
    /// </summary>
    private readonly DbLinkService _dbLinkService;

    /// <summary>
    /// 文件服务.
    /// </summary>
    private readonly IFileManager _fileManager;

    /// <summary>
    /// 用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    /// 数据库管理.
    /// </summary>
    private readonly IDataBaseManager _dataBaseManager;

    /// <summary>
    /// 数据接口.
    /// </summary>
    private readonly DataInterfaceService _dataInterfaceService;

    /// <summary>
    /// 数据集.
    /// </summary>
    private readonly DataSetService _dataSetService;

    /// <summary>
    /// 初始化一个<see cref="PrintDevService"/>类型的新实例.
    /// </summary>
    public PrintDevService(
        ISqlSugarRepository<PrintDevEntity> repository,
        IFileManager fileManager,
        IDataBaseManager dataBaseManager,
        IUserManager userManager,
        DbLinkService dbLinkService,
        DataInterfaceService dataInterfaceService,
        DataSetService dataSetService)
    {
        _repository = repository;
        _dbLinkService = dbLinkService;
        _fileManager = fileManager;
        _dataBaseManager = dataBaseManager;
        _userManager = userManager;
        _dataInterfaceService = dataInterfaceService;
        _dataSetService = dataSetService;
    }

    #region Get

    /// <summary>
    /// 列表(分页).
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("/printDev/page")]
    public async Task<dynamic> GetList([FromQuery] PrintDevListInput input)
    {
        var list = await _repository.AsQueryable()
            .Where(it => it.DeleteMark == null)
            .WhereIF(input.category > 0, it => it.Category == input.category)
            .WhereIF(input.state.IsNotEmptyOrNull(), it => it.State == input.state)
            .WhereIF(input.keyword.IsNotEmptyOrNull(), it => it.FullName.Contains(input.keyword) || it.EnCode.Contains(input.keyword))
            .OrderBy(it => it.SortCode).OrderBy(it => it.CreatedTime, OrderByType.Desc)
            .Select(it => new PrintDevListOutput
            {
                id = it.Id,
                fullName = it.FullName,
                enCode = it.EnCode,
                category = SqlFunc.Subqueryable<PrintDevCategory>().EnableTableFilter().Where(x => x.Id == it.Category).Select(x => x.Name),
                state = it.State,
                sortCode = it.SortCode,
                creatorTime = it.CreatedTime,
                creatorUser = SqlFunc.Subqueryable<SysUser>().EnableTableFilter().Where(x => x.Id == it.CreatedUserId).Select(x => x.Name),
                lastModifyTime = it.UpdatedTime,
                lastModifyUser = SqlFunc.Subqueryable<SysUser>().EnableTableFilter().Where(x =>  x.Id == it.UpdatedUserId).Select(x => x.Name),
            }).ToPagedListAsync(input.currentPage, input.pageSize);
        return list;
    }

    /// <summary>
    /// 列表.
    /// </summary>
    /// <returns></returns>
    [HttpGet("/printDev/Selector")]
    public async Task<dynamic> GetList()
    {
        var list = await _repository.AsSugarClient().Queryable<PrintDevEntity, SysUser, SysUser, PrintDevCategory>((a, b, c, d) => new JoinQueryInfos(JoinType.Left, b.Id == a.CreatedUserId, JoinType.Left, c.Id == a.UpdatedUserId, JoinType.Left, a.Category == d.Id))
            .Where((a, b, c, d) => a.DeleteMark == null && a.State == 1).OrderBy(a => a.SortCode).OrderBy(a => a.CreatedTime, OrderByType.Desc)
            .Select((a, b, c, d) => new PrintDevListTreeOutput
            {
                category = a.Category,
                id = a.Id.ToString(),
                fullName = a.FullName,
                creatorTime = a.CreatedTime,
                creatorUser = b.Name,
                enCode = a.EnCode,
                lastModifyTime = a.UpdatedTime,
                lastModifyUser = c.Name,
                sortCode = a.SortCode,
                parentId = d.Id.ToString(),
            }).ToListAsync();

        // 数据库分类
        var dbTypeList = await _repository.AsSugarClient().Queryable<PrintDevCategory>().ToListAsync();
        var result = new List<PrintDevListTreeOutput>();
        foreach (var item in dbTypeList)
        {
            var index = list.FindAll(x => x.category == item.Id).Count;
            if (index > 0)
            {
                result.Add(new PrintDevListTreeOutput()
                {
                    id = item.Id.ToString(),
                    parentId = "0",
                    fullName = item.Name,
                    num = index
                });
            }
        }

        return new { list = result.OrderBy(x => x.sortCode).Union(list).ToList().ToTree() };
    }

    /// <summary>
    /// 信息.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpGet("/printDev/{id}")]
    public async Task<dynamic> GetInfo(long id)
    {
        return (await GetEntityInfo(id)).Adapt<PrintDevInfoOutput>();
    }

    /// <summary>
    /// 导出.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpGet("/printDev/{id}/Actions/Export")]
    public async Task<dynamic> ActionsExport(long id)
    {
        var entity = await GetEntityInfo(id);
        var version = await GetVersionEntityInfo(entity.Id);
        var dataSetList = await GetDataSetList(version.Id);

        var importModel = version.Adapt<PrintVersionInfoOutput>();
        importModel.id = entity.Id;
        importModel.fullName = entity.FullName;
        importModel.enCode = entity.EnCode;
        importModel.category = entity.Category;
        importModel.description = entity.Description;
        importModel.sortCode = entity.SortCode;
        importModel.versionId = version.Id;
        importModel.dataSetList = dataSetList.Adapt<List<PrintDevDataSetModel>>();

        return importModel;
    }

    /// <summary>
    /// 版本列表.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("/printDev/Version/{id}")]
    public async Task<dynamic> GetVersionList(long id)
    {
        return await _repository.AsSugarClient().Queryable<PrintVersionEntity>()
            .Where(it =>  it.TemplateId == id)
            .OrderBy(it => it.SortCode, OrderByType.Desc).OrderBy(it => it.State).OrderBy(it => it.CreatedTime, OrderByType.Desc)
            .Select(it => new PrintVersionListOutput
            {
                id = it.Id,
                templateId = it.TemplateId,
                fullName = SqlFunc.MergeString("打印版本V", it.Version.ToString()),
                version = it.Version,
                state = it.State,
                printTemplate = it.PrintTemplate,
            })
            .ToListAsync();
    }

    /// <summary>
    /// 版本详情.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("/printDev/Info/{id}")]
    public async Task<dynamic> GetVersionInfo(long id)
    {
        var data = await _repository.AsSugarClient().Queryable<PrintVersionEntity>()
            .Where(it =>  it.Id == id)
            .Select(it => new PrintVersionInfoOutput
            {
                id = it.TemplateId,
                versionId = it.Id,
                version = it.Version,
                state = it.State,
                printTemplate = it.PrintTemplate,
                convertConfig = it.ConvertConfig
            })
            .FirstAsync();
        data.dataSetList = await _repository.AsSugarClient().Queryable<DataSetEntity>()
            .Where(it => it.ObjectId == id)
            .OrderBy(it => it.CreatedTime, OrderByType.Desc)
            .Select(it => new PrintDevDataSetModel
            {
                id = it.Id,
                fullName = it.FullName,
                dataConfigJson = it.DataConfigJson,
                dbLinkId = it.DbLinkId,
                fieldJson = it.FieldJson,
                parameterJson = it.ParameterJson
            })
            .ToListAsync();

        foreach (var item in data.dataSetList)
        {
            var parameter = new List<SugarParameter>()
            {
                new SugarParameter("@formId", null)
            };
            item.children = await _dataSetService.GetFieldModels(item.dbLinkId, item.dataConfigJson, parameter,item.fullName);
        }

        return data;
    }

    #endregion

    #region Post

    /// <summary>
    /// 新建.
    /// </summary>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPost("/printDev/add")]
    public async Task<dynamic> Create([FromBody] PrintDevCrInput input)
    {
        if (await _repository.IsAnyAsync(x => (x.EnCode == input.enCode || x.FullName == input.fullName) && x.DeleteMark == null))
            throw Oops.Oh(ErrorCode.Com1004);

        var entity = input.Adapt<PrintDevEntity>();
        entity.Id = YitIdHelper.NextId();
        entity.CreatedTime = DateTime.Now;
        entity.CreatedUserId = _userManager.UserId;
        var isOk = await _repository.AsInsertable(entity).ExecuteCommandAsync();
        if (isOk < 1) throw Oops.Oh(ErrorCode.COM1000);

        var versionEntity = new PrintVersionEntity()
        {
            TemplateId = entity.Id,
            Version = 1,
        };
        await _repository.AsSugarClient().Insertable(versionEntity).IgnoreColumns(ignoreNullColumn: true).ExecuteCommandAsync();

        return entity.Id;
    }

    /// <summary>
    /// 删除.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpPost("/printDev/{id}/delete")]
    public async Task Delete(long id)
    {
        if (!await _repository.IsAnyAsync(x => x.Id == id && x.DeleteMark == null))
            throw Oops.Oh(ErrorCode.COM1005);
        var isOk = await _repository.AsDeleteable().Where(it => it.Id.Equals(id)).ExecuteCommandHasChangeAsync();
        if (!isOk)
            throw Oops.Oh(ErrorCode.COM1002);
    }

    /// <summary>
    /// 修改.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPost("/printDev/{id}/update")]
    public async Task Update(long id, [FromBody] PrintDevUpInput input)
    {
        if (await _repository.IsAnyAsync(x => x.Id != id && x.DeleteMark == null && (x.EnCode == input.enCode || x.FullName == input.fullName)))
            throw Oops.Oh(ErrorCode.Com1004);
        var entity = input.Adapt<PrintDevEntity>();
        var isOk = await _repository.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandHasChangeAsync();
        if (!isOk)
            throw Oops.Oh(ErrorCode.COM1001);
    }

    /// <summary>
    /// 保存或发布.
    /// </summary>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPost("/printDev/Save")]
    [UnitOfWork]
    public async Task Save([FromBody] PrintDevSaveInput input)
    {
        var entity = input.Adapt<PrintVersionEntity>();
        if (input.type == 1)
        {
            await _repository.AsSugarClient().Updateable<PrintVersionEntity>().SetColumns(it => it.State == 2).SetColumns(it => it.SortCode == 0).Where(it => it.TemplateId == input.id && it.State == 1).ExecuteCommandAsync();
            entity.State = 1;
            entity.SortCode = 1;

            await _repository.AsUpdateable().SetColumns(it => new PrintDevEntity
            {
                State = 1,
                UpdatedTime = SqlFunc.GetDate(),
                UpdatedUserId = _userManager.UserId
            }).Where(it => it.Id == input.id).ExecuteCommandAsync();
        }

        await _repository.AsSugarClient().Updateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();

        // 数据集
        await _dataSetService.SaveDataSetList(input.versionId, "printVersion", input.dataSetList);
    }

    /// <summary>
    /// 复制.
    /// </summary>
    /// <param name="id">主键值</param>
    /// <returns></returns>
    [HttpPost("/printDev/{id}/Actions/Copy")]
    [UnitOfWork]
    public async Task ActionsCopy(long id)
    {
        var entity = await GetEntityInfo(id);
        var version = await GetVersionEntityInfo(id);
        var dataSetList = await GetDataSetList(version.Id);
        var random = new Random().NextLetterAndNumberString(5).ToLower();

        // 打印模板
        entity.Id = YitIdHelper.NextId();
        entity.FullName = entity.FullName + ".副本" + random;
        entity.EnCode += random;
        entity.State = 0;
        entity.CreatedTime = DateTime.Now;
        entity.CreatedUserId = _userManager.UserId;
        entity.UpdatedTime = null;
        entity.UpdatedUserId = null;
        if (entity.FullName.Length >= 50 || entity.EnCode.Length >= 50)
            throw Oops.Oh(ErrorCode.COM1009);

        // 打印模板版本
        version.Id = YitIdHelper.NextId();
        version.TemplateId = entity.Id;
        version.State = 0;
        version.Version = 1;
        version.CreatedTime = DateTime.Now;
        version.CreatedUserId = _userManager.UserId;
        version.UpdatedTime = null;
        version.UpdatedUserId = null;

        // 打印模板版本的数据集
        foreach (var item in dataSetList)
        {
            item.Id = YitIdHelper.NextId();
            item.ObjectId = version.Id;
            item.UpdatedTime = null;
            item.UpdatedUserId = null;
        }

        try
        {
            await _repository.AsInsertable(entity).ExecuteCommandAsync();
            await _repository.AsSugarClient().Insertable(version).ExecuteCommandAsync();
            await _repository.AsSugarClient().Insertable(dataSetList).IgnoreColumns(ignoreNullColumn: true).ExecuteCommandAsync();
        }
        catch (Exception)
        {
            throw Oops.Oh(ErrorCode.COM1008);
        }
    }

    /// <summary>
    /// 导入.
    /// </summary>
    /// <param name="file"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    [HttpPost("/printDev/Actions/Import")]
    public async Task ActionsImport(IFormFile file, int type)
    {
        var fileType = Path.GetExtension(file.FileName).Replace(".", string.Empty);
        if (!fileType.ToLower().Equals(ExportFileType.json.ToString()))
            throw Oops.Oh(ErrorCode.D3006);
        var josn = _fileManager.Import(file);
        PrintVersionInfoOutput? model;
        PrintDevEntity? entity;
        try
        {
            model = josn.ToObjectOld<PrintVersionInfoOutput>();
            entity = model.Adapt<PrintDevEntity>();
        }
        catch
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        var errorMsgList = new List<string>();
        var errorList = new List<string>();
        if (await _repository.AsQueryable().AnyAsync(it => it.DeleteMark == null && it.Id.Equals(model.id))) errorList.Add("ID");
        if (await _repository.AsQueryable().AnyAsync(it => it.DeleteMark == null && it.EnCode.Equals(model.enCode))) errorList.Add("编码");
        if (await _repository.AsQueryable().AnyAsync(it => it.DeleteMark == null && it.FullName.Equals(model.fullName))) errorList.Add("名称");

        if (errorList.Any())
        {
            if (type.Equals(0))
            {
                var error = string.Join("、", errorList);
                errorMsgList.Add(string.Format("{0}重复", error));
            }
            else
            {
                var random = new Random().NextLetterAndNumberString(5);
                entity.Id = YitIdHelper.NextId();
                entity.FullName = string.Format("{0}.副本{1}", model.fullName, random);
                entity.EnCode += random;
            }
        }
        if (errorMsgList.Any() && type.Equals(0)) throw Oops.Oh(ErrorCode.COM1018, string.Join(";", errorMsgList));

        entity.State = 0;
        entity.CreatedTime = DateTime.Now;
        entity.CreatedUserId = _userManager.UserId;
        entity.UpdatedUserId = null;
        entity.UpdatedTime = null;

        var version = model.Adapt<PrintVersionEntity>();
        version.Id = YitIdHelper.NextId();
        version.TemplateId = entity.Id;
        version.State = 0;
        version.Version = 1;
        version.CreatedTime = DateTime.Now;
        version.CreatedUserId = _userManager.UserId;
        version.UpdatedUserId = null;
        version.UpdatedTime = null;

        var dataSetList = new List<DataSetEntity>();
        foreach (var item in model.dataSetList)
        {
            var dataSet = item.Adapt<DataSetEntity>();
            dataSet.ObjectId = version.Id;
            dataSet.ObjectType = "printVersion";
            dataSet.UpdatedUserId = null;
            dataSet.UpdatedTime = null;
            dataSet.Id = YitIdHelper.NextId();
            dataSetList.Add(dataSet);
        }

        try
        {
            var storModuleModel = await _repository.AsSugarClient().Storageable(entity).WhereColumns(it => it.Id).Saveable().ToStorageAsync(); // 存在更新不存在插入 根据主键
            await storModuleModel.AsInsertable.ExecuteCommandAsync(); // 执行插入
            await storModuleModel.AsUpdateable.ExecuteCommandAsync(); // 执行更新

            await _repository.AsSugarClient().Insertable(version).ExecuteCommandAsync();
            await _repository.AsSugarClient().Insertable(dataSetList).IgnoreColumns(ignoreNullColumn: true).ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ErrorCode.COM1020, ex.Message);
        }
    }

    /// <summary>
    /// 模板列表.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/printDev/getListOptions")]
    [AllowAnonymous]
    public async Task<dynamic> GetListOptions([FromBody] PrintDevSqlDataQuery input)
    {
        return await _repository.AsQueryable().Where(x => input.ids.Contains(x.Id)).Select(x => new { id = x.Id, fullName = x.FullName }).ToListAsync();
    }

    /// <summary>
    /// 模板数据.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/printDev/Data")]
    public async Task<dynamic> GetData([FromBody] PrintDevSqlDataQuery input)
    {
        var info = input.formInfo[0];
        return await GetPrintDevDataOutput(input.id, info.formId, info.flowTaskId);
    }

    /// <summary>
    /// 模板数据.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/printDev/BatchData")]
    public async Task<dynamic> GetBatchData([FromBody] PrintDevSqlDataQuery input)
    {
        var output = new List<PrintDevDataOutput>();
        foreach (var info in input.formInfo)
        {
            var data = await GetPrintDevDataOutput(input.id, info.formId, info.flowTaskId);
            output.Add(data);
        }
        return output;
    }

    /// <summary>
    /// 版本新增.
    /// </summary>
    /// <param name="versionId"></param>
    /// <returns></returns>
    [HttpPost("/printDev/Info/{versionId}/add")]
    [UnitOfWork]
    public async Task<dynamic> CreateVersion(long versionId)
    {
        var version = await _repository.AsSugarClient().Queryable<PrintVersionEntity>().Where(it =>  it.Id == versionId).FirstAsync();
        var versionDataSetList = await _repository.AsSugarClient().Queryable<DataSetEntity>().Where(it =>  it.ObjectId == version.Id).ToListAsync();
        var maxVersion = await _repository.AsSugarClient().Queryable<PrintVersionEntity>().Where(it =>  it.TemplateId == version.TemplateId).MaxAsync(it => it.Version);

        // 版本
        var newVersionEntity = new PrintVersionEntity()
        {
            Id = YitIdHelper.NextId(),
            PrintTemplate = version.PrintTemplate,
            TemplateId = version.TemplateId,
            Version = maxVersion + 1,
            ConvertConfig = version.ConvertConfig,
            SortCode = 0,
            CreatedTime = DateTime.Now,
            CreatedUserId = _userManager.UserId
        };

        // 版本数据源
        var newDataSetList = new List<DataSetEntity>();
        foreach (var item in versionDataSetList)
        {
            var newDataSet = item.Adapt<DataSetEntity>();
            newDataSet.Id = YitIdHelper.NextId();
            newDataSet.ObjectId = newVersionEntity.Id;
            newDataSet.CreatedTime = DateTime.Now;
            newDataSet.CreatedUserId = _userManager.UserId;
            newDataSet.UpdatedUserId = null;
            newDataSet.UpdatedTime = null;
            newDataSetList.Add(newDataSet);
        }

        await _repository.AsSugarClient().Insertable(newVersionEntity).ExecuteCommandAsync();
        await _repository.AsSugarClient().Insertable(newDataSetList).ExecuteCommandAsync();

        return newVersionEntity.Id;
    }

    /// <summary>
    /// 版本删除.
    /// </summary>
    /// <param name="versionId">主键值.</param>
    /// <returns></returns>
    [HttpPost("/printDev/Info/{versionId}/delete")]
    public async Task DeleteVersion(long versionId)
    {
        if (!await _repository.AsSugarClient().Queryable<PrintVersionEntity>().AnyAsync(x => x.Id == versionId ))
            throw Oops.Oh(ErrorCode.COM1005);
        var isOk = await _repository.AsSugarClient().Deleteable<PrintVersionEntity>().Where(it => it.Id.Equals(versionId)).ExecuteCommandHasChangeAsync();
        if (!isOk)
            throw Oops.Oh(ErrorCode.COM1002);
    }

    #endregion

    #region PrivateMethod

    /// <summary>
    /// 信息.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    private async Task<PrintDevEntity> GetEntityInfo(long id)
    {
        return await _repository.GetFirstAsync(x => x.Id == id && x.DeleteMark == null);
    }

    /// <summary>
    /// 启用的版本信息.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    private async Task<PrintVersionEntity> GetVersionEntityInfo(long id)
    {
        return await _repository.AsSugarClient().Queryable<PrintVersionEntity>().Where(it =>  it.TemplateId == id && it.State == 1).FirstAsync();
    }

    /// <summary>
    /// 版本的数据集.
    /// </summary>
    /// <param name="versionId"></param>
    /// <returns></returns>
    private async Task<List<DataSetEntity>> GetDataSetList(long versionId)
    {
        return await _repository.AsSugarClient().Queryable<DataSetEntity>().Where(it =>  it.ObjectId == versionId).ToListAsync();
    }

    /// <summary>
    /// 模板数据.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="formId"></param>
    /// <param name="flowTaskId"></param>
    /// <returns></returns>
    private async Task<PrintDevDataOutput> GetPrintDevDataOutput(long id, string formId, string flowTaskId)
    {
        if (!await _repository.AsQueryable().AnyAsync(it => it.DeleteMark == null && it.Id == id)) throw Oops.Oh(ErrorCode.D9010);

        var output = new PrintDevDataOutput();
        var dic = new Dictionary<string, object>();
        var parameter = new List<SugarParameter>() { new("@formId", formId) };

        var version = await GetVersionEntityInfo(id);
        var convertConfig = version.ConvertConfig.IsNotEmptyOrNull() ? version.ConvertConfig.ToObjectOld<List<PrintDevConvertConfigModel>>() : new List<PrintDevConvertConfigModel>();
        var dataSetList = await GetDataSetList(version.Id);
        foreach (var dataSet in dataSetList)
        {
            var link = await _dbLinkService.GetInfo(dataSet.DbLinkId);
            var tenantLink = link ?? _dataBaseManager.GetTenantDbLink(_userManager.TenantId, _userManager.TenantDbName);

            var sql = _dataInterfaceService.GetSqlParameter(dataSet.DataConfigJson, parameter);
            var dataTable = _dataBaseManager.GetSqlData(tenantLink, sql, false, parameter.ToArray());
            if (dataTable.Rows.Count > 0)
            {
                var dataDic = DictionaryExtensions.DataTableToDicList(dataTable);
                var config = convertConfig.FindAll(it => it.field.Contains(dataSet.FullName + "."));
                if (config.Any())
                {
                    var list = new List<Dictionary<string, object>>();
                    foreach (var data in dataDic)
                    {
                        await FieldConversion(config, data);
                        list.Add(data);
                    }

                    dic.Add(dataSet.FullName, list);
                }
                else
                {
                    dic.Add(dataSet.FullName, dataDic);
                }
            }
            else
            {
                var columnsDic = new Dictionary<string, object>();
                for (int j = 0; j < dataTable.Columns.Count; j++)
                {
                    columnsDic.Add(dataTable.Columns[j].ColumnName, string.Empty);
                }

                dic.Add(dataSet.FullName, new List<Dictionary<string, object>> { columnsDic });
            }
        }

        output.printData = dic;
        output.printTemplate = version.PrintTemplate;
        output.convertConfig = version.ConvertConfig;

        if (flowTaskId.IsNotEmptyOrNull())
        {
            // output.operatorRecordList = await _repository.AsSugarClient().Queryable<WorkFlowRecordEntity>()
            //     .Where(a => a.TaskId == flowTaskId)
            //     .Select(a => new PrintDevDataModel()
            //     {
            //         id = a.Id,
            //         handleId = a.HandleId,
            //         handleOpinion = a.HandleOpinion,
            //         handleStatus = a.HandleType,
            //         nodeCode = a.NodeCode,
            //         handleTime = a.HandleTime,
            //         nodeName = a.NodeName,
            //         signImg = a.SignImg,
            //         taskId = a.TaskId,
            //         operatorId = SqlFunc.Subqueryable<UserEntity>().EnableTableFilter().Where(u => u.Id == a.OperatorId).Select(u => SqlFunc.MergeString(u.RealName, "/", u.Account)),
            //         userName = SqlFunc.Subqueryable<UserEntity>().EnableTableFilter().Where(u => u.Id == a.HandleId).Select(u => SqlFunc.MergeString(u.RealName, "/", u.Account)),
            //         status = a.Status,
            //         taskNodeId = a.NodeId,
            //         taskOperatorId = a.OperatorId,
            //     }).ToListAsync();
        }

        return output;
    }

    /// <summary>
    /// 字段转换.
    /// </summary>
    /// <param name="modelList"></param>
    /// <param name="dic"></param>
    /// <returns></returns>
    private async Task FieldConversion(List<PrintDevConvertConfigModel> modelList, Dictionary<string, object> dic)
    {
        var allorg = new List<SysOrg>();
        if (modelList.Any(it => it.type == "organize") || modelList.Any(it => it.type == "department") || modelList.Any(it => it.type == "users")) allorg = await _repository.AsSugarClient().Queryable<SysOrg>().Select(it => new SysOrg { Id = it.Id, OrganizeIdTree = it.OrganizeIdTree, Name = it.Name }).ToListAsync();
        // var allpos = new List<PositionEntity>();
        // if (modelList.Any(it => it.type == "position") || modelList.Any(it => it.type == "users")) allpos = await _repository.AsSugarClient().Queryable<PositionEntity>().Where(it => it.DeleteMark == null).Select(it => new PositionEntity { Id = it.Id, FullName = it.FullName }).ToListAsync();
        var allrole = new List<SysRole>();
        if (modelList.Any(it => it.type == "role") || modelList.Any(it => it.type == "users")) allrole = await _repository.AsSugarClient().Queryable<SysRole>().Select(it => new SysRole { Id = it.Id, Name = it.Name }).ToListAsync();
        // var allgroup = new List<GroupEntity>();
        // if (modelList.Any(it => it.type == "group") || modelList.Any(it => it.type == "users")) allgroup = await _repository.AsSugarClient().Queryable<GroupEntity>().Where(it => it.DeleteMark == null).Select(it => new GroupEntity { Id = it.Id, FullName = it.FullName }).ToListAsync();
        var alluser = new List<SysUser>();
        if (modelList.Any(it => it.type == "user") || modelList.Any(it => it.type == "users")) alluser = await _repository.AsSugarClient().Queryable<SysUser>().Select(it => new SysUser { Id = it.Id, Account = it.Account, Name = it.Name }).ToListAsync();
        // var alladdress = new List<ProvinceEntity>();
        // if (modelList.Any(it => it.type == "address")) alladdress = await _repository.AsSugarClient().Queryable<ProvinceEntity>().Where(it => it.DeleteMark == null).Select(it => new ProvinceEntity { Id = it.Id, FullName = it.FullName }).ToListAsync();

        foreach (var model in modelList)
        {
            var modelName = model.field.Split(".").Last();
            if (dic.ContainsKey(modelName) && dic[modelName].IsNotEmptyOrNull())
            {
                switch (model.type)
                {
                    case "select":
                        {
                            if (dic[modelName].ToString().Contains('['))
                            {
                                var list = dic[modelName].ToString().ToObjectOld<List<string>>();
                                var nameList = new List<string>();
                                foreach (var item in list)
                                {
                                    nameList.Add(await GetSelectField(model, item));
                                }

                                dic[modelName] = string.Join(",", nameList);
                            }
                            else
                            {
                                dic[modelName] = await GetSelectField(model, dic[modelName].ToString());
                            }
                        }

                        break;
                    case "date":
                        {
                            dic[modelName] = string.Format("{0:" + model.config.format + "}", dic[modelName]);
                        }

                        break;
                    case "number":
                        {
                            if (dic[modelName].ToString().Contains('.'))
                            {
                                var dataList = dic[modelName].ToString().Split('.').ToList();

                                if (model.config.thousands) dataList[0] = string.Format("{0:N0}", dataList[0].ParseToLong());

                                if (model.config.precision == 0)
                                {
                                    dic[modelName] = dataList[0];
                                }
                                else
                                {
                                    if (model.config.precision > dataList.Last().Length)
                                    {
                                        dic[modelName] = dataList[0] + "." + dataList.Last().PadRight(model.config.precision, '0');
                                    }
                                    else
                                    {
                                        dic[modelName] = dataList[0] + "." + dataList.Last().Substring(0, model.config.precision);
                                    }
                                }
                            }
                            else
                            {
                                if (model.config.thousands) dic[modelName] = string.Format("{0:N0}", dic[modelName].ParseToLong());

                                if (model.config.precision > 0) dic[modelName] += ".".PadRight(model.config.precision + 1, '0');
                            }
                        }

                        break;
                    // case "address":
                    //     {
                    //         if (dic[modelName].ToString().Contains("[["))
                    //         {
                    //             var treeList = dic[modelName].ToString().ToObjectOld<List<List<string>>>();
                    //             var treeNameList = new List<string>();
                    //             foreach (var list in treeList)
                    //             {
                    //                 var nameList = new List<string>();
                    //                 foreach (var item in list)
                    //                 {
                    //                     var address = alladdress.Find(it => it.Id == item);
                    //                     if (address.IsNotEmptyOrNull()) nameList.Add(address.FullName);
                    //                 }
                    //
                    //                 treeNameList.Add(string.Join("/", nameList));
                    //             }
                    //
                    //             dic[modelName] = string.Join(",", treeNameList);
                    //         }
                    //         else if (dic[modelName].ToString().Contains('['))
                    //         {
                    //             var list = dic[modelName].ToString().ToObjectOld<List<string>>();
                    //             var nameList = new List<string>();
                    //             foreach (var item in list)
                    //             {
                    //                 var address = alladdress.Find(it => it.Id == item);
                    //                 if (address.IsNotEmptyOrNull()) nameList.Add(address.FullName);
                    //             }
                    //
                    //             dic[modelName] = string.Join("/", nameList);
                    //         }
                    //     }

                        break;
                    case "location":
                        {
                            var value = dic[modelName]?.ToString().ToObjectOld<Dictionary<string, object>>();
                            if (value.IsNotEmptyOrNull() && value.ContainsKey("fullAddress"))
                                dic[modelName] = value["fullAddress"];
                        }

                        break;
                    case "organize":
                        {
                            if (dic[modelName].ToString().Contains("[["))
                            {
                                var treeList = dic[modelName].ToString().ToObjectOld<List<List<long>>>();
                                var treeNameList = new List<string>();
                                foreach (var list in treeList)
                                {
                                    var nameList = new List<string>();
                                    foreach (var item in list)
                                    {
                                        var org = allorg.Find(it => it.Id == item);
                                        if (org.IsNotEmptyOrNull()) nameList.Add(org.Name);
                                    }

                                    treeNameList.Add(string.Join("/", nameList));
                                }

                                dic[modelName] = string.Join(",", treeNameList);
                            }
                            else if (dic[modelName].ToString().Contains('['))
                            {
                                var list = dic[modelName].ToString().ToObjectOld<List<long>>();
                                var nameList = new List<string>();
                                foreach (var item in list)
                                {
                                    var org = allorg.Find(it => it.Id == item);
                                    if (org.IsNotEmptyOrNull()) nameList.Add(org.Name);
                                }

                                dic[modelName] = string.Join("/", nameList);
                            }
                        }

                        break;
                    case "department":
                        {
                            if (dic[modelName].ToString().Contains('['))
                            {
                                var list = dic[modelName].ToString().ToObjectOld<List<long>>();
                                var nameList = new List<string>();
                                foreach (var item in list)
                                {
                                    var dep = allorg.Find(it => it.Id == item);
                                    if (dep.IsNotEmptyOrNull()) nameList.Add(dep.Name);
                                }

                                dic[modelName] = string.Join(",", nameList);
                            }
                            else
                            {
                                var dep = allorg.Find(it => it.Id == Convert.ToInt64(dic[modelName]));
                                if (dep.IsNotEmptyOrNull()) dic[modelName] = dep.Name;
                            }
                        }

                        break;
                    // case "position":
                    //     {
                    //         if (dic[modelName].ToString().Contains('['))
                    //         {
                    //             var list = dic[modelName].ToString().ToObjectOld<List<string>>();
                    //             var nameList = new List<string>();
                    //             foreach (var item in list)
                    //             {
                    //                 var pos = allpos.Find(it => it.Id == item);
                    //                 if (pos.IsNotEmptyOrNull()) nameList.Add(pos.FullName);
                    //             }
                    //
                    //             dic[modelName] = string.Join(",", nameList);
                    //         }
                    //         else
                    //         {
                    //             var pos = allpos.Find(it => it.Id == dic[modelName].ToString());
                    //             if (pos.IsNotEmptyOrNull()) dic[modelName] = pos.FullName;
                    //         }
                    //     }
                    //
                    //     break;
                    case "role":
                        {
                            if (dic[modelName].ToString().Contains('['))
                            {
                                var list = dic[modelName].ToString().ToObjectOld<List<long>>();
                                var nameList = new List<string>();
                                foreach (var item in list)
                                {
                                    var role = allrole.Find(it => it.Id == item);
                                    if (role.IsNotEmptyOrNull()) nameList.Add(role.Name);
                                }

                                dic[modelName] = string.Join(",", nameList);
                            }
                            else
                            {
                                var role = allrole.Find(it => it.Id ==Convert.ToInt64( dic[modelName]));
                                if (role.IsNotEmptyOrNull()) dic[modelName] = role.Name;
                            }
                        }

                        break;
                    // case "group":
                    //     {
                    //         if (dic[modelName].ToString().Contains('['))
                    //         {
                    //             var list = dic[modelName].ToString().ToObjectOld<List<string>>();
                    //             var nameList = new List<string>();
                    //             foreach (var item in list)
                    //             {
                    //                 var group = allgroup.Find(it => it.Id == item);
                    //                 if (group.IsNotEmptyOrNull()) nameList.Add(group.FullName);
                    //             }
                    //
                    //             dic[modelName] = string.Join(",", nameList);
                    //         }
                    //         else
                    //         {
                    //             var group = allgroup.Find(it => it.Id == dic[modelName].ToString());
                    //             if (group.IsNotEmptyOrNull()) dic[modelName] = group.FullName;
                    //         }
                    //     }
                    //
                    //     break;
                    case "user":
                        {
                            if (dic[modelName].ToString().Contains('['))
                            {
                                var list = dic[modelName].ToString().ToObjectOld<List<long>>();
                                var nameList = new List<string>();
                                foreach (var item in list)
                                {
                                    var user = alluser.Find(it => it.Id == item);
                                    if (user.IsNotEmptyOrNull()) nameList.Add(user.Name);
                                }

                                dic[modelName] = string.Join(",", nameList);
                            }
                            else
                            {
                                var user = alluser.Find(it => it.Id == Convert.ToInt64(dic[modelName]));
                                if (user.IsNotEmptyOrNull()) dic[modelName] = user.Name;
                            }
                        }

                        break;
                    case "users":
                        {
                            if (dic[modelName].ToString().Contains('['))
                            {
                                var list = dic[modelName].ToString().ToObjectOld<List<string>>();
                                var nameList = new List<string>();
                                foreach (var item in list)
                                {
                                    var value = item.Split("--").ToList();
                                    if (value.Last() == "company" || value.Last() == "department")
                                    {
                                        var org = allorg.Find(it => it.Id == Convert.ToInt64(value.First()));
                                        if (org.IsNotEmptyOrNull()) nameList.Add(org.Name);
                                    }
                                    // else if (value.Last() == "position")
                                    // {
                                    //     var pos = allpos.Find(it => it.Id == value.First());
                                    //     if (pos.IsNotEmptyOrNull()) nameList.Add(pos.FullName);
                                    // }
                                    else if (value.Last() == "role")
                                    {
                                        var role = allrole.Find(it => it.Id == Convert.ToInt64(value.First()));
                                        if (role.IsNotEmptyOrNull()) nameList.Add(role.Name);
                                    }
                                    else if (value.Last() == "user")
                                    {
                                        var user = alluser.Find(it => it.Id == Convert.ToInt64(value.First()));
                                        if (user.IsNotEmptyOrNull()) nameList.Add(string.Format("{0}/{1}", user.Name, user.Account));
                                    }
                                    // else if (value.Last() == "group")
                                    // {
                                    //     var group = allgroup.Find(it => it.Id == value.First());
                                    //     if (group.IsNotEmptyOrNull()) nameList.Add(group.FullName);
                                    // }
                                }

                                dic[modelName] = string.Join(",", nameList);
                            }
                            else
                            {
                                var user = alluser.Find(it => dic[modelName].ToString().Contains(it.Id.ToString()));
                                if (user.IsNotEmptyOrNull()) dic[modelName] =  user.Name;
                            }
                        }

                        break;
                }
            }
        }
    }

    /// <summary>
    /// 枚举字段.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="oldData"></param>
    /// <returns></returns>
    private async Task<string> GetSelectField(PrintDevConvertConfigModel model, string oldData)
    {
        var newData = oldData;
        switch (model.config.dataType)
        {
            case "static":
                {
                    var option = model.config.options.Find(it => it.id == oldData);
                    if (option.IsNotEmptyOrNull()) newData = option.fullName;
                }

                break;
            // case "dictionary":
            //     {
            //         var dicData = await _repository.AsSugarClient().Queryable<DictionaryDataEntity>().Where(it => it.DeleteMark == null && it.DictionaryTypeId == model.config.dictionaryType).ToListAsync();
            //         if (dicData.IsNotEmptyOrNull())
            //         {
            //             var data = dicData.WhereIF(model.config.propsValue == "id", it => it.Id == oldData).WhereIF(model.config.propsValue == "enCode", it => it.EnCode == oldData).FirstOrDefault();
            //             if (data.IsNotEmptyOrNull()) newData = data.FullName;
            //         }
            //     }
            //
            //     break;
        }

        return newData;
    }

    #endregion
}