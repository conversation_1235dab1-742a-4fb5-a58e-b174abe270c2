using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;

namespace Systems.Core.Permission;

/// <summary>
///     系统机构服务
/// </summary>
[ApiDescriptionSettings(Order = 470)]
public class SysOrgService : IDynamicApiController, ITransient
{
    private readonly IUserManager _userManager;
    private readonly ISqlSugarRepository<SysOrg> _sysOrgRep;
    private readonly SysUserRoleService _sysUserRoleService;
    private readonly SysRoleOrgService _sysRoleOrgService;

    public SysOrgService(IUserManager userManager,
        ISqlSugarRepository<SysOrg> sysOrgRep,
        SysUserRoleService sysUserRoleService,
        SysRoleOrgService sysRoleOrgService)
    {
        _sysOrgRep = sysOrgRep;
        _userManager = userManager;
        _sysUserRoleService = sysUserRoleService;
        _sysRoleOrgService = sysRoleOrgService;
    }

    /// <summary>
    ///     获取组织机构树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sysOrg/tree")]
    [DisplayName("获取组织机构树")]
    public async Task<List<SysOrg>> GetOrgTree()
    {
        try
        {
            ISugarQueryable<SysOrg> orgs = _sysOrgRep.AsQueryable()
                .Where(u => u.Status == true)
                .OrderBy(u => u.Sort);

            return await orgs.ToTreeAsync(u => u.Children, u => u.Pid, 0);
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     分页查询组织机构
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysOrg/page")]
    [DisplayName("分页查询组织机构")]
    public async Task<SqlSugarPagedList<SysOrg>> QueryOrgPageList([FromQuery] OrgPageInput input)
    {
        try
        {
            SqlSugarPagedList<SysOrg> orgs = await _sysOrgRep.AsQueryable()
                .WhereIF(!string.IsNullOrEmpty(input.SearchValue?.Trim()), u => u.Name.Contains(input.SearchValue.Trim()))
                .WhereIF(input.Id > 0, u => u.Id == input.Id || u.OrganizeIdTree.Contains(input.Id.ToString()))
                .OrderBy(u => u.Sort)
                .ToPagedListAsync(input.PageNo, input.PageSize);
            return orgs;
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     获取组织机构列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysOrg/list")]
    [DisplayName("获取组织机构列表")]
    public async Task<List<SysOrg>> GetOrgList([FromQuery] OrgListInput input)
    {
        try
        {
            List<SysOrg> orgs = await _sysOrgRep.AsQueryable()
                .WhereIF(input.Pid > 0, u => u.Pid == input.Pid)
                .OrderBy(u => u.Sort).ToListAsync();
            return orgs;
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     增加组织机构
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysOrg/add")]
    [DisplayName("增加组织机构")]
    public async Task AddOrg(OrgAddInput input)
    {
        try
        {
            bool isExist = await _sysOrgRep.IsAnyAsync(u => u.Name == input.Name || u.Code == input.Code);
            if (isExist)
            {
                throw Oops.Oh(ErrorCode.D2002);
            }

            SysOrg sysOrg = input.Adapt<SysOrg>();

            #region 处理 上级ID列表 存储

            List<string>? idList = new();
            if (sysOrg.Pid != 0)
            {
                SysOrg? tree = _sysOrgRep.AsSugarClient().Queryable<SysOrg>().First(x => x.Id == sysOrg.Pid);
                List<string>? ids = tree.OrganizeIdTree.Split(",").ToList();
                idList.AddRange(ids);
            }

            idList.Add(sysOrg.Id.ToString());
            sysOrg.OrganizeIdTree = string.Join(",", idList);

            #endregion

            await _sysOrgRep.InsertAsync(sysOrg);
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     删除组织机构
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysOrg/delete")]
    [DisplayName("删除组织机构")]
    public async Task DeleteOrg(DeleteOrgInput input)
    {
        try
        {
            //默认组织机构禁止删除
            if (input.Id == 31519576587015)
            {
                throw Oops.Oh("禁止删除系统默认组织机构");
            }

            SysOrg sysOrg = await _sysOrgRep.AsQueryable().FirstAsync(u => u.Id == input.Id);
            if (sysOrg == null)
            {
                throw Oops.Oh(ErrorCode.D1002);
            }

            // 级联删除子节点
            List<long> childIdList = await GetChildIdListWithSelfById(sysOrg.Id);
            List<SysOrg> orgs = await _sysOrgRep.AsQueryable().Where(u => childIdList.Contains(u.Id)).ToListAsync();
            List<long> childOrgIdList = orgs.Select(u => u.Id).ToList();
            await _sysOrgRep.DeleteAsync(orgs);


            // 级联删除机构子节点
            await _sysOrgRep.DeleteAsync(u => childOrgIdList.Contains(u.Id));

            // 级联删除角色机构数据
            await _sysRoleOrgService.DeleteRoleOrgByOrgIdList(childOrgIdList);
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     更新组织机构
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysOrg/edit")]
    [DisplayName("更新组织机构")]
    public async Task UpdateOrg(UpdateOrgInput input)
    {
        if (input.Id == input.Pid)
        {
            throw Oops.Oh(ErrorCode.D2001);
        }

        // 如果是编辑，父id不能为自己的子节点
        List<long> childIdListById = await GetChildIdListWithSelfById(input.Id);
        if (childIdListById.Contains(input.Pid))
        {
            throw Oops.Oh(ErrorCode.D2001);
        }

        SysOrg sysOrg = await _sysOrgRep.AsQueryable().FirstAsync(u => u.Id == input.Id);
        if (sysOrg == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        bool isExist = await _sysOrgRep.IsAnyAsync(u => (u.Name == input.Name || u.Code == input.Code) && u.Id != sysOrg.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D2002);
        }

        sysOrg = input.Adapt<SysOrg>();

        #region 处理 上级ID列表 存储

        List<string>? idList = new();
        if (sysOrg.Pid != 0)
        {
            SysOrg? tree = _sysOrgRep.AsSugarClient().Queryable<SysOrg>().First(x => x.Id == sysOrg.Pid);
            _ = tree ?? throw Oops.Oh(ErrorCode.D2000);
            List<string>? ids = tree.OrganizeIdTree.Split(",").ToList();
            idList.AddRange(ids);
        }

        idList.Add(sysOrg.Id.ToString());
        sysOrg.OrganizeIdTree = string.Join(",", idList);

        #endregion

        await _sysOrgRep.AsSugarClient().Updateable(sysOrg).IgnoreColumns(true).ExecuteCommandAsync();
    }

    /// <summary>
    ///     获取组织机构信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysOrg/detail")]
    [DisplayName("获取组织机构信息")]
    public async Task<SysOrg> GetOrg([FromQuery] QueryOrgInput input)
    {
        return await _sysOrgRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    ///     根据用户Id获取机构Id集合
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task<List<long>> GetUserOrgIdList()
    {
        if (_userManager.IsAdministrator)
        {
            return new List<long>();
        }

        List<long> orgIdList = new();
        if (!orgIdList.Contains(_userManager.OrgId))
        {
            orgIdList.Add(_userManager.OrgId);
        }

        return orgIdList;
    }
    
    /// <summary>
    ///     根据节点Id获取子节点Id集合(包含自己)
    /// </summary>
    /// <param name="pid"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<List<long>> GetChildIdListWithSelfById(long pid)
    {
        List<SysOrg> orgTreeList = await _sysOrgRep.AsQueryable().ToChildListAsync(u => u.Pid, pid);
        return orgTreeList.Select(u => u.Id).ToList();
    }
}