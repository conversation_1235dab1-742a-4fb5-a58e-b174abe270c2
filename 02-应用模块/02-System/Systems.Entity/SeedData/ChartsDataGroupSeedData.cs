using Extras.DatabaseAccessor.SqlSugar.Extensions;

namespace Systems.Entity.SeedData;

/// <summary>
///     数据集分组种子数据
/// </summary>
public class ChartsDataGroupSeedData : ISqlSugarEntitySeedData<ChartsDataGroupEntity>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<ChartsDataGroupEntity> HasData()
    {
        return new[]
        {
            new ChartsDataGroupEntity
            {
                Id = 1300000000301,  // 使用一个不太可能冲突的ID
                Name = "数据集分组",
                ParentId = "ROOT",  // 使用ROOT作为特殊标识
                IsGroup = "Y",
                SortCode = 0,
                CreatedTime = DateTime.Parse("2023-01-01 00:00:00")
            }
        };
    }
}