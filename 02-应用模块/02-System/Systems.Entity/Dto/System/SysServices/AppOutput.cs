namespace Systems.Entity.Dto;

/// <summary>
///     系统应用参数
/// </summary>
public class ServiceOutput
{
    /// <summary>
    ///     应用Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     是否默认
    /// </summary>
    public string Active { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 打开方式：1：在新标签中打开；2：在门户内打开
    /// </summary>
    public string OpenType { get; set; }
    
    /// <summary>
    ///     菜单
    /// </summary>
    public List<AntDesignTreeNode> Menus { get; set; }

    /// <summary>
    /// 是否显示 
    /// </summary>
    public bool Display { get; set; } = true;
}