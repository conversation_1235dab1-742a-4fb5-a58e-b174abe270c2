using IotPlatform.Core.Extension;

namespace Systems.Entity;

/// <summary>
///     系统应用表
/// </summary>
[SugarTable("system_app", "系统应用表")]
public class SysApp : EntityTenant
{
    /// <summary>
    ///     appId
    /// </summary>
    [SugarColumn(ColumnDescription = "appId")]
    public string AppId { get; set; }

    /// <summary>
    ///     中英文名称
    /// </summary>
    [SugarColumn(ColumnDescription = "中英文名称", IsJson = true)]
    [Required]
    public MultiName MultiName { get; set; }

    /// <summary>
    ///     版本号
    /// </summary>
    [SugarColumn(ColumnDescription = "版本号")]
    [Required]
    public string Version { get; set; }

    /// <summary>
    ///     开放能力权限 1：全部权限；2：部分权限
    /// </summary>
    [SugarColumn(ColumnDescription = "开放能力权限 1：全部权限；2：部分权限")]
    public string ApiAuthority { get; set; }

    /// <summary>
    ///     登出通知地址
    /// </summary>
    [SugarColumn(ColumnDescription = "登出通知地址", Length = 512)]
    public string NotifyUrl { get; set; }

    /// <summary>
    ///     关闭平台消息通知
    /// </summary>
    [SugarColumn(ColumnDescription = "关闭平台消息通知")]
    public bool ClosePlatformMessage { get; set; }

    /// <summary>
    ///     app发布状态 1:未上线；2：已上线
    /// </summary>
    public MetaDataStatusEnum Status { get; set; } = MetaDataStatusEnum.Offline;

    /// <summary>
    ///     客户端配置
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端配置", IsJson = true)]
    public List<AppClient> Clients { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int Sort { get; set; }
    
    #region 忽略字段

    /// <summary>
    ///     客户端配置
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public AppClient Client => Clients != null ? Clients.FirstOrDefault(f => f.Default) : null;

    /// <summary>
    ///     app发布状态 1:未上线；2：已上线
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string StatusName => Status.GetDescription();

    #endregion
}

/// <summary>
///     app-客户端
/// </summary>
public class AppClient
{
    /// <summary>
    ///     应用类型：1：web；2：移动端；3：小程序
    /// </summary>
    public string AppType { get; set; }

    /// <summary>
    ///     集成方式，默认：INTEGRATION 外部集成
    /// </summary>
    public string DevType { get; set; } = "INTEGRATION";

    /// <summary>
    /// 内部服务Id
    /// </summary>
    public long? ServiceId { get; set; }
    
    /// <summary>
    ///     客户端-中英文名称
    /// </summary>
    public MultiName MultiName { get; set; }

    /// <summary>
    ///     图标地址
    /// </summary>
    public string IconUrl { get; set; }

    /// <summary>
    ///     登录方式:默认:refresh_token
    /// </summary>
    public List<string> Grants { get; set; }

    /// <summary>
    ///     访问地址(访问首页地址)
    /// </summary>
    public string VisitUrl { get; set; }

    /// <summary>
    ///     重定向地址
    /// </summary>
    public List<string> RedirectUris { get; set; }

    /// <summary>
    ///     State
    /// </summary>
    public string State { get; set; }

    /// <summary>
    ///     打开方式：1：在新标签中打开；2：在门户内打开
    /// </summary>
    public string OpenType { get; set; }

    /// <summary>
    ///     ClientSecret
    /// </summary>
    public string ClientSecret { get; set; }

    /// <summary>
    ///     ClientId
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    ///     默认的
    /// </summary>
    public bool Default { get; set; } = true;

    /// <summary>
    ///     描述
    /// </summary>
    public string Remark { get; set; }
}

/// <summary>
///     中英文名称
/// </summary>
public class MultiName
{
    /// <summary>
    ///     中文名称
    /// </summary>
    public string Zh { get; set; }

    /// <summary>
    ///     英文名称
    /// </summary>
    public string En { get; set; }
}

/// <summary>
///     app发布状态 1:未上线；2：已上线
/// </summary>
public enum MetaDataStatusEnum
{
    /// <summary>
    ///     未上线
    /// </summary>
    [Description("未上线")] Offline = 1,

    /// <summary>
    ///     已上线
    /// </summary>
    [Description("已上线")] Online = 2
}