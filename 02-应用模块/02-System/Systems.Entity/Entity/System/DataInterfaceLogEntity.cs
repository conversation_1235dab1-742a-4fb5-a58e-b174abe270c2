namespace Systems.Entity;

/// <summary>
///     数据接口日志
/// </summary>
[SugarTable("business_dataInterfaceLog")]
public class DataInterfaceLogEntity : EntityTenant
{
    /// <summary>
    ///     调用接口id
    /// </summary>
    [SugarColumn(ColumnDescription = "调用接口id")]
    public long InvokId { get; set; }

    /// <summary>
    ///     调用时间
    /// </summary>
    [SugarColumn(ColumnDescription = "调用时间")]
    public DateTime? InvokTime { get; set; }

    /// <summary>
    ///     调用者
    /// </summary>
    [SugarColumn(ColumnDescription = "调用者")]
    public long UserId { get; set; }

    /// <summary>
    ///     请求ip
    /// </summary>
    [SugarColumn(ColumnDescription = "请求ip")]
    public string InvokIp { get; set; }

    /// <summary>
    ///     请求设备
    /// </summary>
    [SugarColumn(ColumnDescription = "请求设备")]
    public string InvokDevice { get; set; }

    /// <summary>
    ///     请求耗时
    /// </summary>
    [SugarColumn(ColumnDescription = "请求耗时")]
    public int? InvokWasteTime { get; set; }

    /// <summary>
    ///     请求类型
    /// </summary>
    [SugarColumn(ColumnDescription = "请求类型")]
    public string InvokType { get; set; }

    /// <summary>
    ///     授权appid
    /// </summary>
    [SugarColumn(ColumnDescription = "授权appid")]
    public string OauthAppId { get; set; }
}