<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Systems.Entity\Systems.Entity.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="System\" />
    </ItemGroup>

</Project>
