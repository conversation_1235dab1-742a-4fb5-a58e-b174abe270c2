global using Furion;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using Mapster;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.Extensions.DependencyInjection;
global using SqlSugar;
global using System;
global using System.Collections.Generic;
global using System.ComponentModel;
global using System.ComponentModel.DataAnnotations;
global using System.Threading.Tasks;
global using System.Linq;
global using System.Threading;
global using Furion.Schedule;
global using IotPlatform.Application.Entity;
global using Microsoft.Extensions.Logging;
global using System.IO;
global using Furion.TimeCrontab;
global using IotPlatform.Application.BackupServices.Dto;
global using IotPlatform.Application.Service.Job;
global using Yitter.IdGenerator;
global using Systems.Entity;
global using System.Collections;
global using System.Reflection;
global using System.Runtime.CompilerServices;
global using Common.Models;
global using Extras.DatabaseAccessor.SqlSugar.Extensions;
global using Extras.DatabaseAccessor.SqlSugar.Internal;
global using Extras.DatabaseAccessor.SqlSugar.Repositories;
global using Furion.DataEncryption;
global using Furion.Logging;
global using IotPlatform.Application.Service.OpenApiServer.Dto;
global using IotPlatform.Core.Attribute;
global using IotPlatform.Core.Const;
global using Extras.TDengine.Model;
global using IotPlatform.Core.Enum;
global using IotPlatform.Core.Extension;
global using Jint;
global using IotPlatform.Application.Service.SysScript.Dto;
global using JsScript.Engine;
global using JsScript.Engine.Model;
global using DateTime = System.DateTime;
global using JsonOptions = Microsoft.AspNetCore.Mvc.JsonOptions;