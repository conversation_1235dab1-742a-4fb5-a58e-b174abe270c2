<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <NoWarn>1701;1702;1591;8632</NoWarn>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
        <Nullable>disable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
      <DocumentationFile />
    </PropertyGroup>

    <ItemGroup>
        <None Remove="IotPlatform.Application.xml"/>
        <None Remove="Configuration\Limit.json"/>
        <None Remove="Configuration\Logging.json"/>
        <None Remove="IotPlatform.Application.csproj.DotSettings"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\01-架构核心\Extras.TDengine\Extras.TDengine.csproj" />
        <ProjectReference Include="..\..\..\01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj" />
        <ProjectReference Include="..\..\02-System\Systems.Entity\Systems.Entity.csproj" />
        <ProjectReference Include="..\..\06-Task\02-ProgramBlock\IotPlatform.ProgramBlock.Entity\IotPlatform.ProgramBlock.Entity.csproj" />
        <ProjectReference Include="..\..\07-Thing\00-Model\IotPlatform.ThingModel\IotPlatform.ThingModel.csproj" />
        <ProjectReference Include="..\..\09-Engine\JsScript.Engine\JsScript.Engine.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Update="Configuration\OSS.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="MySqlBackup.NET" Version="2.3.9" />
        <PackageReference Include="Npgsql" Version="8.0.5" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="System.Text.Json" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Service\BackupServices\PostgreSqlBackupValidationService.cs" />
      <Compile Remove="Service\BackupServices\PostgreSqlToolDetector.cs" />
      <Compile Remove="Service\BackupServices\PostgreSqlToolValidationService.cs" />
      <Compile Remove="Service\BackupServices\PostgreSqlBackup.cs" />
    </ItemGroup>

</Project>
