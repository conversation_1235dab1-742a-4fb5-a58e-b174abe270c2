using System.Diagnostics;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Configuration;

namespace IotPlatform.Application.Service.BackupServices;

/// <summary>
///     PostgreSQL工具检测器
/// </summary>
public static class PostgreSqlToolDetector
{
    /// <summary>
    ///     检测pg_dump工具路径
    /// </summary>
    /// <returns>pg_dump工具路径，如果未找到返回null</returns>
    public static string? DetectPgDumpPath()
    {
        // 首先检查配置文件中是否指定了路径
        string? configPath = App.Configuration["PostgreSQL:PgDumpPath"];
        if (!string.IsNullOrEmpty(configPath) && File.Exists(configPath))
        {
            return configPath;
        }

        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return DetectWindowsPgDumpPath();
        }

        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            return DetectLinuxPgDumpPath();
        }

        if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
        {
            return DetectMacPgDumpPath();
        }

        return null;
    }

    /// <summary>
    ///     检测pg_restore工具路径
    /// </summary>
    /// <returns>pg_restore工具路径，如果未找到返回null</returns>
    public static string? DetectPgRestorePath()
    {
        // 首先检查配置文件中是否指定了路径
        string? configPath = App.Configuration["PostgreSQL:PgRestorePath"];
        if (!string.IsNullOrEmpty(configPath) && File.Exists(configPath))
        {
            return configPath;
        }

        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return DetectWindowsPgRestorePath();
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            return DetectLinuxPgRestorePath();
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
        {
            return DetectMacPgRestorePath();
        }

        return null;
    }

    private static string? DetectWindowsPgDumpPath()
    {
        // 常见的PostgreSQL安装路径
        string[] commonPaths = {
            @"C:\Program Files\PostgreSQL\16\bin\pg_dump.exe",
            @"C:\Program Files\PostgreSQL\15\bin\pg_dump.exe",
            @"C:\Program Files\PostgreSQL\14\bin\pg_dump.exe",
            @"C:\Program Files\PostgreSQL\13\bin\pg_dump.exe",
            @"C:\Program Files\PostgreSQL\12\bin\pg_dump.exe",
            @"C:\Program Files (x86)\PostgreSQL\16\bin\pg_dump.exe",
            @"C:\Program Files (x86)\PostgreSQL\15\bin\pg_dump.exe",
            @"C:\Program Files (x86)\PostgreSQL\14\bin\pg_dump.exe",
            @"C:\Program Files (x86)\PostgreSQL\13\bin\pg_dump.exe",
            @"C:\Program Files (x86)\PostgreSQL\12\bin\pg_dump.exe"
        };

        foreach (string path in commonPaths)
        {
            if (File.Exists(path))
            {
                return path;
            }
        }

        // 尝试从PATH环境变量中查找
        return FindInPath("pg_dump.exe");
    }

    private static string? DetectWindowsPgRestorePath()
    {
        // 常见的PostgreSQL安装路径
        string[] commonPaths = {
            @"C:\Program Files\PostgreSQL\16\bin\pg_restore.exe",
            @"C:\Program Files\PostgreSQL\15\bin\pg_restore.exe",
            @"C:\Program Files\PostgreSQL\14\bin\pg_restore.exe",
            @"C:\Program Files\PostgreSQL\13\bin\pg_restore.exe",
            @"C:\Program Files\PostgreSQL\12\bin\pg_restore.exe",
            @"C:\Program Files (x86)\PostgreSQL\16\bin\pg_restore.exe",
            @"C:\Program Files (x86)\PostgreSQL\15\bin\pg_restore.exe",
            @"C:\Program Files (x86)\PostgreSQL\14\bin\pg_restore.exe",
            @"C:\Program Files (x86)\PostgreSQL\13\bin\pg_restore.exe",
            @"C:\Program Files (x86)\PostgreSQL\12\bin\pg_restore.exe"
        };

        foreach (string path in commonPaths)
        {
            if (File.Exists(path))
            {
                return path;
            }
        }

        // 尝试从PATH环境变量中查找
        return FindInPath("pg_restore.exe");
    }

    private static string? DetectLinuxPgDumpPath()
    {
        // 常见的Linux安装路径
        string[] commonPaths = {
            "/usr/bin/pg_dump",
            "/usr/local/bin/pg_dump",
            "/opt/postgresql/bin/pg_dump"
        };

        foreach (string path in commonPaths)
        {
            if (File.Exists(path))
            {
                return path;
            }
        }

        // 使用which命令查找
        return FindInPath("pg_dump");
    }

    private static string? DetectLinuxPgRestorePath()
    {
        // 常见的Linux安装路径
        string[] commonPaths = {
            "/usr/bin/pg_restore",
            "/usr/local/bin/pg_restore",
            "/opt/postgresql/bin/pg_restore"
        };

        foreach (string path in commonPaths)
        {
            if (File.Exists(path))
            {
                return path;
            }
        }

        // 使用which命令查找
        return FindInPath("pg_restore");
    }

    private static string? DetectMacPgDumpPath()
    {
        // 常见的macOS安装路径
        string[] commonPaths = {
            "/usr/local/bin/pg_dump",
            "/opt/homebrew/bin/pg_dump",
            "/Applications/Postgres.app/Contents/Versions/latest/bin/pg_dump"
        };

        foreach (string path in commonPaths)
        {
            if (File.Exists(path))
            {
                return path;
            }
        }

        // 使用which命令查找
        return FindInPath("pg_dump");
    }

    private static string? DetectMacPgRestorePath()
    {
        // 常见的macOS安装路径
        string[] commonPaths = {
            "/usr/local/bin/pg_restore",
            "/opt/homebrew/bin/pg_restore",
            "/Applications/Postgres.app/Contents/Versions/latest/bin/pg_restore"
        };

        foreach (string path in commonPaths)
        {
            if (File.Exists(path))
            {
                return path;
            }
        }

        // 使用which命令查找
        return FindInPath("pg_restore");
    }

    private static string? FindInPath(string fileName)
    {
        try
        {
            string command = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? "where" : "which";
            
            ProcessStartInfo startInfo = new()
            {
                FileName = command,
                Arguments = fileName,
                RedirectStandardOutput = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using Process process = Process.Start(startInfo)!;
            using StreamReader reader = process.StandardOutput;
            string result = reader.ReadToEnd().Trim();
            process.WaitForExit();

            if (process.ExitCode == 0 && !string.IsNullOrEmpty(result))
            {
                // 在Windows上，where命令可能返回多行，取第一行
                string firstLine = result.Split('\n')[0].Trim();
                if (File.Exists(firstLine))
                {
                    return firstLine;
                }
            }
        }
        catch (Exception ex)
        {
            Log.Warning($"查找PostgreSQL工具时出错: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    ///     检测Docker是否可用
    /// </summary>
    /// <returns>Docker是否可用</returns>
    public static bool IsDockerAvailable()
    {
        try
        {
            ProcessStartInfo startInfo = new()
            {
                FileName = "docker",
                Arguments = "--version",
                RedirectStandardOutput = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using Process process = Process.Start(startInfo)!;
            process.WaitForExit();
            return process.ExitCode == 0;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     检测PostgreSQL容器是否存在
    /// </summary>
    /// <param name="containerName">容器名称</param>
    /// <returns>容器是否存在且运行中</returns>
    public static bool IsPostgreSqlContainerRunning(string containerName = "postgresql")
    {
        try
        {
            ProcessStartInfo startInfo = new()
            {
                FileName = "docker",
                Arguments = $"ps --filter name={containerName} --filter status=running --format \"{{{{.Names}}}}\"",
                RedirectStandardOutput = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using Process process = Process.Start(startInfo)!;
            using StreamReader reader = process.StandardOutput;
            string result = reader.ReadToEnd().Trim();
            process.WaitForExit();

            return process.ExitCode == 0 && !string.IsNullOrEmpty(result);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     验证PostgreSQL工具是否可用
    /// </summary>
    /// <returns>验证结果</returns>
    public static (bool IsAvailable, string Message, string? PgDumpPath, string? PgRestorePath, bool UseDocker) ValidateTools()
    {
        string deploymentMode = App.Configuration["PostgreSQL:DeploymentMode"] ?? "Auto";
        bool forceLocalTools = App.Configuration.GetValue<bool>("PostgreSQL:ForceLocalTools", true);

        // 根据部署模式决定检测策略
        switch (deploymentMode.ToUpper())
        {
            case "HOST":
                return ValidateHostMode(forceLocalTools);
            case "CONTAINER":
                return ValidateContainerMode();
            case "AUTO":
            default:
                return ValidateAutoMode(forceLocalTools);
        }
    }

    /// <summary>
    ///     验证宿主机模式
    /// </summary>
    private static (bool IsAvailable, string Message, string? PgDumpPath, string? PgRestorePath, bool UseDocker) ValidateHostMode(bool forceLocalTools)
    {
        // 宿主机模式：优先使用本地工具
        string? pgDumpPath = DetectPgDumpPath();
        string? pgRestorePath = DetectPgRestorePath();

        if (!string.IsNullOrEmpty(pgDumpPath) && !string.IsNullOrEmpty(pgRestorePath))
        {
            return (true, "PostgreSQL宿主机工具验证成功", pgDumpPath, pgRestorePath, false);
        }

        // 如果强制使用本地工具但未找到，返回失败
        if (forceLocalTools)
        {
            return (false, "宿主机模式下未找到PostgreSQL工具。请安装PostgreSQL客户端工具或修改配置", null, null, false);
        }

        // 否则尝试使用Docker容器内的工具连接宿主机PostgreSQL
        if (IsDockerAvailable() && IsCurrentApplicationInContainer())
        {
            return (true, "将使用容器内工具连接宿主机PostgreSQL", "docker", "docker", true);
        }

        return (false, "未找到可用的PostgreSQL工具", null, null, false);
    }

    /// <summary>
    ///     验证容器模式
    /// </summary>
    private static (bool IsAvailable, string Message, string? PgDumpPath, string? PgRestorePath, bool UseDocker) ValidateContainerMode()
    {
        if (!IsDockerAvailable())
        {
            return (false, "容器模式下Docker不可用", null, null, false);
        }

        string containerName = App.Configuration["PostgreSQL:ContainerName"] ?? "postgresql";
        if (!IsPostgreSqlContainerRunning(containerName))
        {
            return (false, $"PostgreSQL容器 '{containerName}' 未运行", null, null, false);
        }

        return (true, "PostgreSQL容器模式验证成功", "docker", "docker", true);
    }

    /// <summary>
    ///     自动检测模式
    /// </summary>
    private static (bool IsAvailable, string Message, string? PgDumpPath, string? PgRestorePath, bool UseDocker) ValidateAutoMode(bool forceLocalTools)
    {
        // 首先尝试检测本地工具
        string? pgDumpPath = DetectPgDumpPath();
        string? pgRestorePath = DetectPgRestorePath();

        if (!string.IsNullOrEmpty(pgDumpPath) && !string.IsNullOrEmpty(pgRestorePath))
        {
            return (true, "PostgreSQL本地工具验证成功", pgDumpPath, pgRestorePath, false);
        }

        // 如果本地工具不可用，检查Docker方式
        if (!IsDockerAvailable())
        {
            return (false, "未找到PostgreSQL工具，且Docker不可用。请安装PostgreSQL客户端工具或Docker", null, null, false);
        }

        // 检查是否有PostgreSQL容器运行（传统容器模式）
        string containerName = App.Configuration["PostgreSQL:ContainerName"] ?? "postgresql";
        if (IsPostgreSqlContainerRunning(containerName))
        {
            return (true, "PostgreSQL Docker容器验证成功，将使用容器执行备份", "docker", "docker", true);
        }

        // 如果当前应用在容器中，可以使用容器内工具连接外部PostgreSQL
        if (IsCurrentApplicationInContainer())
        {
            return (true, "将使用容器内工具连接外部PostgreSQL", "docker", "docker", true);
        }

        return (false, "未找到可用的PostgreSQL工具或容器", null, null, false);
    }

    /// <summary>
    ///     检测当前应用是否运行在容器中
    /// </summary>
    public static bool IsCurrentApplicationInContainer()
    {
        try
        {
            // 检查是否存在 /.dockerenv 文件（Docker容器标识）
            if (File.Exists("/.dockerenv"))
            {
                return true;
            }

            // 检查 /proc/1/cgroup 文件中是否包含docker信息
            if (File.Exists("/proc/1/cgroup"))
            {
                string cgroup = File.ReadAllText("/proc/1/cgroup");
                return cgroup.Contains("docker") || cgroup.Contains("containerd");
            }

            // Windows容器检测（检查环境变量）
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                string? containerName = Environment.GetEnvironmentVariable("CONTAINER_NAME");
                return !string.IsNullOrEmpty(containerName);
            }
        }
        catch
        {
            // 检测失败，假设不在容器中
        }

        return false;
    }
}
