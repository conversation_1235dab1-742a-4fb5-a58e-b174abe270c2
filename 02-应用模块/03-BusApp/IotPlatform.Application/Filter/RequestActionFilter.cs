using Furion.EventBus;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json.Linq;

namespace IotPlatform.Application.Filter;

/// <summary>
/// </summary>
public class RequestActionFilter : IAsyncActionFilter
{
    private readonly ISqlSugarRepository<OpenApiEntity> _openApiEntity;

    public RequestActionFilter(ISqlSugarRepository<OpenApiEntity> openApiEntity)
    {
        _openApiEntity = openApiEntity;
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        // 获取当前路由地址
        string route = context.HttpContext.Request.Path.Value;

        if (context.HttpContext.Request.Path.Value.StartsWith("/webhook"))
        {
            await MessageCenter.PublishAsync(context.HttpContext.Request.Path.Value, context.HttpContext.Request.Path.Value); //发送事件去触发任务
        }
        else if (context.HttpContext.Request.Path.Value.StartsWith("/script/otherGetAction"))
        {
            // var data = JSON.Deserialize<GetActionInput>(JSON.Serialize(context.ActionArguments.FirstOrDefault().Value));
            // if (data != null)
            //     await MessageCenter.PublishAsync(EventConst.HttpTaskTrigger, data.Id);
        }
        else if (route!.StartsWith("/openapi"))
        {
            try
            {
                long appId = 0;
                if (context.HttpContext.Request.Method == "POST")
                {
                    string body = await context.HttpContext.ReadBodyContentAsync();
                    JObject jsonObject = JObject.Parse(body);
                    appId = (long) jsonObject["AppId"];

                    // 获取 POST 请求的 Body 中的 appid 参数
                }
                else if (context.HttpContext.Request.Method == "GET")
                {
                    // 获取 GET 请求的 Query 中的 appid 参数
                    appId = Convert.ToInt64(context.HttpContext.Request.Query["AppId"]);
                    if (appId <= 0)
                    {
                        context.ActionArguments.TryGetValue("AppId", out object appIdObj);
                        appId = Convert.ToInt64(appIdObj);
                    }
                }

                if (appId <= 0)
                {
                    context.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    return;
                }

                // 请求头中获取token
                string token = context.HttpContext.Request.Headers.Authorization;
                // 在此处进行token的有效性判断，例如调用验证token的方法
                if (!await IsTokenValid(token, route, appId))
                {
                    context.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    return;
                }
            }
            catch
            {
                context.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                return;
            }
        }


        await next();
    }

    /// <summary>
    ///     校验是否有效
    /// </summary>
    /// <param name="token"></param>
    /// <param name="path"></param>
    /// <param name="appId"></param>
    /// <returns></returns>
    private async Task<bool> IsTokenValid(string token, string path, long appId)
    {
        // 无认证
        if (!token.IsNullOrEmpty())
        {
            token = token.Replace("Bearer ", "").Trim();
            (bool isValid, _, _) = JWTEncryption.Validate(token);
            if (isValid)
            {
                return true;
            }
        }

        OpenApiEntity openApi = await _openApiEntity.AsQueryable()
            .Where(w => w.Id == appId)
            .Where(w => w.AuthorizeType == AuthorizeTypeEnum.No)
            .Includes(w => w.OpenApiDetailEntity.Where(x => x.Path == path).ToList())
            .FirstAsync();
        return openApi != null && openApi.OpenApiDetailEntity.Any();
    }
}