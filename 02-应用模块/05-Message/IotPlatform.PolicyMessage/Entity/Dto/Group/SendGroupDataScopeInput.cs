namespace IotPlatform.PolicyMessage.Entity.Dto.Group;

/// <summary>
///     增加推送分组关联数据请求
/// </summary>
public class SendGroupDataScopeInput
{
    /// <summary>
    ///     userId
    /// </summary>
    [Required]
    public List<long> UserId { get; set; }

    /// <summary>
    ///     推送分组Id
    /// </summary>
    [Required]
    public long SendGroupId { get; set; }
}

/// <summary>
/// </summary>
public class SendGroupDataScopeUpdateInput
{
    /// <summary>
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     企业微信UserId
    /// </summary>
    public string WeChatId { get; set; }

    /// <summary>
    ///     邮箱
    /// </summary>
    public string Email { get; set; }
}

/// <summary>
///     推送分组关联数据列表请求参数
/// </summary>
public class SendGroupDataScopePageInput : BasePageInput
{
    /// <summary>
    ///     分组Id
    /// </summary>
    [Required]
    public long SendGroupId { get; set; }
}