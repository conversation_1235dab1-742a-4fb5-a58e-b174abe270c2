namespace IotPlatform.PolicyMessage.Entity;

/// <summary>
///     消息推送模板
/// </summary>
[SugarTable("business_sendMsgTemplate", "消息推送模板")]
public class SendTemplate : EntityTenant
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称")]
    [Required]
    [MaxLength(64)]
    public string Name { get; set; }

    /// <summary>
    ///     模板描述
    /// </summary>
    [SugarColumn(ColumnDescription = "模板描述", Length = 1024,IsNullable = true)]
    public string? Remark { get; set; }

    /// <summary>
    ///     消息类型:1:文本；2：图文；3:卡片
    /// </summary>
    [SugarColumn(ColumnDescription = "消息类型:1:文本；2：图文；3:卡片")]
    public SendTemplateTypeEnum Type { get; set; }

    /// <summary>
    ///     配置内容
    /// </summary>
    [SugarColumn(ColumnDescription = "配置内容",IsNullable = true)]
    public string? Content { get; set; }

    /// <summary>
    ///     输入参数
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(SendTemplateInputData.SendTemplateId))]
    public List<SendTemplateInputData> SendTemplateInputs { get; set; }
}

/// <summary>
///     文本消息
/// </summary>
public class TextContent
{
    /// <summary>
    ///     标题
    /// </summary>
    [Required(ErrorMessage = "文本消息标题不能是空！")]
    public string Title { get; set; }

    /// <summary>
    ///     内容
    /// </summary>
    public string Content { get; set; }
}

/// <summary>
///     图文消息
/// </summary>
public class ImageContent
{
    /// <summary>
    ///     标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    ///     内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    ///     图片地址
    /// </summary>
    public string ImageUrl { get; set; }

    /// <summary>
    ///     跳转地址
    /// </summary>
    public string ToUrl { get; set; }
}

/// <summary>
///     卡片消息
/// </summary>
public class CardContent
{
    /// <summary>
    ///     标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    ///     内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    ///     链接名称
    /// </summary>
    public string LinkName { get; set; }

    /// <summary>
    ///     链接地址
    /// </summary>
    public string LinkUrl { get; set; }
}

/// <summary>
///     消息类型:1:文本；2：图文；3:卡片
/// </summary>
public enum SendTemplateTypeEnum
{
    /// <summary>
    ///     文本消息
    /// </summary>
    [Description("文本消息")] Text = 1,

    /// <summary>
    ///     图文消息
    /// </summary>
    [Description("图文消息")] Image = 2,

    /// <summary>
    ///     卡片消息
    /// </summary>
    [Description("卡片消息")] Card = 3
}