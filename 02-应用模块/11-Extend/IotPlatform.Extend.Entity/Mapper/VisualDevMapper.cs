using IotPlatform.Extend.Entity.Document;
using Mapster;

namespace IotPlatform.Extend.Entity.Mapper;

/// <summary>
///     模块对象映射.
/// </summary>
public class VisualDevMapper : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        config.ForType<DocumentEntity, DocumentListOutput>()
            .Map(dest => dest.uploaderUrl, src => src.UploadUrl);
        config.ForType<DocumentEntity, DocumentTrashOutput>()
            .Map(dest => dest.deleteTime, src => src.UpdatedTime);
    }
}