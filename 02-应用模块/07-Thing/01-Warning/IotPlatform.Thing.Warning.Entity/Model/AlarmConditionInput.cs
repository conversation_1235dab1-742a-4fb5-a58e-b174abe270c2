namespace IotPlatform.Thing.Warning.Entity;

/// <summary>
///     报警条件-新增请求参数
/// </summary>
public class AlarmConditionInput
{
    /// <summary>
    ///     所属报警类型Id
    /// </summary>
    [Required]
    public long AlarmTypeId { get; set; }

    /// <summary>
    ///     报警来源：1接入与建模
    /// </summary>
    [Required]
    public AlarmSourceEnum AlarmSource { get; set; }

    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     物模型名称
    /// </summary>
    [Required]
    public string ModelName { get; set; }

    /// <summary>
    ///     物实例集合
    /// </summary>
    public List<SourceThing> Things { get; set; }

    /// <summary>
    ///     报警集合
    /// </summary>
    public List<SourceAlarm> SourceAlarms { get; set; }
}

/// <summary>
///     报警条件列表
/// </summary>
public class AlarmConditionListInput
{
    /// <summary>
    ///     报警类型Id
    /// </summary>
    [Required]
    public long AlarmTypeId { get; set; }
}