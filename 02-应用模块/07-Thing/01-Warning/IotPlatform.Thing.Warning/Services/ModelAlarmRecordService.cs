using RazorEngine.Compilation.ImpromptuInterface.InvokeExt;
using DateTime = System.DateTime;

namespace IotPlatform.Thing.Warning.Services;

/// <summary>
///     物模型-报警记录
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("物模型")]
public class ModelAlarmRecordService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<ModelAlarmRecord> _thingAlarmRecord;
    private readonly ISchedulerFactory _schedulerFactory;

    /// <summary>
    /// </summary>
    /// <param name="thingAlarmRecord"></param>
    /// <param name="schedulerFactory"></param>
    public ModelAlarmRecordService(ISqlSugarRepository<ModelAlarmRecord> thingAlarmRecord, ISchedulerFactory schedulerFactory)
    {
        _thingAlarmRecord = thingAlarmRecord;
        _schedulerFactory = schedulerFactory;
    }

    /// <summary>
    ///     物模型-报警记录-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/thingAlarmRecord/page")]
    public async Task<SqlSugarPagedList<ModelAlarmRecord>> ThingAlarmRecordPage([FromQuery] ThingAlarmRecordPageInput input)
    {
        SqlSugarPagedList<ModelAlarmRecord> thingPage = await _thingAlarmRecord.AsQueryable()
            .WhereIF(input.ThingIdent.IsNotEmptyOrNull(), w => w.ThingIdent == input.ThingIdent)
            .WhereIF(input.Type == 1, w => w.CloseTime == null || w.ConfirmTime == null)
            .WhereIF(input.ModelId > 0, w => w.ModelId == input.ModelId)
            .WhereIF(input.ThingId > 0, w => w.ThingId == input.ThingId)
            .WhereIF(input.Status > 0, w => w.Status == (ThingAlarmRecordStatusEnum) input.Status)
            .Where(w => w.TriggerTime >= Convert.ToDateTime(input.SearchBeginTime) && w.TriggerTime <= Convert.ToDateTime(input.SearchEndTime))
            .SplitTable(DateTime.Parse(input.SearchBeginTime!.Trim()), DateTime.Parse(input.SearchEndTime.Trim()))
            .Includes(w => w.Model)
            .Includes(w => w.ModelAlarm)
            .Includes(w => w.Thing)
            .WhereIF(input.AlarmLevel.Any(), w => input.AlarmLevel.Contains((int) w.ModelAlarm.AlarmLevel))
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), u => u.ModelAlarm.Identification.Contains(input.SearchValue)
                                                                || u.ModelAlarm.Name.Contains(input.SearchValue))
            .OrderByDescending(w => w.FirstTriggerTime)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return thingPage;
    }

    /// <summary>
    ///     物模型-报警记录-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/thingAlarmRecord/detail")]
    public async Task<ModelAlarmRecord> ThingAlarmRecordDetail([FromQuery] BaseId input)
    {
        ModelAlarmRecord? thing = await _thingAlarmRecord.AsQueryable()
            .Where(w => w.Id == input.Id)
            .SplitTable()
            .Includes(w => w.Model)
            .Includes(w => w.Thing)
            .Includes(w => w.ModelAlarm)
            .FirstAsync();
        return thing;
    }

    /// <summary>
    ///     物模型-报警记录-详情-关联属性
    /// </summary>
    /// <returns></returns>
    [HttpGet("/thingAlarmRecord/rel-properties")]
    public async Task<dynamic> RelProperties([FromQuery] RelPropertiesInput input)
    {
        // 报警记录
        ModelAlarmRecord? thingAlarmRecord = await _thingAlarmRecord.AsQueryable()
            .Where(w => w.Id == input.Id && w.ModelAlarmId == input.ModelAlarmId)
            .SplitTable()
            .Includes(w => w.ModelAlarm)
            .Includes(w => w.Thing)
            .Includes(w => w.Model, w => w.ThingAttributes)
            .FirstAsync();
        if (thingAlarmRecord == null)
        {
            throw Oops.Oh("记录不存在!");
        }

        if (thingAlarmRecord.ModelAlarm == null)
        {
            throw Oops.Oh("关联报警不存在!");
        }

        // 关联属性
        List<ModelAttributes> relThingAttributes = new();
        // 同时上报属性
        List<ModelAttributes> respThingAttributes = new();
        switch (thingAlarmRecord.ModelAlarm.AlarmRuleType)
        {
            case AlarmRulesTypeEnum.Simple:
            {
                AlarmRulesModel? simpleAlarmRulesModel = thingAlarmRecord.ModelAlarm.SimpleAlarmRulesModel;
                if (simpleAlarmRulesModel != null)
                {
                    // 关联属性
                    ModelAttributes? thingAttribute = thingAlarmRecord.Model.ThingAttributes.FirstOrDefault(f => f.Identification == simpleAlarmRulesModel.ThingAttributeName);
                    if (thingAttribute != null)
                    {
                        relThingAttributes.Add(thingAttribute);
                    }
                }
            }
                break;
            case AlarmRulesTypeEnum.AlarmRules:
            {
                List<AlarmRulesModel>? alarmRulesModelList = thingAlarmRecord.ModelAlarm.AlarmRulesModel;
                if (alarmRulesModelList != null)
                {
                    relThingAttributes.AddRange(alarmRulesModelList
                        .Select(alarmRulesModel => thingAlarmRecord.Model.ThingAttributes.FirstOrDefault(f => f.Identification == alarmRulesModel.ThingAttributeName))
                        .Where(thingAttribute => thingAttribute != null));
                }
            }
                break;
            case AlarmRulesTypeEnum.Custom:
                break;
        }

        // 同时上报属性
        if (thingAlarmRecord.ModelAlarm?.ModelAttributesId != null)
        {
            respThingAttributes.AddRange(thingAlarmRecord.ModelAlarm.ModelAttributesId
                .Select(modelAttributesId => thingAlarmRecord.Model.ThingAttributes
                    .FirstOrDefault(f => f.Identification == modelAttributesId)).Where(modelAttributes => modelAttributes != null));
        }

        return new
        {
            relProperties = relThingAttributes.Select(s => new
            {
                s.Identification,
                s.Name,
                s.DataType,
                DataTypeName = s.DataType.GetDescription(),
                s.ReadType,
                ReadTypeName = s.ReadType.GetDescription(),
                s.AttributesValueSource,
                AttributesValueSourceName = s.AttributesValueSource.GetDescription()
            }).ToList(),
            respProperties = respThingAttributes.Select(s => new
            {
                s.Identification,
                s.Name,
                s.DataType,
                DataTypeName = s.DataType.GetDescription(),
                s.ReadType,
                ReadTypeName = s.ReadType.GetDescription(),
                s.AttributesValueSource,
                AttributesValueSourceName = s.AttributesValueSource.GetDescription()
            }).ToList()
        };
    }

    /// <summary>
    ///     物模型-报警记录-确认
    /// </summary>
    /// <returns></returns>
    [HttpPost("/thingAlarmRecord/confirm")]
    public async Task Confirm(BaseId<List<long>> input)
    {
        List<ModelAlarmRecord>? thingAlarmList = await _thingAlarmRecord.AsQueryable()
            .Where(w => input.Id.Contains(w.Id))
            .SplitTable()
            .ToListAsync();
        foreach (ModelAlarmRecord? thingAlarm in thingAlarmList)
        {
            thingAlarm.ConfirmTime = DateTime.Now;
        }

        await _thingAlarmRecord.AsUpdateable(thingAlarmList).UpdateColumns(w => w.ConfirmTime).SplitTable().ExecuteCommandAsync();
    }

    /// <summary>
    ///     物模型-报警记录-手动解除
    /// </summary>
    /// <returns></returns>
    [HttpPost("/thingAlarmRecord/hand")]
    public async Task Hand(BaseId<List<long>> input)
    {
        List<ModelAlarmRecord>? thingAlarmList = await _thingAlarmRecord.AsQueryable()
            .Where(w => input.Id.Contains(w.Id))
            .SplitTable()
            .ToListAsync();
        foreach (ModelAlarmRecord? thingAlarm in thingAlarmList)
        {
            thingAlarm.Status = ThingAlarmRecordStatusEnum.Hand;
            thingAlarm.CloseTime = DateTime.Now;

            await MessageCenter.PublishAsync(EventConst.ThingWarningHandClose, thingAlarm);

            _schedulerFactory.RemoveJob(thingAlarm.Id.ToString());
        }

        await _thingAlarmRecord.AsUpdateable(thingAlarmList).UpdateColumns(w => new {w.Status, w.CloseTime}).SplitTable().ExecuteCommandAsync();
    }
}