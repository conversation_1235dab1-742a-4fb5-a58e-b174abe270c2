<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\..\01-架构核心\Extras.DatabaseAccessor.SqlSugar\Extras.DatabaseAccessor.SqlSugar.csproj" />
      <ProjectReference Include="..\..\..\..\01-架构核心\Extras.TDengine\Extras.TDengine.csproj" />
      <ProjectReference Include="..\..\..\..\01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj" />
      <ProjectReference Include="..\..\..\00-Common\Common.Core\Common.Core.csproj" />
      <ProjectReference Include="..\IotPlatform.Thing.StatisticalRule.Entity\IotPlatform.Thing.StatisticalRule.Entity.csproj" />
    </ItemGroup>

</Project>
