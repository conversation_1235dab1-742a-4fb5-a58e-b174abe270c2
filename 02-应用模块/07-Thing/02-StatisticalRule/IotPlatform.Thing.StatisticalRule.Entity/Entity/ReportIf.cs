using System;

namespace IotPlatform.Thing.StatisticalRule.Entity;

[SplitTable(SplitType._Custom02, typeof(ReportSplitService))]
[SugarTable("report", "条件统计")]
public class ReportIf : EntityTenantId
{
    /// <summary>
    ///     存储的开始时间
    /// </summary>
    [SugarColumn(ColumnDescription = "存储的开始时间")]
    public DateTime StartTime { get; set; }

    /// <summary>
    ///     存储的结束时间
    /// </summary>
    [SugarColumn(ColumnDescription = "存储的结束时间")]
    public DateTime EndTime { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    [SugarColumn(ColumnDescription = "设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     属性标识
    /// </summary>
    [SugarColumn(ColumnDescription = "属性标识")]
    public string TagName { get; set; }

    /// <summary>
    ///     结果值
    /// </summary>
    [SugarColumn(ColumnDescription = "结果值", IsJson = true)]
    public Dictionary<string, ReportIfParam> Params { set; get; }

    /// <summary>
    ///     分表标签
    /// </summary>
    [SplitField]
    [SugarColumn(IsIgnore = true)]
    [JsonIgnore]
    public string Key { get; set; }

    /// <summary>
    ///     时间
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DateTime FTime => StartTime;
}

/// <summary>
///     值
/// </summary>
public class ReportIfParam
{
    /// <summary>
    ///     满足次数
    /// </summary>
    public double OpenNum { get; set; }

    /// <summary>
    ///     满足时长
    /// </summary>
    public double OpenTimeLong { get; set; }

    // public string Expression { get; set; }
}