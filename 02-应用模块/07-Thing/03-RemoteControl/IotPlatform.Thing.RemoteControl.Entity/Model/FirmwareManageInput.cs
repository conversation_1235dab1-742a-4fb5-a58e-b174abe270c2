using System.ComponentModel.DataAnnotations;
using Common.Models;
using Microsoft.AspNetCore.Http;

namespace IotPlatform.Thing.RemoteControl.Entity;

/// <summary>
/// 远程控制-固件管理-新增
/// </summary>
public class FirmwareManageAddInput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     固件名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     固件版本
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    ///     签名算法
    /// </summary>
    [Required]
    public string SignType { get; set; }

    /// <summary>
    ///     上传文件
    /// </summary>
    [Required]
    public long FileId { get; set; }

    /// <summary>
    /// 磁盘建议大小（M）
    /// </summary>
    public long NeedSize { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 远程控制-固件管理-固件升级
/// </summary>
public class FirmwareManageUploadAddInput : BaseId
{
    /// <summary>
    /// 物实例Id
    /// </summary>
    [Required]
    public List<long> ModelThingIds { get; set; } = new();

    /// <summary>
    ///     命令类型：实时命令；离线命令
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    ///     超时时间（秒）
    /// </summary>
    public int TimeOut { get; set; }

    /// <summary>
    ///     重试次数
    /// </summary>
    public short Retries { get; set; }

    /// <summary>
    ///     重试间隔
    /// </summary>
    public short RetryInterval { get; set; }

    /// <summary>
    ///     命令失效时间 -命令失效时间范围为10分钟-43200分钟（30天）
    /// </summary>
    public short CommandExpirationTime { get; set; }

    /// <summary>
    ///     命令失效时间单位（分钟，小时，天）-命令失效时间范围为10分钟-43200分钟（30天）
    /// </summary>
    public string CommandExpirationTimeUnit { get; set; }
}

/// <summary>
/// 下载地址
/// </summary>
public class FirmwareManageDownloadInput
{
    /// <summary>
    /// 升级记录Id
    /// </summary>
    public long RecordId { get; set; }
}