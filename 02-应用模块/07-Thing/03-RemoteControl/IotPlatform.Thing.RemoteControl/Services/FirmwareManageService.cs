using Extras.MQTT;
using Furion;
using Furion.Schedule;
using Furion.TaskQueue;
using IotPlatform.Thing.RemoteControl.Entity;
using IotPlatform.Thing.RemoteControl.Job;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Systems.Core.File;
using Systems.Entity.Dto;
using Yitter.IdGenerator;
using DateTime = System.DateTime;

namespace IotPlatform.Thing.RemoteControl.Services;

/// <summary>
///     固件管理
///     版 本:V5.1.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2024-06-24
/// </summary>
[ApiDescriptionSettings("远程控制")]
public class FirmwareManageService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     文件服务
    /// </summary>
    private readonly FileService _fileService;

    /// <summary>
    ///     固件管理
    /// </summary>
    private readonly ISqlSugarRepository<FirmwareManage> _firmwareManage;

    /// <summary>
    ///     mqtt服务
    /// </summary>
    private readonly MqttService _mqttService;

    /// <summary>
    ///     上下文
    /// </summary>
    private readonly IHttpContextAccessor _httpContextAccessor;

    /// <summary>
    ///     调度工厂
    /// </summary>
    private readonly ISchedulerFactory _schedulerFactory;

    public FirmwareManageService(ISqlSugarRepository<FirmwareManage> firmwareManage, FileService fileService,
     MqttService mqttService, ISchedulerFactory schedulerFactory, IHttpContextAccessor httpContextAccessor)
    {
        _firmwareManage = firmwareManage;
        _fileService = fileService;
        _mqttService = mqttService;
        _schedulerFactory = schedulerFactory;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    ///     远程控制-固件管理-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/firmwareManage/page")]
    public async Task<SqlSugarPagedList<FirmwareManage>> FirmwareManagePage([FromQuery] BasePageInput input)
    {
        SqlSugarPagedList<FirmwareManage> firmwareManagePage = await _firmwareManage.AsQueryable()
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), u => u.Name.Contains(input.SearchValue) || u.Remark.Contains(input.SearchValue))
            .Includes(w => w.Model)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return firmwareManagePage;
    }

    /// <summary>
    ///     远程控制-固件管理-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/firmwareManage/detail")]
    public async Task<FirmwareManage> FirmwareManageDetail([FromQuery] BaseId input)
    {
        FirmwareManage? firmwareManage = await _firmwareManage.AsQueryable().FirstAsync(f => f.Id == input.Id);
        return firmwareManage;
    }

    /// <summary>
    ///     远程控制-固件管理-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/firmwareManage/add")]
    [UnitOfWork]
    public async Task FirmwareManageAdd(FirmwareManageAddInput input)
    {
        Model? model = await _firmwareManage.AsSugarClient().Queryable<Model>().FirstAsync(f => f.Id == input.ModelId);
        if (model == null)
        {
            throw Oops.Oh("物模型已经被删除！");
        }

        if (await _firmwareManage.IsAnyAsync(u => u.Name == input.Name))
        {
            throw Oops.Oh("固件名称已存在！");
        }

        var map = input.Adapt<FirmwareManage>();
        await _firmwareManage.InsertAsync(map);
    }

    /// <summary>
    ///     远程控制-固件管理-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/firmwareManage/delete")]
    [UnitOfWork]
    public async Task FirmwareManageUpdate(BaseId input)
    {
        FirmwareManage? firmwareManage = await _firmwareManage.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (firmwareManage == null)
        {
            throw Oops.Oh("固件已经被删除！");
        }
        // todo 验证是否有进行中的更新记录

        // 删除本地更新包
        await _fileService.DeleteFile(new DeleteFileInput { Id = firmwareManage.FileId });
        await _firmwareManage.DeleteAsync(firmwareManage);
    }

    /// <summary>
    ///     远程控制-固件管理-固件升级
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/firmwareManage/upload/add")]
    [UnitOfWork]
    public async Task FirmwareManageUploadAdd(FirmwareManageUploadAddInput input)
    {
        if (input.ModelThingIds.Count == 0)
        {
            throw Oops.Oh("请选择升级物实例！");
        }
        FirmwareManage? firmwareManage = await _firmwareManage.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.SysFile).FirstAsync();
        if (firmwareManage == null)
        {
            throw Oops.Oh("固件已经被删除！");
        }
        // 验证固件是否已上传
        if (firmwareManage.SysFile == null)
        {
            throw Oops.Oh("固件未上传！");
        }
        FirmwareManageDetail firmwareManageDetail = input.Adapt<FirmwareManageDetail>();
        firmwareManageDetail.Id = YitIdHelper.NextId();
        firmwareManageDetail.FirmwareManageUpdateRecords ??= new();
        firmwareManageDetail.FirmwareManageId = firmwareManage.Id;
        firmwareManageDetail.FirmwareManage = firmwareManage;
        // 验证物实例是否存在
        var thingList = await _firmwareManage.AsSugarClient().Queryable<ModelThing>()
        .Where(w => input.ModelThingIds.Contains(w.Id))
        .ToListAsync();
        // 遍历物实例
        foreach (var modelThing in thingList)
        {
            // 输出日志
            Console.WriteLine("开始验证物实例:" + modelThing.Name);

            // 验证物实例是否为网关
            if (modelThing.ModelType != ModelTypeEnum.Gateway)
            {
                throw Oops.Oh("暂不支持非网关物实例升级！");
            }
            // 验证网关是否存在
            var firmwareManageUpdateRecord = new FirmwareManageUpdateRecord()
            {
                ModelThingId = modelThing.Id, // 物实例id
                ModelThing = modelThing, // 物实例
                Id = YitIdHelper.NextId(), // 升级记录id
                Logs = new Dictionary<DateTime, string>(){
                    {DateTime.Now,"待通知升级"}
                }, // 日志
                Status = "待通知升级", // 状态
                FirmwareManageDetailId = firmwareManageDetail.Id, // 固件管理详情id
                CreatedTime = DateTime.Now // 创建时间
            };
            firmwareManageDetail.FirmwareManageUpdateRecords.Add(firmwareManageUpdateRecord);
        }
        // 
        await _firmwareManage.AsSugarClient().InsertNav(firmwareManageDetail)
            .Include(w => w.FirmwareManageUpdateRecords)
            .ExecuteCommandAsync();

        // 下发升级文件
        await DownFileTask(firmwareManageDetail);
    }

    /// <summary>
    ///     mqtt下发升级文件
    /// </summary>
    private async Task DownFileTask(FirmwareManageDetail firmwareDetail)
    {
        Console.WriteLine("开始下发升级指令");
        await TaskQueued.EnqueueAsync(async (provider, token) =>
        {
            foreach (var updateRecord in firmwareDetail.FirmwareManageUpdateRecords)
                try
                {
                    // 输入日志
                    Console.WriteLine("开始下发升级指令,记录Id:" + updateRecord.Id + "，网关Sn:" + updateRecord.ModelThing.GatewayExampleModel?.Sn);

                    updateRecord.Logs ??= new Dictionary<DateTime, string>();
                    bool result = Convert.ToBoolean(await _mqttService.PublishRpc("ota", JsonConvert.SerializeObject(new
                    {
                        firmwareDetail.FirmwareManage.Name,
                        firmwareDetail.FirmwareManage.Version,
                        CreateTime = DateTime.Now,
                        RecordId = updateRecord.Id
                    }), updateRecord.ModelThing.GatewayExampleModel?.Sn, firmwareDetail.TimeOut));

                    // 输出结果
                    Console.WriteLine("升级指令下发结果:" + result);
                    if (result)
                    {
                        // 添加日志 
                        Console.WriteLine("升级指令已下发");
                        updateRecord.Logs.Add(DateTime.Now, "升级指令已下发");
                        updateRecord.Status = "升级指令已下发";
                        updateRecord.StatusUpdateTime = DateTime.Now;
                    }
                    else
                    {
                        // 添加日志 
                        Console.WriteLine("升级指令下发失败");

                        updateRecord.Status = "升级指令下发失败";
                        updateRecord.Logs.Add(DateTime.Now, "升级指令下发失败");
                        updateRecord.StatusUpdateTime = DateTime.Now;
                        await CreateJobTask(firmwareDetail, updateRecord);
                    }
                }
                catch (Exception e)
                {
                    // 添加日志 
                    Console.WriteLine("升级指令下发异常:" + e.Message);

                    updateRecord.Status = "升级指令下发失败";
                    updateRecord.Logs.Add(DateTime.Now, "升级指令下发失败：" + e.Message);
                    updateRecord.StatusUpdateTime = DateTime.Now;
                    await CreateJobTask(firmwareDetail, updateRecord);
                }
            // 更新升级记录
            Console.WriteLine("更新升级记录");

            await _firmwareManage.AsSugarClient().Updateable(firmwareDetail.FirmwareManageUpdateRecords)
                .ExecuteCommandAsync(token);
        }, 1000);
    }

    /// <summary>
    ///     创建ota升级任务
    /// </summary>
    /// <param name="firmwareDetail"></param>
    /// <param name="record"></param>
    private async Task CreateJobTask(FirmwareManageDetail firmwareDetail, FirmwareManageUpdateRecord record)
    {
        var jobBuilder = JobBuilder.Create<ExecuteScriptJob>()
                .SetJobId(record.Id.ToString()) // 作业 Id
                .SetGroupName(firmwareDetail.FirmwareManage?.Name) // 作业组名称
                .SetJobType("FengLink.Application", "FengLink.Application.ExportDatabaseJob") // 作业类型，支持多个重载
                .SetJobType<ExecuteScriptJob>() // 作业类型，支持多个重载
                .SetJobType(typeof(ExecuteScriptJob)) // 作业类型，支持多个重载
                .SetDescription(record.ModelThing?.Name + "——ota升级作业") // 作业描述
                .SetConcurrent(false) // 并行还是串行方式，false 为 串行
                .SetIncludeAnnotations(true) // 是否扫描 IJob 类型的触发器特性，true 为 扫描
                .SetProperties(new Dictionary<string, object>
                {
                    {"recordId", record.Id}
                })
            ;
        _schedulerFactory.TryAddJob(jobBuilder, new[]
        {
            Triggers.PeriodSeconds(firmwareDetail.RetryInterval).SetTriggerId(record.Id.ToString())
                .SetMaxNumberOfRuns(firmwareDetail.Retries)
        }, out var scheduler);
        scheduler.UpdateDetail(builder =>
        {
            builder.SetDescription(record.ModelThing?.Name + "—ota升级");
            builder.SetGroupName(firmwareDetail.FirmwareManage.Name);
        });
        scheduler.Persist();
    }

    /// <summary>
    ///     远程控制-固件管理-下载
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/firmwareManage/upload/download")]
    [AllowAnonymous]
    [NonUnify]
    public async Task<IActionResult> FirmwareManageDownload([FromQuery] FirmwareManageDownloadInput input)
    {
        // 打印日志
        Console.WriteLine("开始下载固件");

        // 获取升级记录
        var updateRecord = await _firmwareManage.AsSugarClient().Queryable<FirmwareManageUpdateRecord>()
            .Where(w => w.Id == input.RecordId)
            .Includes(w => w.FirmwareManageDetail, w => w.FirmwareManage, w => w.SysFile)
            .Includes(w => w.ModelThing)
            .FirstAsync();

        if (updateRecord == null)
            throw Oops.Oh("暂无权限更新！");

        var file = updateRecord.FirmwareManageDetail.FirmwareManage.SysFile;
        var filePath = Path.Combine(file?.FilePath, file.Id + file.Suffix);
        var path = Path.Combine(App.WebHostEnvironment.ContentRootPath, filePath);

        // 获取文件信息
        var fileInfo = new FileInfo(path);
        if (!fileInfo.Exists)
        {
            throw Oops.Oh("文件不存在！");
        }

        updateRecord.Status = "升级完成";
        updateRecord.Logs.Add(DateTime.Now, "升级完成");
        await _firmwareManage.AsSugarClient().Updateable(updateRecord)
            .UpdateColumns(w => new { w.Status, w.Logs })
            .ExecuteCommandAsync();

        // 设置响应头
        var response = _httpContextAccessor.HttpContext.Response;
        response.Headers.ContentLength = fileInfo.Length;
        response.Headers.ContentDisposition = $"attachment; filename=\"{file?.FileName}{file?.Suffix}\"; filename*=UTF-8''{Uri.EscapeDataString(file?.FileName + file?.Suffix ?? "firmware.bin")}";
        response.Headers.ContentType = "application/octet-stream";

        // 使用 FileStreamResult 进行流式处理
        var stream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.Read);
        return new FileStreamResult(stream, "application/octet-stream")
        {
            FileDownloadName = file?.FileName + file?.Suffix ?? "firmware.rar",
        };
    }
}