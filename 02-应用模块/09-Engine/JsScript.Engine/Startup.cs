namespace JsScript.Engine;

/// <summary>
/// </summary>
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        JintReadFileContent.dbFile = File.ReadAllText(AppDomain.CurrentDomain.BaseDirectory + "/ScriptJs/db.js");
        JintReadFileContent.httpFile = File.ReadAllText(AppDomain.CurrentDomain.BaseDirectory + "/ScriptJs/http.js");
    }
}