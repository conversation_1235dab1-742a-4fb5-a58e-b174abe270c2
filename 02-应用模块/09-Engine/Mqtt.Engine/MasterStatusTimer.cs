namespace Mqtt.Engine;

/// <summary>
///     定时监听主节点连接状态
/// </summary>
public class MasterStatusTimer : BackgroundService
{
    private readonly ILogger<MasterStatusTimer> _logger;
    private readonly Crontab _crontab;
    private readonly IServiceScopeFactory _scopeFactory;

    public MasterStatusTimer(ILogger<MasterStatusTimer> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
        _crontab = Crontab.Parse("0/30 * * ? * *", CronStringFormat.WithSeconds);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay((int) _crontab.GetSleepMilliseconds(DateTime.Now), stoppingToken);
            TaskFactory taskFactory = new(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                try
                {
                    using IServiceScope scope = _scopeFactory.CreateScope();
                    IServiceProvider services = scope.ServiceProvider;
                    await Scoped.CreateAsync(async (_, scope) =>
                    {
                        MasterClient? client = services.GetService<MasterClient>();
                        if (client.Client is {IsConnected: false})
                        {
                            await Task.Delay(1000 * 10, stoppingToken);
                            string? ip = App.Configuration["Mqtt:Ip"];
                            Log.Warning($"当前时间:【{DateTime.Now}】,监听到MQTT离线了，发起ping:【{ip}】");
                            Ping pingSender = new();
                            PingReply reply = pingSender.Send(ip);
                            Log.Warning($"当前时间:【{DateTime.Now}】,监听到MQTT离线了，ping:【{ip}】 结果:【{reply.Status == IPStatus.Success}】");
                            if (reply.Status == IPStatus.Success && !client.Client.IsConnected)
                            {
                                client.ReConnect();
                                Log.Warning($"当前时间:【{DateTime.Now}】,释放Master MQTT,重新建立新的连接!");
                            }
                        }
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError("定时监听主节点连接状态Error:【{ExMessage}】", ex.Message);
                }
            }, stoppingToken);
        }
    }
}