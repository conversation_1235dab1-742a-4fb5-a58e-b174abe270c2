using Furion.Shapeless;
using System.Reflection;
using System.Text.Json;

namespace Common.Security;

/// <summary>
///     种子数据工具类
/// </summary>
public class SeedDataUtil
{
    public static IEnumerable<T> GetSeedData<T>(string jsonName, Func<PropertyInfo, object, object> valueProvider = null)
    {
        string basePath = AppContext.BaseDirectory; // 获取项目目录
        // 拼接 JSON 文件的路径
        string filePath = Path.Combine(basePath + "SeedDataJson/", jsonName);

        // 读取 JSON 文件内容
        if (!File.Exists(filePath))
        {
            return Enumerable.Empty<T>();
        }

        string jsonContent = File.ReadAllText(filePath);
        if (string.IsNullOrEmpty(jsonContent))
        {
            return Enumerable.Empty<T>();
        }

        try
        {
            // 使用System.Text.Json解析JSON
            using JsonDocument doc = JsonDocument.Parse(jsonContent);

            // 检查是否存在RECORDS节点（符合Navicat导出格式）
            if (doc.RootElement.TryGetProperty("RECORDS", out JsonElement recordsElement) &&
                recordsElement.ValueKind == JsonValueKind.Array)
            {
                List<T> result = new List<T>();

                // 获取类型的所有可写属性
                PropertyInfo[] properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                    .Where(p => p.CanWrite).ToArray();

                // 遍历数组中的每个记录
                foreach (JsonElement record in recordsElement.EnumerateArray())
                {
                    T item = Activator.CreateInstance<T>();

                    // 为每个属性赋值
                    foreach (PropertyInfo property in properties)
                    {
                        // 检查JSON记录中是否包含属性
                        if (record.TryGetProperty(property.Name, out JsonElement propValue))
                        {
                            try
                            {
                                object value = null;

                                // 根据属性类型进行转换
                                if (propValue.ValueKind != JsonValueKind.Null)
                                {
                                    if (property.PropertyType == typeof(string))
                                    {
                                        value = propValue.GetString();
                                    }
                                    else if (property.PropertyType == typeof(int) || property.PropertyType == typeof(int?))
                                    {
                                        value = propValue.TryGetInt32(out int intValue) ? intValue : default(int?);
                                    }
                                    else if (property.PropertyType == typeof(long) || property.PropertyType == typeof(long?))
                                    {
                                        if (propValue.ValueKind == JsonValueKind.Number)
                                        {
                                            value = propValue.TryGetInt64(out long longValue) ? longValue : default(long?);
                                        }
                                        else if (propValue.ValueKind == JsonValueKind.String)
                                        {
                                            string strValue = propValue.GetString();
                                            value = long.TryParse(strValue, out long longValue) ? longValue : default(long?);
                                        }
                                        else
                                        {
                                            value = default(long?);
                                        }
                                    }
                                    else if (property.PropertyType == typeof(double) || property.PropertyType == typeof(double?))
                                    {
                                        value = propValue.TryGetDouble(out double doubleValue) ? doubleValue : default(double?);
                                    }
                                    else if (property.PropertyType == typeof(decimal) || property.PropertyType == typeof(decimal?))
                                    {
                                        value = propValue.TryGetDecimal(out decimal decimalValue) ? decimalValue : default(decimal?);
                                    }
                                    else if (property.PropertyType == typeof(bool) || property.PropertyType == typeof(bool?))
                                    {
                                        if (propValue.ValueKind == JsonValueKind.True)
                                        {
                                            value = true;
                                        }
                                        else if (propValue.ValueKind == JsonValueKind.False)
                                        {
                                            value = false;
                                        }
                                        else if (propValue.ValueKind == JsonValueKind.Number && propValue.TryGetInt32(out int boolNumValue))
                                        {
                                            value = boolNumValue != 0;
                                        }
                                        else
                                        {
                                            value = default(bool?);
                                        }
                                    }
                                    else if (property.PropertyType == typeof(DateTime) || property.PropertyType == typeof(DateTime?))
                                    {
                                        value = propValue.TryGetDateTime(out DateTime dateTimeValue) ? dateTimeValue : default(DateTime?);
                                    }
                                    else if (property.PropertyType == typeof(Guid) || property.PropertyType == typeof(Guid?))
                                    {
                                        value = propValue.TryGetGuid(out Guid guidValue) ? guidValue : default(Guid?);
                                    }
                                    else if (property.PropertyType.IsEnum)
                                    {
                                        // 处理枚举类型
                                        if (propValue.ValueKind == JsonValueKind.Number && propValue.TryGetInt32(out int enumValue))
                                        {
                                            value = Enum.ToObject(property.PropertyType, enumValue);
                                        }
                                        else if (propValue.ValueKind == JsonValueKind.String)
                                        {
                                            string enumString = propValue.GetString();
                                            if (!string.IsNullOrEmpty(enumString))
                                            {
                                                value = Enum.Parse(property.PropertyType, enumString);
                                            }
                                        }
                                    }
                                    else
                                    {
                                        // 复杂对象或未知类型，保留原始JSON字符串
                                        value = propValue.GetRawText();
                                    }
                                }

                                // 使用自定义值提供者处理特殊类型
                                if (valueProvider != null)
                                {
                                    value = valueProvider(property, value);
                                }

                                // 设置属性值
                                if (value != null)
                                {
                                    property.SetValue(item, value);
                                }
                            }
                            catch (Exception)
                            {
                                // 忽略单个属性设置失败的情况，继续处理其他属性
                                continue;
                            }
                        }
                    }

                    result.Add(item);
                }

                return result;
            }

            // 兼容处理：如果没有RECORDS节点，尝试直接解析整个JSON作为对象数组
            return JsonSerializer.Deserialize<List<T>>(jsonContent) ?? Enumerable.Empty<T>();
        }
        catch (Exception)
        {
            // 发生异常时返回空集合
            return Enumerable.Empty<T>();
        }
    }
}

/// <summary>
///     种子数据格式实体类,遵循Navicat导出json格式
/// </summary>
/// <typeparam name="T"></typeparam>
public class SeedDataRecords<T>
{
    /// <summary>
    ///     数据
    /// </summary>
    public List<T> Records { get; set; }
}
