using Extras.DatabaseAccessor.SqlSugar.Repositories;
using IotPlatform.Core.Enum;
using SqlSugar;
using Systems.Entity;

namespace Common.Core.Job;

/// <summary>
///     作业持久化（数据库）
/// </summary>
public class DbJobPersistence : IJobPersistence
{
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public DbJobPersistence(IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScopeFactory = serviceScopeFactory;
    }

    /// <summary>
    ///     作业调度服务启动时
    /// </summary>
    /// <param name="stoppingToken"></param>
    /// <returns></returns>
    /// <exception cref="NotSupportedException"></exception>
    public async Task<IEnumerable<SchedulerBuilder>> PreloadAsync(CancellationToken stoppingToken)
    {
        using IServiceScope scope = _serviceScopeFactory.CreateScope();
        ISqlSugarRepository<SysJobDetail> jobDetailRep = scope.ServiceProvider.GetRequiredService<ISqlSugarRepository<SysJobDetail>>();
        ISqlSugarRepository<SysJobTrigger> jobTriggerRep = scope.ServiceProvider.GetRequiredService<ISqlSugarRepository<SysJobTrigger>>();

        // 获取所有定义的作业
        List<SchedulerBuilder> allJobs = App.EffectiveTypes.ScanToBuilders().Where(w => w.GetEnumerable().Any()).ToList();
        // 若数据库不存在任何作业，则直接返回
        if (!await jobDetailRep.CopyNew().IsAnyAsync(u => true, stoppingToken))
        {
            return allJobs;
        }

        // 遍历所有定义的作业
        foreach (SchedulerBuilder? schedulerBuilder in allJobs)
        {
            // 获取作业信息构建器
            JobBuilder? jobBuilder = schedulerBuilder.GetJobBuilder();
            // 加载数据库数据
            SysJobDetail? dbDetail = await jobDetailRep.CopyNew().GetFirstAsync(u => u.JobId == jobBuilder.JobId, stoppingToken);
            if (dbDetail == null)
            {
                continue;
            }

            // 同步数据库数据
            jobBuilder.LoadFrom(dbDetail);

            // 获取作业的所有数据库的触发器
            List<SysJobTrigger>? dbTriggers = await jobTriggerRep.CopyNew().GetListAsync(u => u.JobId == jobBuilder.JobId, stoppingToken);
            // 遍历所有作业触发器
            foreach ((JobBuilder _, TriggerBuilder? triggerBuilder) in schedulerBuilder.GetEnumerable())
            {
                // 加载数据库数据
                SysJobTrigger? dbTrigger = dbTriggers.FirstOrDefault(u => u.JobId == jobBuilder.JobId && u.TriggerId == triggerBuilder.TriggerId);
                if (dbTrigger == null)
                {
                    continue;
                }

                triggerBuilder.LoadFrom(dbTrigger).Updated(); // 标记更新
            }

            // 遍历所有非编译时定义的触发器加入到作业中
            foreach (TriggerBuilder? triggerBuilder in from dbTrigger in dbTriggers
                     where schedulerBuilder.GetTriggerBuilder(dbTrigger.TriggerId)?.JobId != jobBuilder.JobId
                     select TriggerBuilder.Create(dbTrigger.TriggerId).LoadFrom(dbTrigger))
            {
                schedulerBuilder.AddTriggerBuilder(triggerBuilder); // 先添加
                triggerBuilder.Updated(); // 再标记更新
            }

            // 标记更新
            schedulerBuilder.Updated();
        }

        // 获取数据库所有通过脚本创建的作业
        List<SysJobDetail>? allDbScriptJobs = await jobDetailRep.CopyNew().GetListAsync(u => u.CreateType != JobCreateTypeEnum.BuiltIn, stoppingToken);
        foreach (JobBuilder? jobBuilder in from dbDetail in allDbScriptJobs let jobType = typeof(HttpJob) select JobBuilder.Create(jobType).LoadFrom(dbDetail))
        {
            // 强行设置为不扫描 IJob 实现类 [Trigger] 特性触发器，否则 SchedulerBuilder.Create 会再次扫描，导致重复添加同名触发器
            jobBuilder.SetIncludeAnnotations(false);
            // 获取作业的所有数据库的触发器加入到作业中
            List<SysJobTrigger>? dbTriggers = await jobTriggerRep.CopyNew().GetListAsync(u => u.JobId == jobBuilder.JobId, stoppingToken);
            IEnumerable<TriggerBuilder> triggerBuilders = dbTriggers.Select(u => TriggerBuilder.Create(u.TriggerId).LoadFrom(u).Updated());
            SchedulerBuilder? schedulerBuilder = SchedulerBuilder.Create(jobBuilder, triggerBuilders.ToArray());
            // 标记更新
            schedulerBuilder.Updated();
            allJobs.Add(schedulerBuilder);
        }

        return allJobs;
    }

    /// <summary>
    ///     作业计划初始化通知
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="stoppingToken"></param>
    /// <returns></returns>
    public Task<SchedulerBuilder> OnLoadingAsync(SchedulerBuilder builder, CancellationToken stoppingToken)
    {
        return Task.FromResult(builder);
    }

    /// <summary>
    ///     作业计划Scheduler的JobDetail变化时
    /// </summary>
    /// <param name="context"></param>
    public async Task OnChangedAsync(PersistenceContext context)
    {
        using IServiceScope scope = _serviceScopeFactory.CreateScope();
        ISqlSugarClient db = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>();
        SysJobDetail jobDetail = context.JobDetail.Adapt<SysJobDetail>();
        switch (context.Behavior)
        {
            case PersistenceBehavior.Appended:
                await db.CopyNew().Insertable(jobDetail).ExecuteCommandAsync();
                break;

            case PersistenceBehavior.Updated:
                await db.CopyNew().Updateable(jobDetail).WhereColumns(u => new {u.JobId}).IgnoreColumns(u => new {u.Id, u.CreateType, u.ScriptCode}).ExecuteCommandAsync();
                break;

            case PersistenceBehavior.Removed:
                await db.CopyNew().Deleteable<SysJobDetail>().Where(u => u.JobId == jobDetail.JobId).ExecuteCommandAsync();
                break;
        }
    }

    /// <summary>
    ///     作业计划Scheduler的触发器Trigger变化时
    /// </summary>
    /// <param name="context"></param>
    public async Task OnTriggerChangedAsync(PersistenceTriggerContext context)
    {
        using IServiceScope scope = _serviceScopeFactory.CreateScope();
        ISqlSugarClient db = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>();
        SysJobTrigger jobTrigger = context.Trigger.Adapt<SysJobTrigger>();
        switch (context.Behavior)
        {
            case PersistenceBehavior.Appended:
                await db.CopyNew().Insertable(jobTrigger).ExecuteCommandAsync();
                break;

            case PersistenceBehavior.Updated:
                await db.CopyNew().Updateable(jobTrigger).WhereColumns(u => new {u.TriggerId, u.JobId}).IgnoreColumns(u => new {u.Id}).ExecuteCommandAsync();
                break;

            case PersistenceBehavior.Removed:
                await db.CopyNew().Deleteable<SysJobTrigger>().Where(u => u.TriggerId == jobTrigger.TriggerId && u.JobId == jobTrigger.JobId).ExecuteCommandAsync();
                break;
        }
    }

    /// <summary>
    /// 作业触发器运行记录
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task OnExecutionRecordAsync(PersistenceExecutionRecordContext context)
    {
        using IServiceScope scope = _serviceScopeFactory.CreateScope();
        ISqlSugarClient db = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>();
        SysJobTriggerRecord jobTriggerRecord = context.Timeline.Adapt<SysJobTriggerRecord>();
        await db.CopyNew().Insertable(jobTriggerRecord).ExecuteCommandAsync();
        throw new NotImplementedException();
    }
}