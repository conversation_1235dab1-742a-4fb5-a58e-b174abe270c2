import { Skeleton } from "@/components/ui/skeleton"

export default function Loading() {
  return (
    <div className="container mx-auto py-8">
      <div className="grid grid-cols-1 gap-6">
        <div className="border rounded-lg border-t-4 border-t-emerald-500">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Skeleton className="h-5 w-24 mb-2" />
                <div className="flex mt-2">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-10 ml-2" />
                </div>
              </div>
              <div>
                <Skeleton className="h-5 w-24 mb-2" />
                <div className="flex mt-2">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-10 ml-2" />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <div>
                <Skeleton className="h-5 w-24 mb-2" />
                <Skeleton className="h-10 w-full mt-2" />
              </div>
              <div>
                <Skeleton className="h-5 w-24 mb-2" />
                <Skeleton className="h-10 w-full mt-2" />
              </div>
            </div>
          </div>
        </div>

        <div className="border rounded-lg border-t-4 border-t-emerald-500">
          <Skeleton className="h-12 w-full rounded-t-lg" />
          <div className="p-6">
            <div className="space-y-4">
              <div>
                <Skeleton className="h-5 w-32 mb-2" />
                <div className="flex mt-2">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-32 ml-2" />
                </div>
              </div>

              <div>
                <Skeleton className="h-5 w-24 mb-2" />
                <div className="flex mt-2">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-10 ml-2" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
