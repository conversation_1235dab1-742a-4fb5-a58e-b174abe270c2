import { PingTester } from '@/components/debug-tools/ping-tester'
import { DebugToolLayout } from '@/components/debug-tools/debug-tool-layout'
import {
  Radio,
  Server,
  Globe,
  Network,
  BookOpen,
  AlertCircle,
  Clock,
  Wifi,
} from 'lucide-react'
import { useState } from 'react'

export default function PingTestPageContent() {
  const [selectedHost, setSelectedHost] = useState<string>('')

  // 预设的主机列表
  const commonHosts = [
    {
      name: '******* (Google DNS)',
      value: '*******',
      icon: <Server className="h-4 w-4 mr-2 text-blue-500" />,
    },
    {
      name: '******* (Cloudflare DNS)',
      value: '*******',
      icon: <Server className="h-4 w-4 mr-2 text-purple-500" />,
    },
    {
      name: '*********** (默认网关)',
      value: '***********',
      icon: <Network className="h-4 w-4 mr-2 text-green-500" />,
    },
    {
      name: 'baidu.com',
      value: 'baidu.com',
      icon: <Globe className="h-4 w-4 mr-2 text-red-500" />,
    },
    {
      name: 'github.com',
      value: 'github.com',
      icon: <Globe className="h-4 w-4 mr-2 text-gray-500" />,
    },
  ]

  // 历史记录（通常应该从存储中获取，这里用硬编码）
  const historyHosts = [
    {
      name: '服务器A (********)',
      value: '********',
      icon: <Server className="h-4 w-4 mr-2 text-orange-500" />,
    },
    {
      name: '数据库服务器 (********)',
      value: '********',
      icon: <Server className="h-4 w-4 mr-2 text-blue-500" />,
    },
    {
      name: '备份服务器 (*************)',
      value: '*************',
      icon: <Server className="h-4 w-4 mr-2 text-green-500" />,
    },
  ]

  const handleHostSelect = (host: string) => {
    setSelectedHost(host)
  }

  return (
    <DebugToolLayout
      title="Ping测试"
      description="测试网络连接和延迟，检查主机可达性"
      toolIcon={<Radio className="h-8 w-8 text-green-500" />}
      sidebar={
        <div className="space-y-6">
          <div className="bg-card rounded-lg p-3 border shadow-sm">
            <h3 className="mb-3 text-sm font-medium flex items-center">
              <Wifi className="h-4 w-4 mr-2 text-blue-500" />
              常用主机
            </h3>
            <ul className="space-y-1 text-sm">
              {commonHosts.map((host, index) => (
                <li
                  key={index}
                  className={`rounded-md px-3 py-2 cursor-pointer flex items-center transition-colors
                    ${
                      selectedHost === host.value
                        ? 'bg-accent text-accent-foreground font-medium shadow-sm'
                        : 'hover:bg-accent/50'
                    }`}
                  onClick={() => handleHostSelect(host.value)}>
                  {host.icon}
                  {host.name}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-card rounded-lg p-3 border shadow-sm">
            <h3 className="mb-3 text-sm font-medium flex items-center">
              <Clock className="h-4 w-4 mr-2 text-blue-500" />
              历史记录
            </h3>
            <ul className="space-y-1 text-sm">
              {historyHosts.map((host, index) => (
                <li
                  key={index}
                  className={`rounded-md px-3 py-2 cursor-pointer flex items-center transition-colors
                    ${
                      selectedHost === host.value
                        ? 'bg-accent text-accent-foreground font-medium shadow-sm'
                        : 'hover:bg-accent/50'
                    }`}
                  onClick={() => handleHostSelect(host.value)}>
                  {host.icon}
                  {host.name}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-card rounded-lg p-3 border shadow-sm">
            <h3 className="mb-3 text-sm font-medium flex items-center">
              <BookOpen className="h-4 w-4 mr-2 text-blue-500" />
              帮助
            </h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Radio className="h-4 w-4 mr-2 text-blue-500" />
                Ping 命令参数
              </li>
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <AlertCircle className="h-4 w-4 mr-2 text-orange-500" />
                常见网络问题
              </li>
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Clock className="h-4 w-4 mr-2 text-green-500" />
                延迟分析
              </li>
            </ul>
          </div>
        </div>
      }>
      <PingTester selectedHost={selectedHost} />
    </DebugToolLayout>
  )
}
