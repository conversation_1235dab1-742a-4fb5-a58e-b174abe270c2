/**
 * 迁移自: 新创建文件 (补充缺失的debug-tools页面)
 * 迁移时间: 2025-01-14T11:00:00.000Z
 *
 * 迁移说明:
 * - 新创建的设备发现页面，补充debug-tools模块的完整性
 * - 使用react-router-dom进行路由导航
 * - 基于现有DebugToolLayout布局保持一致性
 * - 提供局域网设备扫描和发现功能
 *
 * 注意: 本文件遵循Vite+React迁移规范，保持功能完整性
 */

import { useState, useEffect, useRef } from 'react'
import { DebugToolLayout } from '@/components/debug-tools/debug-tool-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Radio,
  Play,
  Square,
  RefreshCw,
  Search,
  Wifi,
  Monitor,
  Smartphone,
  Router,
  Server,
  HardDrive,
  Printer,
  Camera,
  Globe,
  MapPin,
  Clock,
  Activity,
  AlertCircle,
  CheckCircle,
  Copy,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from '@/components/ui/use-toast'

interface DiscoveredDevice {
  ip: string
  mac: string
  hostname: string
  vendor: string
  deviceType: string
  ports: number[]
  responseTime: number
  lastSeen: string
  status: 'online' | 'offline'
}

interface ScanProgress {
  isScanning: boolean
  progress: number
  currentIP: string
  foundDevices: number
}

export default function DeviceDiscoveryPage() {
  const [devices, setDevices] = useState<DiscoveredDevice[]>([])
  const [scanProgress, setScanProgress] = useState<ScanProgress>({
    isScanning: false,
    progress: 0,
    currentIP: '',
    foundDevices: 0,
  })
  const [scanRange, setScanRange] = useState('***********-254')
  const [scanType, setScanType] = useState('ping')
  const [portScanEnabled, setPortScanEnabled] = useState(false)
  const [customPorts, setCustomPorts] = useState('22,23,80,443,8080')

  const abortControllerRef = useRef<AbortController | null>(null)

  // 设备类型图标映射
  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'router':
        return <Router className="h-4 w-4" />
      case 'computer':
        return <Monitor className="h-4 w-4" />
      case 'mobile':
        return <Smartphone className="h-4 w-4" />
      case 'server':
        return <Server className="h-4 w-4" />
      case 'printer':
        return <Printer className="h-4 w-4" />
      case 'camera':
        return <Camera className="h-4 w-4" />
      case 'storage':
        return <HardDrive className="h-4 w-4" />
      default:
        return <Globe className="h-4 w-4" />
    }
  }

  // 获取设备类型颜色
  const getDeviceTypeColor = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'router':
        return 'bg-blue-100 text-blue-800'
      case 'computer':
        return 'bg-green-100 text-green-800'
      case 'mobile':
        return 'bg-purple-100 text-purple-800'
      case 'server':
        return 'bg-red-100 text-red-800'
      case 'printer':
        return 'bg-yellow-100 text-yellow-800'
      case 'camera':
        return 'bg-indigo-100 text-indigo-800'
      case 'storage':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 开始扫描
  const startScan = async () => {
    if (scanProgress.isScanning) return

    setScanProgress({
      isScanning: true,
      progress: 0,
      currentIP: '',
      foundDevices: 0,
    })
    setDevices([])

    // 创建新的AbortController
    abortControllerRef.current = new AbortController()

    try {
      await simulateDeviceScan()
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('扫描已取消')
      } else {
        console.error('扫描失败:', error)
      }
    } finally {
      setScanProgress({
        isScanning: false,
        progress: 0,
        currentIP: '',
        foundDevices: 0,
      })
    }
  }

  // 停止扫描
  const stopScan = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setScanProgress({
      isScanning: false,
      progress: 0,
      currentIP: '',
      foundDevices: 0,
    })
  }

  // 模拟设备扫描
  const simulateDeviceScan = async () => {
    const signal = abortControllerRef.current?.signal
    const [startIP, endIP] = scanRange.split('-')
    const baseIP = startIP.substring(0, startIP.lastIndexOf('.') + 1)
    const startNum = parseInt(startIP.substring(startIP.lastIndexOf('.') + 1))
    const endNum = parseInt(endIP)

    const totalIPs = endNum - startNum + 1
    const foundDevices: DiscoveredDevice[] = []

    // 模拟设备数据
    const mockDevices = [
      {
        ip: `${baseIP}1`,
        mac: '00:11:22:33:44:55',
        hostname: 'Router',
        vendor: 'TP-Link',
        deviceType: 'router',
        ports: [80, 443],
      },
      {
        ip: `${baseIP}100`,
        mac: '00:AA:BB:CC:DD:EE',
        hostname: 'Desktop-PC',
        vendor: 'Dell',
        deviceType: 'computer',
        ports: [22, 3389],
      },
      {
        ip: `${baseIP}150`,
        mac: '00:FF:EE:DD:CC:BB',
        hostname: 'iPhone',
        vendor: 'Apple',
        deviceType: 'mobile',
        ports: [],
      },
      {
        ip: `${baseIP}200`,
        mac: '00:12:34:56:78:90',
        hostname: 'NAS-Server',
        vendor: 'Synology',
        deviceType: 'storage',
        ports: [80, 443, 5000, 5001],
      },
    ]

    for (let i = startNum; i <= endNum; i++) {
      if (signal?.aborted) throw new Error('AbortError')

      const currentIP = `${baseIP}${i}`
      const progress = ((i - startNum + 1) / totalIPs) * 100

      setScanProgress((prev) => ({
        ...prev,
        progress,
        currentIP,
      }))

      // 模拟扫描延迟
      await new Promise((resolve) => setTimeout(resolve, 50))

      // 随机发现设备
      const mockDevice = mockDevices.find((d) => d.ip === currentIP)
      if (mockDevice || Math.random() < 0.05) {
        // 5%概率发现设备
        const device: DiscoveredDevice = mockDevice || {
          ip: currentIP,
          mac: `00:${Math.random()
            .toString(16)
            .substr(2, 2)
            .toUpperCase()}:${Math.random()
            .toString(16)
            .substr(2, 2)
            .toUpperCase()}:${Math.random()
            .toString(16)
            .substr(2, 2)
            .toUpperCase()}:${Math.random()
            .toString(16)
            .substr(2, 2)
            .toUpperCase()}:${Math.random()
            .toString(16)
            .substr(2, 2)
            .toUpperCase()}`,
          hostname: `Device-${i}`,
          vendor: 'Unknown',
          deviceType: 'unknown',
          ports: [],
        }

        const discoveredDevice: DiscoveredDevice = {
          ...device,
          responseTime: Math.floor(Math.random() * 100) + 1,
          lastSeen: new Date().toISOString(),
          status: 'online',
        }

        foundDevices.push(discoveredDevice)
        setDevices([...foundDevices])
        setScanProgress((prev) => ({
          ...prev,
          foundDevices: foundDevices.length,
        }))
      }
    }
  }

  // 复制IP地址
  const copyIP = (ip: string) => {
    navigator.clipboard.writeText(ip)
    toast({
      title: '已复制',
      description: `IP地址 ${ip} 已复制到剪贴板`,
    })
  }

  // 刷新设备状态
  const refreshDevices = () => {
    setDevices((prev) =>
      prev.map((device) => ({
        ...device,
        status: Math.random() > 0.1 ? 'online' : 'offline',
        responseTime: Math.floor(Math.random() * 100) + 1,
        lastSeen: new Date().toISOString(),
      }))
    )
  }

  return (
    <DebugToolLayout
      title="设备发现"
      description="扫描和发现局域网内的设备"
      toolIcon={<Radio className="h-6 w-6" />}
      sidebar={
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-sm mb-2">扫描类型</h3>
            <div className="text-xs text-muted-foreground space-y-2">
              <p>• Ping扫描：使用ICMP协议检测设备</p>
              <p>• ARP扫描：通过ARP表发现设备</p>
              <p>• TCP扫描：扫描常用端口发现设备</p>
              <p>• UDP扫描：使用UDP协议发现设备</p>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-sm mb-2">常用端口</h3>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• 22: SSH</p>
              <p>• 23: Telnet</p>
              <p>• 80: HTTP</p>
              <p>• 443: HTTPS</p>
              <p>• 3389: RDP</p>
              <p>• 8080: HTTP代理</p>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-sm mb-2">注意事项</h3>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• 扫描可能被防火墙阻止</p>
              <p>• 某些设备可能不响应ping</p>
              <p>• 扫描速度取决于网络状况</p>
              <p>• 请遵守网络使用规范</p>
            </div>
          </div>
        </div>
      }>
      <div className="space-y-6">
        {/* 扫描配置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              扫描配置
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">IP范围</label>
                <Input
                  value={scanRange}
                  onChange={(e) => setScanRange(e.target.value)}
                  placeholder="***********-254"
                  disabled={scanProgress.isScanning}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  扫描类型
                </label>
                <Select
                  value={scanType}
                  onValueChange={setScanType}
                  disabled={scanProgress.isScanning}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ping">Ping扫描</SelectItem>
                    <SelectItem value="arp">ARP扫描</SelectItem>
                    <SelectItem value="tcp">TCP扫描</SelectItem>
                    <SelectItem value="udp">UDP扫描</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={portScanEnabled}
                  onChange={(e) => setPortScanEnabled(e.target.checked)}
                  disabled={scanProgress.isScanning}
                />
                <span className="text-sm">启用端口扫描</span>
              </label>
              {portScanEnabled && (
                <Input
                  value={customPorts}
                  onChange={(e) => setCustomPorts(e.target.value)}
                  placeholder="22,80,443,8080"
                  className="flex-1"
                  disabled={scanProgress.isScanning}
                />
              )}
            </div>

            <div className="flex items-center gap-2">
              {!scanProgress.isScanning ? (
                <Button onClick={startScan} className="flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  开始扫描
                </Button>
              ) : (
                <Button
                  onClick={stopScan}
                  variant="destructive"
                  className="flex items-center gap-2">
                  <Square className="h-4 w-4" />
                  停止扫描
                </Button>
              )}

              <Button
                onClick={refreshDevices}
                variant="outline"
                disabled={devices.length === 0}
                className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                刷新状态
              </Button>
            </div>

            {/* 扫描进度 */}
            {scanProgress.isScanning && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>扫描进度: {scanProgress.currentIP}</span>
                  <span>已发现: {scanProgress.foundDevices} 设备</span>
                </div>
                <Progress value={scanProgress.progress} className="h-2" />
                <div className="text-xs text-muted-foreground">
                  {scanProgress.progress.toFixed(1)}% 完成
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 发现的设备 */}
        {devices.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5" />
                发现的设备 ({devices.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>状态</TableHead>
                    <TableHead>IP地址</TableHead>
                    <TableHead>主机名</TableHead>
                    <TableHead>MAC地址</TableHead>
                    <TableHead>设备类型</TableHead>
                    <TableHead>厂商</TableHead>
                    <TableHead>响应时间</TableHead>
                    <TableHead>开放端口</TableHead>
                    <TableHead>最后发现</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {devices.map((device, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {device.status === 'online' ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-red-600" />
                          )}
                          <span
                            className={cn(
                              'text-xs',
                              device.status === 'online'
                                ? 'text-green-600'
                                : 'text-red-600'
                            )}>
                            {device.status === 'online' ? '在线' : '离线'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono">{device.ip}</TableCell>
                      <TableCell>{device.hostname}</TableCell>
                      <TableCell className="font-mono text-xs">
                        {device.mac}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={getDeviceTypeColor(device.deviceType)}>
                          <div className="flex items-center gap-1">
                            {getDeviceIcon(device.deviceType)}
                            {device.deviceType}
                          </div>
                        </Badge>
                      </TableCell>
                      <TableCell>{device.vendor}</TableCell>
                      <TableCell>{device.responseTime}ms</TableCell>
                      <TableCell>
                        {device.ports.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {device.ports.slice(0, 3).map((port) => (
                              <Badge
                                key={port}
                                variant="secondary"
                                className="text-xs">
                                {port}
                              </Badge>
                            ))}
                            {device.ports.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{device.ports.length - 3}
                              </Badge>
                            )}
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-xs">
                            无
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-xs">
                        {new Date(device.lastSeen).toLocaleTimeString()}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyIP(device.ip)}
                          className="h-6 w-6 p-0">
                          <Copy className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}

        {/* 空状态 */}
        {devices.length === 0 && !scanProgress.isScanning && (
          <Card>
            <CardContent className="text-center py-8">
              <Radio className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">未发现设备</h3>
              <p className="text-muted-foreground mb-4">
                点击"开始扫描"来发现局域网内的设备
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </DebugToolLayout>
  )
}
