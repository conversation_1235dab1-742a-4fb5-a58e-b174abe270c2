import { DebugToolLayout } from '@/components/debug-tools/debug-tool-layout'
import { NetworkDebugger } from '@/components/debug-tools/index'
import { Network } from 'lucide-react'

export default function TcpTestPage() {
  return (
    <DebugToolLayout
      title="网络调试工具"
      description="TCP/UDP客户端和服务器模式调试工具"
      toolIcon={<Network className="h-6 w-6" />}
      toolType="tcp"
      sidebar={
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-sm mb-2">使用说明</h3>
            <div className="text-xs text-muted-foreground space-y-2">
              <p>• TCP客户端模式：连接到指定的TCP服务器</p>
              <p>• TCP服务器模式：启动TCP服务器监听客户端连接</p>
              <p>• UDP客户端模式：使用UDP协议进行数据收发</p>
              <p>• 支持ASCII、UTF-8、HEX等多种编码格式</p>
              <p>• 可导入文件内容进行发送测试</p>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-sm mb-2">快捷键</h3>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• Ctrl+Enter: 发送消息</p>
              <p>• Ctrl+L: 清空日志</p>
              <p>• Ctrl+S: 导出日志</p>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-sm mb-2">支持格式</h3>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• ASCII文本</p>
              <p>• UTF-8编码</p>
              <p>• HEX十六进制</p>
              <p>• Base64编码</p>
            </div>
          </div>
        </div>
      }>
      <NetworkDebugger />
    </DebugToolLayout>
  )
}
