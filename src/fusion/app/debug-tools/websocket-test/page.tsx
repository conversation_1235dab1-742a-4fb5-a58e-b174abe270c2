/**
 * WebSocket测试工具页面
 *
 * 提供WebSocket连接、消息发送和接收功能的完整页面
 * 支持多种数据格式和连接状态管理
 */

import { WebSocketTester } from '@/components/debug-tools/websocket-tester'

export default function WebSocketTestPage() {
  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          WebSocket 测试工具
        </h1>
        <p className="text-muted-foreground">
          测试WebSocket连接、发送消息和接收数据，支持多种数据格式和自动重连
        </p>
      </div>

      <WebSocketTester />
    </div>
  )
}
