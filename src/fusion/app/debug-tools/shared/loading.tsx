import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"

export default function SharedConfigLoading() {
  return (
    <div className="container flex h-screen items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-64" />
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-3 w-48" />
          </div>

          <div className="rounded-md border p-4">
            <Skeleton className="h-5 w-32 mb-2" />
            <Skeleton className="h-4 w-full" />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Skeleton className="h-10 w-20" />
          <div className="space-x-2">
            <Skeleton className="h-10 w-28 inline-block" />
            <Skeleton className="h-10 w-28 inline-block" />
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
