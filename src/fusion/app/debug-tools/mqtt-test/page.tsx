import { MqttClient } from '@/components/debug-tools/mqtt-client'
import { DebugToolLayout } from '@/components/debug-tools/debug-tool-layout'
import { MessageSquare } from 'lucide-react'

export const metadata = {
  title: 'MQTT测试 | 调试工具',
  description: '连接MQTT代理，发布和订阅消息',
}

export default function MqttTestPage() {
  return (
    <DebugToolLayout
      title="MQTT测试"
      description="连接MQTT代理，发布和订阅消息"
      toolIcon={<MessageSquare className="h-8 w-8 text-orange-500" />}
      sidebar={
        <div className="space-y-4">
          <div>
            <h3 className="mb-2 text-sm font-medium">常用代理</h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md bg-accent/50 px-2 py-1 cursor-pointer hover:bg-accent">
                broker.emqx.io:1883
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                broker.hivemq.com:1883
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                test.mosquitto.org:1883
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                localhost:1883
              </li>
            </ul>
          </div>

          <div>
            <h3 className="mb-2 text-sm font-medium">常用主题</h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                test/topic
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                device/+/status
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                sensor/#
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                home/livingroom/temperature
              </li>
            </ul>
          </div>

          <div>
            <h3 className="mb-2 text-sm font-medium">帮助</h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                MQTT协议介绍
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                QoS级别说明
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                主题通配符
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                保留消息
              </li>
            </ul>
          </div>
        </div>
      }>
      <MqttClient />
    </DebugToolLayout>
  )
}
