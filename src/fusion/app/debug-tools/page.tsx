/**
 * 迁移自: Next.js项目 (修复路由点击问题)
 * 迁移时间: 2025-01-14T11:30:00.000Z
 *
 * 迁移说明:
 * - 修复工具卡片点击问题：将Link的href属性改为to属性
 * - 使用react-router-dom进行路由导航
 * - 保持所有工具卡片的功能完整性
 * - 确保工具分类和搜索功能正常工作
 *
 * 注意: 本文件遵循Vite+React迁移规范，保持功能完整性
 */

import { useState } from 'react'
import { Link, Outlet, useLocation } from 'react-router-dom'
import { Input } from '@/components/ui/input'
import { Search, Menu, X } from 'lucide-react'
import { MainLayout } from '@/components/layout/main-layout'
import { SharedDebugToolLayout } from '@/components/debug-tools/shared-debug-tool-layout'

// Tool categories with their respective colors
const categories = {
  network: {
    name: '网络工具',
    color: 'bg-blue-500',
    lightColor: 'bg-blue-50',
    textColor: 'text-blue-600',
    borderColor: 'border-blue-200',
  },
  device: {
    name: '设备工具',
    color: 'bg-green-500',
    lightColor: 'bg-green-50',
    textColor: 'text-green-600',
    borderColor: 'border-green-200',
  },
  protocol: {
    name: '协议工具',
    color: 'bg-purple-500',
    lightColor: 'bg-purple-50',
    textColor: 'text-purple-600',
    borderColor: 'border-purple-200',
  },
  utility: {
    name: '数据工具',
    color: 'bg-emerald-500',
    lightColor: 'bg-emerald-50',
    textColor: 'text-emerald-600',
    borderColor: 'border-emerald-200',
  },
}

// Tools data
const tools = [
  {
    id: 'http-test',
    name: 'HTTP测试',
    description: '测试HTTP请求和响应',
    icon: 'globe',
    href: '/debug-tools/http-test',
    category: 'network',
  },
  {
    id: 'ping-test',
    name: 'Ping测试',
    description: '测试网络连接和延迟',
    icon: 'radio',
    href: '/debug-tools/ping-test',
    category: 'network',
  },
  {
    id: 'port-test',
    name: '端口测试',
    description: '扫描和测试网络端口',
    icon: 'network',
    href: '/debug-tools/port-test',
    category: 'network',
  },
  {
    id: 'packet-capture',
    name: '网络抓包',
    description: '捕获和分析网络数据包',
    icon: 'wifi',
    href: '/debug-tools/packet-capture',
    category: 'network',
  },
  {
    id: 'tcp-test',
    name: 'TCP调试',
    description: 'TCP客户端和服务器通信调试',
    icon: 'network',
    href: '/debug-tools/tcp-test',
    category: 'network',
  },
  {
    id: 'serial-debug',
    name: '串口调试',
    description: '串行端口通信调试',
    icon: 'terminal',
    href: '/debug-tools/serial-debug',
    category: 'device',
  },
  {
    id: 'mqtt-test',
    name: 'MQTT测试',
    description: '测试MQTT发布和订阅',
    icon: 'message-square',
    href: '/debug-tools/mqtt-test',
    category: 'protocol',
  },
  {
    id: 'websocket-test',
    name: 'WebSocket测试',
    description: '测试WebSocket连接和消息',
    icon: 'share-2',
    href: '/debug-tools/websocket-test',
    category: 'protocol',
  },
  {
    id: 'webssh',
    name: 'WebSSH',
    description: '通过Web界面使用SSH',
    icon: 'monitor-smartphone',
    href: '/debug-tools/webssh',
    category: 'device',
  },
  {
    id: 'protocol-analysis',
    name: '协议分析',
    description: '分析和解码通信协议',
    icon: 'file-code',
    href: '/debug-tools/protocol-analysis',
    category: 'protocol',
  },
  {
    id: 'json-formatter',
    name: 'JSON格式化',
    description: '格式化、压缩、验证和分析JSON数据',
    icon: 'file-code',
    href: '/debug-tools/json-formatter',
    category: 'utility',
  },
  {
    id: 'timestamp-converter',
    name: '时间戳转换',
    description: '在不同时间格式之间转换，支持多种时区',
    icon: 'clock',
    href: '/debug-tools/timestamp-converter',
    category: 'utility',
  },
  {
    id: 'data-finder',
    name: '数据查找',
    description: '在二进制数据中查找特定值，支持多种数据类型',
    icon: 'search',
    href: '/debug-tools/data-finder',
    category: 'utility',
  },
  {
    id: 'data-converter',
    name: '数据转换',
    description: '在不同数据类型和格式之间转换，支持多种编码',
    icon: 'arrow-right-left',
    href: '/debug-tools/data-converter',
    category: 'utility',
  },
  {
    id: 'network-speed-test',
    name: '网络测速',
    description: '测试网络连接速度和带宽',
    icon: 'wifi',
    href: '/debug-tools/network-speed-test',
    category: 'network',
  },
  {
    id: 'device-discovery',
    name: '设备发现',
    description: '扫描和发现局域网内的设备',
    icon: 'radio',
    href: '/debug-tools/device-discovery',
    category: 'device',
  },
  {
    id: 'log-analysis',
    name: '日志分析',
    description: '分析和解析系统日志文件',
    icon: 'file-text',
    href: '/debug-tools/log-analysis',
    category: 'utility',
  },
  {
    id: 'logger-test',
    name: 'Logger测试',
    description: '测试和验证统一日志管理系统',
    icon: 'bug',
    href: '/debug-tools/logger-test',
    category: 'utility',
  },
  {
    id: 'storage-manager',
    name: '存储管理',
    description: '查看和管理浏览器localStorage使用情况',
    icon: 'hard-drive',
    href: '/debug-tools/storage-manager',
    category: 'utility',
  },
]

// Icon component to render the appropriate icon based on name
const Icon = ({
  name,
  className = '',
}: {
  name: string
  className?: string
}) => {
  const icons = {
    globe: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <circle cx="12" cy="12" r="10" />
        <line x1="2" x2="22" y1="12" y2="12" />
        <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
      </svg>
    ),
    radio: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <path d="M4.9 19.1C1 15.2 1 8.8 4.9 4.9" />
        <path d="M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5" />
        <circle cx="12" cy="12" r="2" />
        <path d="M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5" />
        <path d="M19.1 4.9C23 8.8 23 15.1 19.1 19" />
      </svg>
    ),
    network: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <rect x="16" y="16" width="6" height="6" rx="1" />
        <rect x="2" y="16" width="6" height="6" rx="1" />
        <rect x="9" y="2" width="6" height="6" rx="1" />
        <path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3" />
        <path d="M12 12V8" />
      </svg>
    ),
    wifi: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <path d="M5 12.55a11 11 0 0 1 14.08 0" />
        <path d="M1.42 9a16 16 0 0 1 21.16 0" />
        <path d="M8.53 16.11a6 6 0 0 1 6.95 0" />
        <line x1="12" x2="12.01" y1="20" y2="20" />
      </svg>
    ),
    terminal: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <polyline points="4 17 10 11 4 5" />
        <line x1="12" x2="20" y1="19" y2="19" />
      </svg>
    ),
    'message-square': (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
      </svg>
    ),
    'share-2': (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <circle cx="18" cy="5" r="3" />
        <circle cx="6" cy="12" r="3" />
        <circle cx="18" cy="19" r="3" />
        <line x1="8.59" x2="15.42" y1="13.51" y2="17.49" />
        <line x1="15.41" x2="8.59" y1="6.51" y2="10.49" />
      </svg>
    ),
    'monitor-smartphone': (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <path d="M18 8V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h8" />
        <path d="M10 19v-3.96 3.15" />
        <path d="M7 19h6" />
        <rect width="6" height="10" x="16" y="12" rx="2" />
      </svg>
    ),
    'file-code': (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
        <path d="m10 13-2 2 2 2" />
        <path d="m14 17 2-2-2-2" />
      </svg>
    ),
    clock: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <circle cx="12" cy="12" r="10" />
        <polyline points="12 6 12 12 16 14" />
      </svg>
    ),
    search: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <circle cx="11" cy="11" r="8" />
        <path d="m21 21-4.35-4.35" />
      </svg>
    ),
    'arrow-right-left': (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <path d="m16 3 4 4-4 4" />
        <path d="M20 7H4" />
        <path d="m8 21-4-4 4-4" />
        <path d="M4 17h16" />
      </svg>
    ),
    'file-text': (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}>
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
        <line x1="16" x2="8" y1="13" y2="13" />
        <line x1="16" x2="8" y1="17" y2="17" />
        <polyline points="10 9 9 9 8 9" />
      </svg>
    ),
  }

  return icons[name as keyof typeof icons] || null
}

export default function DebugToolsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [activeCategory, setActiveCategory] = useState('all')
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const location = useLocation()

  // 检查是否在工具子页面
  const isToolPage = location.pathname !== '/debug-tools'

  const filteredTools = tools.filter(
    (tool) =>
      (tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (activeCategory === 'all' || tool.category === activeCategory)
  )

  // 计算各分类的工具数量
  const getCategoryCount = (category: string) => {
    if (category === 'all') return tools.length
    return tools.filter((tool) => tool.category === category).length
  }

  // 如果在工具子页面，渲染工具内容
  if (isToolPage) {
    return <SharedDebugToolLayout />
  }

  return (
    <MainLayout>
      <div className="flex h-full bg-gray-50">
        {/* 侧边栏 */}
        <div
          className={`
          fixed inset-y-0 left-0 z-50 w-80 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out
          lg:translate-x-0 lg:static lg:inset-0
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <div className="flex flex-col h-full">
            {/* 侧边栏标题 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  调试工具中心
                </h1>
                <p className="text-sm text-gray-500 mt-1">专业调试工具集</p>
              </div>
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100">
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* 搜索框 */}
            <div className="p-6 border-b border-gray-200">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="search"
                  placeholder="搜索调试工具..."
                  className="pl-10 bg-gray-50 border-gray-200 focus:bg-white"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* 分类导航 */}
            <div className="flex-1 p-6">
              <h3 className="text-sm font-medium text-gray-900 mb-4">
                工具分类
              </h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveCategory('all')}
                  className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors ${
                    activeCategory === 'all'
                      ? 'bg-gray-900 text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}>
                  <span>全部工具</span>
                  <span
                    className={`text-xs px-2 py-0.5 rounded-full ${
                      activeCategory === 'all' ? 'bg-white/20' : 'bg-gray-200'
                    }`}>
                    {getCategoryCount('all')}
                  </span>
                </button>

                {Object.entries(categories).map(([key, category]) => (
                  <button
                    key={key}
                    onClick={() => setActiveCategory(key)}
                    className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors ${
                      activeCategory === key
                        ? `${category.color} text-white`
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}>
                    <span>{category.name}</span>
                    <span
                      className={`text-xs px-2 py-0.5 rounded-full ${
                        activeCategory === key ? 'bg-white/20' : 'bg-gray-200'
                      }`}>
                      {getCategoryCount(key)}
                    </span>
                  </button>
                ))}
              </nav>
            </div>

            {/* 底部信息 */}
            <div className="p-6 border-t border-gray-200">
              <div className="text-xs text-gray-500">
                共 {tools.length} 个调试工具
              </div>
            </div>
          </div>
        </div>

        {/* 遮罩层 */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* 主内容区 */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* 移动端顶部栏 */}
          <div className="lg:hidden flex items-center justify-between p-4 bg-white border-b border-gray-200">
            <button
              onClick={() => setSidebarOpen(true)}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100">
              <Menu className="h-5 w-5" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">调试工具</h1>
            <div className="w-9" /> {/* 占位符 */}
          </div>

          {/* 工具展示区 */}
          <div className="flex-1 p-6 lg:p-8">
            {/* 当前分类标题 */}
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {activeCategory === 'all'
                  ? '全部工具'
                  : categories[activeCategory as keyof typeof categories]?.name}
              </h2>
              <p className="text-gray-600">
                {searchTerm
                  ? `搜索 "${searchTerm}" 找到 ${filteredTools.length} 个结果`
                  : `共 ${filteredTools.length} 个工具`}
              </p>
            </div>

            {/* 工具卡片网格 */}
            {filteredTools.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
                {filteredTools.map((tool) => {
                  const categoryInfo =
                    categories[tool.category as keyof typeof categories]
                  return (
                    <Link to={tool.href} key={tool.id} className="block group">
                      <div className="bg-white rounded-xl border border-gray-200 p-6 transition-all duration-200 hover:shadow-lg hover:shadow-gray-200/50 hover:-translate-y-1 group-hover:border-gray-300">
                        <div className="flex items-start space-x-4">
                          <div
                            className={`flex-shrink-0 w-12 h-12 ${categoryInfo?.lightColor} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
                            <Icon
                              name={tool.icon}
                              className={`h-6 w-6 ${categoryInfo?.textColor}`}
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-gray-700">
                              {tool.name}
                            </h3>
                            <p className="text-sm text-gray-600 leading-relaxed">
                              {tool.description}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${categoryInfo?.lightColor} ${categoryInfo?.textColor}`}>
                            {categoryInfo?.name}
                          </span>
                          <div className="flex items-center text-sm text-gray-400 group-hover:text-gray-600 transition-colors">
                            打开工具
                            <svg
                              className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 5l7 7-7 7"
                              />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </Link>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-16 bg-white rounded-xl border border-gray-200">
                <Search className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  未找到匹配的工具
                </h3>
                <p className="text-gray-500">
                  尝试使用不同的搜索词或选择其他分类
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
