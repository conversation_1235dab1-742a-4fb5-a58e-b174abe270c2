import { ProtocolAnalyzer } from '@/components/debug-tools/protocol-analyzer'
import { DebugToolLayout } from '@/components/debug-tools/debug-tool-layout'
import { FileCode } from 'lucide-react'

export const metadata = {
  title: '协议分析 | 调试工具',
  description: '分析和解码通信协议',
}

export default function ProtocolAnalysisPage() {
  return (
    <DebugToolLayout
      title="协议分析"
      description="分析和解码通信协议"
      toolIcon={<FileCode className="h-8 w-8 text-amber-500" />}
      sidebar={
        <div className="space-y-4">
          <div>
            <h3 className="mb-2 text-sm font-medium">支持的协议</h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md bg-accent/50 px-2 py-1 cursor-pointer hover:bg-accent">
                HTTP/HTTPS
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                MQTT
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                Modbus
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                MQTT
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                OPC UA
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                DNP3
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                IEC 61850
              </li>
            </ul>
          </div>

          <div>
            <h3 className="mb-2 text-sm font-medium">保存的分析</h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                Modbus读取寄存器
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                HTTP API调用
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                MQTT消息流
              </li>
            </ul>
          </div>

          <div>
            <h3 className="mb-2 text-sm font-medium">帮助</h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                协议规范
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                数据解析
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                常见问题
              </li>
            </ul>
          </div>
        </div>
      }>
      <ProtocolAnalyzer />
    </DebugToolLayout>
  )
}
