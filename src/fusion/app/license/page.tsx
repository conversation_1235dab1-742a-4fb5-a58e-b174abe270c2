import { Badge } from '@/components/ui/badge'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import {
  Shield,
  Copy,
  CheckCircle2,
  AlertTriangle,
  Info,
  X,
} from 'lucide-react'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'

export default function LicensePage() {
  const [copied, setCopied] = useState(false)
  const [activationDialogOpen, setActivationDialogOpen] = useState(false)
  const [activationCode, setActivationCode] = useState('')
  const [activationSuccess, setActivationSuccess] = useState(false)

  // 模拟授权信息
  const licenseInfo = {
    status: 'active', // active, expired, inactive
    machineId: '086D-3655-6BE2-E937',
    version: '专业版',
    expiryDate: '2026-04-21 12:30:00',
    maxDevices: 100,
    maxPoints: 5000,
    currentDevices: 100,
    currentPoints: 5000,
    activationDate: '2023-04-21 12:30:00',
  }

  const copyMachineId = () => {
    navigator.clipboard.writeText(licenseInfo.machineId)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const handleActivate = () => {
    // 模拟激活过程
    const activateBtn = document.querySelector(
      'button[type="button"]:not([disabled])'
    )
    if (activateBtn) {
      const originalText = activateBtn.innerHTML
      activateBtn.innerHTML =
        '<div class="flex items-center"><svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>正在激活...</div>'
      // 使用类型断言修复TypeScript错误
      ;(activateBtn as HTMLButtonElement).disabled = true
    }

    setTimeout(() => {
      setActivationSuccess(true)
    }, 2000)
  }

  return (
    <MainLayout>
      <div className="w-full px-4 md:px-6 lg:px-8 py-6 space-y-6 max-w-[1920px] mx-auto">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">授权信息</h1>
            <p className="text-muted-foreground">查看和管理系统授权信息</p>
          </div>
          <Dialog
            open={activationDialogOpen}
            onOpenChange={setActivationDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-300 shadow-md">
                激活系统
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md border-0 shadow-xl rounded-xl overflow-hidden p-0">
              {activationSuccess ? (
                <div className="flex flex-col items-center justify-center py-12 px-6 bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/30 dark:to-blue-900/30">
                  <div className="rounded-full bg-green-100 p-4 text-green-600 dark:bg-green-900/60 dark:text-green-300 mb-6 shadow-lg shadow-green-100/50 dark:shadow-green-900/20 animate-bounce">
                    <CheckCircle2 className="h-10 w-10" />
                  </div>
                  <h3 className="text-2xl font-bold text-center bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                    授权激活成功
                  </h3>
                  <p className="text-center text-muted-foreground mt-4 max-w-xs">
                    系统已成功激活，您现在可以使用所有功能。
                  </p>
                  <Button
                    className="mt-8 bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-300 shadow-md"
                    onClick={() => {
                      setActivationDialogOpen(false)
                      setActivationSuccess(false)
                      setActivationCode('')
                    }}>
                    开始使用
                  </Button>
                </div>
              ) : (
                <>
                  <div className="bg-primary p-6 text-primary-foreground">
                    <div className="flex items-center space-x-3">
                      <Shield className="h-8 w-8" />
                      <div>
                        <h2 className="text-xl font-bold">系统激活</h2>
                        <p className="text-primary-foreground/80 text-sm mt-1">
                          输入授权码以解锁全部功能
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="p-6 space-y-6">
                    <div className="p-4 bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/20 dark:to-primary/10 rounded-lg border border-primary/20 dark:border-primary/30">
                      <div className="flex items-start space-x-3">
                        <Info className="h-5 w-5 text-primary dark:text-primary-foreground mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-primary dark:text-primary-foreground/90">
                            激活说明：
                          </h4>
                          <ul className="mt-2 text-sm text-primary/80 dark:text-primary-foreground/70 space-y-2.5">
                            <li className="flex items-start">
                              <div className="w-1.5 h-1.5 rounded-full bg-primary dark:bg-primary-foreground mt-1.5 mr-2 flex-shrink-0"></div>
                              <span>
                                请将您获取的授权激活码完整粘贴到下方输入框
                              </span>
                            </li>
                            <li className="flex items-start">
                              <div className="w-1.5 h-1.5 rounded-full bg-primary dark:bg-primary-foreground mt-1.5 mr-2 flex-shrink-0"></div>
                              <span>
                                激活码与您的机器码是一一对应的，请勿在其他设备使用
                              </span>
                            </li>
                            <li className="flex items-start">
                              <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400 mr-1.5 mt-0.5 flex-shrink-0" />
                              <span>
                                频繁更换硬件或系统环境可能导致授权失效，请谨慎操作
                              </span>
                            </li>
                            <li className="flex items-start">
                              <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400 mr-1.5 mt-0.5 flex-shrink-0" />
                              <span>
                                异常使用授权（如篡改、虚拟化转移）将触发安全机制
                              </span>
                            </li>
                            <li className="flex items-start">
                              <div className="w-1.5 h-1.5 rounded-full bg-primary dark:bg-primary-foreground mt-1.5 mr-2 flex-shrink-0"></div>
                              <span>
                                如有问题，请联系系统供应商获取技术支持
                              </span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <label
                        htmlFor="activationCode"
                        className="text-sm font-medium flex items-center">
                        <span className="text-red-500 mr-1">*</span> 授权激活码
                      </label>
                      <div className="relative">
                        <Textarea
                          id="activationCode"
                          value={activationCode}
                          onChange={(e) => setActivationCode(e.target.value)}
                          placeholder="请输入授权激活码"
                          className="h-24 pr-10 border-blue-200 dark:border-blue-800 focus:border-blue-400 dark:focus:border-blue-600 transition-all duration-300"
                        />
                        {activationCode && (
                          <button
                            className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                            onClick={() => setActivationCode('')}
                            type="button">
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        请确保输入的激活码格式正确，包含所有字符和连字符
                      </p>
                    </div>
                    <div className="pt-4 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 border-t">
                      <div className="text-sm text-muted-foreground flex items-center">
                        <Info className="h-4 w-4 mr-1.5 text-primary" />
                        如需获取激活码，请联系系统供应商
                      </div>
                      <Button
                        type="button"
                        onClick={handleActivate}
                        disabled={!activationCode.trim()}
                        className={`${
                          activationCode.trim()
                            ? 'bg-primary hover:bg-primary/90 text-primary-foreground'
                            : 'bg-gray-200 text-gray-500 dark:bg-gray-800'
                        } transition-all duration-300 shadow-md`}>
                        {activationCode.trim() ? (
                          <span className="flex items-center">
                            <Shield className="mr-1.5 h-4 w-4" />
                            输入激活
                          </span>
                        ) : (
                          '请输入激活码'
                        )}
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </DialogContent>
          </Dialog>
        </div>

        <Tabs defaultValue="license" className="space-y-4">
          <TabsList className="w-full md:w-auto mx-auto flex justify-center">
            <TabsTrigger value="license">授权状态</TabsTrigger>
            <TabsTrigger value="usage">资源使用</TabsTrigger>
            <TabsTrigger value="history">授权历史</TabsTrigger>
          </TabsList>
          <TabsContent value="license" className="space-y-4 w-full">
            <Card className="w-full">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/50">
                      <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <CardTitle>授权状态</CardTitle>
                  </div>
                  <div
                    className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${
                      licenseInfo.status === 'active'
                        ? 'bg-gradient-to-r from-green-100 to-green-50 text-green-800 dark:from-green-900/70 dark:to-green-900/30 dark:text-green-300'
                        : licenseInfo.status === 'expired'
                        ? 'bg-gradient-to-r from-red-100 to-red-50 text-red-800 dark:from-red-900/70 dark:to-red-900/30 dark:text-red-300'
                        : 'bg-gradient-to-r from-yellow-100 to-yellow-50 text-yellow-800 dark:from-yellow-900/70 dark:to-yellow-900/30 dark:text-yellow-300'
                    }`}>
                    {licenseInfo.status === 'active' ? (
                      <>
                        <span className="w-2 h-2 rounded-full bg-green-500 mr-1.5 animate-pulse"></span>
                        已激活
                      </>
                    ) : licenseInfo.status === 'expired' ? (
                      <>
                        <span className="w-2 h-2 rounded-full bg-red-500 mr-1.5"></span>
                        已过期
                      </>
                    ) : (
                      <>
                        <span className="w-2 h-2 rounded-full bg-yellow-500 mr-1.5"></span>
                        未激活
                      </>
                    )}
                  </div>
                </div>
                <CardDescription>
                  查看当前系统的授权状态和详细信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {licenseInfo.status === 'inactive' ? (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>系统未激活</AlertTitle>
                    <AlertDescription>
                      您的系统尚未激活，部分功能可能无法使用。请联系管理员获取授权码进行激活。
                    </AlertDescription>
                  </Alert>
                ) : licenseInfo.status === 'expired' ? (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>授权已过期</AlertTitle>
                    <AlertDescription>
                      您的系统授权已过期，部分功能可能无法使用。请联系管理员续期授权。
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert
                    variant="success"
                    className="bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-300">
                    <CheckCircle2 className="h-4 w-4" />
                    <AlertTitle>授权有效</AlertTitle>
                    <AlertDescription>
                      您的系统已成功激活，所有功能均可正常使用。
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">
                        机器码
                      </h3>
                      <div className="flex items-center space-x-2">
                        <code className="bg-gradient-to-r from-gray-100 to-gray-50 dark:from-gray-900 dark:to-gray-800 px-3 py-1.5 rounded text-sm font-mono flex-1 border border-gray-200 dark:border-gray-700">
                          {licenseInfo.machineId}
                        </code>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={copyMachineId}
                          className="transition-all duration-300 hover:bg-primary/10 hover:text-primary dark:hover:bg-primary/20 dark:hover:text-primary-foreground">
                          {copied ? (
                            <span className="flex items-center text-green-600 dark:text-green-400">
                              <CheckCircle2 className="h-4 w-4 mr-1" /> 已复制
                            </span>
                          ) : (
                            <span className="flex items-center">
                              <Copy className="h-4 w-4 mr-1" /> 复制
                            </span>
                          )}
                        </Button>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">
                        授权版本
                      </h3>
                      <p className="font-medium">{licenseInfo.version}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">
                        激活日期
                      </h3>
                      <p className="font-medium">
                        {licenseInfo.activationDate}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">
                        到期日期
                      </h3>
                      <p className="font-medium">{licenseInfo.expiryDate}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">
                        最大设备数
                      </h3>
                      <p className="font-medium">{licenseInfo.maxDevices}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">
                        最大点位数
                      </h3>
                      <p className="font-medium">{licenseInfo.maxPoints}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-6">
                <Button
                  variant="outline"
                  className="border-primary/20 hover:bg-primary/10 hover:text-primary dark:border-primary/40 dark:hover:bg-primary/20 dark:hover:text-primary-foreground transition-all duration-300">
                  <span className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-1.5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    导出授权信息
                  </span>
                </Button>
                <Button
                  variant="default"
                  className="bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-300 shadow-md">
                  <span className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-1.5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    更新授权
                  </span>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="usage" className="space-y-4 w-full">
            <Card className="w-full">
              <CardHeader>
                <CardTitle>资源使用情况</CardTitle>
                <CardDescription>
                  查看当前系统资源的使用情况和限制
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <div className="flex justify-between mb-2">
                    <h3 className="text-sm font-medium">设备使用情况</h3>
                    <span className="text-sm">
                      {licenseInfo.currentDevices} / {licenseInfo.maxDevices}
                    </span>
                  </div>
                  <Progress
                    value={
                      (licenseInfo.currentDevices / licenseInfo.maxDevices) *
                      100
                    }
                    className={`h-2 ${
                      licenseInfo.currentDevices / licenseInfo.maxDevices > 0.9
                        ? 'bg-secondary [&>div]:bg-red-500'
                        : licenseInfo.currentDevices / licenseInfo.maxDevices >
                          0.7
                        ? 'bg-secondary [&>div]:bg-amber-500'
                        : 'bg-secondary [&>div]:bg-green-500'
                    }`}
                  />
                  {licenseInfo.currentDevices / licenseInfo.maxDevices >
                    0.9 && (
                    <p className="text-xs text-red-500 mt-1">
                      设备数量接近上限，请考虑升级授权
                    </p>
                  )}
                </div>

                <div>
                  <div className="flex justify-between mb-2">
                    <h3 className="text-sm font-medium">点位使用情况</h3>
                    <span className="text-sm">
                      {licenseInfo.currentPoints} / {licenseInfo.maxPoints}
                    </span>
                  </div>
                  <Progress
                    value={
                      (licenseInfo.currentPoints / licenseInfo.maxPoints) * 100
                    }
                    className={`h-2 ${
                      licenseInfo.currentPoints / licenseInfo.maxPoints > 0.9
                        ? 'bg-secondary [&>div]:bg-red-500'
                        : licenseInfo.currentPoints / licenseInfo.maxPoints >
                          0.7
                        ? 'bg-secondary [&>div]:bg-amber-500'
                        : 'bg-secondary [&>div]:bg-green-500'
                    }`}
                  />
                  {licenseInfo.currentPoints / licenseInfo.maxPoints > 0.9 && (
                    <p className="text-xs text-red-500 mt-1">
                      点位数量接近上限，请考虑升级授权
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">功能限制</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center">
                          <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                          基础数据采集
                        </li>
                        <li className="flex items-center">
                          <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                          设备管理
                        </li>
                        <li className="flex items-center">
                          <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                          数据转发
                        </li>
                        <li className="flex items-center">
                          <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                          工作流编排
                        </li>
                        <li className="flex items-center">
                          <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                          高级数据分析
                        </li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">授权建议</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <p className="text-sm">
                          根据您当前的资源使用情况，我们建议您考虑以下升级选项：
                        </p>
                        <ul className="space-y-2 text-sm">
                          <li className="flex items-start">
                            <Info className="h-4 w-4 text-blue-500 mr-2 mt-0.5" />
                            <span>升级到企业版以获取更多设备和点位配额</span>
                          </li>
                          <li className="flex items-start">
                            <Info className="h-4 w-4 text-blue-500 mr-2 mt-0.5" />
                            <span>
                              添加高级分析模块以获取更强大的数据分析能力
                            </span>
                          </li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4 w-full">
            <Card className="w-full">
              <CardHeader>
                <CardTitle>授权历史</CardTitle>
                <CardDescription>查看系统授权的历史记录和变更</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  <div className="flex">
                    <div className="flex flex-col items-center mr-4">
                      <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-br from-green-100 to-green-50 text-green-600 dark:from-green-900/50 dark:to-green-900/20 dark:text-green-300 shadow-md">
                        <CheckCircle2 className="h-5 w-5" />
                      </div>
                      <div className="w-px h-full bg-gradient-to-b from-green-200 to-blue-200 dark:from-green-800 dark:to-blue-800 mt-2"></div>
                    </div>
                    <div className="pb-8">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium">授权激活</h3>
                        <Badge
                          variant="outline"
                          className="bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-800">
                          当前
                        </Badge>
                      </div>
                      <time className="text-sm text-muted-foreground mt-1 block">
                        2023-04-21 12:30:00
                      </time>
                      <p className="mt-3 text-sm">
                        系统授权成功激活，版本：专业版
                      </p>
                      <div className="mt-3 p-3 bg-gradient-to-r from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 rounded-md border border-gray-100 dark:border-gray-700 text-xs text-muted-foreground">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                            <span>最大设备数：100</span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                            <span>最大点位数：5000</span>
                          </div>
                          <div className="flex items-center col-span-2">
                            <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                            <span>有效期至：2026-04-21 12:30:00</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex">
                    <div className="flex flex-col items-center mr-4">
                      <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-br from-blue-100 to-blue-50 text-blue-600 dark:from-blue-900/50 dark:to-blue-900/20 dark:text-blue-300 shadow-md">
                        <Info className="h-5 w-5" />
                      </div>
                      <div className="w-px h-full bg-gradient-to-b from-blue-200 to-slate-200 dark:from-blue-800 dark:to-slate-700 mt-2"></div>
                    </div>
                    <div className="pb-8">
                      <h3 className="font-medium">试用期开始</h3>
                      <time className="text-sm text-muted-foreground mt-1 block">
                        2023-03-21 09:15:00
                      </time>
                      <p className="mt-3 text-sm">
                        系统开始30天试用期，版本：标准版
                      </p>
                      <div className="mt-3 p-3 bg-gradient-to-r from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 rounded-md border border-gray-100 dark:border-gray-700 text-xs text-muted-foreground">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                            <span>最大设备数：10</span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                            <span>最大点位数：500</span>
                          </div>
                          <div className="flex items-center col-span-2">
                            <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                            <span>有效期至：2023-04-20 09:15:00</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex">
                    <div className="flex flex-col items-center mr-4">
                      <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-br from-slate-100 to-slate-50 text-slate-600 dark:from-slate-800 dark:to-slate-700 dark:text-slate-300 shadow-md">
                        <Shield className="h-5 w-5" />
                      </div>
                    </div>
                    <div>
                      <h3 className="font-medium">系统安装</h3>
                      <time className="text-sm text-muted-foreground mt-1 block">
                        2023-03-20 15:42:00
                      </time>
                      <p className="mt-3 text-sm">系统首次安装，生成机器码</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
