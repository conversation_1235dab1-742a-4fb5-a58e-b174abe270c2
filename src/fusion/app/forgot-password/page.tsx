import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { User, ArrowLeft, CheckCircle } from 'lucide-react'
import { FusionTrackLogo } from '@/components/fusion-track-logo'

export default function ForgotPasswordPage() {
  const [username, setUsername] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // 模拟发送重置密码邮件的请求
      await new Promise((resolve) => setTimeout(resolve, 1500))
      setIsSubmitted(true)
    } catch (err) {
      console.error('Error sending reset email:', err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <FusionTrackLogo size="md" />
        </div>
      </header>

      <main className="flex-1 flex items-center justify-center p-4 bg-gray-50">
        <Card className="w-full max-w-md">
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Link to="/login" className="text-gray-500 hover:text-gray-700">
                  <ArrowLeft className="h-4 w-4" />
                </Link>
                <CardTitle>找回密码</CardTitle>
              </div>
              <CardDescription>
                输入您的账号，我们将向您的邮箱发送重置密码的链接
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isSubmitted ? (
                <div className="flex flex-col items-center justify-center py-6 text-center">
                  <div className="mb-4 rounded-full bg-green-100 p-3">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="mb-2 text-lg font-medium">邮件已发送</h3>
                  <p className="text-sm text-gray-500 max-w-xs">
                    我们已向与账号 {username}{' '}
                    关联的邮箱发送了一封包含重置密码链接的邮件。请检查您的收件箱。
                  </p>
                  <Button asChild className="mt-6">
                    <Link to="/login">返回登录</Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="username">账号</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="username"
                        placeholder="请输入账号"
                        className="pl-10"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    请联系系统管理员重置您的密码。
                  </p>
                </div>
              )}
            </CardContent>
            {!isSubmitted && (
              <CardFooter className="flex justify-center">
                <Button variant="outline" asChild>
                  <Link to="/login">返回登录</Link>
                </Button>
              </CardFooter>
            )}
          </form>
        </Card>
      </main>

      <footer className="border-t py-4 bg-background">
        <div className="container flex flex-col items-center justify-between gap-4 md:flex-row">
          <p className="text-sm text-gray-500">
            © 2025 FusionTrack. 保留所有权利.
          </p>
          <div className="flex gap-4">
            <Link to="#" className="text-sm text-gray-500 hover:underline">
              服务条款
            </Link>
            <Link to="#" className="text-sm text-gray-500 hover:underline">
              隐私政策
            </Link>
            <Link to="#" className="text-sm text-gray-500 hover:underline">
              联系我们
            </Link>
          </div>
        </div>
      </footer>
    </div>
  )
}
