import type React from 'react'

import { useState, useRef } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useConfigStore } from '@/lib/config/config-store'
import { Upload, Trash2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { MainLayout } from '@/components/layout/main-layout'

export default function BrandingSettingsPage() {
  const { config, setConfig } = useConfigStore()
  const { toast } = useToast()
  const [systemName, setSystemName] = useState(config.systemName || '')
  const [logoPreview, setLogoPreview] = useState<string | null>(
    config.logoUrl || null
  )
  const [darkLogoPreview, setDarkLogoPreview] = useState<string | null>(
    config.darkLogoUrl || null
  )
  const [faviconPreview, setFaviconPreview] = useState<string | null>(
    config.faviconUrl || null
  )

  const logoInputRef = useRef<HTMLInputElement>(null)
  const darkLogoInputRef = useRef<HTMLInputElement>(null)
  const faviconInputRef = useRef<HTMLInputElement>(null)

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const objectUrl = URL.createObjectURL(file)
      setLogoPreview(objectUrl)
    }
  }

  const handleDarkLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const objectUrl = URL.createObjectURL(file)
      setDarkLogoPreview(objectUrl)
    }
  }

  const handleFaviconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const objectUrl = URL.createObjectURL(file)
      setFaviconPreview(objectUrl)
    }
  }

  const handleRemoveLogo = () => {
    setLogoPreview('/logo.svg')
    if (logoInputRef.current) {
      logoInputRef.current.value = ''
    }
  }

  const handleRemoveDarkLogo = () => {
    setDarkLogoPreview('/logo-dark.svg')
    if (darkLogoInputRef.current) {
      darkLogoInputRef.current.value = ''
    }
  }

  const handleRemoveFavicon = () => {
    setFaviconPreview('/favicon.ico')
    if (faviconInputRef.current) {
      faviconInputRef.current.value = ''
    }
  }

  const handleSave = () => {
    setConfig({
      systemName,
      logoUrl: logoPreview || '/logo.svg',
      darkLogoUrl: darkLogoPreview || '/logo-dark.svg',
      faviconUrl: faviconPreview || '/favicon.ico',
    })

    toast({
      title: '品牌设置已保存',
      description: '系统品牌设置已成功更新。',
    })
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">品牌设置</h1>
          <p className="text-muted-foreground">
            配置系统品牌，包括系统名称、Logo和图标
          </p>
        </div>

        <Tabs defaultValue="basic">
          <TabsList>
            <TabsTrigger value="basic">基本设置</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic">
            <Card>
              <CardHeader>
                <CardTitle>基本品牌设置</CardTitle>
                <CardDescription>配置系统名称和主要Logo</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="systemName">系统名称</Label>
                  <Input
                    id="systemName"
                    value={systemName}
                    onChange={(e) => setSystemName(e.target.value)}
                    placeholder="输入系统名称"
                  />
                  <p className="text-xs text-muted-foreground">
                    系统名称将显示在浏览器标题、登录页面和整个界面中
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>系统Logo (亮色模式)</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-16 w-16 rounded-md border flex items-center justify-center overflow-hidden bg-background">
                      {logoPreview && (
                        <img
                          src={logoPreview || '/placeholder.svg'}
                          alt="系统Logo"
                          className="h-full w-auto object-contain"
                        />
                      )}
                    </div>
                    <div className="flex flex-col gap-2">
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => logoInputRef.current?.click()}>
                          <Upload className="h-4 w-4 mr-2" />
                          上传Logo
                        </Button>
                        {logoPreview && logoPreview !== '/logo.svg' && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleRemoveLogo}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            移除
                          </Button>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        推荐尺寸: 512x512 像素, PNG 或 SVG 格式
                      </p>
                    </div>
                    <input
                      type="file"
                      ref={logoInputRef}
                      className="hidden"
                      accept="image/png,image/jpeg,image/svg+xml"
                      onChange={handleLogoChange}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleSave}>保存设置</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="advanced">
            <Card>
              <CardHeader>
                <CardTitle>高级品牌设置</CardTitle>
                <CardDescription>配置暗色模式Logo和网站图标</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>系统Logo (暗色模式)</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-16 w-16 rounded-md border flex items-center justify-center overflow-hidden bg-slate-800">
                      {darkLogoPreview && (
                        <img
                          src={darkLogoPreview || '/placeholder.svg'}
                          alt="暗色模式Logo"
                          className="h-full w-auto object-contain"
                        />
                      )}
                    </div>
                    <div className="flex flex-col gap-2">
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => darkLogoInputRef.current?.click()}>
                          <Upload className="h-4 w-4 mr-2" />
                          上传Logo
                        </Button>
                        {darkLogoPreview &&
                          darkLogoPreview !== '/logo-dark.svg' && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleRemoveDarkLogo}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              移除
                            </Button>
                          )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        推荐尺寸: 512x512 像素, PNG 或 SVG 格式
                      </p>
                    </div>
                    <input
                      type="file"
                      ref={darkLogoInputRef}
                      className="hidden"
                      accept="image/png,image/jpeg,image/svg+xml"
                      onChange={handleDarkLogoChange}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>网站图标 (Favicon)</Label>
                  <div className="flex items-center gap-4">
                    <div className="h-16 w-16 rounded-md border flex items-center justify-center overflow-hidden bg-background">
                      {faviconPreview && (
                        <img
                          src={faviconPreview || '/placeholder.svg'}
                          alt="网站图标"
                          className="h-full w-auto object-contain"
                        />
                      )}
                    </div>
                    <div className="flex flex-col gap-2">
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => faviconInputRef.current?.click()}>
                          <Upload className="h-4 w-4 mr-2" />
                          上传图标
                        </Button>
                        {faviconPreview &&
                          faviconPreview !== '/favicon.ico' && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleRemoveFavicon}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              移除
                            </Button>
                          )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        推荐尺寸: 32x32 或 16x16 像素, ICO, PNG 或 SVG 格式
                      </p>
                    </div>
                    <input
                      type="file"
                      ref={faviconInputRef}
                      className="hidden"
                      accept="image/png,image/jpeg,image/svg+xml,image/x-icon"
                      onChange={handleFaviconChange}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleSave}>保存设置</Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
