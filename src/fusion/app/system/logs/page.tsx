/**
 * 系统日志页面
 *
 * 菜单位置：权限管理 > 系统日志
 * 路由地址：/system/logs
 * 页面功能：查看和管理系统操作日志，支持日志搜索、过滤和导出
 *
 * 技术特点：
 * - 实时日志流显示
 * - 多维度日志过滤
 * - 日志级别分类
 * - 操作审计追踪
 *
 * 业务价值：
 * - 提供系统操作审计
 * - 支持问题排查和分析
 * - 确保操作可追溯性
 * - 满足合规要求
 */

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  systemLogApi,
  SystemLog,
  LogStats,
  LogQueryParams,
  LogExportParams
} from '@/lib/api/system-log-api'
import { ApiChecker, API_ENDPOINTS } from '@/lib/api/api-checker'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import {
  FileText,
  Search,
  RefreshCw,
  Download,
  Filter,
  Calendar,
  User,
  Activity,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Eye,
  Clock,
  MapPin,
  Monitor,
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

// 使用API中定义的类型，这里不需要重复定义

export default function SystemLogsPage() {
  const { toast } = useToast()
  const [logs, setLogs] = useState<SystemLog[]>([])
  const [stats, setStats] = useState<LogStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLevel, setSelectedLevel] = useState<string>('all')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedUser, setSelectedUser] = useState<string>('all')
  const [apiAvailable, setApiAvailable] = useState(false)

  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(false)

  // Mock数据已移除，使用真实API

  useEffect(() => {
    checkApiAndLoadData()

    if (autoRefresh) {
      const interval = setInterval(() => {
        checkApiAndLoadData()
      }, 10000) // 每10秒刷新一次

      return () => clearInterval(interval)
    }
  }, [autoRefresh, selectedLevel, selectedCategory, selectedUser, searchTerm])

  const checkApiAndLoadData = async () => {
    setLoading(true)
    try {
      // 检查API可用性
      const available = await ApiChecker.checkApi(API_ENDPOINTS.LOG_LIST)
      setApiAvailable(available)

      if (available) {
        await loadData()
      } else {
        toast({
          title: 'API不可用',
          description: '系统日志API尚未实现，请联系开发人员',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('检查API失败:', error)
      toast({
        title: '检查失败',
        description: '无法检查API状态',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const loadData = async () => {
    try {
      // 加载系统日志列表
      const queryParams: LogQueryParams = {
        page: 1,
        pageSize: 50,
        level: selectedLevel !== 'all' ? selectedLevel as any : undefined,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        username: selectedUser !== 'all' ? selectedUser : undefined,
        keyword: searchTerm || undefined
      }

      const logsResponse = await systemLogApi.getLogList(queryParams)
      if (logsResponse.data) {
        setLogs(logsResponse.data.items)
      }

      // 加载统计数据
      const statsResponse = await systemLogApi.getLogStats()
      if (statsResponse.data) {
        setStats(statsResponse.data)
      }

      toast({
        title: '加载成功',
        description: '系统日志数据加载完成',
      })
    } catch (error) {
      console.error('加载系统日志失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载系统日志，请检查网络连接',
        variant: 'destructive',
      })
    }
  }

  // loadStats函数已合并到loadData中

  const handleViewDetails = (log: SystemLog) => {
    setSelectedLog(log)
    setIsDetailDialogOpen(true)
  }

  const handleExportLogs = async () => {
    if (!apiAvailable) {
      toast({
        title: '功能不可用',
        description: '日志导出API尚未实现',
        variant: 'destructive',
      })
      return
    }

    try {
      const exportParams: LogExportParams = {
        format: 'excel',
        level: selectedLevel !== 'all' ? selectedLevel : undefined,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        maxRecords: 10000
      }

      const response = await systemLogApi.exportLogs(exportParams)
      if (response.data) {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `system-logs-${new Date().toISOString().split('T')[0]}.xlsx`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)

        toast({
          title: '导出成功',
          description: '日志文件已生成并开始下载',
        })
      }
    } catch (error) {
      console.error('导出日志失败:', error)
      toast({
        title: '导出失败',
        description: '无法导出日志文件，请检查网络连接',
        variant: 'destructive',
      })
    }
  }

  const getLevelBadge = (level: SystemLog['level']) => {
    switch (level) {
      case 'error':
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            错误
          </Badge>
        )
      case 'warn':
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            <AlertTriangle className="h-3 w-3 mr-1" />
            警告
          </Badge>
        )
      case 'success':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            成功
          </Badge>
        )
      case 'info':
        return (
          <Badge variant="outline">
            <Info className="h-3 w-3 mr-1" />
            信息
          </Badge>
        )
      case 'debug':
        return (
          <Badge variant="secondary">
            <Monitor className="h-3 w-3 mr-1" />
            调试
          </Badge>
        )
      default:
        return <Badge variant="secondary">{level}</Badge>
    }
  }

  const getStatusBadge = (status: SystemLog['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">成功</Badge>
      case 'failed':
        return <Badge variant="destructive">失败</Badge>
      case 'pending':
        return <Badge variant="secondary">处理中</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const filteredLogs = logs.filter(log => {
    const matchesSearch = 
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (log.username && log.username.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesLevel = selectedLevel === 'all' || log.level === selectedLevel
    const matchesCategory = selectedCategory === 'all' || log.category === selectedCategory
    const matchesUser = selectedUser === 'all' || log.username === selectedUser

    return matchesSearch && matchesLevel && matchesCategory && matchesUser
  })

  const uniqueCategories = Array.from(new Set(logs.map(log => log.category)))
  const uniqueUsers = Array.from(new Set(logs.map(log => log.username).filter(Boolean)))

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">系统日志</h1>
          <p className="text-muted-foreground">
            查看系统操作日志和审计信息
          </p>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-full bg-blue-50 dark:bg-blue-950">
                    <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">今日日志</p>
                    <p className="text-2xl font-bold">{stats.todayLogs}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-full bg-red-50 dark:bg-red-950">
                    <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">错误日志</p>
                    <p className="text-2xl font-bold">{stats.errorLogs}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-full bg-yellow-50 dark:bg-yellow-950">
                    <AlertTriangle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">警告日志</p>
                    <p className="text-2xl font-bold">{stats.warningLogs}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-full bg-green-50 dark:bg-green-950">
                    <User className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">活跃用户</p>
                    <p className="text-2xl font-bold">{stats.uniqueUsers}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 日志列表 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>系统日志</CardTitle>
                <CardDescription>系统操作和事件的详细记录</CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant={autoRefresh ? "default" : "outline"}
                  size="sm"
                  onClick={() => setAutoRefresh(!autoRefresh)}>
                  <Activity className="h-4 w-4 mr-2" />
                  {autoRefresh ? '停止自动刷新' : '自动刷新'}
                </Button>
                <Button variant="outline" onClick={handleExportLogs}>
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Button variant="outline" onClick={loadLogs}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  刷新
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap items-center gap-4 mb-6">
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索日志..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64"
                />
              </div>
              
              <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="日志级别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部级别</SelectItem>
                  <SelectItem value="error">错误</SelectItem>
                  <SelectItem value="warn">警告</SelectItem>
                  <SelectItem value="success">成功</SelectItem>
                  <SelectItem value="info">信息</SelectItem>
                  <SelectItem value="debug">调试</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  {uniqueCategories.map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedUser} onValueChange={setSelectedUser}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="用户" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部用户</SelectItem>
                  {uniqueUsers.map(user => (
                    <SelectItem key={user} value={user!}>{user}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                <span>加载中...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>时间</TableHead>
                    <TableHead>级别</TableHead>
                    <TableHead>分类</TableHead>
                    <TableHead>操作</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>消息</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>耗时</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="text-sm">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span>{log.createTime}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getLevelBadge(log.level)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{log.category}</Badge>
                      </TableCell>
                      <TableCell className="font-medium">{log.action}</TableCell>
                      <TableCell>
                        {log.username ? (
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{log.username}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">系统</span>
                        )}
                      </TableCell>
                      <TableCell className="max-w-xs truncate">{log.message}</TableCell>
                      <TableCell>{getStatusBadge(log.status)}</TableCell>
                      <TableCell className="text-sm">
                        {log.duration ? `${log.duration}ms` : '-'}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetails(log)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* 日志详情对话框 */}
        <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>日志详情</DialogTitle>
              <DialogDescription>
                查看日志的详细信息
              </DialogDescription>
            </DialogHeader>
            {selectedLog && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">基本信息</Label>
                    <div className="text-sm space-y-1">
                      <div>时间: {selectedLog.timestamp}</div>
                      <div>级别: {getLevelBadge(selectedLog.level)}</div>
                      <div>分类: {selectedLog.category}</div>
                      <div>操作: {selectedLog.action}</div>
                      <div>状态: {getStatusBadge(selectedLog.status)}</div>
                      {selectedLog.duration && <div>耗时: {selectedLog.duration}ms</div>}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">用户信息</Label>
                    <div className="text-sm space-y-1">
                      <div>用户: {selectedLog.username || '系统'}</div>
                      <div>IP地址: {selectedLog.ipAddress}</div>
                      <div>模块: {selectedLog.module}</div>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-sm font-medium">消息内容</Label>
                  <div className="text-sm p-3 bg-muted rounded-md">
                    {selectedLog.message}
                  </div>
                </div>

                {selectedLog.userAgent && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">用户代理</Label>
                    <div className="text-sm p-3 bg-muted rounded-md font-mono">
                      {selectedLog.userAgent}
                    </div>
                  </div>
                )}

                {selectedLog.details && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">详细信息</Label>
                    <div className="text-sm p-3 bg-muted rounded-md">
                      <pre className="whitespace-pre-wrap">
                        {JSON.stringify(selectedLog.details, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}

export default SystemLogsPage
