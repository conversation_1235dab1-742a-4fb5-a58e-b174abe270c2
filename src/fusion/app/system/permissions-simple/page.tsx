import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'
import {
  permissionManagementApi,
  SysPermission,
  PermissionTemplate
} from '@/lib/api/permission-management-api'
import { Api<PERSON>hecker, API_ENDPOINTS } from '@/lib/api/api-checker'

export default function PermissionsSimplePage() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [permissions, setPermissions] = useState<SysPermission[]>([])
  const [templates, setTemplates] = useState<PermissionTemplate[]>([])
  const [apiStatus, setApiStatus] = useState<{
    permissionApi: boolean
    templateApi: boolean
  }>({ permissionApi: false, templateApi: false })

  useEffect(() => {
    checkApiAndLoadData()
  }, [])

  const checkApiAndLoadData = async () => {
    setLoading(true)
    try {
      // 检查API可用性
      const permissionApiAvailable = await ApiChecker.checkApi(API_ENDPOINTS.PERMISSION_TREE)
      const templateApiAvailable = await ApiChecker.checkApi(API_ENDPOINTS.PERMISSION_TEMPLATES)

      setApiStatus({
        permissionApi: permissionApiAvailable,
        templateApi: templateApiAvailable
      })

      if (permissionApiAvailable || templateApiAvailable) {
        await loadData()
      } else {
        toast({
          title: 'API不可用',
          description: '权限管理API尚未实现，请联系开发人员',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('检查API失败:', error)
      toast({
        title: '检查失败',
        description: '无法检查API状态',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const loadData = async () => {
    try {
      // 只有在API可用时才加载数据
      if (apiStatus.permissionApi) {
        const permissionsResponse = await permissionManagementApi.getPermissionTree()
        if (permissionsResponse.data) {
          setPermissions(permissionsResponse.data)
        }
      }

      if (apiStatus.templateApi) {
        const templatesResponse = await permissionManagementApi.getPermissionTemplates()
        if (templatesResponse.data) {
          setTemplates(templatesResponse.data)
        }
      }

      if (apiStatus.permissionApi || apiStatus.templateApi) {
        toast({
          title: '加载成功',
          description: '权限数据加载完成',
        })
      }
    } catch (error) {
      console.error('加载权限数据失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载权限数据，请检查网络连接',
        variant: 'destructive',
      })
    }
  }

  const handleRefresh = () => {
    checkApiAndLoadData()
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">权限配置</h1>
          <p className="text-muted-foreground">
            管理系统权限和权限模板配置
          </p>
          {/* API状态显示 */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${apiStatus.permissionApi ? 'bg-green-500' : 'bg-red-500'}`} />
              <span>权限API: {apiStatus.permissionApi ? '可用' : '不可用'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${apiStatus.templateApi ? 'bg-green-500' : 'bg-red-500'}`} />
              <span>模板API: {apiStatus.templateApi ? '可用' : '不可用'}</span>
            </div>
          </div>
        </div>

        {/* 权限模板 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>权限模板</CardTitle>
              <Button variant="outline" onClick={handleRefresh}>
                刷新
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <p>加载中...</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {templates.map((template) => (
                  <Card key={template.id}>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium">{template.name}</h3>
                        <Badge variant={template.type === 'system' ? 'default' : 'secondary'}>
                          {template.type === 'system' ? '系统' : '自定义'}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {template.description}
                      </p>
                      <p className="text-sm">
                        权限数量: {template.permissions.length}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 权限列表 */}
        <Card>
          <CardHeader>
            <CardTitle>权限列表</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <p>加载中...</p>
              </div>
            ) : (
              <div className="space-y-4">
                {permissions.map((permission) => (
                  <Card key={permission.id}>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{permission.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            编码: {permission.code}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {permission.description}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline">{permission.type}</Badge>
                          <p className="text-sm mt-1">
                            状态: {permission.enabled ? '启用' : '禁用'}
                          </p>
                          <p className="text-sm">
                            排序: {permission.sort}
                          </p>
                        </div>
                      </div>
                      {permission.children && permission.children.length > 0 && (
                        <div className="mt-4 pl-4 border-l-2 border-muted">
                          <p className="text-sm font-medium mb-2">子权限:</p>
                          <div className="space-y-2">
                            {permission.children.map((child) => (
                              <div key={child.id} className="flex justify-between items-center">
                                <span className="text-sm">{child.name}</span>
                                <Badge variant="secondary" size="sm">{child.type}</Badge>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
