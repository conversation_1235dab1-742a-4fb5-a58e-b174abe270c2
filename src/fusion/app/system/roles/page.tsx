/**
 * 角色管理页面 - 系统角色和权限管理
 *
 * 菜单位置：系统管理 > 角色管理
 * 路由地址：/system/roles
 * 页面功能：管理系统角色，配置角色权限和菜单访问控制
 */

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Shield,
  RefreshCw,
  Settings,
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'

// 角色数据类型
interface Role {
  id: number
  name: string
  code: string
  description?: string
  status: boolean
  isDefault: boolean
  accountType?: string
  sort: number
  userCount: number
  createTime: string
}

// 菜单权限数据类型
interface MenuPermission {
  id: number
  name: string
  code: string
  parentId?: number
  children?: MenuPermission[]
  checked: boolean
}

export default function RolesPage() {
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isPermissionDialogOpen, setIsPermissionDialogOpen] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [menuPermissions, setMenuPermissions] = useState<MenuPermission[]>([])

  // 模拟角色数据
  useEffect(() => {
    const mockRoles: Role[] = [
      {
        id: 1,
        name: '超级管理员',
        code: 'SUPER_ADMIN',
        description: '系统超级管理员，拥有所有权限',
        status: true,
        isDefault: true,
        accountType: 'SuperAdmin',
        sort: 1,
        userCount: 1,
        createTime: '2024-01-01 00:00:00',
      },
      {
        id: 2,
        name: '系统管理员',
        code: 'SYS_ADMIN',
        description: '系统管理员，拥有系统管理权限',
        status: true,
        isDefault: true,
        accountType: 'SysAdmin',
        sort: 2,
        userCount: 2,
        createTime: '2024-01-01 00:00:00',
      },
      {
        id: 3,
        name: '普通用户',
        code: 'NORMAL_USER',
        description: '普通用户，拥有基础功能权限',
        status: true,
        isDefault: true,
        accountType: 'NormalUser',
        sort: 3,
        userCount: 8,
        createTime: '2024-01-01 00:00:00',
      },
      {
        id: 4,
        name: '操作员',
        code: 'OPERATOR',
        description: '设备操作员，拥有设备操作权限',
        status: true,
        isDefault: false,
        sort: 4,
        userCount: 3,
        createTime: '2024-03-15 10:30:00',
      },
      {
        id: 5,
        name: '观察员',
        code: 'VIEWER',
        description: '只读用户，仅能查看数据',
        status: false,
        isDefault: false,
        sort: 5,
        userCount: 0,
        createTime: '2024-06-20 14:20:00',
      },
    ]

    // 模拟菜单权限数据
    const mockMenuPermissions: MenuPermission[] = [
      {
        id: 1,
        name: '仪表盘',
        code: 'dashboard',
        checked: true,
      },
      {
        id: 2,
        name: '数据采集',
        code: 'data-collection',
        checked: true,
        children: [
          { id: 21, name: '设备列表', code: 'device-list', checked: true },
          { id: 22, name: '设备配置', code: 'device-config', checked: false },
        ],
      },
      {
        id: 3,
        name: '系统管理',
        code: 'system',
        checked: false,
        children: [
          { id: 31, name: '用户管理', code: 'user-management', checked: false },
          { id: 32, name: '角色管理', code: 'role-management', checked: false },
          { id: 33, name: '菜单管理', code: 'menu-management', checked: false },
        ],
      },
    ]
    
    setTimeout(() => {
      setRoles(mockRoles)
      setMenuPermissions(mockMenuPermissions)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredRoles = roles.filter(
    (role) =>
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddRole = () => {
    setEditingRole(null)
    setIsAddDialogOpen(true)
  }

  const handleEditRole = (role: Role) => {
    setEditingRole(role)
    setIsAddDialogOpen(true)
  }

  const handleDeleteRole = (role: Role) => {
    if (role.isDefault) {
      toast({
        title: '无法删除',
        description: '默认角色不能删除',
        variant: 'destructive',
      })
      return
    }

    if (role.userCount > 0) {
      toast({
        title: '无法删除',
        description: '该角色下还有用户，请先移除用户',
        variant: 'destructive',
      })
      return
    }

    if (confirm(`确定要删除角色 "${role.name}" 吗？`)) {
      setRoles(roles.filter(r => r.id !== role.id))
      toast({
        title: '删除成功',
        description: `角色 "${role.name}" 已被删除`,
      })
    }
  }

  const handleToggleStatus = (role: Role) => {
    if (role.isDefault) {
      toast({
        title: '无法修改',
        description: '默认角色状态不能修改',
        variant: 'destructive',
      })
      return
    }

    const updatedRoles = roles.map(r =>
      r.id === role.id ? { ...r, status: !r.status } : r
    )
    setRoles(updatedRoles)
    toast({
      title: role.status ? '角色已禁用' : '角色已启用',
      description: `角色 "${role.name}" 状态已更新`,
    })
  }

  const handleConfigurePermissions = (role: Role) => {
    setSelectedRole(role)
    setIsPermissionDialogOpen(true)
  }

  const getAccountTypeLabel = (type?: string) => {
    switch (type) {
      case 'SuperAdmin':
        return '超级管理员'
      case 'SysAdmin':
        return '系统管理员'
      case 'NormalUser':
        return '普通用户'
      default:
        return '自定义'
    }
  }

  const renderMenuPermissions = (menus: MenuPermission[], level = 0) => {
    return menus.map((menu) => (
      <div key={menu.id} style={{ marginLeft: level * 20 }}>
        <div className="flex items-center space-x-2 py-1">
          <Checkbox
            id={`menu-${menu.id}`}
            checked={menu.checked}
            onCheckedChange={(checked) => {
              // 更新菜单权限状态的逻辑
              console.log(`Menu ${menu.name} checked: ${checked}`)
            }}
          />
          <Label htmlFor={`menu-${menu.id}`} className="text-sm">
            {menu.name}
          </Label>
        </div>
        {menu.children && renderMenuPermissions(menu.children, level + 1)}
      </div>
    ))
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">角色管理</h1>
          <p className="text-muted-foreground">
            管理系统角色和权限配置
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>角色列表</CardTitle>
            <CardDescription>
              系统中所有角色的管理界面
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* 操作栏 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索角色..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 w-64"
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setLoading(true)}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  刷新
                </Button>
              </div>
              <Button onClick={handleAddRole}>
                <Plus className="h-4 w-4 mr-2" />
                添加角色
              </Button>
            </div>

            {/* 角色表格 */}
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <RefreshCw className="h-6 w-6 animate-spin" />
                <span className="ml-2">加载中...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>角色名称</TableHead>
                    <TableHead>角色编码</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>用户数</TableHead>
                    <TableHead>排序</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRoles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <span>{role.name}</span>
                          {role.isDefault && (
                            <Badge variant="outline" className="text-xs">
                              默认
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-muted px-1 py-0.5 rounded">
                          {role.code}
                        </code>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {role.description || '-'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {getAccountTypeLabel(role.accountType)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={role.status ? 'default' : 'secondary'}>
                          {role.status ? '启用' : '禁用'}
                        </Badge>
                      </TableCell>
                      <TableCell>{role.userCount}</TableCell>
                      <TableCell>{role.sort}</TableCell>
                      <TableCell>{role.createTime}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditRole(role)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleConfigurePermissions(role)}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                          {!role.isDefault && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleToggleStatus(role)}
                              >
                                <Shield className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteRole(role)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* 添加/编辑角色对话框 */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingRole ? '编辑角色' : '添加角色'}
              </DialogTitle>
              <DialogDescription>
                {editingRole ? '修改角色信息' : '创建新的系统角色'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="roleName" className="text-right">
                  角色名称
                </Label>
                <Input
                  id="roleName"
                  defaultValue={editingRole?.name}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="roleCode" className="text-right">
                  角色编码
                </Label>
                <Input
                  id="roleCode"
                  defaultValue={editingRole?.code}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="roleDescription" className="text-right">
                  描述
                </Label>
                <Textarea
                  id="roleDescription"
                  defaultValue={editingRole?.description}
                  className="col-span-3"
                  rows={3}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="roleSort" className="text-right">
                  排序
                </Label>
                <Input
                  id="roleSort"
                  type="number"
                  defaultValue={editingRole?.sort}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={() => setIsAddDialogOpen(false)}>
                {editingRole ? '保存' : '创建'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 权限配置对话框 */}
        <Dialog open={isPermissionDialogOpen} onOpenChange={setIsPermissionDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>配置权限 - {selectedRole?.name}</DialogTitle>
              <DialogDescription>
                为角色配置菜单访问权限
              </DialogDescription>
            </DialogHeader>
            <div className="max-h-96 overflow-y-auto py-4">
              {renderMenuPermissions(menuPermissions)}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPermissionDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={() => setIsPermissionDialogOpen(false)}>
                保存权限
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
