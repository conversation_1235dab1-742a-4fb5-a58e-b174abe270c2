/**
 * 在线用户页面
 *
 * 菜单位置：权限管理 > 在线用户
 * 路由地址：/system/online-users
 * 页面功能：监控当前在线用户，管理用户会话和连接状态
 *
 * 技术特点：
 * - 实时用户状态监控
 * - WebSocket连接管理
 * - 会话超时控制
 * - 强制下线功能
 *
 * 业务价值：
 * - 提供用户活动监控
 * - 支持会话安全管理
 * - 防止异常登录
 * - 优化系统资源使用
 */

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  onlineUserApi,
  OnlineUser,
  OnlineUserStats,
  OnlineUserQueryParams,
  KickUserInput
} from '@/lib/api/online-user-api'
import {
  mockOnlineUsers,
  mockOnlineUserStats,
  simulateApiDelay
} from '@/lib/api/mock-data-service'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Label } from '@/components/ui/label'
import {
  UserCheck,
  Search,
  RefreshCw,
  LogOut,
  MapPin,
  Clock,
  Monitor,
  Smartphone,
  Tablet,
  Globe,
  Wifi,
  WifiOff,
  Activity,
  AlertTriangle,
  Eye,
  MoreHorizontal,
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

// 使用API中定义的类型，这里不需要重复定义

export default function OnlineUsersPage() {
  const { toast } = useToast()
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([])
  const [stats, setStats] = useState<OnlineUserStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<OnlineUser | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isKickDialogOpen, setIsKickDialogOpen] = useState(false)
  const [userToKick, setUserToKick] = useState<OnlineUser | null>(null)

  // 模拟数据已移动到mock-data-service.ts中

  useEffect(() => {
    loadOnlineUsers()
    loadStats()
    
    // 设置定时刷新
    const interval = setInterval(() => {
      loadOnlineUsers()
      loadStats()
    }, 30000) // 每30秒刷新一次

    return () => clearInterval(interval)
  }, [])

  const loadOnlineUsers = async () => {
    try {
      // 尝试调用真实API
      try {
        const response = await onlineUserApi.getOnlineUserList()
        if (response.data) {
          setOnlineUsers(response.data.items)
          return
        }
      } catch (apiError) {
        console.warn('在线用户API未实现，使用模拟数据:', apiError)
      }

      // 如果API调用失败，使用模拟数据
      await simulateApiDelay(800)
      setOnlineUsers(mockOnlineUsers)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载在线用户数据',
        variant: 'destructive',
      })
    }
  }

  const loadStats = async () => {
    try {
      // 尝试调用真实API
      try {
        const response = await onlineUserApi.getOnlineUserStats()
        if (response.data) {
          setStats(response.data)
          return
        }
      } catch (apiError) {
        console.warn('在线用户统计API未实现，使用模拟数据:', apiError)
      }

      // 如果API调用失败，使用模拟数据
      await simulateApiDelay(500)
      setStats(mockOnlineUserStats)
    } catch (error) {
      console.error('Failed to load stats:', error)
    }
  }

  const handleViewDetails = (user: OnlineUser) => {
    setSelectedUser(user)
    setIsDetailDialogOpen(true)
  }

  const handleKickUser = (user: OnlineUser) => {
    setUserToKick(user)
    setIsKickDialogOpen(true)
  }

  const confirmKickUser = async () => {
    if (!userToKick) return

    try {
      const kickInput: KickUserInput = {
        sessionId: userToKick.sessionId,
        reason: '管理员强制下线'
      }

      const response = await onlineUserApi.kickUser(kickInput)
      if (response.data) {
        toast({
          title: '操作成功',
          description: `用户 "${userToKick.displayName}" 已被强制下线`,
        })

        // 重新加载数据
        loadOnlineUsers()
        loadStats()
        setIsKickDialogOpen(false)
        setUserToKick(null)
      }
    } catch (error) {
      toast({
        title: '操作失败',
        description: '无法强制用户下线',
        variant: 'destructive',
      })
    }
  }

  const getDeviceIcon = (device: OnlineUser['device']) => {
    switch (device) {
      case 'desktop':
        return <Monitor className="h-4 w-4" />
      case 'mobile':
        return <Smartphone className="h-4 w-4" />
      case 'tablet':
        return <Tablet className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  const getStatusBadge = (status: OnlineUser['status']) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <Activity className="h-3 w-3 mr-1" />
            活跃
          </Badge>
        )
      case 'idle':
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            空闲
          </Badge>
        )
      case 'away':
        return (
          <Badge variant="outline">
            <AlertTriangle className="h-3 w-3 mr-1" />
            离开
          </Badge>
        )
      default:
        return <Badge variant="secondary">未知</Badge>
    }
  }

  const getConnectionIcon = (type: OnlineUser['connectionType']) => {
    return type === 'websocket' ? (
      <Wifi className="h-4 w-4 text-green-600" />
    ) : (
      <Globe className="h-4 w-4 text-blue-600" />
    )
  }

  const filteredUsers = onlineUsers.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.ipAddress.includes(searchTerm) ||
    user.location.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">在线用户</h1>
          <p className="text-muted-foreground">
            监控当前在线用户状态和会话信息
          </p>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-full bg-blue-50 dark:bg-blue-950">
                    <UserCheck className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">在线用户</p>
                    <p className="text-2xl font-bold">{stats.totalOnline}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-full bg-green-50 dark:bg-green-950">
                    <Activity className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">活跃用户</p>
                    <p className="text-2xl font-bold">{stats.activeUsers}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-full bg-purple-50 dark:bg-purple-950">
                    <Wifi className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">WebSocket连接</p>
                    <p className="text-2xl font-bold">{stats.websocketConnections}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-full bg-amber-50 dark:bg-amber-950">
                    <Clock className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">平均会话时长</p>
                    <p className="text-lg font-bold">{stats.averageSessionTime}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 在线用户列表 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>在线用户列表</CardTitle>
                <CardDescription>当前系统中的所有在线用户</CardDescription>
              </div>
              <Button variant="outline" onClick={loadOnlineUsers}>
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 mb-6">
              <div className="flex items-center space-x-2 flex-1">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索用户名、IP地址或位置..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
              </div>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                <span>加载中...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>用户</TableHead>
                    <TableHead>角色</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>设备</TableHead>
                    <TableHead>连接</TableHead>
                    <TableHead>IP地址</TableHead>
                    <TableHead>位置</TableHead>
                    <TableHead>登录时间</TableHead>
                    <TableHead>最后活动</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{user.displayName}</div>
                          <div className="text-sm text-muted-foreground">@{user.username}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{user.role}</Badge>
                      </TableCell>
                      <TableCell>{getStatusBadge(user.status)}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getDeviceIcon(user.device)}
                          <div className="text-sm">
                            <div>{user.browser}</div>
                            <div className="text-muted-foreground">{user.os}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getConnectionIcon(user.connectionType)}
                          <span className="text-sm capitalize">{user.connectionType}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-muted px-2 py-1 rounded">
                          {user.ipAddress}
                        </code>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{user.location}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-sm">{user.loginTime}</TableCell>
                      <TableCell className="text-sm">{user.lastActivity}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetails(user)}>
                              <Eye className="h-4 w-4 mr-2" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleKickUser(user)}
                              className="text-red-600">
                              <LogOut className="h-4 w-4 mr-2" />
                              强制下线
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* 用户详情对话框 */}
        <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>用户详情</DialogTitle>
              <DialogDescription>
                查看用户的详细会话信息
              </DialogDescription>
            </DialogHeader>
            {selectedUser && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">用户信息</Label>
                  <div className="text-sm space-y-1">
                    <div>用户名: {selectedUser.username}</div>
                    <div>显示名: {selectedUser.displayName}</div>
                    <div>角色: {selectedUser.role}</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium">会话信息</Label>
                  <div className="text-sm space-y-1">
                    <div>会话ID: {selectedUser.sessionId}</div>
                    <div>登录时间: {selectedUser.loginTime}</div>
                    <div>最后活动: {selectedUser.lastActivity}</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium">设备信息</Label>
                  <div className="text-sm space-y-1">
                    <div>设备类型: {selectedUser.device}</div>
                    <div>浏览器: {selectedUser.browser}</div>
                    <div>操作系统: {selectedUser.os}</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium">网络信息</Label>
                  <div className="text-sm space-y-1">
                    <div>IP地址: {selectedUser.ipAddress}</div>
                    <div>位置: {selectedUser.location}</div>
                    <div>连接类型: {selectedUser.connectionType}</div>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>
                关闭
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 强制下线确认对话框 */}
        <AlertDialog open={isKickDialogOpen} onOpenChange={setIsKickDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认强制下线</AlertDialogTitle>
              <AlertDialogDescription>
                您确定要强制用户 "{userToKick?.displayName}" 下线吗？
                此操作将立即终止该用户的会话，用户需要重新登录。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction onClick={confirmKickUser} className="bg-red-600 hover:bg-red-700">
                确认下线
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </MainLayout>
  )
}
