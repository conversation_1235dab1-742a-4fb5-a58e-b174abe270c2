/**
 * 菜单管理页面 - 系统菜单结构管理
 *
 * 菜单位置：系统管理 > 菜单管理
 * 路由地址：/system/menus
 * 页面功能：管理系统菜单结构，配置菜单层级和访问权限
 */

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Menu,
  RefreshCw,
  Eye,
  EyeOff,
  ChevronRight,
  ChevronDown,
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'

// 菜单数据类型
interface MenuItem {
  id: number
  name: string
  code: string
  path?: string
  icon?: string
  parentId?: number
  level: number
  sort: number
  status: boolean
  hidden: boolean
  menuType: 'Directory' | 'Menu' | 'Button'
  children?: MenuItem[]
  expanded?: boolean
  createTime: string
}

export default function MenusPage() {
  const [menus, setMenus] = useState<MenuItem[]>([])
  const [flatMenus, setFlatMenus] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingMenu, setEditingMenu] = useState<MenuItem | null>(null)

  // 模拟菜单数据
  useEffect(() => {
    const mockMenus: MenuItem[] = [
      {
        id: 1,
        name: '仪表盘',
        code: 'dashboard',
        path: '/dashboard',
        icon: 'BarChart3',
        level: 1,
        sort: 1,
        status: true,
        hidden: false,
        menuType: 'Menu',
        createTime: '2024-01-01 00:00:00',
      },
      {
        id: 2,
        name: '数据采集',
        code: 'data-collection',
        icon: 'Database',
        level: 1,
        sort: 2,
        status: true,
        hidden: false,
        menuType: 'Directory',
        createTime: '2024-01-01 00:00:00',
        children: [
          {
            id: 21,
            name: '设备管理',
            code: 'devices',
            path: '/devices',
            parentId: 2,
            level: 2,
            sort: 1,
            status: true,
            hidden: false,
            menuType: 'Menu',
            createTime: '2024-01-01 00:00:00',
          },
          {
            id: 22,
            name: '数据标签',
            code: 'tags',
            path: '/devices/tags',
            parentId: 2,
            level: 2,
            sort: 2,
            status: true,
            hidden: false,
            menuType: 'Menu',
            createTime: '2024-01-01 00:00:00',
          },
        ],
      },
      {
        id: 3,
        name: '系统管理',
        code: 'system',
        icon: 'Settings',
        level: 1,
        sort: 10,
        status: true,
        hidden: false,
        menuType: 'Directory',
        createTime: '2024-01-01 00:00:00',
        children: [
          {
            id: 31,
            name: '用户管理',
            code: 'users',
            path: '/system/users',
            parentId: 3,
            level: 2,
            sort: 1,
            status: true,
            hidden: false,
            menuType: 'Menu',
            createTime: '2024-01-01 00:00:00',
          },
          {
            id: 32,
            name: '角色管理',
            code: 'roles',
            path: '/system/roles',
            parentId: 3,
            level: 2,
            sort: 2,
            status: true,
            hidden: false,
            menuType: 'Menu',
            createTime: '2024-01-01 00:00:00',
          },
          {
            id: 33,
            name: '菜单管理',
            code: 'menus',
            path: '/system/menus',
            parentId: 3,
            level: 2,
            sort: 3,
            status: true,
            hidden: false,
            menuType: 'Menu',
            createTime: '2024-01-01 00:00:00',
          },
        ],
      },
    ]

    // 展平菜单数据用于表格显示
    const flattenMenus = (menus: MenuItem[], result: MenuItem[] = []): MenuItem[] => {
      menus.forEach(menu => {
        result.push({ ...menu, expanded: true })
        if (menu.children) {
          flattenMenus(menu.children, result)
        }
      })
      return result
    }
    
    setTimeout(() => {
      setMenus(mockMenus)
      setFlatMenus(flattenMenus(mockMenus))
      setLoading(false)
    }, 1000)
  }, [])

  const filteredMenus = flatMenus.filter(
    (menu) =>
      menu.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      menu.code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddMenu = () => {
    setEditingMenu(null)
    setIsAddDialogOpen(true)
  }

  const handleEditMenu = (menu: MenuItem) => {
    setEditingMenu(menu)
    setIsAddDialogOpen(true)
  }

  const handleDeleteMenu = (menu: MenuItem) => {
    if (menu.children && menu.children.length > 0) {
      toast({
        title: '无法删除',
        description: '该菜单下还有子菜单，请先删除子菜单',
        variant: 'destructive',
      })
      return
    }

    if (confirm(`确定要删除菜单 "${menu.name}" 吗？`)) {
      // 这里应该调用删除API
      toast({
        title: '删除成功',
        description: `菜单 "${menu.name}" 已被删除`,
      })
    }
  }

  const handleToggleStatus = (menu: MenuItem) => {
    // 这里应该调用更新API
    toast({
      title: menu.status ? '菜单已禁用' : '菜单已启用',
      description: `菜单 "${menu.name}" 状态已更新`,
    })
  }

  const handleToggleHidden = (menu: MenuItem) => {
    // 这里应该调用更新API
    toast({
      title: menu.hidden ? '菜单已显示' : '菜单已隐藏',
      description: `菜单 "${menu.name}" 可见性已更新`,
    })
  }

  const getMenuTypeLabel = (type: string) => {
    switch (type) {
      case 'Directory':
        return '目录'
      case 'Menu':
        return '菜单'
      case 'Button':
        return '按钮'
      default:
        return type
    }
  }

  const getMenuTypeBadge = (type: string) => {
    switch (type) {
      case 'Directory':
        return <Badge variant="outline">目录</Badge>
      case 'Menu':
        return <Badge variant="default">菜单</Badge>
      case 'Button':
        return <Badge variant="secondary">按钮</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  const renderMenuName = (menu: MenuItem) => {
    const indent = (menu.level - 1) * 20
    return (
      <div className="flex items-center" style={{ marginLeft: indent }}>
        {menu.children && menu.children.length > 0 ? (
          menu.expanded ? (
            <ChevronDown className="h-4 w-4 mr-1" />
          ) : (
            <ChevronRight className="h-4 w-4 mr-1" />
          )
        ) : (
          <div className="w-5" />
        )}
        <span>{menu.name}</span>
      </div>
    )
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">菜单管理</h1>
          <p className="text-muted-foreground">
            管理系统菜单结构和权限配置
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>菜单列表</CardTitle>
            <CardDescription>
              系统菜单的层级结构管理
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* 操作栏 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索菜单..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 w-64"
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setLoading(true)}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  刷新
                </Button>
              </div>
              <Button onClick={handleAddMenu}>
                <Plus className="h-4 w-4 mr-2" />
                添加菜单
              </Button>
            </div>

            {/* 菜单表格 */}
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <RefreshCw className="h-6 w-6 animate-spin" />
                <span className="ml-2">加载中...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>菜单名称</TableHead>
                    <TableHead>菜单编码</TableHead>
                    <TableHead>路径</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>图标</TableHead>
                    <TableHead>排序</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>可见</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMenus.map((menu) => (
                    <TableRow key={menu.id}>
                      <TableCell className="font-medium">
                        {renderMenuName(menu)}
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-muted px-1 py-0.5 rounded">
                          {menu.code}
                        </code>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {menu.path || '-'}
                        </span>
                      </TableCell>
                      <TableCell>{getMenuTypeBadge(menu.menuType)}</TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {menu.icon || '-'}
                        </span>
                      </TableCell>
                      <TableCell>{menu.sort}</TableCell>
                      <TableCell>
                        <Badge variant={menu.status ? 'default' : 'secondary'}>
                          {menu.status ? '启用' : '禁用'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={menu.hidden ? 'secondary' : 'default'}>
                          {menu.hidden ? '隐藏' : '显示'}
                        </Badge>
                      </TableCell>
                      <TableCell>{menu.createTime}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditMenu(menu)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleHidden(menu)}
                          >
                            {menu.hidden ? (
                              <Eye className="h-4 w-4" />
                            ) : (
                              <EyeOff className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteMenu(menu)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* 添加/编辑菜单对话框 */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingMenu ? '编辑菜单' : '添加菜单'}
              </DialogTitle>
              <DialogDescription>
                {editingMenu ? '修改菜单信息' : '创建新的系统菜单'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuName" className="text-right">
                  菜单名称
                </Label>
                <Input
                  id="menuName"
                  defaultValue={editingMenu?.name}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuCode" className="text-right">
                  菜单编码
                </Label>
                <Input
                  id="menuCode"
                  defaultValue={editingMenu?.code}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuPath" className="text-right">
                  路径
                </Label>
                <Input
                  id="menuPath"
                  defaultValue={editingMenu?.path}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuType" className="text-right">
                  菜单类型
                </Label>
                <Select defaultValue={editingMenu?.menuType}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="选择菜单类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Directory">目录</SelectItem>
                    <SelectItem value="Menu">菜单</SelectItem>
                    <SelectItem value="Button">按钮</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="parentMenu" className="text-right">
                  父级菜单
                </Label>
                <Select>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="选择父级菜单（可选）" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">根目录</SelectItem>
                    <SelectItem value="2">数据采集</SelectItem>
                    <SelectItem value="3">系统管理</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuIcon" className="text-right">
                  图标
                </Label>
                <Input
                  id="menuIcon"
                  defaultValue={editingMenu?.icon}
                  className="col-span-3"
                  placeholder="Lucide图标名称"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuSort" className="text-right">
                  排序
                </Label>
                <Input
                  id="menuSort"
                  type="number"
                  defaultValue={editingMenu?.sort}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuStatus" className="text-right">
                  状态
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="menuStatus"
                    defaultChecked={editingMenu?.status ?? true}
                  />
                  <Label htmlFor="menuStatus">启用</Label>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuHidden" className="text-right">
                  可见性
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="menuHidden"
                    defaultChecked={!(editingMenu?.hidden ?? false)}
                  />
                  <Label htmlFor="menuHidden">显示</Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={() => setIsAddDialogOpen(false)}>
                {editingMenu ? '保存' : '创建'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
