/**
 * 权限配置页面
 *
 * 菜单位置：权限管理 > 权限配置
 * 路由地址：/system/permissions
 * 页面功能：配置系统权限策略，包括功能权限、数据权限、API权限等
 *
 * 技术特点：
 * - 权限树形结构管理
 * - 权限继承和覆盖机制
 * - 细粒度权限控制
 * - 权限模板和预设
 *
 * 业务价值：
 * - 提供灵活的权限配置能力
 * - 支持复杂的权限策略
 * - 简化权限管理流程
 * - 确保系统安全性
 */

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  permissionManagementApi,
  SysPermission,
  PermissionTemplate,
  PermissionInput,
  PermissionTemplateInput
} from '@/lib/api/permission-management-api'
import {
  mockPermissions,
  mockPermissionTemplates,
  simulateApiDelay
} from '@/lib/api/mock-data-service'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Key,
  Search,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Settings,
  Shield,
  Database,
  Globe,
  FileText,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  ChevronRight,
  ChevronDown,
  Menu,
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

// 使用API中定义的类型，这里不需要重复定义

export default function PermissionsPage() {
  const { toast } = useToast()
  const [permissions, setPermissions] = useState<SysPermission[]>([])
  const [templates, setTemplates] = useState<PermissionTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [isPermissionDialogOpen, setIsPermissionDialogOpen] = useState(false)
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false)
  const [editingPermission, setEditingPermission] = useState<SysPermission | null>(null)
  const [editingTemplate, setEditingTemplate] = useState<PermissionTemplate | null>(null)
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())

  // 模拟权限数据
  const mockPermissions: Permission[] = [
    {
      id: '1',
      name: '仪表盘',
      code: 'dashboard',
      type: 'module',
      description: '仪表盘模块访问权限',
      enabled: true,
      visible: true,
      sort: 1,
      createdAt: '2024-01-01 00:00:00',
      updatedAt: '2024-01-01 00:00:00',
      children: [
        {
          id: '1-1',
          name: '查看仪表盘',
          code: 'dashboard:view',
          type: 'function',
          description: '查看仪表盘数据',
          parentId: '1',
          enabled: true,
          visible: true,
          sort: 1,
          createdAt: '2024-01-01 00:00:00',
          updatedAt: '2024-01-01 00:00:00',
        },
        {
          id: '1-2',
          name: '导出数据',
          code: 'dashboard:export',
          type: 'function',
          description: '导出仪表盘数据',
          parentId: '1',
          enabled: true,
          visible: true,
          sort: 2,
          createdAt: '2024-01-01 00:00:00',
          updatedAt: '2024-01-01 00:00:00',
        },
      ],
    },
    {
      id: '2',
      name: '数据采集',
      code: 'data-collection',
      type: 'module',
      description: '数据采集模块权限',
      enabled: true,
      visible: true,
      sort: 2,
      createdAt: '2024-01-01 00:00:00',
      updatedAt: '2024-01-01 00:00:00',
      children: [
        {
          id: '2-1',
          name: '设备管理',
          code: 'devices:manage',
          type: 'function',
          description: '设备增删改查权限',
          parentId: '2',
          enabled: true,
          visible: true,
          sort: 1,
          createdAt: '2024-01-01 00:00:00',
          updatedAt: '2024-01-01 00:00:00',
        },
        {
          id: '2-2',
          name: '设备数据',
          code: 'devices:data',
          type: 'data',
          description: '设备数据访问权限',
          parentId: '2',
          enabled: true,
          visible: true,
          sort: 2,
          createdAt: '2024-01-01 00:00:00',
          updatedAt: '2024-01-01 00:00:00',
        },
      ],
    },
    {
      id: '3',
      name: '系统管理',
      code: 'system',
      type: 'module',
      description: '系统管理模块权限',
      enabled: true,
      visible: true,
      sort: 10,
      createdAt: '2024-01-01 00:00:00',
      updatedAt: '2024-01-01 00:00:00',
      children: [
        {
          id: '3-1',
          name: '用户管理',
          code: 'system:users',
          type: 'function',
          description: '用户管理权限',
          parentId: '3',
          enabled: true,
          visible: true,
          sort: 1,
          createdAt: '2024-01-01 00:00:00',
          updatedAt: '2024-01-01 00:00:00',
        },
        {
          id: '3-2',
          name: '角色管理',
          code: 'system:roles',
          type: 'function',
          description: '角色管理权限',
          parentId: '3',
          enabled: true,
          visible: true,
          sort: 2,
          createdAt: '2024-01-01 00:00:00',
          updatedAt: '2024-01-01 00:00:00',
        },
        {
          id: '3-3',
          name: '权限配置',
          code: 'system:permissions',
          type: 'function',
          description: '权限配置管理',
          parentId: '3',
          enabled: true,
          visible: true,
          sort: 3,
          createdAt: '2024-01-01 00:00:00',
          updatedAt: '2024-01-01 00:00:00',
        },
      ],
    },
  ]

  // 模拟权限模板数据
  const mockTemplates: PermissionTemplate[] = [
    {
      id: '1',
      name: '超级管理员',
      description: '拥有所有权限的超级管理员模板',
      permissions: ['1', '1-1', '1-2', '2', '2-1', '2-2', '3', '3-1', '3-2', '3-3'],
      type: 'system',
      createdAt: '2024-01-01 00:00:00',
    },
    {
      id: '2',
      name: '普通用户',
      description: '基础功能权限模板',
      permissions: ['1', '1-1', '2', '2-2'],
      type: 'system',
      createdAt: '2024-01-01 00:00:00',
    },
    {
      id: '3',
      name: '设备操作员',
      description: '设备管理相关权限模板',
      permissions: ['1', '1-1', '2', '2-1', '2-2'],
      type: 'custom',
      createdAt: '2024-03-15 10:30:00',
    },
  ]

  useEffect(() => {
    loadPermissions()
    loadTemplates()
  }, [])

  const loadPermissions = async () => {
    try {
      // 尝试调用真实API
      try {
        const response = await permissionManagementApi.getPermissionTree()
        if (response.data) {
          setPermissions(response.data)
          // 默认展开所有节点
          const allIds = new Set<string>()
          const collectIds = (items: SysPermission[]) => {
            items.forEach(item => {
              allIds.add(item.id.toString())
              if (item.children) {
                collectIds(item.children)
              }
            })
          }
          collectIds(response.data)
          setExpandedNodes(allIds)
          return
        }
      } catch (apiError) {
        console.warn('权限API未实现，使用模拟数据:', apiError)
      }

      // 如果API调用失败，使用模拟数据
      await simulateApiDelay(1000)
      setPermissions(mockPermissions)
      // 默认展开所有节点
      const allIds = new Set<string>()
      const collectIds = (items: SysPermission[]) => {
        items.forEach(item => {
          allIds.add(item.id.toString())
          if (item.children) {
            collectIds(item.children)
          }
        })
      }
      collectIds(mockPermissions)
      setExpandedNodes(allIds)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载权限数据',
        variant: 'destructive',
      })
    }
  }

  const loadTemplates = async () => {
    try {
      // 尝试调用真实API
      try {
        const response = await permissionManagementApi.getPermissionTemplates()
        if (response.data) {
          setTemplates(response.data)
          return
        }
      } catch (apiError) {
        console.warn('权限模板API未实现，使用模拟数据:', apiError)
      }

      // 如果API调用失败，使用模拟数据
      await simulateApiDelay(500)
      setTemplates(mockPermissionTemplates)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载权限模板',
        variant: 'destructive',
      })
    }
  }

  const handleCreatePermission = () => {
    setEditingPermission(null)
    setIsPermissionDialogOpen(true)
  }

  const handleEditPermission = (permission: SysPermission) => {
    setEditingPermission(permission)
    setIsPermissionDialogOpen(true)
  }

  const handleDeletePermission = async (permission: SysPermission) => {
    try {
      const response = await permissionManagementApi.deletePermission(permission.id)
      if (response.data) {
        toast({
          title: '删除成功',
          description: `权限 "${permission.name}" 已删除`,
        })
        loadPermissions()
      }
    } catch (error) {
      toast({
        title: '删除失败',
        description: '无法删除权限',
        variant: 'destructive',
      })
    }
  }

  const handleCreateTemplate = () => {
    setEditingTemplate(null)
    setIsTemplateDialogOpen(true)
  }

  const handleEditTemplate = (template: PermissionTemplate) => {
    setEditingTemplate(template)
    setIsTemplateDialogOpen(true)
  }

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
  }

  const getPermissionTypeIcon = (type: Permission['type']) => {
    switch (type) {
      case 'module':
        return <Shield className="h-4 w-4" />
      case 'function':
        return <Key className="h-4 w-4" />
      case 'data':
        return <Database className="h-4 w-4" />
      case 'api':
        return <Globe className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getPermissionTypeBadge = (type: Permission['type']) => {
    const variants = {
      module: 'default',
      function: 'secondary',
      data: 'outline',
      api: 'destructive',
    } as const

    const labels = {
      module: '模块',
      function: '功能',
      data: '数据',
      api: 'API',
    }

    return (
      <Badge variant={variants[type]}>
        {labels[type]}
      </Badge>
    )
  }

  const renderPermissionTree = (items: Permission[], level = 0) => {
    return items.map((permission) => (
      <div key={permission.id}>
        <TableRow className="hover:bg-muted/50">
          <TableCell>
            <div className="flex items-center" style={{ paddingLeft: `${level * 20}px` }}>
              {permission.children && permission.children.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 mr-2"
                  onClick={() => toggleNode(permission.id)}>
                  {expandedNodes.has(permission.id) ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              )}
              <div className="flex items-center space-x-2">
                {getPermissionTypeIcon(permission.type)}
                <span className="font-medium">{permission.name}</span>
              </div>
            </div>
          </TableCell>
          <TableCell>
            <code className="text-xs bg-muted px-2 py-1 rounded">
              {permission.code}
            </code>
          </TableCell>
          <TableCell>{getPermissionTypeBadge(permission.type)}</TableCell>
          <TableCell className="max-w-xs truncate">
            {permission.description}
          </TableCell>
          <TableCell>
            {permission.enabled ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <Unlock className="h-3 w-3 mr-1" />
                启用
              </Badge>
            ) : (
              <Badge variant="secondary">
                <Lock className="h-3 w-3 mr-1" />
                禁用
              </Badge>
            )}
          </TableCell>
          <TableCell>
            {permission.visible ? (
              <Eye className="h-4 w-4 text-green-600" />
            ) : (
              <EyeOff className="h-4 w-4 text-gray-400" />
            )}
          </TableCell>
          <TableCell>{permission.sort}</TableCell>
          <TableCell>{permission.createTime}</TableCell>
          <TableCell>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleEditPermission(permission)}>
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDeletePermission(permission)}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </TableCell>
        </TableRow>
        {permission.children && 
         expandedNodes.has(permission.id) && 
         renderPermissionTree(permission.children, level + 1)}
      </div>
    ))
  }

  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.code.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || permission.type === selectedType
    return matchesSearch && matchesType
  })

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">权限配置</h1>
          <p className="text-muted-foreground">
            配置系统权限策略，管理功能权限、数据权限和API权限
          </p>
        </div>

        {/* 权限模板卡片 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>权限模板</CardTitle>
                <CardDescription>预定义的权限配置模板</CardDescription>
              </div>
              <Button onClick={handleCreateTemplate}>
                <Plus className="h-4 w-4 mr-2" />
                新建模板
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {templates.map((template) => (
                <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-base">{template.name}</CardTitle>
                      <Badge variant={template.type === 'system' ? 'default' : 'secondary'}>
                        {template.type === 'system' ? '系统' : '自定义'}
                      </Badge>
                    </div>
                    <CardDescription className="text-sm">
                      {template.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">
                        {template.permissions.length} 个权限
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditTemplate(template)}>
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 权限列表 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>权限列表</CardTitle>
                <CardDescription>系统中所有权限的层级结构</CardDescription>
              </div>
              <Button onClick={handleCreatePermission}>
                <Plus className="h-4 w-4 mr-2" />
                添加权限
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 mb-6">
              <div className="flex items-center space-x-2 flex-1">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索权限..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
              </div>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="module">模块</SelectItem>
                  <SelectItem value="function">功能</SelectItem>
                  <SelectItem value="data">数据</SelectItem>
                  <SelectItem value="api">API</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={loadPermissions}>
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                <span>加载中...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>权限名称</TableHead>
                    <TableHead>权限编码</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>可见</TableHead>
                    <TableHead>排序</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {renderPermissionTree(filteredPermissions)}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
