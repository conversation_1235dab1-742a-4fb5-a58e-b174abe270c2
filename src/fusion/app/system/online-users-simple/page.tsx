import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import {
  onlineUserApi,
  OnlineUser,
  OnlineUserStats
} from '@/lib/api/online-user-api'
import { ApiChecker, API_ENDPOINTS } from '@/lib/api/api-checker'

export default function OnlineUsersSimplePage() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [users, setUsers] = useState<OnlineUser[]>([])
  const [stats, setStats] = useState<OnlineUserStats | null>(null)
  const [apiAvailable, setApiAvailable] = useState(false)

  useEffect(() => {
    checkApiAndLoadData()
  }, [])

  const checkApiAndLoadData = async () => {
    setLoading(true)
    try {
      // 检查API可用性
      const available = await ApiChecker.checkApi(API_ENDPOINTS.ONLINE_USERS)
      setApiAvailable(available)

      if (available) {
        await loadData()
      } else {
        toast({
          title: 'API不可用',
          description: '在线用户API尚未实现，请联系开发人员',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('检查API失败:', error)
      toast({
        title: '检查失败',
        description: '无法检查API状态',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const loadData = async () => {
    try {
      // 加载在线用户列表
      const usersResponse = await onlineUserApi.getOnlineUserList()
      if (usersResponse.data) {
        setUsers(usersResponse.data.items)
      }

      // 加载统计数据
      const statsResponse = await onlineUserApi.getOnlineUserStats()
      if (statsResponse.data) {
        setStats(statsResponse.data)
      }

      toast({
        title: '加载成功',
        description: '在线用户数据加载完成',
      })
    } catch (error) {
      console.error('加载在线用户数据失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载在线用户数据，请检查网络连接',
        variant: 'destructive',
      })
    }
  }

  const handleRefresh = () => {
    checkApiAndLoadData()
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">在线用户</h1>
          <p className="text-muted-foreground">
            监控当前在线用户状态和会话信息
          </p>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">在线用户</p>
                  <p className="text-2xl font-bold">{stats.totalOnline}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">活跃用户</p>
                  <p className="text-2xl font-bold">{stats.activeUsers}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">WebSocket连接</p>
                  <p className="text-2xl font-bold">{stats.websocketConnections}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">平均会话时长</p>
                  <p className="text-lg font-bold">{stats.averageSessionTime}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 在线用户列表 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>在线用户列表</CardTitle>
              </div>
              <Button variant="outline" onClick={handleRefresh}>
                刷新
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <p>加载中...</p>
              </div>
            ) : (
              <div className="space-y-4">
                {users.map((user) => (
                  <Card key={user.id}>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-medium">{user.displayName}</h3>
                          <p className="text-sm text-muted-foreground">@{user.username}</p>
                          <p className="text-sm text-muted-foreground">{user.role}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm">状态: {user.status}</p>
                          <p className="text-sm">IP: {user.ipAddress}</p>
                          <p className="text-sm">位置: {user.location}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
