/**
 * 用户管理页面 - 系统用户的增删改查
 *
 * 菜单位置：系统管理 > 用户管理
 * 路由地址：/system/users
 * 页面功能：管理系统用户，包括用户的创建、编辑、删除和状态管理
 */

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  User<PERSON>heck,
  UserX,
  RefreshCw,
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { useUserManagement } from '@/hooks/use-user-management'
import { SysUser, UserInput } from '@/lib/api/user-management-api'

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<SysUser | null>(null)
  const [formData, setFormData] = useState<UserInput>({
    account: '',
    name: '',
    password: '',
    accountType: 'NormalUser',
    status: true,
  })

  // 使用用户管理Hook
  const {
    users,
    loading,
    createLoading,
    updateLoading,
    deleteLoading,
    createUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    searchUsers,
    refresh,
  } = useUserManagement()

  // 搜索处理
  const handleSearch = async (term: string) => {
    setSearchTerm(term)
    await searchUsers(term)
  }

  const handleAddUser = () => {
    setEditingUser(null)
    setFormData({
      account: '',
      name: '',
      password: '',
      accountType: 'NormalUser',
      status: true,
    })
    setIsAddDialogOpen(true)
  }

  const handleEditUser = (user: SysUser) => {
    setEditingUser(user)
    setFormData({
      account: user.account,
      name: user.name,
      accountType: user.accountType,
      status: user.status,
    })
    setIsAddDialogOpen(true)
  }

  const handleDeleteUser = async (user: SysUser) => {
    if (confirm(`确定要删除用户 "${user.name}" 吗？`)) {
      await deleteUser(user.id)
    }
  }

  const handleToggleStatus = async (user: SysUser) => {
    await toggleUserStatus(user.id, !user.status)
  }

  const handleSaveUser = async () => {
    if (!formData.account || !formData.name) {
      toast({
        title: '验证失败',
        description: '请填写必填字段',
        variant: 'destructive',
      })
      return
    }

    let success = false
    if (editingUser) {
      success = await updateUser(editingUser.id, formData)
    } else {
      if (!formData.password) {
        toast({
          title: '验证失败',
          description: '新用户必须设置密码',
          variant: 'destructive',
        })
        return
      }
      success = await createUser(formData)
    }

    if (success) {
      setIsAddDialogOpen(false)
      setEditingUser(null)
    }
  }

  const getAccountTypeLabel = (type: string) => {
    switch (type) {
      case 'SuperAdmin':
        return '超级管理员'
      case 'SysAdmin':
        return '系统管理员'
      case 'NormalUser':
        return '普通用户'
      default:
        return type
    }
  }

  const getAccountTypeBadge = (type: string) => {
    switch (type) {
      case 'SuperAdmin':
        return <Badge variant="destructive">超级管理员</Badge>
      case 'SysAdmin':
        return <Badge variant="default">系统管理员</Badge>
      case 'NormalUser':
        return <Badge variant="secondary">普通用户</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">用户管理</h1>
          <p className="text-muted-foreground">
            管理系统用户账号，包括用户信息、权限和状态
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>用户列表</CardTitle>
            <CardDescription>
              系统中所有用户的管理界面
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* 操作栏 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索用户..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-8 w-64"
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refresh}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  刷新
                </Button>
              </div>
              <Button onClick={handleAddUser}>
                <Plus className="h-4 w-4 mr-2" />
                添加用户
              </Button>
            </div>

            {/* 用户表格 */}
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <RefreshCw className="h-6 w-6 animate-spin" />
                <span className="ml-2">加载中...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>账号</TableHead>
                    <TableHead>姓名</TableHead>
                    <TableHead>账号类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>最后登录</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.account}</TableCell>
                      <TableCell>{user.name}</TableCell>
                      <TableCell>{getAccountTypeBadge(user.accountType)}</TableCell>
                      <TableCell>
                        <Badge variant={user.status ? 'default' : 'secondary'}>
                          {user.status ? '启用' : '禁用'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {user.lastLoginTime ? (
                          <div>
                            <div>{user.lastLoginTime}</div>
                            <div className="text-xs text-muted-foreground">
                              {user.lastLoginIp}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">从未登录</span>
                        )}
                      </TableCell>
                      <TableCell>{user.createTime}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleStatus(user)}
                          >
                            {user.status ? (
                              <UserX className="h-4 w-4" />
                            ) : (
                              <UserCheck className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteUser(user)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* 添加用户对话框 */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>添加用户</DialogTitle>
              <DialogDescription>
                创建新的系统用户账号
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="account" className="text-right">
                  账号
                </Label>
                <Input
                  id="account"
                  value={formData.account}
                  onChange={(e) => setFormData({ ...formData, account: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  姓名
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                />
              </div>
              {!editingUser && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="password" className="text-right">
                    密码
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password || ''}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="col-span-3"
                  />
                </div>
              )}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="accountType" className="text-right">
                  账号类型
                </Label>
                <Select
                  value={formData.accountType}
                  onValueChange={(value) => setFormData({ ...formData, accountType: value })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="选择账号类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="NormalUser">普通用户</SelectItem>
                    <SelectItem value="SysAdmin">系统管理员</SelectItem>
                    <SelectItem value="SuperAdmin">超级管理员</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleSaveUser}
                disabled={createLoading || updateLoading}
              >
                {createLoading || updateLoading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                {editingUser ? '保存' : '创建用户'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
