/**
 * 系统管理主页面 - 权限管理和系统配置中心
 *
 * 菜单位置：侧边栏主导航 > 系统管理
 * 路由地址：/system
 * 页面功能：提供用户、角色、菜单等权限管理功能的统一入口
 *
 * 安全考虑：
 * - 权限验证和访问控制
 * - 敏感操作的审计日志
 * - 重要操作的二次确认
 * - 数据权限的细粒度控制
 *
 * 业务价值：
 * - 提供权限管理的统一入口
 * - 简化用户和角色管理工作
 * - 提升系统安全性和管理效率
 * - 支持灵活的权限配置
 */

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Link } from 'react-router-dom'
import {
  Users,
  Shield,
  Menu,
  Settings,
  UserCheck,
  Lock,
} from 'lucide-react'
import { MainLayout } from '@/components/layout/main-layout'

export default function SystemPage() {
  const systemCategories = [
    {
      title: '用户管理',
      description: '管理系统用户，包括添加、编辑、删除用户和用户状态管理',
      href: '/system/users',
      icon: Users,
      stats: '2 个用户',
    },
    {
      title: '角色管理',
      description: '管理用户角色，配置角色权限和菜单访问控制',
      href: '/system/roles',
      icon: Shield,
      stats: '3 个角色',
    },
    {
      title: '菜单管理',
      description: '管理系统菜单结构，配置菜单层级和访问权限',
      href: '/system/menus',
      icon: Menu,
      stats: '15 个菜单',
    },
    {
      title: '权限配置',
      description: '配置系统权限策略，管理功能权限和数据权限',
      href: '/system/permissions',
      icon: Lock,
      stats: '配置中心',
    },
    {
      title: '在线用户',
      description: '查看当前在线用户，管理用户会话和强制下线',
      href: '/system/online-users',
      icon: UserCheck,
      stats: '1 个在线',
    },
    {
      title: '系统日志',
      description: '查看系统操作日志，包括登录日志和操作审计',
      href: '/system/logs',
      icon: Settings,
      stats: '审计追踪',
    },
  ]

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">系统管理</h1>
          <p className="text-muted-foreground">
            管理系统用户、角色、权限和安全配置
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {systemCategories.map((category) => (
            <Link to={category.href} key={category.href}>
              <Card className="h-full hover:bg-muted/50 transition-colors cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-lg font-medium">
                    {category.title}
                  </CardTitle>
                  <category.icon className="h-5 w-5 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm mb-2">
                    {category.description}
                  </CardDescription>
                  <div className="text-xs text-muted-foreground">
                    {category.stats}
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* 快速统计 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总用户数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2</div>
              <p className="text-xs text-muted-foreground">
                全部活跃
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃角色</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">
                全部启用
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">在线用户</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1</div>
              <p className="text-xs text-muted-foreground">
                实时统计
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">系统菜单</CardTitle>
              <Menu className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">15</div>
              <p className="text-xs text-muted-foreground">
                多级结构
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
