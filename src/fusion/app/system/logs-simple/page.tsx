import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'
import {
  systemLogApi,
  SystemLog,
  LogStats
} from '@/lib/api/system-log-api'
import { Api<PERSON>he<PERSON>, API_ENDPOINTS } from '@/lib/api/api-checker'

export default function SystemLogsSimplePage() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [logs, setLogs] = useState<SystemLog[]>([])
  const [stats, setStats] = useState<LogStats | null>(null)
  const [apiAvailable, setApiAvailable] = useState(false)

  useEffect(() => {
    checkApiAndLoadData()
  }, [])

  const checkApiAndLoadData = async () => {
    setLoading(true)
    try {
      // 检查API可用性
      const available = await ApiChecker.checkApi(API_ENDPOINTS.LOG_LIST)
      setApiAvailable(available)

      if (available) {
        await loadData()
      } else {
        toast({
          title: 'API不可用',
          description: '系统日志API尚未实现，请联系开发人员',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('检查API失败:', error)
      toast({
        title: '检查失败',
        description: '无法检查API状态',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const loadData = async () => {
    try {
      // 加载系统日志列表
      const logsResponse = await systemLogApi.getLogList({ page: 1, pageSize: 20 })
      if (logsResponse.data) {
        setLogs(logsResponse.data.items)
      }

      // 加载统计数据
      const statsResponse = await systemLogApi.getLogStats()
      if (statsResponse.data) {
        setStats(statsResponse.data)
      }

      toast({
        title: '加载成功',
        description: '系统日志数据加载完成',
      })
    } catch (error) {
      console.error('加载系统日志失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载系统日志，请检查网络连接',
        variant: 'destructive',
      })
    }
  }

  const handleRefresh = () => {
    checkApiAndLoadData()
  }

  const handleExport = async () => {
    try {
      const response = await systemLogApi.exportLogs({ format: 'excel' })
      if (response.data) {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `system-logs-${new Date().toISOString().split('T')[0]}.xlsx`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)

        toast({
          title: '导出成功',
          description: '日志文件已生成并开始下载',
        })
      }
    } catch (error) {
      console.error('导出日志失败:', error)
      toast({
        title: '导出失败',
        description: '无法导出日志文件',
        variant: 'destructive',
      })
    }
  }

  const getLevelBadge = (level: string) => {
    switch (level) {
      case 'error':
        return <Badge variant="destructive">错误</Badge>
      case 'warn':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">警告</Badge>
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">成功</Badge>
      case 'info':
        return <Badge variant="outline">信息</Badge>
      case 'debug':
        return <Badge variant="secondary">调试</Badge>
      default:
        return <Badge variant="secondary">{level}</Badge>
    }
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">系统日志</h1>
          <p className="text-muted-foreground">
            查看系统操作日志和审计信息
          </p>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">今日日志</p>
                  <p className="text-2xl font-bold">{stats.todayLogs}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">错误日志</p>
                  <p className="text-2xl font-bold">{stats.errorLogs}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">警告日志</p>
                  <p className="text-2xl font-bold">{stats.warningLogs}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">活跃用户</p>
                  <p className="text-2xl font-bold">{stats.uniqueUsers}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 日志列表 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>系统日志</CardTitle>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={handleExport}>
                  导出
                </Button>
                <Button variant="outline" onClick={handleRefresh}>
                  刷新
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <p>加载中...</p>
              </div>
            ) : (
              <div className="space-y-4">
                {logs.map((log) => (
                  <Card key={log.id}>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            {getLevelBadge(log.level)}
                            <Badge variant="outline">{log.category}</Badge>
                            <span className="text-sm text-muted-foreground">{log.createTime}</span>
                          </div>
                          <h3 className="font-medium">{log.action}</h3>
                          <p className="text-sm text-muted-foreground mt-1">{log.message}</p>
                          {log.username && (
                            <p className="text-sm text-muted-foreground mt-1">
                              用户: {log.username} | IP: {log.ipAddress}
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-sm">模块: {log.module}</p>
                          {log.duration && (
                            <p className="text-sm">耗时: {log.duration}ms</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
