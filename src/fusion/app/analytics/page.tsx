import {MainLayout} from '@/components/layout/main-layout'
import {Button} from '@/components/ui/button'
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import {<PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger} from '@/components/ui/tabs'
import {Calendar, Download, RefreshCw, Filter} from 'lucide-react'
// 数据分析页面 - 数据可视化和分析中心
export default function AnalyticsPage() {
    return (
        <MainLayout>
            <div className="container py-6">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">数据分析</h1>
                        <p className="text-gray-500">分析和可视化边缘数据</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" className="gap-2">
                            <Calendar className="h-4 w-4"/>
                            最近7天
                        </Button>
                        <Button variant="outline" size="sm" className="gap-2">
                            <Filter className="h-4 w-4"/>
                            筛选
                        </Button>
                        <Button variant="outline" size="sm" className="gap-2">
                            <RefreshCw className="h-4 w-4"/>
                            刷新
                        </Button>
                        <Button variant="outline" size="sm" className="gap-2">
                            <Download className="h-4 w-4"/>
                            导出
                        </Button>
                    </div>
                </div>

                <Tabs defaultValue="overview" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="overview">数据概览</TabsTrigger>
                        <TabsTrigger value="trends">趋势分析</TabsTrigger>
                        <TabsTrigger value="reports">报表</TabsTrigger>
                        <TabsTrigger value="custom">自定义分析</TabsTrigger>
                    </TabsList>
                    <TabsContent value="overview" className="space-y-4">
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">
                                        总数据量
                                    </CardTitle>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        className="h-4 w-4 text-muted-foreground">
                                        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                                    </svg>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">128.5 TB</div>
                                    <p className="text-xs text-muted-foreground">
                                        <span className="text-green-500">↑ 12%</span> 较上月
                                    </p>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">
                                        采集点数量
                                    </CardTitle>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        className="h-4 w-4 text-muted-foreground">
                                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                        <circle cx="9" cy="7" r="4"/>
                                        <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75"/>
                                    </svg>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">1,024</div>
                                    <p className="text-xs text-muted-foreground">
                                        <span className="text-green-500">↑ 48</span> 较上月
                                    </p>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">
                                        平均采集速率
                                    </CardTitle>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        className="h-4 w-4 text-muted-foreground">
                                        <rect width="20" height="14" x="2" y="5" rx="2"/>
                                        <path d="M2 10h20"/>
                                    </svg>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">15.6 MB/s</div>
                                    <p className="text-xs text-muted-foreground">
                                        <span className="text-green-500">↑ 2.3 MB/s</span> 较上月
                                    </p>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">
                                        数据完整率
                                    </CardTitle>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        className="h-4 w-4 text-muted-foreground">
                                        <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                                    </svg>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-green-600">99.8%</div>
                                    <p className="text-xs text-muted-foreground">
                                        <span className="text-green-500">↑ 0.3%</span> 较上月
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                            <Card className="col-span-4">
                                <CardHeader>
                                    <CardTitle>数据采集趋势</CardTitle>
                                    <CardDescription>过去7天的数据采集量</CardDescription>
                                </CardHeader>
                                <CardContent className="pl-2">
                                    <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                                        <p className="text-gray-500">数据采集趋势图表</p>
                                    </div>
                                </CardContent>
                            </Card>
                            <Card className="col-span-3">
                                <CardHeader>
                                    <CardTitle>数据类型分布</CardTitle>
                                    <CardDescription>按数据类型统计</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-[300px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                                        <p className="text-gray-500">数据类型分布图表</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>
                    <TabsContent value="trends" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>趋势分析</CardTitle>
                                <CardDescription>数据趋势和模式分析</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="h-[400px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                                    <p className="text-gray-500">趋势分析图表将显示在这里</p>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                    <TabsContent value="reports" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>报表</CardTitle>
                                <CardDescription>预定义和自定义报表</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="h-[400px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                                    <p className="text-gray-500">报表将显示在这里</p>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                    <TabsContent value="custom" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>自定义分析</CardTitle>
                                <CardDescription>创建自定义数据分析</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="h-[400px] w-full bg-gray-100 rounded-md flex items-center justify-center">
                                    <p className="text-gray-500">自定义分析工具将显示在这里</p>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </MainLayout>
    )
}
