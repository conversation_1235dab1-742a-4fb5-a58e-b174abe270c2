/**
 * 迁移自: app/data-forwarding/offline-data/page.tsx
 * 迁移时间: 2025-01-13T09:00:00.000Z
 *
 * 迁移说明:
 * - 已移除  指令
 * - 保持所有样式、API调用、业务逻辑完全不变
 *
 * 注意: 本文件保持与原始文件100%功能一致性
 */

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from '@/components/ui/use-toast'
import {
  Database,
  Search,
  Filter,
  Trash2,
  RefreshCw,
  Download,
  AlertCircle,
  Clock,
  Eye,
  RotateCcw,
  Calendar,
  HardDrive,
} from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { Textarea } from '@/components/ui/textarea'

// API 导入
import { RetryManagementApi } from '@/lib/api-services/apis/retry-management-api'
import {
  ForwardFailureRecord,
  ForwardFailureRecordStatus,
} from '@/lib/api-services/models'
import { Configuration } from '@/lib/api-services/configuration'

// 离线数据项类型定义
interface OfflineDataItem {
  id: number // 对应 ForwardFailureRecord.id
  forwardingConfigId: number // 对应 ForwardFailureRecord.forwardConfigId
  forwardingConfigName: string // 需要从配置名称中获取，暂时使用ID
  protocol: 'MQTT' | 'HTTP' | 'WebSocket' // 根据协议类型推断
  data: any // 对应 ForwardFailureRecord.data (需解析JSON)
  originalTimestamp: string // 对应 ForwardFailureRecord.createTime
  failureTimestamp: string // 对应 ForwardFailureRecord.updateTime
  failureReason: string // 对应 ForwardFailureRecord.failureReason
  retryCount: number // 对应 ForwardFailureRecord.retryCount
  maxRetries: number // 默认为3
  status: 'Pending' | 'Retrying' | 'failed' | 'expired' // 映射自 ForwardFailureRecordStatus
  size: number // 根据data大小计算
}

// 创建API实例
const createRetryManagementApi = () => {
  const config = new Configuration({
    basePath: '',
    accessToken:
      typeof window !== 'undefined'
        ? () => window.localStorage.getItem('access-token') || ''
        : () => '',
  })
  return new RetryManagementApi(config)
}

// 状态映射函数
const mapApiStatusToUiStatus = (
  apiStatus: ForwardFailureRecordStatus
): 'Pending' | 'Retrying' | 'failed' | 'expired' => {
  switch (apiStatus) {
    case ForwardFailureRecordStatus.NUMBER_0: // Pending
      return 'Pending'
    case ForwardFailureRecordStatus.NUMBER_1: // Retrying
      return 'Retrying'
    default:
      return 'failed'
  }
}

// 字符串状态映射函数（处理API直接返回字符串状态的情况）
const mapStringStatusToUiStatus = (
  statusString: string
): 'Pending' | 'Retrying' | 'failed' | 'expired' => {
  switch (statusString) {
    case 'Pending':
      return 'Pending'
    case 'Retrying':
      return 'Retrying'
    case 'Completed':
      return 'expired' // 已完成的记录在UI中显示为过期
    case 'Abandoned':
      return 'failed'
    default:
      return 'failed'
  }
}

// 协议字符串映射函数
const mapProtocolStringToProtocol = (
  protocolType: string | null | undefined
): 'MQTT' | 'HTTP' | 'WebSocket' => {
  if (!protocolType) return 'HTTP'

  const protocol = protocolType.toUpperCase()
  switch (protocol) {
    case 'MQTT':
      return 'MQTT'
    case 'HTTP':
    case 'HTTPS':
      return 'HTTP'
    case 'WEBSOCKET':
    case 'WS':
    case 'WSS':
      return 'WebSocket'
    default:
      return 'HTTP'
  }
}

// 将API返回的数据转换为UI所需格式
const convertApiRecordToUiItem = (
  record: ForwardFailureRecord
): OfflineDataItem => {
  // 保留原始数据
  let originalData: string = record.data || ''
  let dataSize = 0
  if (record.data) {
    dataSize = new TextEncoder().encode(record.data).length
  }

  return {
    id: record.id || 0,
    forwardingConfigId: record.forwardConfigId || 0,
    forwardingConfigName: `转发配置-${record.forwardConfigName}`, // 暂时使用ID作为名称
    protocol: mapProtocolStringToProtocol(record.protocolType),
    data: originalData,
    originalTimestamp: record.createTime
      ? new Date(record.createTime).toISOString()
      : new Date().toISOString(),
    failureTimestamp: record.updateTime
      ? new Date(record.updateTime).toISOString()
      : new Date().toISOString(),
    failureReason: record.failureReason || '未知错误',
    retryCount: record.retryCount || 0,
    maxRetries: 3, // 默认最大重试次数
    status:
      typeof record.status === 'string'
        ? mapStringStatusToUiStatus(record.status)
        : mapApiStatusToUiStatus(
            record.status || ForwardFailureRecordStatus.NUMBER_0
          ),
    size: dataSize,
  }
}

// 从API获取离线数据
const fetchOfflineData = async (
  page: number = 1,
  pageSize: number = 10,
  status?: ForwardFailureRecordStatus,
  keyword?: string,
  forwardConfigId?: number
): Promise<{ items: OfflineDataItem[]; total: number }> => {
  try {
    const api = createRetryManagementApi()
    const response = await api.apiRetryManagementRecordsGet(
      forwardConfigId, // forwardConfigId
      status, // status
      keyword, // keyword
      page, // page
      pageSize, // pageSize
      undefined, // field
      undefined, // order
      undefined // descStr
    )

    if (response.data.succeeded && response.data.data) {
      const items =
        response.data.data.items?.map(convertApiRecordToUiItem) || []
      const total = response.data.data.total || 0
      return { items, total }
    }
    return { items: [], total: 0 }
  } catch (error) {
    console.error('Failed to fetch offline data:', error)
    toast({
      title: '获取数据失败',
      description: '无法获取离线数据，请稍后再试',
      variant: 'destructive',
    })
    return { items: [], total: 0 }
  }
}

// 获取统计信息
const fetchStatistics = async (): Promise<{
  total: number
  pending: number
  retrying: number
  failed: number
  expired: number
  storageSize: number
}> => {
  try {
    const api = createRetryManagementApi()
    const response = await api.apiRetryManagementStatisticsGet()

    if (response.data.succeeded && response.data.data) {
      const stats = response.data.data
      return {
        total: stats.totalCount || 0,
        pending: stats.pendingCount || 0,
        retrying: stats.processingCount || 0,
        failed: stats.completedCount || 0,
        expired: 0,
        storageSize: stats.storageSize || 0,
      }
    }
    return {
      total: 0,
      pending: 0,
      retrying: 0,
      failed: 0,
      expired: 0,
      storageSize: 0,
    }
  } catch (error) {
    console.error('Failed to fetch statistics:', error)
    toast({
      title: '获取统计信息失败',
      description: '无法获取统计信息，请稍后再试',
      variant: 'destructive',
    })
    return {
      total: 0,
      pending: 0,
      retrying: 0,
      failed: 0,
      expired: 0,
      storageSize: 0,
    }
  }
}

// 获取转发配置列表（用于下拉框选项）
const fetchForwardingConfigs = async (): Promise<
  Array<{ id: number; name: string }>
> => {
  try {
    // 注意：这里需要根据实际的转发配置API进行调整
    // 暂时返回从现有数据中提取的配置信息
    const api = createRetryManagementApi()
    const response = await api.apiRetryManagementRecordsGet(
      undefined, // forwardConfigId
      undefined, // status
      undefined, // keyword
      1, // page
      100, // pageSize - 获取更多数据以提取配置信息
      undefined, // field
      undefined, // order
      undefined // descStr
    )

    if (response.data.succeeded && response.data.data?.items) {
      // 从现有记录中提取唯一的转发配置
      const configMap = new Map<number, string>()
      response.data.data.items.forEach((item) => {
        if (item.forwardConfigId && item.forwardConfigName) {
          configMap.set(item.forwardConfigId, item.forwardConfigName)
        }
      })

      return Array.from(configMap.entries()).map(([id, name]) => ({
        id,
        name: name || `转发配置-${id}`,
      }))
    }
    return []
  } catch (error) {
    console.error('Failed to fetch forwarding configs:', error)
    return []
  }
}

// 模拟离线数据
const generateOfflineData = (): OfflineDataItem[] => {
  const protocols: ('MQTT' | 'HTTP' | 'WebSocket')[] = [
    'MQTT',
    'HTTP',
    'WebSocket',
  ]
  const statuses: ('Pending' | 'Retrying' | 'failed' | 'expired')[] = [
    'Pending',
    'Retrying',
    'failed',
    'expired',
  ]

  const data: OfflineDataItem[] = []

  for (let i = 0; i < 50; i++) {
    const now = Date.now()
    const failureTime = now - Math.random() * 7 * 24 * 3600000 // 最近7天内
    const originalTime = failureTime - Math.random() * 3600000 // 失败前1小时内

    data.push({
      id: Math.floor(Math.random() * 10000) + 1,
      forwardingConfigId: Math.floor(Math.random() * 10) + 1,
      forwardingConfigName: `转发配置-${Math.floor(Math.random() * 10) + 1}`,
      protocol: protocols[Math.floor(Math.random() * protocols.length)],
      data: {
        deviceId: `device_${Math.floor(Math.random() * 100) + 1}`,
        timestamp: new Date(originalTime).toISOString(),
        values: Array.from(
          { length: Math.floor(Math.random() * 5) + 1 },
          (_, idx) => ({
            name: `sensor_${idx + 1}`,
            value: Math.random() * 100,
            quality: 'GOOD',
          })
        ),
      },
      originalTimestamp: new Date(originalTime).toISOString(),
      failureTimestamp: new Date(failureTime).toISOString(),
      failureReason: '网络连接超时',
      retryCount: Math.floor(Math.random() * 3),
      maxRetries: 3,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      size: Math.floor(Math.random() * 10000) + 500,
    })
  }

  return data.sort(
    (a, b) =>
      new Date(b.failureTimestamp).getTime() -
      new Date(a.failureTimestamp).getTime()
  )
}

export default function OfflineDataManagementPage() {
  // 数据状态
  const [offlineData, setOfflineData] = useState<OfflineDataItem[]>([])
  const [filteredData, setFilteredData] = useState<OfflineDataItem[]>([])
  const [selectedItems, setSelectedItems] = useState<number[]>([])

  // 过滤状态
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [protocolFilter, setProtocolFilter] = useState<string>('all')
  const [forwardConfigFilter, setForwardConfigFilter] = useState<string>('all')

  // 转发配置列表
  const [forwardingConfigs, setForwardingConfigs] = useState<
    Array<{ id: number; name: string }>
  >([])

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalItems, setTotalItems] = useState(0)

  // UI状态
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingStats, setIsLoadingStats] = useState(false)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [viewingItem, setViewingItem] = useState<OfflineDataItem | null>(null)
  const [jsonViewDialogOpen, setJsonViewDialogOpen] = useState(false)
  const [viewingJsonData, setViewingJsonData] = useState<any>(null)
  const [batchOperationDialogOpen, setBatchOperationDialogOpen] =
    useState(false)
  const [batchOperation, setBatchOperation] = useState<
    'retry' | 'abandon' | 'export'
  >('retry')

  // 统计信息
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    retrying: 0,
    failed: 0,
    expired: 0,
    storageSize: 0,
  })

  // 加载数据
  const loadData = async () => {
    setIsLoading(true)
    try {
      // 转换状态过滤器为API状态
      let apiStatus: ForwardFailureRecordStatus | undefined
      if (statusFilter !== 'all') {
        switch (statusFilter) {
          case 'Pending':
            apiStatus = ForwardFailureRecordStatus.NUMBER_0
            break
          case 'Retrying':
            apiStatus = ForwardFailureRecordStatus.NUMBER_1
            break
        }
      }

      // 转换转发配置过滤器为API参数
      let forwardConfigId: number | undefined
      if (forwardConfigFilter !== 'all') {
        forwardConfigId = parseInt(forwardConfigFilter)
      }

      // 获取数据
      const { items, total } = await fetchOfflineData(
        currentPage,
        pageSize,
        apiStatus,
        searchTerm || undefined,
        forwardConfigId
      )

      setOfflineData(items)
      setFilteredData(items)
      setTotalItems(total)
    } catch (error) {
      console.error('Failed to load data:', error)
      toast({
        title: '加载失败',
        description: '无法加载离线数据，请稍后再试',
        variant: 'destructive',
      })
      // 使用模拟数据作为备选
      const mockData = generateOfflineData()
      setOfflineData(mockData)
      setFilteredData(mockData)
      setTotalItems(mockData.length)
    } finally {
      setIsLoading(false)
    }
  }

  // 加载统计信息
  const loadStats = async () => {
    setIsLoadingStats(true)
    try {
      const statsData = await fetchStatistics()
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load statistics:', error)
      // 保持当前统计信息
    } finally {
      setIsLoadingStats(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadData()
    loadStats()
    // 加载转发配置列表
    fetchForwardingConfigs().then(setForwardingConfigs)
  }, [currentPage, pageSize])

  // 当过滤条件变化时重新加载数据
  useEffect(() => {
    // 如果是本地过滤（协议），则在前端过滤
    if (protocolFilter !== 'all') {
      let filtered = offlineData

      // 协议过滤
      if (protocolFilter !== 'all') {
        filtered = filtered.filter((item) => item.protocol === protocolFilter)
      }

      setFilteredData(filtered)
    } else {
      // 如果只有搜索词或状态过滤，则从API重新获取
      loadData()
    }
  }, [statusFilter, protocolFilter, searchTerm, forwardConfigFilter])

  // 选择/取消选择项目
  const toggleSelectItem = (id: number) => {
    setSelectedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    )
  }

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedItems.length === filteredData.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(filteredData.map((item) => item.id))
    }
  }

  // 重试单个项目
  const retryItem = async (id: number) => {
    setIsLoading(true)
    try {
      const api = createRetryManagementApi()
      const response = await api.apiRetryManagementRecordsRecordIdRetryPost(id)

      if (response.data.succeeded) {
        toast({
          title: '重试已开始',
          description: '数据重新转发已开始执行',
        })

        // 刷新数据和统计信息
        await loadData()
        await loadStats()
      } else {
        throw new Error(response.data.errors?.toString() || '重试失败')
      }
    } catch (error) {
      console.error('Retry failed:', error)
      toast({
        title: '重试失败',
        description: '重试操作失败，请稍后再试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 删除单个项目（实际是批量删除）
  const deleteItem = async (id: number) => {
    setIsLoading(true)
    try {
      const api = createRetryManagementApi()

      // 使用批量删除API
      const deleteResponse =
        await api.apiRetryManagementRecordsBatchDeleteDelete([id])

      if (deleteResponse.data.succeeded) {
        // 第二步：清空数据
        const clearResponse = await api.apiRetryManagementRecordsClearDelete(
          true
        )

        toast({
          title: '删除成功',
          description: '离线数据已删除',
        })

        // 刷新数据和统计信息
        await loadData()
        await loadStats()

        if (!clearResponse.data.succeeded) {
          console.warn('删除成功，但清空操作失败:', clearResponse.data.errors)
        }
      } else {
        throw new Error(deleteResponse.data.errors?.toString() || '删除失败')
      }
    } catch (error) {
      console.error('Delete failed:', error)
      toast({
        title: '删除失败',
        description: '删除操作失败，请稍后再试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 批量重试
  const batchRetry = async (ids: number[]): Promise<boolean> => {
    if (ids.length === 0) return false

    try {
      const api = createRetryManagementApi()
      const response = await api.apiRetryManagementRecordsBatchRetryPost(ids)

      if (response.data.succeeded) {
        return true
      } else {
        throw new Error(response.data.errors?.toString() || '批量重试失败')
      }
    } catch (error) {
      console.error('Batch retry failed:', error)
      toast({
        title: '批量重试失败',
        description: '批量重试操作失败，请稍后再试',
        variant: 'destructive',
      })
      return false
    }
  }

  // 批量删除（实际是批量删除+清空）
  const batchDelete = async (ids: number[]): Promise<boolean> => {
    if (ids.length === 0) return false

    try {
      const api = createRetryManagementApi()

      // 第一步：批量删除
      const deleteResponse =
        await api.apiRetryManagementRecordsBatchDeleteDelete(ids)

      if (deleteResponse.data.succeeded) {
        // 第二步：清空数据
        const clearResponse = await api.apiRetryManagementRecordsClearDelete(
          true
        )

        if (clearResponse.data.succeeded) {
          return true
        } else {
          console.warn('删除成功，但清空操作失败:', clearResponse.data.errors)
          // 即使清空失败，删除操作已成功，仍返回true
          return true
        }
      } else {
        throw new Error(
          deleteResponse.data.errors?.toString() || '批量删除失败'
        )
      }
    } catch (error) {
      console.error('Batch delete failed:', error)
      toast({
        title: '批量删除失败',
        description: '批量删除操作失败，请稍后再试',
        variant: 'destructive',
      })
      return false
    }
  }

  // 批量删除（不清空）
  const batchDeleteOnly = async (ids: number[]): Promise<boolean> => {
    if (ids.length === 0) return false

    try {
      const api = createRetryManagementApi()
      const response = await api.apiRetryManagementRecordsBatchDeleteDelete(ids)

      if (response.data.succeeded) {
        return true
      } else {
        throw new Error(response.data.errors?.toString() || '批量删除失败')
      }
    } catch (error) {
      console.error('Batch delete failed:', error)
      toast({
        title: '批量删除失败',
        description: '批量删除操作失败，请稍后再试',
        variant: 'destructive',
      })
      return false
    }
  }

  // 批量操作
  const executeBatchOperation = async () => {
    setIsLoading(true)
    try {
      let success = false

      switch (batchOperation) {
        case 'retry':
          success = await batchRetry(selectedItems)
          if (success) {
            toast({
              title: '批量重试已开始',
              description: `已开始重试 ${selectedItems.length} 个数据项`,
            })
            // 刷新数据和统计信息
            await loadData()
            await loadStats()
          }
          break

        case 'abandon':
          success = await batchDeleteOnly(selectedItems)
          if (success) {
            toast({
              title: '批量删除成功',
              description: `已删除 ${selectedItems.length} 个数据项`,
            })
            setSelectedItems([])
            // 刷新数据和统计信息
            await loadData()
            await loadStats()
          }
          break

        case 'export':
          // 导出选中的数据项为JSON文件
          const itemsToExport = offlineData.filter((item) =>
            selectedItems.includes(item.id)
          )

          const jsonString = JSON.stringify(itemsToExport, null, 2)
          const blob = new Blob([jsonString], { type: 'application/json' })
          const url = URL.createObjectURL(blob)

          const link = document.createElement('a')
          link.href = url
          link.download = `offline-data-export-${new Date()
            .toISOString()
            .slice(0, 10)}.json`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          toast({
            title: '导出成功',
            description: `已导出 ${selectedItems.length} 个数据项`,
          })
          success = true
          break
      }

      if (success) {
        setBatchOperationDialogOpen(false)
      }
    } catch (error) {
      console.error('Batch operation failed:', error)
      toast({
        title: '操作失败',
        description: '批量操作执行失败，请稍后再试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 查看数据详情
  const viewItemDetails = (item: OfflineDataItem) => {
    setViewingItem(item)
    setViewDialogOpen(true)
  }

  // 查看JSON数据
  const viewJsonData = (data: any) => {
    setViewingJsonData(data)
    setJsonViewDialogOpen(true)
  }

  // 检测是否为有效JSON
  const isValidJSON = (str: string): boolean => {
    try {
      JSON.parse(str)
      return true
    } catch (error) {
      return false
    }
  }

  // 获取数据预览文本（截断显示）
  const getDataPreview = (data: string, maxLength: number = 50) => {
    try {
      // 如果是JSON，显示格式化后的第一行
      if (isValidJSON(data)) {
        const parsed = JSON.parse(data)
        const formatted = JSON.stringify(parsed, null, 2)
        const firstLine = formatted.split('\n')[0]
        if (firstLine.length <= maxLength) {
          return firstLine + (formatted.includes('\n') ? '...' : '')
        }
        return firstLine.substring(0, maxLength) + '...'
      } else {
        // 如果不是JSON，直接截断显示
        if (data.length <= maxLength) {
          return data
        }
        return data.substring(0, maxLength) + '...'
      }
    } catch (error) {
      return '无效数据'
    }
  }

  // 获取状态徽章
  const getStatusBadge = (status: OfflineDataItem['status']) => {
    switch (status) {
      case 'Pending':
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200">
            待重试
          </Badge>
        )
      case 'Retrying':
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            重试中
          </Badge>
        )
      default:
        return <Badge variant="outline">未知状态</Badge>
    }
  }

  // 获取协议徽章
  const getProtocolBadge = (protocol: OfflineDataItem['protocol']) => {
    switch (protocol) {
      case 'MQTT':
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-700">
            MQTT
          </Badge>
        )
      case 'HTTP':
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-700">
            HTTP
          </Badge>
        )
      case 'WebSocket':
        return (
          <Badge variant="outline" className="bg-green-100 text-green-700">
            WebSocket
          </Badge>
        )
    }
  }

  return (
    <MainLayout>
      <div className="w-full px-6 py-6 space-y-6">
        {/* 页头 */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">离线数据管理</h1>
            <p className="text-muted-foreground">
              管理转发失败的缓存数据，支持重试、删除和导出操作
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={async () => {
                setIsLoadingStats(true)
                try {
                  const api = createRetryManagementApi()
                  const response =
                    await api.apiRetryManagementRecordsClearDelete(true)

                  if (response.data.succeeded) {
                    toast({
                      title: '清空完成',
                      description: '已清空数据',
                    })
                    await loadData()
                    await loadStats()
                  } else {
                    throw new Error(
                      response.data.errors?.toString() || '清空失败'
                    )
                  }
                } catch (error) {
                  console.error('Clear failed:', error)
                  toast({
                    title: '清空失败',
                    description: '清空操作失败，请稍后再试',
                    variant: 'destructive',
                  })
                } finally {
                  setIsLoadingStats(false)
                }
              }}>
              <Trash2
                className={`h-4 w-4 mr-2 ${
                  isLoadingStats ? 'animate-spin' : ''
                }`}
              />
              清空数据
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                loadData()
                loadStats()
              }}>
              <RefreshCw
                className={`h-4 w-4 mr-2 ${
                  isLoadingStats ? 'animate-spin' : ''
                }`}
              />
              刷新
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                setBatchOperation('export')
                setBatchOperationDialogOpen(true)
              }}
              disabled={selectedItems.length === 0}>
              <Download className="h-4 w-4 mr-2" />
              导出选中
            </Button>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总数据量</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                共 {(stats.storageSize / 1024 / 1024).toFixed(2)} MB
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">待重试</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats.pending}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.total > 0
                  ? ((stats.pending / stats.total) * 100).toFixed(1)
                  : 0}
                %
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">重试中</CardTitle>
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats.retrying}
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.total > 0
                  ? ((stats.retrying / stats.total) * 100).toFixed(1)
                  : 0}
                %
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">存储占用</CardTitle>
              <HardDrive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold">
                {(stats.storageSize / 1024 / 1024).toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">MB</p>
            </CardContent>
          </Card>
        </div>

        {/* 过滤和搜索 */}
        <Card>
          <CardHeader>
            <CardTitle>数据过滤</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="space-y-2">
                <Label htmlFor="search">搜索</Label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="搜索配置名称、原因..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>状态</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="Pending">待重试</SelectItem>
                    <SelectItem value="Retrying">重试中</SelectItem>
                    <SelectItem value="expired">已过期</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>协议</Label>
                <Select
                  value={protocolFilter}
                  onValueChange={setProtocolFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择协议" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部协议</SelectItem>
                    <SelectItem value="MQTT">MQTT</SelectItem>
                    <SelectItem value="HTTP">HTTP</SelectItem>
                    <SelectItem value="WebSocket">WebSocket</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>转发配置</Label>
                <Select
                  value={forwardConfigFilter}
                  onValueChange={setForwardConfigFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择转发配置" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部配置</SelectItem>
                    {forwardingConfigs.map((config) => (
                      <SelectItem key={config.id} value={config.id.toString()}>
                        {config.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 flex flex-col justify-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('')
                    setStatusFilter('all')
                    setProtocolFilter('all')
                    setForwardConfigFilter('all')
                  }}>
                  <Filter className="h-4 w-4 mr-2" />
                  清除过滤
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 批量操作 */}
        {selectedItems.length > 0 && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="font-medium">
                    已选择 {selectedItems.length} 个项目
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedItems([])}>
                    取消选择
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    onClick={() => {
                      setBatchOperation('retry')
                      setBatchOperationDialogOpen(true)
                    }}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    批量重试
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setBatchOperation('export')
                      setBatchOperationDialogOpen(true)
                    }}>
                    <Download className="h-4 w-4 mr-2" />
                    批量导出
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      setBatchOperation('abandon')
                      setBatchOperationDialogOpen(true)
                    }}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    批量删除
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      setIsLoading(true)
                      try {
                        const api = createRetryManagementApi()
                        const response =
                          await api.apiRetryManagementRecordsClearDelete(true)

                        if (response.data.succeeded) {
                          toast({
                            title: '清空完成',
                            description: '已清空数据',
                          })
                          await loadData()
                          await loadStats()
                        } else {
                          throw new Error(
                            response.data.errors?.toString() || '清空失败'
                          )
                        }
                      } catch (error) {
                        console.error('Clear failed:', error)
                        toast({
                          title: '清空失败',
                          description: '清空操作失败，请稍后再试',
                          variant: 'destructive',
                        })
                      } finally {
                        setIsLoading(false)
                      }
                    }}>
                    <RefreshCw
                      className={`h-4 w-4 mr-2 ${
                        isLoading ? 'animate-spin' : ''
                      }`}
                    />
                    清空数据
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 数据表格 */}
        <Card>
          <CardHeader>
            <CardTitle>离线数据列表</CardTitle>
            <CardDescription>
              显示 {filteredData.length} / {totalItems} 个项目
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={
                          filteredData.length > 0 &&
                          selectedItems.length === filteredData.length
                        }
                        onCheckedChange={toggleSelectAll}
                      />
                    </TableHead>
                    <TableHead className="w-40">转发配置</TableHead>
                    <TableHead className="w-20">协议</TableHead>
                    <TableHead className="w-24">状态</TableHead>
                    <TableHead className="min-w-48">失败原因</TableHead>
                    <TableHead className="w-48">数据内容</TableHead>
                    <TableHead className="w-32">重试次数</TableHead>
                    <TableHead className="w-36">失败时间</TableHead>
                    <TableHead className="w-20">大小</TableHead>
                    <TableHead className="w-32 text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={11} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <RefreshCw className="h-8 w-8 mb-2 animate-spin" />
                          <p>正在加载数据...</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={11} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Database className="h-8 w-8 mb-2" />
                          <p>没有找到匹配的离线数据</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredData.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedItems.includes(item.id)}
                            onCheckedChange={() => toggleSelectItem(item.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {item.forwardingConfigName}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              ID: {item.forwardingConfigId}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getProtocolBadge(item.protocol)}</TableCell>
                        <TableCell>{getStatusBadge(item.status)}</TableCell>
                        <TableCell>
                          <div
                            className="break-words"
                            title={item.failureReason}>
                            {item.failureReason}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div
                            className="text-sm text-muted-foreground font-mono max-w-32 truncate cursor-pointer hover:text-blue-600 hover:bg-blue-50 px-2 py-1 rounded transition-colors"
                            onClick={() => viewJsonData(item.data)}
                            title="点击查看完整内容">
                            {getDataPreview(item.data)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span>
                              {item.retryCount} / {item.maxRetries}
                            </span>
                            <Progress
                              value={(item.retryCount / item.maxRetries) * 100}
                              className="w-16 h-2"
                            />
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(item.failureTimestamp).toLocaleString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {(item.size / 1024).toFixed(1)} KB
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => viewItemDetails(item)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            {item.status === 'Pending' &&
                              item.retryCount < item.maxRetries && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => retryItem(item.id)}
                                  disabled={isLoading}>
                                  <RotateCcw className="h-4 w-4" />
                                </Button>
                              )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteItem(item.id)}
                              disabled={isLoading}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* 分页控件 */}
            {totalItems > 0 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  共 {totalItems} 条记录，每页 {pageSize} 条
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(1, prev - 1))
                    }
                    disabled={currentPage <= 1 || isLoading}>
                    上一页
                  </Button>

                  {/* 页码显示 */}
                  <div className="flex items-center space-x-1">
                    {(() => {
                      const totalPages = Math.ceil(totalItems / pageSize)
                      const pageNumbers = []

                      // 计算显示的页码范围
                      let startPage = Math.max(1, currentPage - 2)
                      let endPage = Math.min(totalPages, currentPage + 2)

                      // 确保显示5个页码（如果可能）
                      if (endPage - startPage < 4) {
                        if (startPage === 1) {
                          endPage = Math.min(totalPages, startPage + 4)
                        } else {
                          startPage = Math.max(1, endPage - 4)
                        }
                      }

                      // 第一页
                      if (startPage > 1) {
                        pageNumbers.push(
                          <Button
                            key={1}
                            variant={currentPage === 1 ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(1)}
                            disabled={isLoading}>
                            1
                          </Button>
                        )
                        if (startPage > 2) {
                          pageNumbers.push(
                            <span key="start-ellipsis" className="px-1">
                              ...
                            </span>
                          )
                        }
                      }

                      // 中间页码
                      for (let i = startPage; i <= endPage; i++) {
                        pageNumbers.push(
                          <Button
                            key={i}
                            variant={currentPage === i ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(i)}
                            disabled={isLoading}>
                            {i}
                          </Button>
                        )
                      }

                      // 最后一页
                      if (endPage < totalPages) {
                        if (endPage < totalPages - 1) {
                          pageNumbers.push(
                            <span key="end-ellipsis" className="px-1">
                              ...
                            </span>
                          )
                        }
                        pageNumbers.push(
                          <Button
                            key={totalPages}
                            variant={
                              currentPage === totalPages ? 'default' : 'outline'
                            }
                            size="sm"
                            onClick={() => setCurrentPage(totalPages)}
                            disabled={isLoading}>
                            {totalPages}
                          </Button>
                        )
                      }

                      return pageNumbers
                    })()}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage((prev) => prev + 1)}
                    disabled={
                      currentPage >= Math.ceil(totalItems / pageSize) ||
                      isLoading
                    }>
                    下一页
                  </Button>

                  {/* 跳转到指定页 */}
                  <div className="flex items-center space-x-2 ml-4">
                    <span className="text-sm text-muted-foreground">
                      跳转至
                    </span>
                    <Input
                      type="number"
                      min={1}
                      max={Math.ceil(totalItems / pageSize)}
                      className="w-16 h-8"
                      placeholder={currentPage.toString()}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const target = e.target as HTMLInputElement
                          const page = parseInt(target.value)
                          if (
                            page >= 1 &&
                            page <= Math.ceil(totalItems / pageSize)
                          ) {
                            setCurrentPage(page)
                            target.value = ''
                          }
                        }
                      }}
                    />
                    <span className="text-sm text-muted-foreground">页</span>
                  </div>

                  <Select
                    value={pageSize.toString()}
                    onValueChange={(value) => {
                      setPageSize(parseInt(value))
                      setCurrentPage(1)
                    }}>
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10/页</SelectItem>
                      <SelectItem value="20">20/页</SelectItem>
                      <SelectItem value="50">50/页</SelectItem>
                      <SelectItem value="100">100/页</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 查看详情对话框 */}
        <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>离线数据详情</DialogTitle>
              <DialogDescription>
                查看离线数据的详细信息和原始数据内容
              </DialogDescription>
            </DialogHeader>
            {viewingItem && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>转发配置</Label>
                    <div>{viewingItem.forwardingConfigName}</div>
                  </div>
                  <div className="space-y-2">
                    <Label>协议类型</Label>
                    <div>{getProtocolBadge(viewingItem.protocol)}</div>
                  </div>
                  <div className="space-y-2">
                    <Label>状态</Label>
                    <div>{getStatusBadge(viewingItem.status)}</div>
                  </div>
                  <div className="space-y-2">
                    <Label>原始时间</Label>
                    <div>
                      {new Date(viewingItem.originalTimestamp).toLocaleString()}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>失败时间</Label>
                    <div>
                      {new Date(viewingItem.failureTimestamp).toLocaleString()}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>失败原因</Label>
                  <div className="p-2 bg-red-50 border border-red-200 rounded text-red-800">
                    {viewingItem.failureReason}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>原始数据</Label>
                  <Textarea
                    readOnly
                    className="font-mono text-sm"
                    rows={10}
                    value={JSON.stringify(viewingItem.data, null, 2)}
                  />
                </div>
              </div>
            )}
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setViewDialogOpen(false)}>
                关闭
              </Button>
              {viewingItem &&
                (viewingItem.status === 'Pending' ||
                  viewingItem.status === 'failed') &&
                viewingItem.retryCount < viewingItem.maxRetries && (
                  <Button
                    onClick={() => {
                      retryItem(viewingItem.id)
                      setViewDialogOpen(false)
                    }}>
                    重试此项目
                  </Button>
                )}
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 批量操作确认对话框 */}
        <Dialog
          open={batchOperationDialogOpen}
          onOpenChange={setBatchOperationDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                确认批量
                {batchOperation === 'retry'
                  ? '重试'
                  : batchOperation === 'abandon'
                  ? '删除'
                  : '导出'}
              </DialogTitle>
              <DialogDescription>
                {batchOperation === 'retry' &&
                  '确定要重试选中的项目吗？这将重新尝试转发这些数据。'}
                {batchOperation === 'abandon' &&
                  '确定要删除选中的项目吗？此操作不可撤销。'}
                {batchOperation === 'export' &&
                  '确定要导出选中的项目吗？将生成JSON格式的文件。'}
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <p className="text-sm text-muted-foreground">
                已选择 {selectedItems.length} 个项目
              </p>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setBatchOperationDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={executeBatchOperation}
                disabled={isLoading}
                variant={
                  batchOperation === 'abandon' ? 'destructive' : 'default'
                }>
                {isLoading && (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                )}
                确认
                {batchOperation === 'retry'
                  ? '重试'
                  : batchOperation === 'abandon'
                  ? '删除'
                  : '导出'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* JSON数据查看对话框 */}
        <Dialog open={jsonViewDialogOpen} onOpenChange={setJsonViewDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh]">
            <DialogHeader>
              <DialogTitle>数据内容</DialogTitle>
              <DialogDescription>格式化显示的JSON数据内容</DialogDescription>
            </DialogHeader>
            <div className="overflow-auto">
              <pre className="bg-gray-50 p-4 rounded-lg text-sm font-mono overflow-auto">
                {viewingJsonData
                  ? JSON.stringify(viewingJsonData, null, 2)
                  : ''}
              </pre>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setJsonViewDialogOpen(false)}>
                关闭
              </Button>
              <Button
                onClick={() => {
                  if (viewingJsonData) {
                    const jsonString = JSON.stringify(viewingJsonData, null, 2)
                    navigator.clipboard.writeText(jsonString)
                    toast({
                      title: '复制成功',
                      description: 'JSON数据已复制到剪贴板',
                    })
                  }
                }}>
                复制JSON
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
