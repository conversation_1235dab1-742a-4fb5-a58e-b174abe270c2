import { useState, useEffect, useCallback, useRef } from 'react'
import { Link } from 'react-router-dom'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Search,
  Filter,
  RefreshCw,
  Edit,
  PenSquare,
  Trash2,
  ExternalLink,
  ChevronDown,
  ChevronRight,
  Wifi,
  WifiOff,
  Activity,
  AlertTriangle,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { type DeviceTag, type DeviceTagGroup, TagService } from '@/lib/api/tag-api'
import type { Device } from '@/lib/api/device-api'
import { TagValueWriter } from '@/components/devices/tag-value-writer'
import { useSignalREvent } from '@/lib/signalr/signalr-context'

// 智能值显示组件
const ObjectValueDisplay = ({
  value,
  dataType,
}: {
  value: any
  dataType: string
}) => {
  if (value === null || value === undefined) {
    return <span className="text-gray-400">-</span>
  }

  // 对于基础数据类型，直接显示
  if (typeof value !== 'object') {
    return <span>{String(value)}</span>
  }

  // 对于数组类型
  if (Array.isArray(value)) {
    return (
      <div className="group relative">
        <Badge
          variant="outline"
          className="cursor-pointer bg-blue-50 text-blue-700 border-blue-200">
          [{value.length} 项]
        </Badge>
        <div className="absolute z-50 hidden group-hover:block bg-white border rounded-md shadow-lg p-3 mt-1 left-0 min-w-[300px] max-w-md">
          <div className="text-xs font-medium text-gray-700 mb-2">
            数组内容：
          </div>
          <pre className="text-xs overflow-auto max-h-[200px] whitespace-pre-wrap bg-gray-50 p-2 rounded">
            {JSON.stringify(value, null, 2)}
          </pre>
        </div>
      </div>
    )
  }

  // 对于对象类型
  const objectKeys = Object.keys(value)
  return (
    <div className="group relative">
      <Badge
        variant="outline"
        className="cursor-pointer bg-indigo-50 text-indigo-700 border-indigo-200">
        {objectKeys.length} 属性
      </Badge>
      <div className="absolute z-50 hidden group-hover:block bg-white border rounded-md shadow-lg p-3 mt-1 left-0 min-w-[300px] max-w-md">
        <div className="text-xs font-medium text-gray-700 mb-2">对象内容：</div>
        <pre className="text-xs overflow-auto max-h-[200px] whitespace-pre-wrap bg-gray-50 p-2 rounded">
          {JSON.stringify(value, null, 2)}
        </pre>
      </div>
    </div>
  )
}

// 格式化标签值的智能显示函数
const formatTagValue = (
  value: any,
  dataType: string,
  custom?: Record<string, string>
) => {
  // 处理空值
  if (value === null || value === undefined) {
    return <span className="text-gray-400">-</span>
  }

  // 处理自定义值映射
  const renderMappedValue = (originalValue: any) => {
    if (
      !custom ||
      (typeof originalValue !== 'string' && typeof originalValue !== 'number')
    ) {
      return <span>{String(originalValue)}</span>
    }

    const valueKey = String(originalValue)
    const mappedValue = custom[valueKey]

    if (mappedValue) {
      return (
        <span>
          {String(originalValue)}
          <span className="text-gray-500">({mappedValue})</span>
        </span>
      )
    }

    return <span>{String(originalValue)}</span>
  }

  // 根据数据类型进行特殊处理
  switch (dataType) {
    case 'Boolean':
      const boolValue = value ? '真' : '假'
      const originalBoolValue = value

      // 检查布尔值的映射
      if (custom) {
        const boolKey = String(originalBoolValue)
        const mappedBool = custom[boolKey]
        if (mappedBool) {
          return (
            <Badge
              className={
                value
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }>
              {boolValue} ({mappedBool})
            </Badge>
          )
        }
      }

      return (
        <Badge
          className={
            value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }>
          {boolValue}
        </Badge>
      )

    case 'Array':
    case 'Object':
      return <ObjectValueDisplay value={value} dataType={dataType} />
    case 'Binary':
      if (value instanceof ArrayBuffer || value instanceof Uint8Array) {
        const size =
          value instanceof ArrayBuffer ? value.byteLength : 0
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700">
            二进制数据 ({size} 字节)
          </Badge>
        )
      }
      break

    default:
      // 对于其他类型，检查是否为对象
      if (typeof value === 'object' && value !== null) {
        return <ObjectValueDisplay value={value} dataType={dataType} />
      }
      return renderMappedValue(value)
  }

  return renderMappedValue(value)
}

export default function AllTagsPage() {
  const [deviceTagGroups, setDeviceTagGroups] = useState<DeviceTagGroup[]>([])
  const [filteredGroups, setFilteredGroups] = useState<DeviceTagGroup[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [deviceFilter, setDeviceFilter] = useState<string>('all')
  const [dataTypeFilter, setDataTypeFilter] = useState('all')
  const [showWriteDialog, setShowWriteDialog] = useState(false)
  const [selectedTag, setSelectedTag] = useState<DeviceTag | null>(null)
  const [recentlyUpdatedTags, setRecentlyUpdatedTags] = useState<Set<number>>(new Set())

  // 用于批量处理实时更新的ref
  const updateBatchRef = useRef<Map<number, any>>(new Map())
  const lastUpdateTimeRef = useRef<number>(0)

  // 加载所有设备的标签分组
  const loadAllTagsGrouped = async () => {
    setLoading(true)
    try {
      const groups = await TagService.getAllTagsGroupedByDevice()
      setDeviceTagGroups(groups)
      setFilteredGroups(groups)
    } catch (error) {
      console.error('加载设备标签分组失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载设备标签列表',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // 处理实时数据更新
  const handleRealTimeUpdate = useCallback((updateData: any) => {
    if (!updateData) return

    const currentTime = Date.now()

    // 频率控制
    if (currentTime - lastUpdateTimeRef.current < 50) {
      return
    }

    // 将数据统一处理为数组格式
    const dataArray = Array.isArray(updateData) ? updateData : [updateData]

    // 将更新数据添加到批量处理队列
    dataArray.forEach((item: any) => {
      if (item.id) {
        updateBatchRef.current.set(item.id, item)
      }
    })

    // 批量处理更新
    if (updateBatchRef.current.size > 0) {
      setDeviceTagGroups(prevGroups => {
        const updatedGroups = prevGroups.map(group => {
          const updatedTags = group.tags.map(tag => {
            const updateItem = updateBatchRef.current.get(tag.id)
            if (updateItem) {
              return {
                ...tag,
                value: updateItem.value,
                status: updateItem.status,
                timestamp: updateItem.timestamp || new Date().toISOString()
              }
            }
            return tag
          })

          // 更新设备组统计信息
          const stats = {
            total: updatedTags.length,
            online: updatedTags.filter(tag => tag.status === 'Good' || tag.status === 'good').length,
            error: updatedTags.filter(tag => tag.status === 'Bad' || tag.status === 'bad').length,
            lastUpdate: new Date()
          }

          return {
            ...group,
            tags: updatedTags,
            stats
          }
        })

        // 更新高亮的标签ID集合
        const updatedIds = new Set(Array.from(updateBatchRef.current.keys()))
        setRecentlyUpdatedTags(updatedIds)

        // 清空批量处理队列
        updateBatchRef.current.clear()
        lastUpdateTimeRef.current = currentTime

        // 3秒后清除高亮
        setTimeout(() => {
          setRecentlyUpdatedTags(new Set())
        }, 3000)

        return updatedGroups
      })
    }
  }, [])

  // 订阅SignalR实时数据更新
  useSignalREvent('online', handleRealTimeUpdate)

  // 切换设备组展开/收起状态
  const toggleDeviceGroup = (deviceId: number) => {
    setDeviceTagGroups(prevGroups =>
      prevGroups.map(group =>
        group.device.id === deviceId
          ? { ...group, isExpanded: !group.isExpanded }
          : group
      )
    )
  }

  // 初始加载
  useEffect(() => {
    loadAllTagsGrouped()
  }, [])

  // 过滤设备组和标签
  useEffect(() => {
    let filteredGroups = [...deviceTagGroups]

    // 设备过滤
    if (deviceFilter !== 'all') {
      filteredGroups = filteredGroups.filter(group => group.device.id === Number(deviceFilter))
    }

    // 搜索和数据类型过滤
    if (searchQuery || dataTypeFilter !== 'all') {
      filteredGroups = filteredGroups.map(group => {
        let filteredTags = [...group.tags]

        // 搜索过滤
        if (searchQuery) {
          filteredTags = filteredTags.filter(tag =>
            tag.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            tag.alias?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            tag.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            group.device.name.toLowerCase().includes(searchQuery.toLowerCase())
          )
        }

        // 数据类型过滤
        if (dataTypeFilter !== 'all') {
          filteredTags = filteredTags.filter(tag => tag.dataType === dataTypeFilter)
        }

        return {
          ...group,
          tags: filteredTags
        }
      }).filter(group => group.tags.length > 0) // 只显示有匹配标签的设备组
    }

    setFilteredGroups(filteredGroups)
  }, [searchQuery, deviceFilter, dataTypeFilter, deviceTagGroups])

  // 处理点位写入
  const handleWriteTag = (tag: DeviceTag) => {
    if (tag.readWrite === '只读') {
      toast({
        title: '无法写入',
        description: '此点位为只读，不支持写入操作',
        variant: 'destructive',
      })
      return
    }

    setSelectedTag(tag)
    setShowWriteDialog(true)
  }

  // 处理点位删除
  const handleDeleteTag = async (id: number) => {
    if (!confirm('确定要删除此点位吗？')) return

    try {
      await TagService.deleteTag(id)

      // 从设备组中移除删除的标签
      setDeviceTagGroups(prevGroups =>
        prevGroups.map(group => ({
          ...group,
          tags: group.tags.filter(tag => tag.id !== id),
          stats: {
            ...group.stats,
            total: group.tags.filter(tag => tag.id !== id).length
          }
        }))
      )

      toast({
        title: '删除成功',
        description: '点位已删除',
      })
    } catch (error) {
      console.error('删除点位失败:', error)
      toast({
        title: '删除失败',
        description: '无法删除点位',
        variant: 'destructive',
      })
    }
  }

  // 获取状态徽章
  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'Good':
        return <Badge className="bg-green-100 text-green-800">正常</Badge>
      case 'Bad':
        return <Badge className="bg-red-100 text-red-800">错误</Badge>
      case 'Uncertain':
      case 'Warning':
        return <Badge className="bg-amber-100 text-amber-800">警告</Badge>
      case 'Stale':
        return <Badge className="bg-gray-100 text-gray-800">过期</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <MainLayout>
      <div className="w-full px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">所有设备点位</h1>
            <p className="text-gray-500">查看和管理所有设备的数据采集点位</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={loadAllTagsGrouped} disabled={loading}>
              <RefreshCw
                className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`}
              />
              刷新
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="搜索点位..."
                className="pl-8 w-[300px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
              <span className="sr-only">筛选</span>
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Select value={deviceFilter} onValueChange={setDeviceFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择设备" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有设备</SelectItem>
                {deviceTagGroups.map((group) => (
                  <SelectItem key={group.device.id} value={String(group.device.id)}>
                    {group.device.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={dataTypeFilter} onValueChange={setDataTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="数据类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                <SelectItem value="Boolean">布尔值</SelectItem>
                <SelectItem value="Number">数值</SelectItem>
                <SelectItem value="Integer">整数</SelectItem>
                <SelectItem value="String">字符串</SelectItem>
                <SelectItem value="Array">数组</SelectItem>
                <SelectItem value="Object">对象</SelectItem>
                <SelectItem value="Binary">二进制</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>点位列表</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all">
              <TabsList className="mb-4">
                <TabsTrigger value="all">所有点位</TabsTrigger>
                <TabsTrigger value="read">只读点位</TabsTrigger>
                <TabsTrigger value="write">可写点位</TabsTrigger>
                <TabsTrigger value="alarm">报警点位</TabsTrigger>
              </TabsList>

              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="flex flex-col items-center">
                    <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
                    <p className="mt-4 text-gray-500">加载设备标签中...</p>
                  </div>
                </div>
              ) : filteredGroups.length === 0 ? (
                <div className="flex justify-center items-center py-12">
                  <div className="text-center">
                    <p className="text-gray-500 mb-4">未找到设备或标签</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredGroups.map((group) => (
                    <Card key={group.device.id} className="overflow-hidden">
                      <CardHeader
                        className="cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => toggleDeviceGroup(group.device.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Button variant="ghost" size="sm" className="p-0 h-auto">
                              {group.isExpanded ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </Button>
                            <div>
                              <CardTitle className="text-lg flex items-center gap-2">
                                {group.device.name}
                                {group.device.isConnect ? (
                                  <Wifi className="h-4 w-4 text-green-500" />
                                ) : (
                                  <WifiOff className="h-4 w-4 text-red-500" />
                                )}
                              </CardTitle>
                              <p className="text-sm text-gray-500">
                                {group.device.identifier} • {group.device.protocol}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="text-right">
                              <div className="flex items-center gap-2 text-sm">
                                <Badge variant="outline" className="bg-blue-50">
                                  总计: {group.stats.total}
                                </Badge>
                                <Badge variant="outline" className="bg-green-50">
                                  在线: {group.stats.online}
                                </Badge>
                                {group.stats.error > 0 && (
                                  <Badge variant="outline" className="bg-red-50">
                                    错误: {group.stats.error}
                                  </Badge>
                                )}
                              </div>
                              {group.stats.lastUpdate && (
                                <p className="text-xs text-gray-400 mt-1">
                                  最后更新: {group.stats.lastUpdate.toLocaleTimeString()}
                                </p>
                              )}
                            </div>
                            <Button variant="ghost" size="sm" asChild>
                              <Link to={`/devices/${group.device.id}/tags`}>
                                <ExternalLink className="h-4 w-4" />
                              </Link>
                            </Button>
                          </div>
                        </div>
                      </CardHeader>

                      {group.isExpanded && (
                        <CardContent className="pt-0">
                          <div className="overflow-x-auto">
                            <table className="w-full">
                              <thead>
                                <tr className="bg-gray-50 text-left text-sm text-gray-500 border-b">
                                  <th className="px-4 py-3 font-medium">点位名称</th>
                                  <th className="px-4 py-3 font-medium">数据类型</th>
                                  <th className="px-4 py-3 font-medium">当前值</th>
                                  <th className="px-4 py-3 font-medium">状态</th>
                                  <th className="px-4 py-3 font-medium">更新时间</th>
                                  <th className="px-4 py-3 font-medium">操作</th>
                                </tr>
                              </thead>
                              <tbody>
                                {group.tags.map((tag) => (
                                  <tr
                                    key={tag.id}
                                    className={`border-b hover:bg-gray-50 transition-colors ${
                                      recentlyUpdatedTags.has(tag.id) ? 'bg-yellow-50' : ''
                                    }`}
                                  >
                                    <td className="px-4 py-3">
                                      <div className="font-medium">{tag.name}</div>
                                      <div className="text-xs text-gray-500">
                                        {tag.alias}
                                      </div>
                                    </td>
                                    <td className="px-4 py-3">
                                      <Badge variant="outline">{tag.dataType}</Badge>
                                    </td>
                                    <td className="px-4 py-3 font-medium">
                                      {formatTagValue(
                                        tag.value,
                                        tag.dataType,
                                        tag.custom
                                      )}
                                    </td>
                                    <td className="px-4 py-3">
                                      {getStatusBadge(tag.status)}
                                    </td>
                                    <td className="px-4 py-3 text-sm">
                                      {tag.timestamp || '-'}
                                    </td>
                                    <td className="px-4 py-3">
                                      <div className="flex items-center gap-1">
                                        <Button variant="ghost" size="icon" asChild>
                                          <Link
                                            to={`/devices/${tag.deviceId}/tags?edit=${tag.id}`}>
                                            <Edit className="h-4 w-4" />
                                          </Link>
                                        </Button>
                                        {tag.readWrite !== '只读' && (
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => handleWriteTag(tag)}>
                                            <PenSquare className="h-4 w-4" />
                                          </Button>
                                        )}
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          onClick={() => handleDeleteTag(tag.id)}>
                                          <Trash2 className="h-4 w-4 text-red-500" />
                                        </Button>
                                      </div>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              )}
            </Tabs>
          </CardContent>
        </Card>

        {/* 点位写入对话框 */}
        <TagValueWriter
          open={showWriteDialog}
          onOpenChange={setShowWriteDialog}
          tag={selectedTag}
        />
      </div>
    </MainLayout>
  )
}
