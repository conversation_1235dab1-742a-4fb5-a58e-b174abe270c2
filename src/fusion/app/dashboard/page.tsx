/**
 * 仪表盘页面 - 系统主控台
 *
 * 菜单位置：侧边栏主导航 > 仪表盘
 * 路由地址：/dashboard
 * 页面功能：工作流编排平台的核心控制面板，提供系统全局状态监控和快速导航

 * 技术特点：
 * - 使用SignalR实现实时数据推送和系统状态更新
 * - 集成ECharts提供丰富的数据可视化图表
 * - 响应式设计，支持不同屏幕尺寸
 * - 模块化设计，支持快速导航到各功能子系统
 * - 实时连接状态监控，确保数据传输稳定性
 *
 * 业务价值：
 * - 为管理员提供系统全局视图和健康状况监控
 * - 及时发现和响应系统异常和设备故障
 * - 提供统一的功能入口，提升操作效率
 * - 支持实时监控，确保系统稳定运行
 */

import { useNavigate } from 'react-router-dom'
import { useEffect, useRef, useState, useMemo, useCallback, memo } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Database,
  Server,
  Activity,
  BarChart2,
  Workflow,
  Settings,
  Share,
  TrendingUp,
  Bell,
  ArrowUpRight,
  User,
  LogOut,
  HelpCircle,
  AlertTriangle,
  CheckCircle2,
  Info,
  LayoutDashboard,
  Terminal,
  Code,
  Shield,
  ListChecks,
  Tag,
  Clock,
  Key,
} from 'lucide-react'
import * as echarts from 'echarts'
import type { EChartsType } from 'echarts'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { FusionTrackLogo } from '@/components/fusion-track-logo'
import { Link } from 'react-router-dom'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { authApi } from '@/lib/api/auth-api'
import { useToast } from '@/components/ui/use-toast'
import {
  useSignalR,
  useSignalREvent,
  ConnectionState,
} from '@/lib/signalr/signalr-context'
import { MainLayout } from '@/components/layout/main-layout'
import { LoginUserOutput } from '@/lib/api-services/models/login-user-output'
import { RecentActivities } from '@/components/dashboard/recent-activities'
import {
  notificationAPI,
  type NotificationResponse,
  formatNotificationTime,
} from '@/lib/api/notifications'

// 系统统计数据类型
interface SystemStatistics {
  // 原始字段
  connectedDevices: number
  endpoints: number
  cpuUsage: number
  memoryUsage: number
  dataRecords: number
  processedRecords: number
  systemRunTime: {
    days: number
    hours: number
    minutes: number
  }
  serviceRunTime: {
    days: number
    hours: number
    minutes: number
  }
  systemTime: string
  systemVersion: string
  machineId: string
  networkStats: {
    uploadSpeed: number
    downloadSpeed: number
    totalSent: number
    totalReceived: number
  }
  alerts: {
    critical: number
    warning: number
    info: number
  }
  deviceStatus: {
    online: number
    offline: number
    warning: number
  }

  // 新增真实数据字段
  deviceCount?: number
  labelCount?: number
  totalOfflineCount?: number
  totalSendCount?: number
  totalSentMB?: number
  totalReceivedGB?: number
  systemUptime?: string
  applicationUptime?: string
  dateTime?: string
  machineCode?: string
  uploadSpeed?: number
  downloadSpeed?: number
  uploadUtilization?: number
  downloadUtilization?: number
  onlineDeviceCount?: number
  offlineDeviceCount?: number
  disabledDeviceCount?: number
  cpuUsageHistory?: any[]
  memoryUsageHistory?: any[]
  diskUsageHistory?: any[]
  uploadSpeedHistory?: any[]
  downloadSpeedHistory?: any[]
}

// 使用React.memo包装DashboardContent组件，减少不必要的重新渲染
const DashboardContent = memo(function DashboardContent() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const chartRef = useRef<HTMLDivElement>(null)
  const [chart, setChart] = useState<EChartsType | null>(null)
  const [showLogoutDialog, setShowLogoutDialog] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const [userInfo, setUserInfo] = useState<LoginUserOutput | null>(null)
  const [resourceType, setResourceType] = useState<
    'cpu' | 'memory' | 'disk' | 'network'
  >('cpu')

  // ===========================
  // SignalR 实时数据集成模块
  // ===========================
  // 使用SignalR上下文获取实时系统数据
  const { connectionState, connection, sendMessage } = useSignalR()
  const [systemStats, setSystemStats] = useState<SystemStatistics | null>(null)
  const [lastReceivedData, setLastReceivedData] = useState<any>(null)
  const [prevTimeLabels, setPrevTimeLabels] = useState<string[]>([])
  const chartInstanceRef = useRef<EChartsType | null>(null)

  // 显式订阅system_statistics主题，建立实时数据连接
  useEffect(() => {
    const subscribeToTopic = async () => {
      if (connection && connectionState === 'connected') {
        await sendMessage('SubscribeTopic', 'system_statistics')
      }
    }

    subscribeToTopic()

    // 组件卸载时取消订阅，避免内存泄漏
    return () => {
      if (connection && connectionState === 'connected') {
        sendMessage('UnsubscribeTopic', 'system_statistics')
          .then(() => console.log('已取消订阅system_statistics主题'))
          .catch((err) =>
            console.error('取消订阅system_statistics主题失败:', err)
          )
      }
    }
  }, [connection, connectionState, sendMessage])

  // ===========================
  // 默认数据和系统初始化模块
  // ===========================
  // 使用useMemo包装默认系统统计数据，避免每次渲染都创建新对象
  const defaultSystemStats = useMemo<SystemStatistics>(
    () => ({
      connectedDevices: 3,
      endpoints: 9,
      cpuUsage: 3.57,
      memoryUsage: 30.58,
      dataRecords: 497087,
      processedRecords: 173264,
      systemRunTime: {
        days: 85,
        hours: 4,
        minutes: 10,
      },
      serviceRunTime: {
        days: 4,
        hours: 20,
        minutes: 42,
      },
      systemTime: '2025-04-04 21:37:49',
      systemVersion: 'v1.0.1.692',
      machineId: '086D-3655-6BE2-E937',
      networkStats: {
        uploadSpeed: 18.28,
        downloadSpeed: 2.79,
        totalSent: 18885.73,
        totalReceived: 9.3,
      },
      alerts: {
        critical: 0,
        warning: 2,
        info: 5,
      },
      deviceStatus: {
        online: 3,
        offline: 0,
        warning: 0,
      },
    }),
    []
  )

  // 如果没有实时数据，使用默认数据
  useEffect(() => {
    if (!systemStats) {
      setSystemStats(defaultSystemStats)
    }
  }, [systemStats])

  // 订阅SignalR的system_statistics事件
  useSignalREvent(
    'system_statistics',
    (data) => {
      // 保持原有的状态更新逻辑
      setSystemStats(data)
      setLastReceivedData(data)
    },
    []
  )

  // ===========================
  // 功能模块配置和导航
  // ===========================
  // 定义菜单模块 - 系统核心功能入口配置
  const menuModules = [
    {
      title: '数据采集',
      description: '配置和管理数据采集任务',
      icon: Database,
      href: '/devices/data-collection',
      color:
        'from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900',
      iconColor: 'text-emerald-600 dark:text-emerald-400',
      stats: '3 台设备',
      trend: '5 个活跃',
      trendUp: true,
    },
    {
      title: '数据转发',
      description: '配置数据转发规则和目标',
      icon: Share,
      href: '/data-forwarding',
      color:
        'from-violet-50 to-violet-100 dark:from-violet-950 dark:to-violet-900',
      iconColor: 'text-violet-600 dark:text-violet-400',
      stats: '8 个规则',
      trend: '2 个待配置',
      trendUp: false,
    },
    {
      title: '工作流编排',
      description: '创建和管理自动化工作流',
      icon: Workflow,
      href: '/workflows',
      color: 'from-amber-50 to-amber-100 dark:from-amber-950 dark:to-amber-900',
      iconColor: 'text-amber-600 dark:text-amber-400',
      stats: '5 个工作流',
      trend: '正在开发中..',
      trendUp: true,
      developmentAccessible: true,
    },
    {
      title: '数据监控',
      description: '实时监控设备和系统状态，查询历史数据',
      icon: Activity,
      href: '/monitoring',
      color: 'from-rose-50 to-rose-100 dark:from-rose-950 dark:to-rose-900',
      iconColor: 'text-rose-600 dark:text-rose-400',
      stats: '24/7 监控',
      trend: '2 个警报',
      trendUp: false,
    },
    {
      title: '数据分析',
      description: '分析和可视化数据趋势',
      icon: BarChart2,
      href: '/analytics',
      color:
        'from-indigo-50 to-indigo-100 dark:from-indigo-950 dark:to-indigo-900',
      iconColor: 'text-indigo-600 dark:text-indigo-400',
      stats: '15 个报表',
      trend: '开发中',
      trendUp: false,
      inDevelopment: true,
    },
    {
      title: '调试工具',
      description: '网络、协议和设备调试工具集',
      icon: Terminal,
      href: '/debug-tools',
      color: 'from-cyan-50 to-cyan-100 dark:from-cyan-950 dark:to-cyan-900',
      iconColor: 'text-cyan-600 dark:text-cyan-400',
      stats: '10+ 工具',
      trend: '新增功能',
      trendUp: true,
    },
    {
      title: '系统设置',
      description: '配置系统参数和服务',
      icon: Settings,
      href: '/settings',
      color: 'from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800',
      iconColor: 'text-slate-600 dark:text-slate-400',
      stats: '8 个服务',
      trend: '全部运行中',
      trendUp: true,
    },
    {
      title: '开放接口',
      description: '管理API接口、文档和访问权限',
      icon: Code,
      href: '/api-management',
      color:
        'from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900',
      iconColor: 'text-purple-600 dark:text-purple-400',
      stats: '18 个接口',
      trend: '开发中',
      trendUp: true,
      inDevelopment: true,
    },
    {
      title: '任务中心',
      description: '管理定时任务和全局任务',
      icon: ListChecks,
      href: '/task-center',
      color: 'from-green-50 to-green-100 dark:from-green-950 dark:to-green-900',
      iconColor: 'text-green-600 dark:text-green-400',
      stats: '2 种任务',
      trend: '开发中',
      trendUp: true,
      inDevelopment: true,
    },
    {
      title: '权限管理',
      description: '管理用户、角色和权限配置',
      icon: Shield,
      href: '/system/users',
      color: 'from-red-50 to-red-100 dark:from-red-950 dark:to-red-900',
      iconColor: 'text-red-600 dark:text-red-400',
      stats: '3 个模块',
      trend: '2 个用户',
      trendUp: true,
    },
  ]

  // 系统概览数据
  const overviewItems = [
    {
      label: '在线设备',
      value:
        systemStats?.onlineDeviceCount ||
        systemStats?.deviceStatus?.online ||
        '0',
      icon: Server,
    },
    {
      label: '警告设备',
      value:
        systemStats?.disabledDeviceCount ||
        systemStats?.deviceStatus?.warning ||
        '0',
      icon: AlertTriangle,
    },
    {
      label: '紧急告警',
      value: systemStats?.alerts?.critical || '0',
      icon: AlertTriangle,
    },
    {
      label: '一般告警',
      value: systemStats?.alerts?.warning || '0',
      icon: AlertTriangle,
    },
  ]

  const handleModuleClick = (
    href: string,
    inDevelopment?: boolean,
    developmentAccessible?: boolean
  ) => {
    if (inDevelopment && !developmentAccessible) {
      toast({
        title: '功能开发中',
        description: '该功能正在开发中，敬请期待',
        variant: 'default',
      })
      return
    }

    if (developmentAccessible) {
      toast({
        title: '功能开发中',
        description: '该功能正在开发完善中，部分功能可能不完整',
        variant: 'default',
      })
    }

    navigate(href)
  }

  // ===========================
  // 通知功能模块
  // ===========================
  // 通知状态管理
  const [notifications, setNotifications] = useState<NotificationResponse[]>([])
  const [notificationsLoading, setNotificationsLoading] = useState(true)
  const [notificationsError, setNotificationsError] = useState<string | null>(
    null
  )

  // 标记通知为已读
  const markAsRead = async (id: number) => {
    try {
      await notificationAPI.markAsRead(id)
      // 更新本地状态
      setNotifications(
        notifications.map((notification) =>
          notification.id === id
            ? { ...notification, isRead: true }
            : notification
        )
      )
    } catch (error) {
      console.error('标记通知已读失败:', error)
      toast({
        title: '操作失败',
        description: '标记通知已读失败，请重试',
        variant: 'destructive',
      })
    }
  }

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    try {
      await notificationAPI.markAllAsRead()
      // 更新本地状态
      setNotifications(
        notifications.map((notification) => ({ ...notification, isRead: true }))
      )
    } catch (error) {
      console.error('标记所有通知已读失败:', error)
      toast({
        title: '操作失败',
        description: '标记所有通知已读失败，请重试',
        variant: 'destructive',
      })
    }
  }

  // 计算未读通知数量
  const unreadCount = notifications.filter(
    (notification) => !notification.isRead
  ).length

  // 获取通知数据
  const fetchNotifications = async () => {
    try {
      setNotificationsLoading(true)
      setNotificationsError(null)

      const response = await notificationAPI.getNotifications({
        page: 1,
        pageSize: 10, // 获取最近10条通知
      })

      setNotifications(response.items)
    } catch (error) {
      console.error('获取通知失败:', error)
      setNotificationsError('获取通知失败，请重试')
    } finally {
      setNotificationsLoading(false)
    }
  }

  // 格式化通知时间显示
  const formatNotificationDisplayTime = (isoString: string): string => {
    const now = new Date()
    const date = new Date(isoString)
    const diffInMs = now.getTime() - date.getTime()
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60))
    const diffInHours = Math.floor(diffInMinutes / 60)
    const diffInDays = Math.floor(diffInHours / 24)

    if (diffInMinutes < 1) {
      return '刚刚'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`
    } else {
      return `${diffInDays}天前`
    }
  }

  // 初始化获取通知数据
  useEffect(() => {
    fetchNotifications()
  }, [])

  // 更新退出登录处理函数
  const handleLogout = async () => {
    setIsLoggingOut(true)
    try {
      // 调用登出API
      await authApi.logout()

      // 显示成功提示
      toast({
        title: '退出成功',
        description: '您已成功退出系统',
      })

      // 重定向到登录页
      navigate('/login')
    } catch (error) {
      console.error('退出登录失败', error)

      // 显示错误提示
      toast({
        title: '退出失败',
        description: '退出系统时发生错误，请重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoggingOut(false)
      setShowLogoutDialog(false)
    }
  }

  // 获取SignalR连接状态的显示文本和Badge变体
  const getConnectionStatusInfo = (state: ConnectionState) => {
    switch (state) {
      case 'connected':
        return { text: '已连接', variant: 'success' as const }
      case 'connecting':
        return { text: '正在连接...', variant: 'secondary' as const }
      case 'reconnecting':
        return { text: '重新连接中...', variant: 'warning' as const }
      case 'error':
        return { text: '连接错误', variant: 'destructive' as const }
      case 'disconnected':
      default:
        return { text: '未连接', variant: 'destructive' as const }
    }
  }

  // ===========================
  // 用户信息获取模块
  // ===========================
  // 获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      const response = await authApi.getUserInfo()
      if (response && response.data && response.data.data) {
        // 直接使用API返回的数据
        setUserInfo(response.data.data)
      } else {
        // 如果没有用户信息，可能需要跳转到登录页
        navigate('/login')
      }
    }

    fetchUserInfo()
  }, [])

  // ===========================
  // ECharts 图表渲染模块
  // ===========================
  // 优化图表更新逻辑，避免频繁重新创建
  useEffect(() => {
    if (!chartRef.current || !systemStats) return

    // 避免频繁初始化图表
    if (!chartInstanceRef.current && chartRef.current) {
      const chart = echarts.init(chartRef.current)
      chartInstanceRef.current = chart
      setChart(chart)
    }

    if (!chartInstanceRef.current) return

    // 提取出获取历史数据的逻辑到useEffect外部，减少每次渲染时的计算
    const historyData = getHistoryData()

    // 如果historyData为null，不更新图表
    if (!historyData) return

    // 设置图表配置
    const option = {
      grid: {
        top: 30,
        right: 15,
        bottom: 30,
        left: 55,
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
      },
      legend:
        resourceType === 'network'
          ? {
              data: ['上行速度', '下行速度'],
              top: 0,
              textStyle: {
                fontSize: 12,
              },
            }
          : undefined,
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: historyData.timeLabels,
        axisLabel: {
          fontSize: 10,
          interval: 4,
        },
      },
      yAxis: {
        type: 'value',
        max: historyData.max,
        axisLabel: {
          formatter: historyData.formatter,
          fontSize: 10,
        },
      },
      series: historyData.series,
    }

    // 检查是否是新的资源类型，单独提取为函数减少useEffect中的复杂度
    const isNewResourceType = checkIfNewResourceType()
    chartInstanceRef.current.setOption(option, isNewResourceType)

    // 更新历史时间标签
    setPrevTimeLabels(historyData.timeLabels)
  }, [systemStats, resourceType])

  // 检查是否是新的资源类型，单独提取为函数减少useEffect中的复杂度
  const checkIfNewResourceType = useCallback(() => {
    if (!chartInstanceRef.current || prevTimeLabels.length === 0) return true

    const currentOption = chartInstanceRef.current.getOption()
    const legendData =
      currentOption?.legend && Array.isArray(currentOption.legend)
        ? currentOption.legend[0]?.data
        : undefined

    // 资源类型发生变化
    if (resourceType === 'network') {
      return (
        !legendData ||
        !Array.isArray(legendData) ||
        !legendData.includes('上行速度') ||
        !legendData.includes('下行速度')
      )
    } else {
      return (
        !legendData ||
        !Array.isArray(legendData) ||
        !legendData.includes(
          resourceType === 'cpu'
            ? 'CPU'
            : resourceType === 'memory'
            ? '内存'
            : '磁盘'
        )
      )
    }
  }, [resourceType, prevTimeLabels])

  // 将获取历史数据的方法提取出来，减少useEffect中的复杂度
  const getHistoryData = useCallback(() => {
    if (!systemStats) return null

    // 处理时间戳数据
    const getTimeLabels = (history: any[] | undefined) => {
      if (!history || history.length === 0) {
        // 生成默认时间数据
        const now = new Date()
        return Array.from({ length: 20 }, (_, i) => {
          const time = new Date(now.getTime() - (20 - i) * 15000)
          return `${time.getHours().toString().padStart(2, '0')}:${time
            .getMinutes()
            .toString()
            .padStart(2, '0')}:${time.getSeconds().toString().padStart(2, '0')}`
        })
      }

      // 使用真实历史数据中的时间戳
      return history.map((item) => {
        const date = new Date(item.timestamp)
        return `${date.getHours().toString().padStart(2, '0')}:${date
          .getMinutes()
          .toString()
          .padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
      })
    }

    switch (resourceType) {
      case 'cpu':
        return {
          timeLabels: getTimeLabels(systemStats.cpuUsageHistory),
          series: [
            {
              name: 'CPU',
              type: 'line',
              smooth: true,
              lineStyle: { width: 2, color: '#10b981' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                  { offset: 1, color: 'rgba(16, 185, 129, 0.1)' },
                ]),
              },
              data:
                systemStats.cpuUsageHistory?.map((item) => item.value) ||
                Array.from({ length: 20 }, () => Math.random() * 20),
              emphasis: {
                focus: 'series',
              },
            },
          ],
          formatter: '{value}%',
          max: 100,
        }
      case 'memory':
        return {
          timeLabels: getTimeLabels(systemStats.memoryUsageHistory),
          series: [
            {
              name: '内存',
              type: 'line',
              smooth: true,
              lineStyle: { width: 2, color: '#6366f1' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(99, 102, 241, 0.3)' },
                  { offset: 1, color: 'rgba(99, 102, 241, 0.1)' },
                ]),
              },
              data:
                systemStats.memoryUsageHistory?.map((item) => item.value) ||
                Array.from({ length: 20 }, () => Math.random() * 40 + 20),
              emphasis: {
                focus: 'series',
              },
            },
          ],
          formatter: '{value}%',
          max: 100,
        }
      case 'disk':
        return {
          timeLabels: getTimeLabels(systemStats.diskUsageHistory),
          series: [
            {
              name: '磁盘',
              type: 'line',
              smooth: true,
              lineStyle: { width: 2, color: '#f59e0b' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(245, 158, 11, 0.3)' },
                  { offset: 1, color: 'rgba(245, 158, 11, 0.1)' },
                ]),
              },
              data:
                systemStats.diskUsageHistory?.map((item) => item.value) ||
                Array.from({ length: 20 }, () => Math.random() * 30 + 10),
              emphasis: {
                focus: 'series',
              },
            },
          ],
          formatter: '{value}%',
          max: 100,
        }
      case 'network':
        return {
          timeLabels: getTimeLabels(systemStats.uploadSpeedHistory),
          series: [
            {
              name: '上行速度',
              type: 'line',
              smooth: true,
              lineStyle: { width: 2, color: '#10b981' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                  { offset: 1, color: 'rgba(16, 185, 129, 0.1)' },
                ]),
              },
              data:
                systemStats.uploadSpeedHistory?.map((item) => item.value) ||
                Array.from({ length: 20 }, () => Math.random() * 30),
              emphasis: {
                focus: 'series',
              },
            },
            {
              name: '下行速度',
              type: 'line',
              smooth: true,
              lineStyle: { width: 2, color: '#3b82f6' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                  { offset: 1, color: 'rgba(59, 130, 246, 0.1)' },
                ]),
              },
              data:
                systemStats.downloadSpeedHistory?.map((item) => item.value) ||
                Array.from({ length: 20 }, () => Math.random() * 15),
              emphasis: {
                focus: 'series',
              },
            },
          ],
          formatter: '{value} KB/s',
          max: undefined, // 自动计算最大值
        }
      default:
        return {
          timeLabels: getTimeLabels(systemStats.cpuUsageHistory),
          series: [
            {
              name: 'CPU',
              type: 'line',
              smooth: true,
              lineStyle: { width: 2, color: '#10b981' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                  { offset: 1, color: 'rgba(16, 185, 129, 0.1)' },
                ]),
              },
              data: Array.from({ length: 20 }, () => Math.random() * 20),
              emphasis: {
                focus: 'series',
              },
            },
          ],
          formatter: '{value}%',
          max: 100,
        }
    }
  }, [resourceType, systemStats])

  // 优化窗口大小调整处理
  useEffect(() => {
    const handleResize = () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize()
      }
    }

    // 使用requestAnimationFrame防止频繁调用resize
    let resizeTimer: number | null = null
    const throttledResize = () => {
      if (resizeTimer) {
        window.cancelAnimationFrame(resizeTimer)
      }
      resizeTimer = window.requestAnimationFrame(handleResize)
    }

    window.addEventListener('resize', throttledResize)
    return () => {
      window.removeEventListener('resize', throttledResize)
      if (resizeTimer) {
        window.cancelAnimationFrame(resizeTimer)
      }
    }
  }, [])

  // 获取连接状态显示信息
  const connectionStatusInfo = getConnectionStatusInfo(connectionState)

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 左侧边栏 */}
      <div className="w-64 border-r border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950 flex flex-col">
        <div className="p-4 border-b border-gray-200 dark:border-gray-800">
          <FusionTrackLogo size="lg" />
        </div>

        <div className="p-4">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
              <User className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">欢迎回来</p>
              <h2 className="text-xl font-bold text-primary">
                {userInfo?.name || userInfo?.account || '用户'}
              </h2>
              <p className="text-xs text-muted-foreground mt-0.5">
                上次登录: 今天 08:30
              </p>
            </div>
          </div>

          <div className="space-y-1 mb-6">
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => navigate('/dashboard')}>
              <LayoutDashboard className="mr-2 h-4 w-4" />
              仪表盘
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => navigate('/profile')}>
              <User className="mr-2 h-4 w-4" />
              个人资料
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => navigate('/license')}>
              <Shield className="mr-2 h-4 w-4" />
              授权信息
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => navigate('/help')}>
              <HelpCircle className="mr-2 h-4 w-4" />
              帮助中心
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950"
              onClick={() => setShowLogoutDialog(true)}>
              <LogOut className="mr-2 h-4 w-4" />
              退出登录
            </Button>
          </div>
        </div>

        <div className="mt-auto p-4 border-t border-gray-200 dark:border-gray-800">
          <div className="text-xs text-muted-foreground">
            <p>系统版本: {systemStats?.systemVersion || '未知'}</p>
            <p className="mt-1">© 2025 FusionTrack</p>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部系统状态和用户信息 */}
        <header className="h-16 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950 flex items-center px-6 justify-between">
          <div className="flex items-center space-x-6">
            {/* CPU使用率 */}
            <div className="flex items-center space-x-2.5">
              <div className="p-2 rounded-full bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-300">
                <Activity className="h-4 w-4" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  CPU
                </p>
                <p className="text-sm font-semibold">
                  {systemStats?.cpuUsage?.toFixed(2) || 0}%
                </p>
              </div>
            </div>

            {/* 内存使用率 */}
            <div className="flex items-center space-x-2.5">
              <div className="p-2 rounded-full bg-emerald-50 text-emerald-600 dark:bg-emerald-900/30 dark:text-emerald-300">
                <Database className="h-4 w-4" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  内存
                </p>
                <p className="text-sm font-semibold">
                  {systemStats?.memoryUsage?.toFixed(2) || 0}%
                </p>
              </div>
            </div>

            {/* 设备状态 */}
            <div className="flex items-center space-x-2.5">
              <div className="p-2 rounded-full bg-amber-50 text-amber-600 dark:bg-amber-900/30 dark:text-amber-300">
                <Server className="h-4 w-4" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  设备总数
                </p>
                <p className="text-sm font-semibold">
                  {systemStats?.deviceCount || 0}
                </p>
              </div>
            </div>

            {/* 标签数 */}
            <div className="flex items-center space-x-2.5">
              <div className="p-2 rounded-full bg-purple-50 text-purple-600 dark:bg-purple-900/30 dark:text-purple-300">
                <Tag className="h-4 w-4" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  数据点数
                </p>
                <p className="text-sm font-semibold">
                  {systemStats?.labelCount || 0}
                </p>
              </div>
            </div>

            {/* 离线存储数据 */}
            <div className="flex items-center space-x-2.5">
              <div className="p-2 rounded-full bg-indigo-50 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-300">
                <Database className="h-4 w-4" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  离线存储
                </p>
                <p className="text-sm font-semibold">
                  {(systemStats?.totalOfflineCount || 0).toLocaleString()}
                </p>
              </div>
            </div>

            {/* 累计发送数据 */}
            <div className="flex items-center space-x-2.5">
              <div className="p-2 rounded-full bg-green-50 text-green-600 dark:bg-green-900/30 dark:text-green-300">
                <Share className="h-4 w-4" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  累计发送
                </p>
                <p className="text-sm font-semibold">
                  {(systemStats?.totalSendCount || 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9 relative">
                  <Bell className="h-4 w-4 mr-2" />
                  通知
                  {unreadCount > 0 && (
                    <Badge
                      className="ml-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
                      variant="destructive">
                      {unreadCount}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel className="flex items-center justify-between">
                  <span>通知</span>
                  {unreadCount > 0 && !notificationsLoading && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={markAllAsRead}
                      className="h-8 text-xs">
                      全部标为已读
                    </Button>
                  )}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {notificationsLoading ? (
                  <div className="text-center py-4 text-muted-foreground">
                    正在加载通知...
                  </div>
                ) : notificationsError ? (
                  <div className="text-center py-4 text-muted-foreground">
                    <p>{notificationsError}</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={fetchNotifications}
                      className="mt-2 h-8 text-xs">
                      重试
                    </Button>
                  </div>
                ) : notifications.length === 0 ? (
                  <div className="text-center py-4 text-muted-foreground">
                    暂无通知
                  </div>
                ) : (
                  notifications.map((notification) => (
                    <DropdownMenuItem
                      key={notification.id}
                      className="p-0 focus:bg-transparent"
                      onSelect={(e) => e.preventDefault()}>
                      <div
                        className={`w-full p-3 hover:bg-accent cursor-pointer ${
                          !notification.isRead ? 'bg-accent/50' : ''
                        }`}
                        onClick={() => markAsRead(notification.id)}>
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-2">
                            <div
                              className={`mt-0.5 rounded-full p-1 ${
                                notification.type === 'critical'
                                  ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300'
                                  : notification.type === 'success'
                                  ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300'
                                  : 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                              }`}>
                              {notification.type === 'critical' ? (
                                <AlertTriangle className="h-3 w-3" />
                              ) : notification.type === 'success' ? (
                                <CheckCircle2 className="h-3 w-3" />
                              ) : (
                                <Info className="h-3 w-3" />
                              )}
                            </div>
                            <div>
                              <p className="text-sm font-medium">
                                {notification.title}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {notification.message}
                              </p>
                            </div>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {formatNotificationDisplayTime(
                              notification.createdTime
                            )}
                          </span>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link
                    to="/notifications"
                    className="w-full text-center cursor-pointer">
                    <span className="mx-auto">查看全部通知</span>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex items-center space-x-1 text-sm">
              <span className="text-muted-foreground">当前用户:</span>
              <span>{userInfo?.name || userInfo?.account || '未知用户'}</span>
            </div>
          </div>
        </header>

        {/* 主内容 */}
        <main className="flex-1 p-6 overflow-auto">
          <div className="flex flex-col space-y-6">
            {/* 欢迎信息和系统概览 */}
            <div className="flex flex-col md:flex-row gap-6">
              <Card className="flex-1">
                <CardHeader className="pb-2">
                  <CardTitle className="text-2xl">
                    <span className="text-muted-foreground font-normal">
                      欢迎回到
                    </span>{' '}
                    <span className="text-primary">
                      {userInfo?.name ? `${userInfo.name}` : ''}
                    </span>
                    <span>{userInfo?.name ? '，' : ''}</span>
                    <span>边缘数据智能平台</span>
                  </CardTitle>
                  <CardDescription>
                    今天是{' '}
                    {new Date().toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      weekday: 'long',
                    })}
                    {systemStats?.dateTime
                      ? ` | 系统时间: ${systemStats.dateTime}`
                      : ''}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {overviewItems.map((item, index) => (
                      <div key={index} className="flex flex-col space-y-1">
                        <div className="flex items-center space-x-2">
                          <div className="p-2 rounded-md bg-primary/10">
                            <item.icon className="h-4 w-4 text-primary" />
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {item.label}
                          </span>
                        </div>
                        <div className="flex items-baseline">
                          <span className="text-2xl font-bold">
                            {item.value}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="w-full md:w-80">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">系统状态</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* 系统运行时间 */}
                  <div className="flex justify-between items-center py-1 border-b border-gray-100 dark:border-gray-800">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                      <span className="text-sm">系统运行</span>
                    </div>
                    <span className="text-sm font-medium">
                      {systemStats?.systemUptime || '0天 0时'}
                    </span>
                  </div>

                  {/* 服务运行时间 */}
                  <div className="flex justify-between items-center py-1 border-b border-gray-100 dark:border-gray-800">
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4 text-green-500 dark:text-green-400" />
                      <span className="text-sm">服务运行</span>
                    </div>
                    <span className="text-sm font-medium">
                      {systemStats?.applicationUptime || '0天 0时'}
                    </span>
                  </div>

                  {/* 机器码 */}
                  <div className="flex justify-between items-center py-1 border-b border-gray-100 dark:border-gray-800">
                    <div className="flex items-center space-x-2">
                      <Key className="h-4 w-4 text-orange-500 dark:text-orange-400" />
                      <span className="text-sm">机器码</span>
                    </div>
                    <span className="text-sm font-medium">
                      {systemStats?.machineCode || '-'}
                    </span>
                  </div>

                  {/* 系统版本 */}
                  <div className="flex justify-between items-center py-1">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                      <span className="text-sm">系统版本</span>
                    </div>
                    <span className="text-sm font-medium">
                      {systemStats?.systemVersion
                        ? systemStats.systemVersion.startsWith('v')
                          ? systemStats.systemVersion
                          : 'v' + systemStats.systemVersion
                        : '-'}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 功能模块网格 */}
            <div>
              <h2 className="text-xl font-semibold mb-4">功能模块</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {menuModules.map((module, index) => (
                  <Card
                    key={index}
                    className={`overflow-hidden transition-all duration-300 hover:shadow-md cursor-pointer group ${
                      module.inDevelopment && !module.developmentAccessible
                        ? 'opacity-75'
                        : ''
                    }`}
                    onClick={() =>
                      handleModuleClick(
                        module.href,
                        module.inDevelopment,
                        module.developmentAccessible
                      )
                    }>
                    <div
                      className={`h-1 w-full bg-gradient-to-r ${module.color}`}></div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div
                          className={`p-2 rounded-lg bg-gradient-to-br ${
                            module.color
                          } ${
                            module.inDevelopment || module.developmentAccessible
                              ? 'relative'
                              : ''
                          }`}>
                          <module.icon
                            className={`h-5 w-5 ${module.iconColor}`}
                          />
                          {module.inDevelopment &&
                            !module.developmentAccessible && (
                              <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 rounded-full"></div>
                            )}
                          {module.developmentAccessible && (
                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                        <ArrowUpRight className="h-5 w-5 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                      <CardTitle className="mt-2 text-lg flex items-center">
                        {module.title}
                        {module.inDevelopment &&
                          !module.developmentAccessible && (
                            <Badge variant="secondary" className="ml-2 text-xs">
                              开发中
                            </Badge>
                          )}
                        {module.developmentAccessible && (
                          <Badge
                            variant="outline"
                            className="ml-2 text-xs border-blue-500 text-blue-600 dark:text-blue-400">
                            开发中
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription>{module.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between items-center">
                        <div className="text-sm font-medium">
                          {module.stats}
                        </div>
                        <div
                          className={`flex items-center text-xs ${
                            module.inDevelopment &&
                            !module.developmentAccessible
                              ? 'text-orange-600 dark:text-orange-400'
                              : module.developmentAccessible
                              ? 'text-blue-600 dark:text-blue-400'
                              : module.trendUp
                              ? 'text-green-600 dark:text-green-400'
                              : 'text-amber-600 dark:text-amber-400'
                          }`}>
                          {module.inDevelopment &&
                          !module.developmentAccessible ? (
                            <Clock className="h-3 w-3 mr-1" />
                          ) : module.developmentAccessible ? (
                            <Clock className="h-3 w-3 mr-1" />
                          ) : module.trendUp ? (
                            <TrendingUp className="h-3 w-3 mr-1" />
                          ) : (
                            <Activity className="h-3 w-3 mr-1" />
                          )}
                          {module.trend}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* 底部区域：系统资源监控和最近活动 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 系统资源监控 */}
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>系统资源监控</CardTitle>
                      <CardDescription>
                        {resourceType === 'cpu' && '实时CPU使用率'}
                        {resourceType === 'memory' && '实时内存使用率'}
                        {resourceType === 'disk' && '实时磁盘使用率'}
                        {resourceType === 'network' && '实时网络流量 (KB/s)'}
                      </CardDescription>
                    </div>
                    <div className="flex space-x-1">
                      <Button
                        size="sm"
                        variant={resourceType === 'cpu' ? 'default' : 'outline'}
                        onClick={() => setResourceType('cpu')}
                        className="h-7 text-xs px-2">
                        CPU
                      </Button>
                      <Button
                        size="sm"
                        variant={
                          resourceType === 'memory' ? 'default' : 'outline'
                        }
                        onClick={() => setResourceType('memory')}
                        className="h-7 text-xs px-2">
                        内存
                      </Button>
                      <Button
                        size="sm"
                        variant={
                          resourceType === 'disk' ? 'default' : 'outline'
                        }
                        onClick={() => setResourceType('disk')}
                        className="h-7 text-xs px-2">
                        磁盘
                      </Button>
                      <Button
                        size="sm"
                        variant={
                          resourceType === 'network' ? 'default' : 'outline'
                        }
                        onClick={() => setResourceType('network')}
                        className="h-7 text-xs px-2">
                        流量
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-[250px] w-full" ref={chartRef} />
                  {/* 网络数据统计 - 始终显示 */}
                  <div className="grid grid-cols-4 gap-4 mt-4 text-sm">
                    <div className="flex flex-col items-center border rounded-md p-3 bg-gray-50 dark:bg-gray-800">
                      <div className="text-muted-foreground mb-1 flex items-center space-x-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 text-green-500"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round">
                          <path d="M12 19V5M5 12l7-7 7 7" />
                        </svg>
                        <span>上行速度</span>
                      </div>
                      <div className="font-medium text-base">
                        {systemStats?.uploadSpeed?.toFixed(2) || '0'}{' '}
                        <span className="text-xs text-muted-foreground">
                          KB/s
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col items-center border rounded-md p-3 bg-gray-50 dark:bg-gray-800">
                      <div className="text-muted-foreground mb-1 flex items-center space-x-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 text-blue-500"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round">
                          <path d="M12 5v14M5 12l7 7 7-7" />
                        </svg>
                        <span>下行速度</span>
                      </div>
                      <div className="font-medium text-base">
                        {systemStats?.downloadSpeed?.toFixed(2) || '0'}{' '}
                        <span className="text-xs text-muted-foreground">
                          KB/s
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col items-center border rounded-md p-3 bg-gray-50 dark:bg-gray-800">
                      <div className="text-muted-foreground mb-1">总发送</div>
                      <div className="font-medium text-base">
                        {systemStats?.totalSentMB?.toFixed(2) || '0'}{' '}
                        <span className="text-xs text-muted-foreground">
                          MB
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col items-center border rounded-md p-3 bg-gray-50 dark:bg-gray-800">
                      <div className="text-muted-foreground mb-1">总接收</div>
                      <div className="font-medium text-base">
                        {systemStats?.totalReceivedGB?.toFixed(2) || '0'}{' '}
                        <span className="text-xs text-muted-foreground">
                          GB
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 最近活动 */}
              <RecentActivities />
            </div>
          </div>
        </main>
      </div>
      {/* 退出登录确认对话框 */}
      <AlertDialog open={showLogoutDialog} onOpenChange={setShowLogoutDialog}>
        <AlertDialogContent className="max-w-md bg-white dark:bg-gray-950 shadow-lg">
          <AlertDialogHeader>
            <div className="flex items-center space-x-2 mb-2">
              <LogOut className="h-5 w-5" />
              <AlertDialogTitle className="text-lg font-semibold">
                确认退出
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-gray-700 dark:text-gray-300">
              您确定要退出登录吗？所有未保存的更改可能会丢失。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="mt-4">
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleLogout} disabled={isLoggingOut}>
              {isLoggingOut ? '正在退出...' : '确认退出'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
})

// 使用React.memo包装导出的组件
export default memo(function DashboardPage() {
  return (
    <MainLayout showNavbar={false}>
      <DashboardContent />
    </MainLayout>
  )
})
