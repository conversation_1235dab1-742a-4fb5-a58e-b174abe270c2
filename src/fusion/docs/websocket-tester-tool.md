# WebSocket 测试工具

## 概述

WebSocket 测试工具是一个用于测试 WebSocket 连接、发送消息和接收数据的实用工具。该工具在两个地方可用：

1. **调试工具中心** - 作为独立工具在 `/debug-tools/websocket-test` 路径下
2. **浮动助手** - 作为内嵌工具在浮动助手面板中

## 功能特点

- **WebSocket 连接管理** - 支持连接到任何 WebSocket 服务器，包括协议设置
- **消息发送** - 向 WebSocket 服务器发送消息，支持文本、JSON 和二进制格式
- **自动重连** - 可配置的自动重连功能，包括重连间隔和最大重试次数
- **消息历史** - 查看和管理已发送和接收的消息历史
- **导出功能** - 将消息历史导出为 JSON 格式
- **实时状态** - 显示连接状态和错误信息
- **格式化显示** - 自动格式化 JSON 消息以提高可读性

## 代码架构

该工具采用了组件复用的架构设计，主要包含以下部分：

### 1. 核心逻辑 - `useWebSocketTester` Hook

位于 `hooks/use-websocket-tester.ts`，提供了 WebSocket 测试的核心功能：

- WebSocket 连接管理
- 消息发送和接收
- 自动重连逻辑
- 消息历史管理
- 错误处理

### 2. UI 组件 - `WebSocketTester` 组件

位于 `components/debug-tools/websocket-tester.tsx`，提供完整的用户界面：

- 连接设置表单
- 消息发送界面
- 消息历史查看器
- 格式选择器

### 3. 对话框包装 - `WebSocketTesterDialog` 组件

位于 `components/dialogs/tool-dialogs/websocket-tester-dialog.tsx`，将 WebSocketTester 组件包装为对话框形式，用于浮动助手：

- 对话框容器
- 全屏模式
- 跳转到完整工具的链接

### 4. 集成点

- **调试工具页面** - `app/debug-tools/websocket-test/page.tsx`
- **浮动助手** - `components/ui/floating-assistant.tsx`

## 使用方法

### 在调试工具中心使用

1. 导航到 `/debug-tools`
2. 点击 "WebSocket 测试" 工具卡片
3. 使用工具连接 WebSocket 服务器、发送消息和查看响应

### 在浮动助手中使用

1. 点击浮动助手图标打开面板
2. 选择 "WebSocket 测试" 工具
3. 在对话框中使用工具
4. 可选择全屏模式或跳转到完整工具

## 技术实现

### WebSocket 连接管理

```typescript
// 连接WebSocket
const connect = useCallback(() => {
  // 清除任何现有连接
  if (socketRef.current) {
    socketRef.current.close()
    socketRef.current = null
  }

  clearReconnectTimeout()
  setConnectionError(null)

  try {
    setConnectionStatus('正在连接...')

    // 解析协议
    const parsedProtocols = protocols
      ? protocols.split(',').map((p) => p.trim())
      : undefined

    // 创建WebSocket实例
    const socket = new WebSocket(url, parsedProtocols)
    socketRef.current = socket

    // 设置事件处理程序
    socket.onopen = () => {
      setIsConnected(true)
      setConnectionStatus('已连接')
      reconnectCountRef.current = 0
    }

    socket.onclose = (event) => {
      setIsConnected(false)
      setConnectionStatus(`已断开 (代码: ${event.code})`)

      // 如果启用了自动重连且不是正常关闭
      if (autoReconnect && event.code !== 1000 && event.code !== 1001) {
        handleReconnect()
      }
    }

    // ... 其他事件处理
  } catch (error) {
    setConnectionError(`连接错误: ${(error as Error).message}`)
    setConnectionStatus('连接错误')
  }
}, [url, protocols, autoReconnect, clearReconnectTimeout])
```

### 自动重连逻辑

```typescript
// 处理重连逻辑
const handleReconnect = useCallback(() => {
  if (reconnectCountRef.current >= reconnectAttempts) {
    setConnectionStatus('重连失败，已达到最大尝试次数')
    reconnectCountRef.current = 0
    return
  }

  reconnectCountRef.current += 1
  setConnectionStatus(
    `重新连接中... (${reconnectCountRef.current}/${reconnectAttempts})`
  )

  reconnectTimeoutRef.current = window.setTimeout(() => {
    connect()
  }, reconnectInterval)
}, [connect, reconnectAttempts, reconnectInterval])
```

### 消息发送与格式处理

```typescript
// 发送消息
const sendMessage = useCallback(() => {
  if (!socketRef.current || !isConnected) {
    setConnectionError('未连接到WebSocket服务器')
    return
  }

  try {
    let dataToSend: string | ArrayBuffer = messageToSend

    // 根据格式处理数据
    if (messageFormat === 'json') {
      try {
        // 检查是否已经是有效的JSON
        JSON.parse(messageToSend)
        dataToSend = messageToSend
      } catch (e) {
        // 不是有效的JSON，尝试将其转换为JSON字符串
        dataToSend = JSON.stringify(messageToSend)
      }
    } else if (messageFormat === 'binary') {
      // 将文本转换为二进制数据
      const encoder = new TextEncoder()
      dataToSend = encoder.encode(messageToSend).buffer
    }

    // 发送数据
    socketRef.current.send(dataToSend)

    // 添加到消息历史
    const message: WebSocketMessage = {
      id: messageIdCounter,
      timestamp: new Date(),
      data: messageToSend,
      direction: 'send',
      format: messageFormat,
    }

    setMessages((prev) => [message, ...prev].slice(0, 100))
    setMessageIdCounter((prev) => prev + 1)
  } catch (error) {
    setConnectionError(`发送错误: ${(error as Error).message}`)
  }
}, [messageToSend, messageFormat, isConnected, messageIdCounter])
```

## 常见问题

1. **为什么无法连接到 WebSocket 服务器？**

   - 检查 URL 是否正确（应以 ws://或 wss://开头）
   - 确认服务器是否在线并支持 WebSocket
   - 检查是否需要特定的协议
   - 检查网络连接是否正常

2. **什么是 WebSocket 协议？**

   - WebSocket 协议是一种在单个 TCP 连接上进行全双工通信的协议
   - 它允许服务器主动向客户端推送数据，而不需要客户端频繁发起请求
   - WebSocket 连接以 ws://或 wss://(安全 WebSocket)开头

3. **如何处理不同格式的消息？**

   - 文本格式：直接发送文本字符串
   - JSON 格式：发送 JSON 格式的数据，工具会自动处理 JSON 的格式化
   - 二进制格式：发送二进制数据，适用于传输非文本内容

4. **自动重连功能如何工作？**
   - 启用自动重连后，当连接意外断开时（非正常关闭），工具会自动尝试重新连接
   - 可以设置重连间隔（毫秒）和最大重试次数
   - 正常关闭连接（如用户手动断开）不会触发自动重连

## 集成指南

如需在其他组件中集成 WebSocket 测试功能，可以使用 `useWebSocketTester` Hook：

```typescript
import { useWebSocketTester } from '@/hooks/use-websocket-tester'

function YourComponent() {
  const {
    url,
    setUrl,
    protocols,
    setProtocols,
    isConnected,
    connectionStatus,
    messageToSend,
    setMessageToSend,
    messageFormat,
    setMessageFormat,
    messages,
    connect,
    disconnect,
    sendMessage,
  } = useWebSocketTester()

  // 使用这些方法和状态...
}
```

## 示例用例

### 1. 测试 Echo 服务器

```
URL: ws://echo.websocket.org
消息: Hello, WebSocket!
```

Echo 服务器会将您发送的任何消息原样返回，适合测试基本连接和消息发送功能。

### 2. 测试 JSON API

```
URL: wss://demo.piesocket.com/v3/channel_1
消息: {"type":"message","content":"Test message"}
格式: JSON
```

许多 WebSocket API 使用 JSON 格式进行通信，可以使用 JSON 格式发送结构化数据。

### 3. 测试本地开发服务器

```
URL: ws://localhost:8080
消息: 根据您的应用需求
```

在开发过程中，可以连接到本地运行的 WebSocket 服务器进行测试。
