# 工具集成与代码复用指南

## 概述

本文档提供了在系统中集成和复用工具组件的标准规范和最佳实践。通过遵循这些规则，可以确保工具组件的一致性、可维护性和可复用性。

## 核心原则

1. **单一数据源**: 工具逻辑应当只在一个地方实现，其他地方通过引用复用
2. **关注点分离**: 将核心逻辑、UI 组件和集成层分开
3. **一致的用户体验**: 无论在哪里使用工具，都应提供一致的用户体验
4. **灵活的集成方式**: 支持多种集成方式（独立页面、对话框、嵌入式等）

## 工具组件架构

### 1. 核心逻辑层 - Hooks

将工具的核心逻辑封装为自定义 Hook，实现与 UI 无关的业务逻辑。

```typescript
// hooks/use-tool-name.ts
export function useToolName() {
  // 状态管理
  const [state, setState] = useState(initialState)

  // 核心功能实现
  const coreFunction = () => {
    // 实现逻辑
  }

  return {
    // 返回状态和方法
    state,
    coreFunction,
  }
}
```

### 2. UI 组件层 - 组件

基于核心逻辑 Hook 创建可复用的 UI 组件。

```typescript
// components/debug-tools/tool-name.tsx
import { useToolName } from '@/hooks/use-tool-name'

export function ToolName() {
  const { state, coreFunction } = useToolName()

  return <div>{/* 组件UI实现 */}</div>
}
```

### 3. 集成层 - 页面和对话框

#### 3.1 调试工具页面

```typescript
// app/debug-tools/tool-name/page.tsx
import { DataToolLayout } from '@/components/debug-tools/data-tool-layout'
import { ToolName } from '@/components/debug-tools/tool-name'

export default function ToolNamePage() {
  return (
    <DataToolLayout
      title="工具名称"
      description="工具描述"
      toolIcon={<ToolIcon />}
      sidebar={<ToolSidebar />}>
      <ToolName />
    </DataToolLayout>
  )
}
```

#### 3.2 对话框包装器

```typescript
// components/ui/tool-name-dialog.tsx
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ToolName } from '@/components/debug-tools/tool-name'

export function ToolNameDialog({ open, onOpenChange }) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>工具名称</DialogTitle>
        </DialogHeader>
        <ToolName />
      </DialogContent>
    </Dialog>
  )
}
```

## 集成流程

### 1. 创建核心逻辑 Hook

1. 在 `hooks/` 目录下创建 `use-tool-name.ts` 文件
2. 实现工具的核心逻辑和状态管理
3. 导出必要的状态和方法

### 2. 创建 UI 组件

1. 在 `components/debug-tools/` 目录下创建 `tool-name.tsx` 文件
2. 引入核心逻辑 Hook
3. 实现组件 UI 和交互逻辑

### 3. 创建调试工具页面

1. 在 `app/debug-tools/tool-name/` 目录下创建 `page.tsx` 文件
2. 使用 `DataToolLayout` 组件作为页面布局
3. 引入 UI 组件作为内容

### 4. 创建对话框包装器

1. 在 `components/ui/` 目录下创建 `tool-name-dialog.tsx` 文件
2. 使用 `Dialog` 组件包装 UI 组件
3. 添加全屏模式和导航功能

### 5. 集成到浮动助手

1. 在 `components/ui/floating-assistant.tsx` 中导入对话框组件
2. 添加工具到工具列表
3. 在适当位置渲染对话框组件

## 代码示例：JSON 格式化工具

以下是 JSON 格式化工具的集成示例，可作为参考：

### 1. 核心逻辑 - `hooks/use-json-formatter.ts`

```typescript
import { useState, useCallback } from 'react'

export function useJsonFormatter() {
  const [input, setInput] = useState('')
  const [formattedJson, setFormattedJson] = useState('')

  const formatJson = useCallback(() => {
    try {
      const parsed = JSON.parse(input)
      setFormattedJson(JSON.stringify(parsed, null, 2))
      // ...
    } catch (e) {
      // 错误处理
    }
  }, [input])

  return {
    input,
    formattedJson,
    setInput,
    formatJson,
    // ...
  }
}
```

### 2. UI 组件 - `components/debug-tools/json-formatter.tsx`

```typescript
import { useJsonFormatter } from '@/hooks/use-json-formatter'

export function JsonFormatter() {
  const { input, formattedJson, setInput, formatJson } = useJsonFormatter()

  return (
    <div className="grid grid-cols-2 gap-4">
      <div>
        {/* 输入区域 */}
        <textarea value={input} onChange={(e) => setInput(e.target.value)} />
      </div>
      <div>
        {/* 输出区域 */}
        <pre>{formattedJson}</pre>
      </div>
      {/* 工具栏 */}
    </div>
  )
}
```

### 3. 调试工具页面 - `app/debug-tools/json-formatter/page.tsx`

```typescript
import { DataToolLayout } from '@/components/debug-tools/data-tool-layout'
import { JsonFormatter } from '@/components/debug-tools/json-formatter'
import { FileJson } from 'lucide-react'

export default function JsonFormatterPage() {
  return (
    <DataToolLayout
      title="JSON格式化工具"
      description="格式化、验证和美化JSON数据"
      toolIcon={<FileJson className="h-5 w-5" />}
      sidebar={<JsonFormatterSidebar />}>
      <JsonFormatter />
    </DataToolLayout>
  )
}
```

### 4. 对话框包装器 - `components/ui/json-formatter-dialog.tsx`

```typescript
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { JsonFormatter } from '@/components/debug-tools/json-formatter'
import { useNavigate } from 'react-router-dom'

export function JsonFormatterDialog({ open, onOpenChange }) {
  const navigate = useNavigate()

  const goToFullTool = () => {
    onOpenChange(false)
    navigate('/debug-tools/json-formatter')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>JSON格式化工具</DialogTitle>
        </DialogHeader>
        <JsonFormatter />
        <Button onClick={goToFullTool}>打开完整工具</Button>
      </DialogContent>
    </Dialog>
  )
}
```

### 5. 集成到浮动助手 - `components/ui/floating-assistant.tsx`

```typescript
import { JsonFormatterDialog } from '@/components/ui/json-formatter-dialog'

export function FloatingAssistant() {
  const [activeToolDialog, setActiveToolDialog] = useState(null)

  const tools = [
    {
      id: 'json-formatter',
      name: 'JSON格式化',
      icon: <FileJson />,
      onClick: () => setActiveToolDialog('json-formatter'),
    },
    // 其他工具...
  ]

  return (
    <>
      {/* 浮动助手UI */}

      {/* 工具对话框 */}
      <JsonFormatterDialog
        open={activeToolDialog === 'json-formatter'}
        onOpenChange={(open) => {
          if (!open) setActiveToolDialog(null)
        }}
      />
    </>
  )
}
```

## 最佳实践

1. **命名一致性**

   - 核心 Hook: `use-tool-name.ts`
   - UI 组件: `tool-name.tsx`
   - 页面: `tool-name/page.tsx`
   - 对话框: `tool-name-dialog.tsx`

2. **代码复用**

   - 避免在多个地方实现相同功能
   - 始终从核心 Hook 获取逻辑和状态
   - 组件应专注于渲染和用户交互

3. **状态管理**

   - 在 Hook 中集中管理状态
   - 避免在 UI 组件中维护重复状态
   - 使用回调函数进行状态更新

4. **错误处理**

   - 在核心 Hook 中处理错误
   - 提供错误状态给 UI 组件
   - 在 UI 中显示友好的错误信息

5. **性能优化**

   - 使用 `useCallback` 和 `useMemo` 优化函数和计算
   - 避免不必要的渲染
   - 考虑大数据集的处理方式

6. **文档**
   - 为每个工具创建文档
   - 说明工具的功能、用法和集成方式
   - 提供代码示例和使用场景

## 工具集成检查清单

- [ ] 创建核心逻辑 Hook
- [ ] 实现 UI 组件
- [ ] 创建调试工具页面
- [ ] 创建对话框包装器
- [ ] 集成到浮动助手
- [ ] 添加到路由配置
- [ ] 编写文档
- [ ] 进行测试

## 常见问题

1. **如何在多个组件之间共享状态？**

   - 使用核心 Hook 管理状态
   - 考虑使用 Context API 进行全局状态管理

2. **如何处理大型工具的性能问题？**

   - 考虑延迟加载和虚拟化
   - 分批处理大数据集
   - 使用 Web Worker 进行耗时计算

3. **如何处理工具之间的交互？**
   - 使用事件系统或共享状态
   - 考虑使用发布-订阅模式
   - 在浮动助手中实现工具间通信机制
