import { MenuTreeNode } from '@/lib/api/permission-api'

/**
 * 权限工具函数
 */

/**
 * 检查用户是否有指定权限
 * @param permissions 用户权限列表
 * @param permissionCode 权限代码
 * @returns 是否有权限
 */
export function hasPermission(permissions: string[], permissionCode: string): boolean {
  if (!permissions || !permissionCode) {
    return false
  }
  
  return permissions.some(p => 
    p.toLowerCase() === permissionCode.toLowerCase()
  )
}

/**
 * 检查用户是否有菜单访问权限
 * @param permissions 用户权限列表
 * @param menuCode 菜单代码
 * @returns 是否有权限
 */
export function hasMenuPermission(permissions: string[], menuCode: string): boolean {
  return hasPermission(permissions, menuCode)
}

/**
 * 检查用户是否有模块访问权限
 * @param permissions 用户权限列表
 * @param moduleCode 模块代码
 * @returns 是否有权限
 */
export function hasModulePermission(permissions: string[], moduleCode: string): boolean {
  return hasPermission(permissions, moduleCode)
}

/**
 * 根据路径获取对应的菜单代码
 * @param path 路由路径
 * @returns 菜单代码
 */
export function getMenuCodeByPath(path: string): string | null {
  // 路径到菜单代码的映射
  const pathToMenuCode: Record<string, string> = {
    '/dashboard': 'DASHBOARD',
    '/devices': 'DEVICE_LIST',
    '/devices/alarms': 'DEVICE_ALARMS',
    '/devices/events': 'DEVICE_EVENTS',
    '/devices/groups': 'DEVICE_GROUPS',
    '/devices/templates': 'DEVICE_TEMPLATES',
    '/data-forwarding': 'FORWARDING_CONFIG',
    '/data-forwarding/statistics': 'FORWARDING_STATISTICS',
    '/data-forwarding/offline-data': 'OFFLINE_DATA',
    '/task-center': 'TASK_OVERVIEW',
    '/task-center/scheduled-tasks': 'SCHEDULED_TASKS',
    '/task-center/task-config': 'TASK_CONFIG',
    '/monitoring': 'MONITORING_DASHBOARD',
    '/monitoring/database': 'DATABASE_MANAGEMENT',
    '/analytics': 'ANALYTICS',
    '/data-history': 'DATA_HISTORY',
    '/api-management': 'API_MANAGEMENT',
    '/debug-tools': 'DEBUG_TOOLS',
    '/script-modules': 'SCRIPT_MODULES',
    '/examples': 'COMPONENT_EXAMPLES',
    '/system/users': 'USER_MANAGEMENT',
    '/system/roles': 'ROLE_MANAGEMENT',
    '/system/menus': 'MENU_MANAGEMENT',
    '/system/settings': 'SYSTEM_SETTINGS',
  }

  return pathToMenuCode[path] || null
}

/**
 * 检查用户是否有路径访问权限
 * @param permissions 用户权限列表
 * @param path 路由路径
 * @returns 是否有权限
 */
export function hasPathPermission(permissions: string[], path: string): boolean {
  const menuCode = getMenuCodeByPath(path)
  if (!menuCode) {
    // 如果没有找到对应的菜单代码，默认允许访问（向后兼容）
    return true
  }
  
  return hasMenuPermission(permissions, menuCode)
}

/**
 * 过滤用户可访问的菜单
 * @param menus 菜单列表
 * @param permissions 用户权限列表
 * @returns 过滤后的菜单列表
 */
export function filterAccessibleMenus(
  menus: MenuTreeNode[], 
  permissions: string[]
): MenuTreeNode[] {
  return menus.filter(menu => {
    // 检查当前菜单权限
    if (!hasMenuPermission(permissions, menu.code)) {
      return false
    }

    // 递归过滤子菜单
    if (menu.children && menu.children.length > 0) {
      menu.children = filterAccessibleMenus(menu.children, permissions)
    }

    return true
  })
}

/**
 * 将菜单树转换为扁平的菜单列表
 * @param menus 菜单树
 * @returns 扁平菜单列表
 */
export function flattenMenuTree(menus: MenuTreeNode[]): MenuTreeNode[] {
  const result: MenuTreeNode[] = []
  
  function traverse(menuList: MenuTreeNode[]) {
    for (const menu of menuList) {
      result.push(menu)
      if (menu.children && menu.children.length > 0) {
        traverse(menu.children)
      }
    }
  }
  
  traverse(menus)
  return result
}

/**
 * 根据菜单代码查找菜单项
 * @param menus 菜单列表
 * @param menuCode 菜单代码
 * @returns 菜单项或null
 */
export function findMenuByCode(menus: MenuTreeNode[], menuCode: string): MenuTreeNode | null {
  const flatMenus = flattenMenuTree(menus)
  return flatMenus.find(menu => menu.code === menuCode) || null
}

/**
 * 检查菜单是否为开发中状态
 * @param path 路由路径
 * @returns 是否为开发中
 */
export function isMenuInDevelopment(path: string): boolean {
  // 开发中的菜单路径列表
  const developmentPaths = [
    '/devices/alarms',
    '/devices/events',
    '/devices/groups',
    '/devices/templates',
  ]
  
  return developmentPaths.includes(path)
}

/**
 * 获取菜单的显示名称
 * @param menu 菜单项
 * @returns 显示名称
 */
export function getMenuDisplayName(menu: MenuTreeNode): string {
  return menu.name
}

/**
 * 检查是否为超级管理员
 * @param permissions 用户权限列表
 * @returns 是否为超级管理员
 */
export function isSuperAdmin(permissions: string[]): boolean {
  // 超级管理员通常拥有所有权限，这里可以通过特定权限来判断
  // 或者通过权限数量来判断（超级管理员权限数量会很多）
  return permissions.length > 20 || permissions.includes('SUPER_ADMIN')
}
