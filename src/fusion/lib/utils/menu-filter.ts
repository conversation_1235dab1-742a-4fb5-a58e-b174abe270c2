import { MenuTreeNode } from '@/lib/api/permission-api'

/**
 * 导航菜单项接口（与现有navbar.tsx兼容）
 */
export interface NavigationItem {
  name: string
  to: string
  icon: any
  submenu?: NavigationItem[]
  inDevelopment?: boolean
}

/**
 * 将后端菜单树转换为前端导航菜单格式
 * @param menus 后端菜单树
 * @returns 前端导航菜单
 */
export function convertMenuTreeToNavigation(menus: MenuTreeNode[]): NavigationItem[] {
  // 图标映射（需要根据实际使用的图标库调整）
  const iconMap: Record<string, string> = {
    'LayoutDashboard': 'LayoutDashboard',
    'Server': 'Server',
    'Share': 'Share',
    'CalendarClock': 'CalendarClock',
    'Activity': 'Activity',
    'BarChart2': 'BarChart2',
    'BarChart': 'BarChart',
    'Code': 'Code',
    'Settings': 'Settings',
    'List': 'List',
    'BellRing': 'BellRing',
    'Group': 'Group',
    'FileCode': 'FileCode',
    'TrendingUp': 'TrendingUp',
    'HardDrive': 'HardDrive',
    'Clock': 'Clock',
    'Database': 'Database',
    'Terminal': 'Terminal',
    'BookOpen': 'BookOpen',
    'Users': 'Users',
    'Shield': 'Shield',
    'Menu': 'Menu',
    'Plus': 'Plus',
  }

  function convertMenu(menu: MenuTreeNode): NavigationItem {
    const navItem: NavigationItem = {
      name: menu.name,
      to: menu.path || '',
      icon: iconMap[menu.icon || ''] || 'Settings',
    }

    // 转换子菜单
    if (menu.children && menu.children.length > 0) {
      navItem.submenu = menu.children.map(convertMenu)
    }

    return navItem
  }

  return menus.map(convertMenu)
}

/**
 * 根据权限过滤导航菜单
 * @param navigation 导航菜单
 * @param permissions 用户权限列表
 * @returns 过滤后的导航菜单
 */
export function filterNavigationByPermissions(
  navigation: NavigationItem[],
  permissions: string[]
): NavigationItem[] {
  // 路径到权限代码的映射
  const pathToPermission: Record<string, string> = {
    '/dashboard': 'DASHBOARD',
    '/devices/data-collection': 'DATA_COLLECTION',
    '/devices': 'DEVICE_LIST',
    '/devices/alarms': 'DEVICE_ALARMS',
    '/devices/events': 'DEVICE_EVENTS',
    '/devices/groups': 'DEVICE_GROUPS',
    '/devices/templates': 'DEVICE_TEMPLATES',
    '/data-forwarding': 'DATA_FORWARDING',
    '/data-forwarding/statistics': 'FORWARDING_STATISTICS',
    '/data-forwarding/offline-data': 'OFFLINE_DATA',
    '/data-forwarding/add': 'FORWARDING_CONFIG',
    '/task-center': 'TASK_CENTER',
    '/task-center/scheduled-tasks': 'SCHEDULED_TASKS',
    '/task-center/task-config': 'TASK_CONFIG',
    '/monitoring': 'MONITORING',
    '/monitoring/database': 'DATABASE_MANAGEMENT',
    '/analytics': 'ANALYTICS',
    '/data-history': 'DATA_HISTORY',
    '/api-management': 'API_MANAGEMENT',
    '/debug-tools': 'DEV_TOOLS',
    '/script-modules': 'SCRIPT_MODULES',
    '/examples': 'COMPONENT_EXAMPLES',
    '/system/users': 'USER_MANAGEMENT',
    '/system/roles': 'ROLE_MANAGEMENT',
    '/system/menus': 'MENU_MANAGEMENT',
    '/system/settings': 'SYSTEM_SETTINGS',
  }

  /**
   * 检查用户是否有访问指定路径的权限
   */
  function hasPathPermission(path: string): boolean {
    const permissionCode = pathToPermission[path]
    if (!permissionCode) {
      // 如果没有找到对应的权限代码，默认允许访问（向后兼容）
      return true
    }
    
    return permissions.some(p => 
      p.toLowerCase() === permissionCode.toLowerCase()
    )
  }

  /**
   * 递归过滤菜单项
   */
  function filterMenuItem(item: NavigationItem): NavigationItem | null {
    // 检查当前菜单项权限
    if (!hasPathPermission(item.to)) {
      return null
    }

    // 如果有子菜单，递归过滤
    if (item.submenu && item.submenu.length > 0) {
      const filteredSubmenu = item.submenu
        .map(filterMenuItem)
        .filter((subItem): subItem is NavigationItem => subItem !== null)

      // 如果过滤后没有子菜单，但父菜单有权限，仍然显示父菜单
      return {
        ...item,
        submenu: filteredSubmenu.length > 0 ? filteredSubmenu : undefined,
      }
    }

    return item
  }

  return navigation
    .map(filterMenuItem)
    .filter((item): item is NavigationItem => item !== null)
}

/**
 * 检查菜单项是否应该显示
 * @param item 菜单项
 * @param permissions 用户权限列表
 * @returns 是否应该显示
 */
export function shouldShowMenuItem(item: NavigationItem, permissions: string[]): boolean {
  const pathToPermission: Record<string, string> = {
    '/dashboard': 'DASHBOARD',
    '/devices/data-collection': 'DATA_COLLECTION',
    '/devices': 'DEVICE_LIST',
    '/devices/alarms': 'DEVICE_ALARMS',
    '/devices/events': 'DEVICE_EVENTS',
    '/data-forwarding': 'DATA_FORWARDING',
    '/task-center': 'TASK_CENTER',
    '/monitoring': 'MONITORING',
    '/analytics': 'ANALYTICS',
    '/data-history': 'DATA_HISTORY',
    '/debug-tools': 'DEV_TOOLS',
    '/system': 'SYSTEM_MANAGEMENT',
  }

  const permissionCode = pathToPermission[item.to]
  if (!permissionCode) {
    return true // 默认显示
  }

  return permissions.some(p => 
    p.toLowerCase() === permissionCode.toLowerCase()
  )
}

/**
 * 获取用户可访问的模块列表
 * @param permissions 用户权限列表
 * @returns 可访问的模块代码列表
 */
export function getAccessibleModules(permissions: string[]): string[] {
  const modulePermissions = [
    'DASHBOARD',
    'DATA_COLLECTION',
    'DATA_FORWARDING',
    'TASK_CENTER',
    'MONITORING',
    'ANALYTICS',
    'DATA_HISTORY',
    'DEV_TOOLS',
    'SYSTEM_MANAGEMENT',
  ]

  return modulePermissions.filter(module =>
    permissions.some(p => p.toLowerCase() === module.toLowerCase())
  )
}
