/**
 * 开发者工具阻止器
 * 在生产环境中对非superAdmin用户禁用F12等开发者工具功能
 */

import { authApi } from '@/lib/api/auth-api'
import { AccountTypeEnum } from '@/lib/api-services/models/account-type-enum'

// 阻止器状态
interface BlockerState {
  isEnabled: boolean
  isInitialized: boolean
  userRole: string | null
  listeners: Array<() => void>
}

// 全局状态
const blockerState: BlockerState = {
  isEnabled: false,
  isInitialized: false,
  userRole: null,
  listeners: []
}

// 键盘组合类型定义
interface KeyCombination {
  key: string
  ctrlKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
}

// 需要阻止的键盘快捷键
const BLOCKED_KEY_COMBINATIONS: KeyCombination[] = [
  { key: 'F12' },
  { key: 'I', ctrlKey: true, shiftKey: true }, // Ctrl+Shift+I
  { key: 'J', ctrlKey: true, shiftKey: true }, // Ctrl+Shift+J
  { key: 'C', ctrlKey: true, shiftKey: true }, // Ctrl+Shift+C
  { key: 'U', ctrlKey: true }, // Ctrl+U (查看源代码)
  { key: 'S', ctrlKey: true }, // Ctrl+S (保存页面)
  { key: 'A', ctrlKey: true }, // Ctrl+A (全选，防止复制页面内容)
]

/**
 * 检查键盘事件是否应该被阻止
 */
function shouldBlockKeyEvent(event: KeyboardEvent): boolean {
  return BLOCKED_KEY_COMBINATIONS.some(combo => {
    const keyMatch = combo.key === event.key || combo.key === event.code
    const ctrlMatch = combo.ctrlKey ? event.ctrlKey : !event.ctrlKey
    const shiftMatch = combo.shiftKey ? event.shiftKey : !event.shiftKey
    const altMatch = combo.altKey ? event.altKey : !event.altKey

    return keyMatch && ctrlMatch && shiftMatch && altMatch
  })
}

/**
 * 键盘事件处理器
 */
function handleKeyDown(event: KeyboardEvent): void {
  if (!blockerState.isEnabled) return

  if (shouldBlockKeyEvent(event)) {
    event.preventDefault()
    event.stopPropagation()
    event.stopImmediatePropagation()

    // 显示警告信息
    showAccessDeniedMessage()
  }
}

/**
 * 右键菜单事件处理器
 */
function handleContextMenu(event: MouseEvent): void {
  if (!blockerState.isEnabled) return

  event.preventDefault()
  event.stopPropagation()
  showAccessDeniedMessage()
}

/**
 * 显示访问被拒绝的消息
 */
function showAccessDeniedMessage(): void {
  // 创建一个临时的提示消息
  const message = document.createElement('div')
  message.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 8px;
    z-index: 10000;
    font-family: Arial, sans-serif;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  `
  message.textContent = '访问被拒绝：您没有权限使用开发者工具'
  
  document.body.appendChild(message)
  
  // 2秒后自动移除消息
  setTimeout(() => {
    if (message.parentNode) {
      message.parentNode.removeChild(message)
    }
  }, 2000)
}

/**
 * 检测开发者工具是否打开
 */
function detectDevTools(): void {
  if (!blockerState.isEnabled) return
  
  const threshold = 160
  
  // 通过console.log的执行时间来检测开发者工具
  const start = performance.now()
  console.clear()
  const end = performance.now()
  
  if (end - start > threshold) {
    // 开发者工具可能已打开
    showDevToolsDetectedWarning()
  }
  
  // 通过窗口大小变化检测
  const widthThreshold = window.outerWidth - window.innerWidth > threshold
  const heightThreshold = window.outerHeight - window.innerHeight > threshold
  
  if (widthThreshold || heightThreshold) {
    showDevToolsDetectedWarning()
  }
}

/**
 * 显示开发者工具检测警告
 */
function showDevToolsDetectedWarning(): void {
  const warning = document.createElement('div')
  warning.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 0, 0, 0.9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    font-family: Arial, sans-serif;
    font-size: 24px;
    text-align: center;
  `
  warning.innerHTML = `
    <div>
      <h2>⚠️ 检测到开发者工具</h2>
      <p>请关闭开发者工具以继续使用系统</p>
      <p style="font-size: 14px; margin-top: 20px;">如需技术支持，请联系系统管理员</p>
    </div>
  `
  
  document.body.appendChild(warning)
  
  // 5秒后移除警告（给用户时间关闭开发者工具）
  setTimeout(() => {
    if (warning.parentNode) {
      warning.parentNode.removeChild(warning)
    }
  }, 5000)
}

/**
 * 启用开发者工具阻止功能
 */
function enableDevToolsBlocking(): void {
  if (blockerState.isEnabled) return
  
  // 添加事件监听器
  const keydownListener = (e: KeyboardEvent) => handleKeyDown(e)
  const contextmenuListener = (e: MouseEvent) => handleContextMenu(e)
  
  document.addEventListener('keydown', keydownListener, true)
  document.addEventListener('contextmenu', contextmenuListener, true)
  
  // 保存监听器引用以便后续移除
  blockerState.listeners.push(
    () => document.removeEventListener('keydown', keydownListener, true),
    () => document.removeEventListener('contextmenu', contextmenuListener, true)
  )
  
  // 定期检测开发者工具
  const detectionInterval = setInterval(detectDevTools, 1000)
  blockerState.listeners.push(() => clearInterval(detectionInterval))
  
  blockerState.isEnabled = true
  console.log('开发者工具阻止功能已启用')
}

/**
 * 禁用开发者工具阻止功能
 */
function disableDevToolsBlocking(): void {
  if (!blockerState.isEnabled) return
  
  // 移除所有事件监听器
  blockerState.listeners.forEach(cleanup => cleanup())
  blockerState.listeners = []
  
  blockerState.isEnabled = false
  console.log('开发者工具阻止功能已禁用')
}

/**
 * 检查用户权限并应用相应的限制
 */
export async function checkUserPermissionAndApplyRestrictions(): Promise<void> {
  // 开发环境下不启用任何限制
  if (import.meta.env.DEV) {
    console.log('开发环境：跳过开发者工具限制')
    return
  }
  
  try {
    // 获取用户信息
    const response = await authApi.getUserInfo()
    if (response?.data?.data) {
      const accountType = response.data.data.accountType
      blockerState.userRole = accountType?.toString() || null

      // 检查是否为superAdmin (999)
      const isSuperAdmin = accountType === AccountTypeEnum.NUMBER_999
      
      if (isSuperAdmin) {
        // superAdmin用户不受限制
        disableDevToolsBlocking()
        console.log('SuperAdmin用户：开发者工具访问已允许')
      } else {
        // 非superAdmin用户启用限制
        enableDevToolsBlocking()
        console.log('普通用户：开发者工具访问已限制')
      }
    } else {
      // 无法获取用户信息时，默认启用限制
      enableDevToolsBlocking()
      console.log('未知用户：开发者工具访问已限制')
    }
  } catch (error) {
    // 获取用户信息失败时，默认启用限制
    console.warn('获取用户信息失败，启用默认限制:', error)
    enableDevToolsBlocking()
  }
}

/**
 * 初始化开发者工具阻止器
 */
export function initializeDevToolsBlocker(): void {
  if (blockerState.isInitialized) return
  
  blockerState.isInitialized = true
  
  // 延迟检查用户权限，等待应用完全初始化
  setTimeout(() => {
    checkUserPermissionAndApplyRestrictions()
  }, 1000)
}

/**
 * 获取当前阻止器状态
 */
export function getBlockerState(): Readonly<BlockerState> {
  return { ...blockerState }
}

/**
 * 紧急解除限制（用于调试或紧急情况）
 * 需要在控制台中输入特定命令
 */
export function emergencyDisable(): void {
  disableDevToolsBlocking()
  console.log('紧急解除：开发者工具限制已临时禁用')
}

// 将紧急解除函数暴露到全局作用域（仅在生产环境下）
if (!import.meta.env.DEV && typeof window !== 'undefined') {
  ;(window as any).__emergencyDisableDevToolsBlocking = emergencyDisable
}
