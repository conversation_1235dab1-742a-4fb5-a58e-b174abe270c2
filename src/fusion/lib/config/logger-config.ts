/**
 * Logger配置同步
 * 用于将系统配置与Logger实例同步
 */

import { useConfigStore } from './config-store'
import { logger, LogLevel } from '../utils/logger'

/**
 * 日志级别映射
 */
const LOG_LEVEL_MAP = {
  'debug': LogLevel.DEBUG,
  'info': LogLevel.INFO,
  'warn': LogLevel.WARN,
  'error': LogLevel.ERROR,
  'none': LogLevel.NONE,
} as const

/**
 * 初始化Logger配置
 * 在应用启动时调用
 */
export function initializeLoggerConfig() {
  if (typeof window !== 'undefined') {
    const config = useConfigStore.getState().config
    syncLoggerConfig(config)
  }
}

/**
 * 设置Logger配置监听器
 * 在配置变更时自动同步Logger设置
 */
export function setupLoggerConfigListener() {
  if (typeof window !== 'undefined') {
    // 订阅配置更改
    const unsubscribe = useConfigStore.subscribe((state) => {
      syncLoggerConfig(state.config)
    })

    return unsubscribe
  }
  return () => {}
}

/**
 * 同步Logger配置
 */
function syncLoggerConfig(config: any) {
  // 设置日志级别
  if (config.debugMode) {
    // 调试模式下强制使用DEBUG级别
    logger.setLevel(LogLevel.DEBUG)
  } else {
    // 根据配置设置日志级别
    const level = LOG_LEVEL_MAP[config.logLevel as keyof typeof LOG_LEVEL_MAP] ?? LogLevel.INFO
    logger.setLevel(level)
  }

  // 设置是否启用控制台日志
  logger.setEnabled(config.enableConsoleLog)

  // 设置是否启用日志持久化
  logger.setPersist(config.enableLogPersist)

  // 设置最大日志条数
  logger.setMaxLogs(config.maxLogEntries)

  // 输出配置同步信息（仅在调试模式下）
  if (config.debugMode) {
    logger.info('Logger配置已同步', {
      level: config.debugMode ? 'debug' : config.logLevel,
      enableConsoleLog: config.enableConsoleLog,
      enableLogPersist: config.enableLogPersist,
      maxLogEntries: config.maxLogEntries
    })
  }
}

/**
 * 获取当前Logger状态
 */
export function getLoggerStatus() {
  const settings = logger.getSettings()
  const config = useConfigStore.getState().config
  
  return {
    ...settings,
    configLevel: config.logLevel,
    debugMode: config.debugMode,
    enableConsoleLog: config.enableConsoleLog,
    enableLogPersist: config.enableLogPersist,
    maxLogEntries: config.maxLogEntries,
    isConfigSynced: true
  }
}

/**
 * 导出Logger实例和便捷方法
 */
export { logger } from '../utils/logger'
export { log } from '../utils/logger'
