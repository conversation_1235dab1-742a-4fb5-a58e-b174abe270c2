import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface SystemConfig {
  // API配置
  apiBaseUrl: string
  apiTimeout: number

  // SignalR配置
  signalrHubUrl: string
  signalrAutoReconnect: boolean
  signalrWithCredentials: boolean

  // 品牌设置
  systemName: string
  logoUrl: string
  darkLogoUrl: string
  faviconUrl: string

  // 其他系统配置
  debugMode: boolean
  theme: 'light' | 'dark' | 'system'
  language: string
  dateFormat: string
  timeFormat: string

  // 日志配置
  logLevel: 'debug' | 'info' | 'warn' | 'error' | 'none'
  enableConsoleLog: boolean
  enableLogPersist: boolean
  maxLogEntries: number
}

// 默认配置
const defaultConfig: SystemConfig = {
  apiBaseUrl: 'http://localhost:5005',
  apiTimeout: 30000,

  signalrHubUrl: 'http://localhost:5005/websocket',
  signalrAutoReconnect: true,
  signalrWithCredentials: true,

  systemName: '工业边缘网关',
  logoUrl: '/logo.svg',
  darkLogoUrl: '/logo-dark.svg',
  faviconUrl: '/favicon.ico',

  debugMode: false,
  theme: 'system',
  language: 'zh-CN',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: 'HH:mm:ss',

  // 日志配置默认值
  logLevel: 'info',
  enableConsoleLog: true,
  enableLogPersist: false,
  maxLogEntries: 1000,
}

// 创建配置存储
export const useConfigStore = create<{
  config: SystemConfig
  setConfig: (config: Partial<SystemConfig>) => void
  resetConfig: () => void
  getConfig: <K extends keyof SystemConfig>(key: K) => SystemConfig[K]
}>()(
  persist(
    (set, get) => ({
      config: defaultConfig,

      setConfig: (newConfig) =>
        set((state) => ({
          config: { ...state.config, ...newConfig },
        })),

      resetConfig: () => set({ config: defaultConfig }),

      getConfig: <K extends keyof SystemConfig>(key: K) => get().config[key],
    }),
    {
      name: 'system-config',
    }
  )
)

// 辅助函数，用于在服务器组件中获取配置
export function getServerConfig(): SystemConfig {
  // 在服务器端，我们返回默认配置
  // 客户端组件将使用 useConfigStore 获取实际配置
  return defaultConfig
}
