import { useState, useEffect, useCallback } from 'react'
import { permissionApi, MenuTreeNode } from '@/lib/api/permission-api'
import { 
  hasPermission, 
  hasMenuPermission, 
  hasModulePermission, 
  hasPathPermission,
  filterAccessibleMenus,
  getMenuCodeByPath
} from '@/lib/utils/permission-utils'

/**
 * 权限状态接口
 */
interface PermissionState {
  permissions: string[]
  userMenus: MenuTreeNode[]
  isLoading: boolean
  error: string | null
  lastUpdated: number | null
}

/**
 * 权限Hook返回值接口
 */
interface UsePermissionReturn {
  // 状态
  permissions: string[]
  userMenus: MenuTreeNode[]
  isLoading: boolean
  error: string | null
  
  // 权限检查方法
  hasPermission: (permissionCode: string) => boolean
  hasMenuPermission: (menuCode: string) => boolean
  hasModulePermission: (moduleCode: string) => boolean
  hasPathPermission: (path: string) => boolean
  
  // 菜单过滤方法
  getAccessibleMenus: (menus: MenuTreeNode[]) => MenuTreeNode[]
  
  // 刷新方法
  refreshPermissions: () => Promise<void>
  
  // 清除方法
  clearPermissions: () => void
}

/**
 * 权限管理Hook
 * 提供权限检查、菜单过滤等功能
 */
export function usePermission(): UsePermissionReturn {
  const [state, setState] = useState<PermissionState>({
    permissions: [],
    userMenus: [],
    isLoading: false,
    error: null,
    lastUpdated: null,
  })

  /**
   * 加载用户权限和菜单
   */
  const loadPermissions = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      // 并行加载权限和菜单
      const [permissionsResponse, menusResponse] = await Promise.all([
        permissionApi.getUserPermissions(),
        permissionApi.getUserMenus(),
      ])

      // 检查响应是否成功
      const permissionsData = permissionsResponse.data
      const menusData = menusResponse.data

      setState(prev => ({
        ...prev,
        permissions: Array.isArray(permissionsData) ? permissionsData : [],
        userMenus: Array.isArray(menusData) ? menusData : [],
        isLoading: false,
        error: null,
        lastUpdated: Date.now(),
      }))
    } catch (error) {
      console.error('加载权限数据失败:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : '加载权限数据失败',
      }))
    }
  }, [])

  /**
   * 刷新权限数据
   */
  const refreshPermissions = useCallback(async () => {
    await loadPermissions()
  }, [loadPermissions])

  /**
   * 清除权限数据
   */
  const clearPermissions = useCallback(() => {
    setState({
      permissions: [],
      userMenus: [],
      isLoading: false,
      error: null,
      lastUpdated: null,
    })
  }, [])

  /**
   * 检查权限的包装方法
   */
  const checkPermission = useCallback((permissionCode: string) => {
    return hasPermission(state.permissions, permissionCode)
  }, [state.permissions])

  const checkMenuPermission = useCallback((menuCode: string) => {
    return hasMenuPermission(state.permissions, menuCode)
  }, [state.permissions])

  const checkModulePermission = useCallback((moduleCode: string) => {
    return hasModulePermission(state.permissions, moduleCode)
  }, [state.permissions])

  const checkPathPermission = useCallback((path: string) => {
    return hasPathPermission(state.permissions, path)
  }, [state.permissions])

  /**
   * 获取可访问的菜单
   */
  const getAccessibleMenus = useCallback((menus: MenuTreeNode[]) => {
    return filterAccessibleMenus(menus, state.permissions)
  }, [state.permissions])

  // 组件挂载时加载权限数据
  useEffect(() => {
    // 检查是否有访问令牌
    const accessToken = window.localStorage.getItem('access-token')
    if (accessToken) {
      loadPermissions()
    }
  }, [loadPermissions])

  // 监听存储变化，当用户登录状态改变时重新加载权限
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'access-token') {
        if (event.newValue) {
          // 用户登录，加载权限
          setTimeout(() => {
            loadPermissions()
          }, 500) // 延迟加载，确保用户信息已更新
        } else {
          // 用户登出，清除权限
          clearPermissions()
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [loadPermissions, clearPermissions])

  return {
    // 状态
    permissions: state.permissions,
    userMenus: state.userMenus,
    isLoading: state.isLoading,
    error: state.error,
    
    // 权限检查方法
    hasPermission: checkPermission,
    hasMenuPermission: checkMenuPermission,
    hasModulePermission: checkModulePermission,
    hasPathPermission: checkPathPermission,
    
    // 菜单过滤方法
    getAccessibleMenus,
    
    // 刷新和清除方法
    refreshPermissions,
    clearPermissions,
  }
}
