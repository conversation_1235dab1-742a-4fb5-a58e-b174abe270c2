import { NetworkTopologyApi } from '@/lib/api-services/apis/network-topology-api'
import {
  PingRequest,
  PingResult,
  PortScanRequest,
  PortScanResult,
  RESTfulResultListPingResult,
  RESTfulResultListPortScanResult,
  RESTfulResultPortScanResult,
} from '@/lib/api-services/models'
import { feature, LoadingManager, getAPI } from '@/lib/api-services/axios-utils'
import { AxiosResponse } from 'axios'

// 加载状态键
export const LOADING_KEY = {
  PING: 'network.ping',
  SCAN_PORTS: 'network.scanPorts',
  CHECK_PORT: 'network.checkPort',
}

// 新的API返回数据结构接口
interface ApiPingResponse {
  statusCode: number
  data: ApiPingResult[]
  succeeded: boolean
  timestamp: number
}

interface ApiPingResult {
  target: string
  responseTime: number
  ttl: number
  status: string // "Success" 或其他状态
}

/**
 * 网络工具服务类
 * 负责调用网络拓扑API执行Ping测试、端口扫描等网络诊断功能
 */
class NetworkToolsService {
  /**
   * 执行Ping测试
   * @param targets 目标主机数组
   * @param timeout 超时时间（毫秒）
   * @param ttl TTL值
   * @returns Ping测试结果
   */
  async pingHosts(
    targets: string[],
    timeout?: number,
    ttl?: number
  ): Promise<PingResult[]> {
    try {
      LoadingManager.setLoading(LOADING_KEY.PING, true)

      const request: PingRequest = {
        targets,
        timeout,
        ttl,
      }

      // 使用feature函数处理API调用
      const [error, response] = await feature<
        AxiosResponse<RESTfulResultListPingResult>
      >(getAPI(NetworkTopologyApi).batchPing(request))

      if (error) {
        console.error('Ping测试失败:', error)
        return []
      }

      // 处理新的API返回数据结构
      // 检查返回的数据结构是否符合新格式
      const responseData = response.data

      // 如果是新的API返回格式
      if (
        responseData.data &&
        Array.isArray(responseData.data) &&
        responseData.statusCode !== undefined
      ) {
        // 将新的API返回格式转换为旧的PingResult格式
        return (responseData.data as unknown as ApiPingResult[]).map(
          (result) =>
            ({
              target: result.target,
              responseTime: result.responseTime,
              ttl: result.ttl,
              status: result.status === 'Success' ? 0 : 1, // 将字符串状态转换为数字状态
              error: result.status !== 'Success' ? result.status : undefined,
            } as unknown as PingResult)
        )
      }

      // 如果是旧的API返回格式，直接返回
      return response.data.data || []
    } finally {
      LoadingManager.setLoading(LOADING_KEY.PING, false)
    }
  }

  /**
   * 执行端口扫描
   * @param target 目标主机
   * @param startPort 起始端口
   * @param endPort 结束端口
   * @param timeout 超时时间（毫秒）
   * @param concurrentScans 并发扫描数
   * @returns 端口扫描结果
   */
  async scanPorts(
    target: string,
    startPort?: number,
    endPort?: number,
    timeout?: number,
    concurrentScans?: number
  ): Promise<PortScanResult[]> {
    try {
      LoadingManager.setLoading(LOADING_KEY.SCAN_PORTS, true)

      const request: PortScanRequest = {
        target,
        startPort,
        endPort,
        timeout,
        concurrentScans,
      }

      const [error, response] = await feature<
        AxiosResponse<RESTfulResultListPortScanResult>
      >(getAPI(NetworkTopologyApi).scanPorts(request))

      if (error) {
        console.error('端口扫描失败:', error)
        return []
      }

      return response.data.data || []
    } finally {
      LoadingManager.setLoading(LOADING_KEY.SCAN_PORTS, false)
    }
  }

  /**
   * 检查单个端口
   * @param target 目标主机
   * @param port 端口号
   * @param timeout 超时时间（毫秒）
   * @returns 端口检查结果
   */
  async checkPort(
    target: string,
    port: number,
    timeout?: number
  ): Promise<PortScanResult | null> {
    try {
      LoadingManager.setLoading(LOADING_KEY.CHECK_PORT, true)

      const [error, response] = await feature<
        AxiosResponse<RESTfulResultPortScanResult>
      >(getAPI(NetworkTopologyApi).checkPort(target, port, timeout))

      if (error) {
        console.error('端口检查失败:', error)
        return null
      }

      return response.data.data || null
    } finally {
      LoadingManager.setLoading(LOADING_KEY.CHECK_PORT, false)
    }
  }

  /**
   * 检查指定操作是否正在加载中
   * @param key 操作键，例如 LOADING_KEY.PING
   * @returns 是否加载中
   */
  isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}

// 导出单例实例
export const networkToolsApi = new NetworkToolsService()

// 为了向后兼容，保留原有的导出函数接口
export const pingHosts = (
  targets: string[],
  timeout?: number,
  ttl?: number
): Promise<PingResult[]> => {
  return networkToolsApi.pingHosts(targets, timeout, ttl)
}

export const scanPorts = (
  target: string,
  startPort?: number,
  endPort?: number,
  timeout?: number,
  concurrentScans?: number
): Promise<PortScanResult[]> => {
  return networkToolsApi.scanPorts(
    target,
    startPort,
    endPort,
    timeout,
    concurrentScans
  )
}

export const checkPort = (
  target: string,
  port: number,
  timeout?: number
): Promise<PortScanResult | null> => {
  return networkToolsApi.checkPort(target, port, timeout)
}
