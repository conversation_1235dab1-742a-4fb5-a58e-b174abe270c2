/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Device } from './device';
import { ProtectTypeEnum } from './protect-type-enum';
import { SendTypeEnum } from './send-type-enum';
import { ValueSourceEnum } from './value-source-enum';
 /**
 * ????
 *
 * @export
 * @interface DeviceLabel
 */
export interface DeviceLabel {

    /**
     * @type {number}
     * @memberof DeviceLabel
     */
    id?: number;

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceLabel
     */
    deviceId?: number;

    /**
     * ???
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    identifier?: string | null;

    /**
     * ???
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    name?: string | null;

    /**
     * ??????
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    transitionType?: string | null;

    /**
     * @type {ValueSourceEnum}
     * @memberof DeviceLabel
     */
    valueSource?: ValueSourceEnum;

    /**
     * ?????:0??
     *
     * @type {number}
     * @memberof DeviceLabel
     */
    actionOrder?: number;

    /**
     * ???????
     *
     * @type {{ [key: string]: any; }}
     * @memberof DeviceLabel
     */
    custom?: { [key: string]: any; } | null;

    /**
     * ???/????/???
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    content?: string | null;

    /**
     * @type {SendTypeEnum}
     * @memberof DeviceLabel
     */
    sendType?: SendTypeEnum;

    /**
     * ??
     *
     * @type {Array<string>}
     * @memberof DeviceLabel
     */
    tags?: Array<string> | null;

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceLabel
     */
    period?: number;

    /**
     * ??????
     *
     * @type {number}
     * @memberof DeviceLabel
     */
    archiveTime?: number | null;

    /**
     * ??????(??:?)
     *
     * @type {number}
     * @memberof DeviceLabel
     */
    uploadInterval?: number | null;

    /**
     * ??(????????????)
     *
     * @type {number}
     * @memberof DeviceLabel
     */
    length?: number | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    unit?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    description?: string | null;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof DeviceLabel
     */
    enable?: boolean;

    /**
     * @type {ProtectTypeEnum}
     * @memberof DeviceLabel
     */
    protectType?: ProtectTypeEnum;

    /**
     * ??????
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    dataType?: string | null;

    /**
     * ?????
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    encoding?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    method?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceLabel
     */
    registerAddress?: string | null;

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceLabel
     */
    readLength?: number | null;

    /**
     * @type {Device}
     * @memberof DeviceLabel
     */
    device?: Device;

    /**
     * ???
     *
     * @type {any}
     * @memberof DeviceLabel
     */
    currentValue?: any | null;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof DeviceLabel
     */
    updateTime?: Date | null;
}
