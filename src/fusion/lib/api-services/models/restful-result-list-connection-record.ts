/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ConnectionRecord } from './connection-record';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListConnectionRecord
 */
export interface RESTfulResultListConnectionRecord {

    /**
     * @type {number}
     * @memberof RESTfulResultListConnectionRecord
     */
    statusCode?: number | null;

    /**
     * @type {Array<ConnectionRecord>}
     * @memberof RESTfulResultListConnectionRecord
     */
    data?: Array<ConnectionRecord> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListConnectionRecord
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListConnectionRecord
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListConnectionRecord
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListConnectionRecord
     */
    timestamp?: number;
}
