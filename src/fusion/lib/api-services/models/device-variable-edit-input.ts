/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ProtectTypeEnum } from './protect-type-enum';
import { SendTypeEnum } from './send-type-enum';
import { ValueSourceEnum } from './value-source-enum';
 /**
 * ?????
 *
 * @export
 * @interface DeviceVariableEditInput
 */
export interface DeviceVariableEditInput {

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceVariableEditInput
     */
    deviceId: number;

    /**
     * ???
     *
     * @type {string}
     * @memberof DeviceVariableEditInput
     */
    identifier: string;

    /**
     * ???
     *
     * @type {string}
     * @memberof DeviceVariableEditInput
     */
    name: string;

    /**
     * ??????
     *
     * @type {string}
     * @memberof DeviceVariableEditInput
     */
    transitionType: string;

    /**
     * @type {ValueSourceEnum}
     * @memberof DeviceVariableEditInput
     */
    valueSource: ValueSourceEnum;

    /**
     * ?????:0??
     *
     * @type {number}
     * @memberof DeviceVariableEditInput
     */
    actionOrder?: number;

    /**
     * ?????????
     *
     * @type {{ [key: string]: any; }}
     * @memberof DeviceVariableEditInput
     */
    custom?: { [key: string]: any; } | null;

    /**
     * ???/????/???
     *
     * @type {string}
     * @memberof DeviceVariableEditInput
     */
    content?: string | null;

    /**
     * @type {SendTypeEnum}
     * @memberof DeviceVariableEditInput
     */
    sendType: SendTypeEnum;

    /**
     * ??
     *
     * @type {Array<string>}
     * @memberof DeviceVariableEditInput
     */
    tags?: Array<string> | null;

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceVariableEditInput
     */
    period?: number | null;

    /**
     * ??????
     *
     * @type {number}
     * @memberof DeviceVariableEditInput
     */
    archiveTime?: number | null;

    /**
     * ??????(??:?)
     *
     * @type {number}
     * @memberof DeviceVariableEditInput
     */
    uploadInterval?: number | null;

    /**
     * ??(????????????)
     *
     * @type {number}
     * @memberof DeviceVariableEditInput
     */
    length?: number | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof DeviceVariableEditInput
     */
    description?: string | null;

    /**
     * @type {ProtectTypeEnum}
     * @memberof DeviceVariableEditInput
     */
    protectType?: ProtectTypeEnum;

    /**
     * ??????
     *
     * @type {string}
     * @memberof DeviceVariableEditInput
     */
    dataType?: string | null;

    /**
     * ?????
     *
     * @type {string}
     * @memberof DeviceVariableEditInput
     */
    encoding?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceVariableEditInput
     */
    method?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceVariableEditInput
     */
    registerAddress?: string | null;

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceVariableEditInput
     */
    readLength?: number | null;

    /**
     * ???Id
     *
     * @type {number}
     * @memberof DeviceVariableEditInput
     */
    id: number;
}
