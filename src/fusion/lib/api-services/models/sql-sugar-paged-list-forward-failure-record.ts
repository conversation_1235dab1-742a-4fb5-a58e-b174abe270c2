/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ForwardFailureRecord } from './forward-failure-record';
 /**
 * 
 *
 * @export
 * @interface SqlSugarPagedListForwardFailureRecord
 */
export interface SqlSugarPagedListForwardFailureRecord {

    /**
     * @type {number}
     * @memberof SqlSugarPagedListForwardFailureRecord
     */
    page?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListForwardFailureRecord
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListForwardFailureRecord
     */
    total?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListForwardFailureRecord
     */
    totalPages?: number;

    /**
     * @type {Array<ForwardFailureRecord>}
     * @memberof SqlSugarPagedListForwardFailureRecord
     */
    items?: Array<ForwardFailureRecord> | null;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListForwardFailureRecord
     */
    hasPrevPage?: boolean;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListForwardFailureRecord
     */
    hasNextPage?: boolean;
}
