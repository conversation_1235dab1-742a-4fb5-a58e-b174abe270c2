/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListEdgeChannel } from './sql-sugar-paged-list-edge-channel';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultSqlSugarPagedListEdgeChannel
 */
export interface RESTfulResultSqlSugarPagedListEdgeChannel {

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListEdgeChannel
     */
    statusCode?: number | null;

    /**
     * @type {SqlSugarPagedListEdgeChannel}
     * @memberof RESTfulResultSqlSugarPagedListEdgeChannel
     */
    data?: SqlSugarPagedListEdgeChannel;

    /**
     * @type {boolean}
     * @memberof RESTfulResultSqlSugarPagedListEdgeChannel
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListEdgeChannel
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListEdgeChannel
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListEdgeChannel
     */
    timestamp?: number;
}
