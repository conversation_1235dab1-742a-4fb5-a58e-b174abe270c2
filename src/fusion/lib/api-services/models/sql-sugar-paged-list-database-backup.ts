/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DatabaseBackup } from './database-backup';
 /**
 * 
 *
 * @export
 * @interface SqlSugarPagedListDatabaseBackup
 */
export interface SqlSugarPagedListDatabaseBackup {

    /**
     * @type {number}
     * @memberof SqlSugarPagedListDatabaseBackup
     */
    page?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListDatabaseBackup
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListDatabaseBackup
     */
    total?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListDatabaseBackup
     */
    totalPages?: number;

    /**
     * @type {Array<DatabaseBackup>}
     * @memberof SqlSugarPagedListDatabaseBackup
     */
    items?: Array<DatabaseBackup> | null;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListDatabaseBackup
     */
    hasPrevPage?: boolean;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListDatabaseBackup
     */
    hasNextPage?: boolean;
}
