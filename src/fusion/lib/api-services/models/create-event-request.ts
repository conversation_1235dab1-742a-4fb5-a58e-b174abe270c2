/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { EventAction } from './event-action';
import { EventCondition } from './event-condition';
import { TimerTriggerConfig } from './timer-trigger-config';
import { TriggerEventTypeEnum } from './trigger-event-type-enum';
 /**
 * ??????????
 *
 * @export
 * @interface CreateEventRequest
 */
export interface CreateEventRequest {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof CreateEventRequest
     */
    deviceId: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof CreateEventRequest
     */
    eventName: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof CreateEventRequest
     */
    description?: string | null;

    /**
     * @type {EventCondition}
     * @memberof CreateEventRequest
     */
    condition: EventCondition;

    /**
     * @type {TriggerEventTypeEnum}
     * @memberof CreateEventRequest
     */
    triggerEventType?: TriggerEventTypeEnum;

    /**
     * @type {TimerTriggerConfig}
     * @memberof CreateEventRequest
     */
    timerConfig?: TimerTriggerConfig;

    /**
     * ??????
     *
     * @type {Array<EventAction>}
     * @memberof CreateEventRequest
     */
    actions?: Array<EventAction> | null;

    /**
     * ?????
     *
     * @type {number}
     * @memberof CreateEventRequest
     */
    priority?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof CreateEventRequest
     */
    eventGroup?: string | null;
}
