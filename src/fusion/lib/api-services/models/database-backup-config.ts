/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ???????
 *
 * @export
 * @interface DatabaseBackupConfig
 */
export interface DatabaseBackupConfig {

    /**
     * ????????
     *
     * @type {boolean}
     * @memberof DatabaseBackupConfig
     */
    enableAutoBackup?: boolean;

    /**
     * ????(?)
     *
     * @type {number}
     * @memberof DatabaseBackupConfig
     */
    backupInterval?: number;

    /**
     * ????(HH:mm)
     *
     * @type {string}
     * @memberof DatabaseBackupConfig
     */
    backupTime?: string | null;

    /**
     * ??????
     *
     * @type {number}
     * @memberof DatabaseBackupConfig
     */
    keepBackupCount?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof DatabaseBackupConfig
     */
    backupPath?: string | null;
}
