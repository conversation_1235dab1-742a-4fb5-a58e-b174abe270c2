/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { LinkStatus } from './link-status';
import { LinkType } from './link-type';
 /**
 * ??????
 *
 * @export
 * @interface NetworkLinkInfo
 */
export interface NetworkLinkInfo {

    /**
     * ??ID
     *
     * @type {string}
     * @memberof NetworkLinkInfo
     */
    id?: string | null;

    /**
     * ???ID
     *
     * @type {string}
     * @memberof NetworkLinkInfo
     */
    sourceId?: string | null;

    /**
     * ????ID
     *
     * @type {string}
     * @memberof NetworkLinkInfo
     */
    targetId?: string | null;

    /**
     * @type {LinkType}
     * @memberof NetworkLinkInfo
     */
    type?: LinkType;

    /**
     * @type {LinkStatus}
     * @memberof NetworkLinkInfo
     */
    status?: LinkStatus;

    /**
     * ??(Mbps)
     *
     * @type {number}
     * @memberof NetworkLinkInfo
     */
    bandwidth?: number;
}
