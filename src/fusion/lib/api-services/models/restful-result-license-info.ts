/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { LicenseInfo } from './license-info';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultLicenseInfo
 */
export interface RESTfulResultLicenseInfo {

    /**
     * @type {number}
     * @memberof RESTfulResultLicenseInfo
     */
    statusCode?: number | null;

    /**
     * @type {LicenseInfo}
     * @memberof RESTfulResultLicenseInfo
     */
    data?: LicenseInfo;

    /**
     * @type {boolean}
     * @memberof RESTfulResultLicenseInfo
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultLicenseInfo
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultLicenseInfo
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultLicenseInfo
     */
    timestamp?: number;
}
