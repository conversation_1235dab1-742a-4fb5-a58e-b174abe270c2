/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ???????? - ?????????
 *
 * @export
 * @interface EventLogActionResult
 */
export interface EventLogActionResult {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof EventLogActionResult
     */
    actionId?: number;

    /**
     * ??????
     *
     * @type {boolean}
     * @memberof EventLogActionResult
     */
    isSuccessful?: boolean;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventLogActionResult
     */
    result?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventLogActionResult
     */
    errorMessage?: string | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof EventLogActionResult
     */
    executionTime?: Date;

    /**
     * ????(??)
     *
     * @type {number}
     * @memberof EventLogActionResult
     */
    executionTimeMs?: number;
}
