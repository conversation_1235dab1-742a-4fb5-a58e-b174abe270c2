/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { NetworkNodeInfo } from './network-node-info';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultNetworkNodeInfo
 */
export interface RESTfulResultNetworkNodeInfo {

    /**
     * @type {number}
     * @memberof RESTfulResultNetworkNodeInfo
     */
    statusCode?: number | null;

    /**
     * @type {NetworkNodeInfo}
     * @memberof RESTfulResultNetworkNodeInfo
     */
    data?: NetworkNodeInfo;

    /**
     * @type {boolean}
     * @memberof RESTfulResultNetworkNodeInfo
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultNetworkNodeInfo
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultNetworkNodeInfo
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultNetworkNodeInfo
     */
    timestamp?: number;
}
