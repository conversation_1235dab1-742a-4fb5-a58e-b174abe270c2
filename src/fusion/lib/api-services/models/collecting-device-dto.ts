/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DataReportTypeEnum } from './data-report-type-enum';
import { DeviceStatusTypeEnum } from './device-status-type-enum';
 /**
 * ?????????DTO
 *
 * @export
 * @interface CollectingDeviceDto
 */
export interface CollectingDeviceDto {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof CollectingDeviceDto
     */
    id?: number;

    /**
     * ?????
     *
     * @type {string}
     * @memberof CollectingDeviceDto
     */
    identifier?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof CollectingDeviceDto
     */
    name?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof CollectingDeviceDto
     */
    description?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof CollectingDeviceDto
     */
    channelName?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof CollectingDeviceDto
     */
    driverName?: string | null;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof CollectingDeviceDto
     */
    isConnect?: boolean;

    /**
     * @type {DeviceStatusTypeEnum}
     * @memberof CollectingDeviceDto
     */
    status?: DeviceStatusTypeEnum;

    /**
     * ????:active-??, paused-??, stopped-??, error-??
     *
     * @type {string}
     * @memberof CollectingDeviceDto
     */
    runStatus?: string | null;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof CollectingDeviceDto
     */
    lastActiveTime?: Date | null;

    /**
     * ????(??)
     *
     * @type {number}
     * @memberof CollectingDeviceDto
     */
    collectionPeriod?: number | null;

    /**
     * ?????
     *
     * @type {number}
     * @memberof CollectingDeviceDto
     */
    totalDataPoints?: number;

    /**
     * ????????
     *
     * @type {number}
     * @memberof CollectingDeviceDto
     */
    enabledDataPoints?: number;

    /**
     * @type {DataReportTypeEnum}
     * @memberof CollectingDeviceDto
     */
    reportType?: DataReportTypeEnum;

    /**
     * ????
     *
     * @type {Array<string>}
     * @memberof CollectingDeviceDto
     */
    groupTags?: Array<string> | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof CollectingDeviceDto
     */
    location?: string | null;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof CollectingDeviceDto
     */
    collectionStartTime?: Date | null;

    /**
     * ??????(??)
     *
     * @type {number}
     * @memberof CollectingDeviceDto
     */
    todayOnlineMinutes?: number | null;
}
