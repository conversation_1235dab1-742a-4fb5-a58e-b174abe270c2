/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DeviceLabel } from './device-label';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultDeviceLabel
 */
export interface RESTfulResultDeviceLabel {

    /**
     * @type {number}
     * @memberof RESTfulResultDeviceLabel
     */
    statusCode?: number | null;

    /**
     * @type {DeviceLabel}
     * @memberof RESTfulResultDeviceLabel
     */
    data?: DeviceLabel;

    /**
     * @type {boolean}
     * @memberof RESTfulResultDeviceLabel
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultDeviceLabel
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultDeviceLabel
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultDeviceLabel
     */
    timestamp?: number;
}
