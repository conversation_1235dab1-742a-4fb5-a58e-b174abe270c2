/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * MQTT?????
 *
 * @export
 * @interface MqttClientConfig
 */
export interface MqttClientConfig {

    /**
     * ?????
     *
     * @type {string}
     * @memberof MqttClientConfig
     */
    host: string;

    /**
     * ?????
     *
     * @type {number}
     * @memberof MqttClientConfig
     */
    port: number;

    /**
     * ???ID
     *
     * @type {string}
     * @memberof MqttClientConfig
     */
    clientId?: string | null;

    /**
     * ???
     *
     * @type {string}
     * @memberof MqttClientConfig
     */
    username?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof MqttClientConfig
     */
    password?: string | null;

    /**
     * ????TLS
     *
     * @type {boolean}
     * @memberof MqttClientConfig
     */
    useTls?: boolean;

    /**
     * ??????(?)
     *
     * @type {number}
     * @memberof MqttClientConfig
     */
    timeout?: number;

    /**
     * ??????(?)
     *
     * @type {number}
     * @memberof MqttClientConfig
     */
    keepAlive?: number;
}
