/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

/**
 * <br />&nbsp;???? DeviceManagement = 1<br />&nbsp;???? DataForwarding = 2<br />&nbsp;???? SystemManagement = 3<br />&nbsp;???? DataCollection = 4<br />&nbsp;????? WorkflowEngine = 5<br />&nbsp;???? SecurityMonitoring = 6<br />&nbsp;???? DeviceMonitoring = 7<br />
 * @export
 * @enum {string}
 */
export enum NotificationSourceEnum {
    NUMBER_1 = 1,
    NUMBER_2 = 2,
    NUMBER_3 = 3,
    NUMBER_4 = 4,
    NUMBER_5 = 5,
    NUMBER_6 = 6,
    NUMBER_7 = 7
}

