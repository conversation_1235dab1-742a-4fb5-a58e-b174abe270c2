/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ActionResultListEventScheduleTaskResponse } from './action-result-list-event-schedule-task-response';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultActionResultListEventScheduleTaskResponse
 */
export interface RESTfulResultActionResultListEventScheduleTaskResponse {

    /**
     * @type {number}
     * @memberof RESTfulResultActionResultListEventScheduleTaskResponse
     */
    statusCode?: number | null;

    /**
     * @type {ActionResultListEventScheduleTaskResponse}
     * @memberof RESTfulResultActionResultListEventScheduleTaskResponse
     */
    data?: ActionResultListEventScheduleTaskResponse;

    /**
     * @type {boolean}
     * @memberof RESTfulResultActionResultListEventScheduleTaskResponse
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultActionResultListEventScheduleTaskResponse
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultActionResultListEventScheduleTaskResponse
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultActionResultListEventScheduleTaskResponse
     */
    timestamp?: number;
}
