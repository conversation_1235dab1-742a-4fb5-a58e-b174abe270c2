/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ?????? - ?????????????
 *
 * @export
 * @interface TaskMemoryStatus
 */
export interface TaskMemoryStatus {

    /**
     * ?????????
     *
     * @type {boolean}
     * @memberof TaskMemoryStatus
     */
    isRegistered?: boolean;

    /**
     * ????
     *
     * @type {Date}
     * @memberof TaskMemoryStatus
     */
    registerTime?: Date | null;

    /**
     * ???ID
     *
     * @type {string}
     * @memberof TaskMemoryStatus
     */
    schedulerId?: string | null;

    /**
     * ?????
     *
     * @type {boolean}
     * @memberof TaskMemoryStatus
     */
    isPaused?: boolean;

    /**
     * ????
     *
     * @type {Date}
     * @memberof TaskMemoryStatus
     */
    pauseTime?: Date | null;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof TaskMemoryStatus
     */
    lastCheckTime?: Date | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof TaskMemoryStatus
     */
    scheduleNode?: string | null;

    /**
     * ??ID
     *
     * @type {string}
     * @memberof TaskMemoryStatus
     */
    threadId?: string | null;

    /**
     * ???ID
     *
     * @type {number}
     * @memberof TaskMemoryStatus
     */
    processorId?: number | null;

    /**
     * ????(KB)
     *
     * @type {number}
     * @memberof TaskMemoryStatus
     */
    memoryUsage?: number;
}
