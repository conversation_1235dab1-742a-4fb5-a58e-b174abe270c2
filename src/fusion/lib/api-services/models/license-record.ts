/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ????
 *
 * @export
 * @interface LicenseRecord
 */
export interface LicenseRecord {

    /**
     * @type {number}
     * @memberof LicenseRecord
     */
    id?: number;

    /**
     * ???
     *
     * @type {string}
     * @memberof LicenseRecord
     */
    machineCode?: string | null;

    /**
     * ???
     *
     * @type {string}
     * @memberof LicenseRecord
     */
    activationCode?: string | null;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof LicenseRecord
     */
    isActivated?: boolean;

    /**
     * ??????
     *
     * @type {number}
     * @memberof LicenseRecord
     */
    deviceLimit?: number;

    /**
     * ????????
     *
     * @type {number}
     * @memberof LicenseRecord
     */
    tagLimit?: number;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof LicenseRecord
     */
    startTime?: Date;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof LicenseRecord
     */
    expireTime?: Date;

    /**
     * ????
     *
     * @type {string}
     * @memberof LicenseRecord
     */
    edition?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof LicenseRecord
     */
    customer?: string | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof LicenseRecord
     */
    activateTime?: Date;

    /**
     * ???
     *
     * @type {string}
     * @memberof LicenseRecord
     */
    operator?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof LicenseRecord
     */
    remark?: string | null;
}
