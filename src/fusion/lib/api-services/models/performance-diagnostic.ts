/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PerformanceTrend } from './performance-trend';
 /**
 * ??????
 *
 * @export
 * @interface PerformanceDiagnostic
 */
export interface PerformanceDiagnostic {

    /**
     * ??????(??)
     *
     * @type {number}
     * @memberof PerformanceDiagnostic
     */
    avgResponseTime?: number;

    /**
     * ??????(??)
     *
     * @type {number}
     * @memberof PerformanceDiagnostic
     */
    maxResponseTime?: number;

    /**
     * CPU???(%)
     *
     * @type {number}
     * @memberof PerformanceDiagnostic
     */
    cpuUsage?: number;

    /**
     * ?????(MB)
     *
     * @type {number}
     * @memberof PerformanceDiagnostic
     */
    memoryUsage?: number;

    /**
     * ?????(????????)
     *
     * @type {number}
     * @memberof PerformanceDiagnostic
     */
    dataThroughput?: number;

    /**
     * ????(??)
     *
     * @type {number}
     * @memberof PerformanceDiagnostic
     */
    queueDelay?: number;

    /**
     * @type {PerformanceTrend}
     * @memberof PerformanceDiagnostic
     */
    trend?: PerformanceTrend;
}
