/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListDatabaseBackup } from './sql-sugar-paged-list-database-backup';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultSqlSugarPagedListDatabaseBackup
 */
export interface RESTfulResultSqlSugarPagedListDatabaseBackup {

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListDatabaseBackup
     */
    statusCode?: number | null;

    /**
     * @type {SqlSugarPagedListDatabaseBackup}
     * @memberof RESTfulResultSqlSugarPagedListDatabaseBackup
     */
    data?: SqlSugarPagedListDatabaseBackup;

    /**
     * @type {boolean}
     * @memberof RESTfulResultSqlSugarPagedListDatabaseBackup
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListDatabaseBackup
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListDatabaseBackup
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListDatabaseBackup
     */
    timestamp?: number;
}
