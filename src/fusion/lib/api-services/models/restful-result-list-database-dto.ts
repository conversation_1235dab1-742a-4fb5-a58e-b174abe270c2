/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DatabaseDto } from './database-dto';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListDatabaseDto
 */
export interface RESTfulResultListDatabaseDto {

    /**
     * @type {number}
     * @memberof RESTfulResultListDatabaseDto
     */
    statusCode?: number | null;

    /**
     * @type {Array<DatabaseDto>}
     * @memberof RESTfulResultListDatabaseDto
     */
    data?: Array<DatabaseDto> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListDatabaseDto
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListDatabaseDto
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListDatabaseDto
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListDatabaseDto
     */
    timestamp?: number;
}
