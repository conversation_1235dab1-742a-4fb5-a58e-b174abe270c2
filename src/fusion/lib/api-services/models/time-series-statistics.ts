/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { TagStatistics } from './tag-statistics';
 /**
 * 
 *
 * @export
 * @interface TimeSeriesStatistics
 */
export interface TimeSeriesStatistics {

    /**
     * @type {string}
     * @memberof TimeSeriesStatistics
     */
    deviceId?: string | null;

    /**
     * @type {number}
     * @memberof TimeSeriesStatistics
     */
    dataPointCount?: number;

    /**
     * @type {{ [key: string]: TagStatistics; }}
     * @memberof TimeSeriesStatistics
     */
    tagStats?: { [key: string]: TagStatistics; } | null;

    /**
     * @type {Date}
     * @memberof TimeSeriesStatistics
     */
    firstDataTime?: Date | null;

    /**
     * @type {Date}
     * @memberof TimeSeriesStatistics
     */
    lastDataTime?: Date | null;
}
