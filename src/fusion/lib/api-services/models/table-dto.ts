/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ?????
 *
 * @export
 * @interface TableDto
 */
export interface TableDto {

    /**
     * ??
     *
     * @type {string}
     * @memberof TableDto
     */
    name?: string | null;

    /**
     * ???
     *
     * @type {string}
     * @memberof TableDto
     */
    comment?: string | null;

    /**
     * ???
     *
     * @type {number}
     * @memberof TableDto
     */
    rowCount?: number;

    /**
     * ????(MB)
     *
     * @type {number}
     * @memberof TableDto
     */
    dataSize?: number;

    /**
     * ????(MB)
     *
     * @type {number}
     * @memberof TableDto
     */
    indexSize?: number;
}
