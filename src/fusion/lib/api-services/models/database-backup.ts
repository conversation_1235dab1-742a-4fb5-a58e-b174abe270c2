/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ???????
 *
 * @export
 * @interface DatabaseBackup
 */
export interface DatabaseBackup {

    /**
     * @type {number}
     * @memberof DatabaseBackup
     */
    id?: number;

    /**
     * @type {Date}
     * @memberof DatabaseBackup
     */
    createTime?: Date | null;

    /**
     * @type {Date}
     * @memberof DatabaseBackup
     */
    updateTime?: Date | null;

    /**
     * @type {number}
     * @memberof DatabaseBackup
     */
    createUserId?: number | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DatabaseBackup
     */
    database?: string | null;

    /**
     * ??????
     *
     * @type {string}
     * @memberof DatabaseBackup
     */
    filePath?: string | null;

    /**
     * ??????(MB)
     *
     * @type {number}
     * @memberof DatabaseBackup
     */
    fileSize?: number;

    /**
     * ????(Manual/Auto)
     *
     * @type {string}
     * @memberof DatabaseBackup
     */
    backupType?: string | null;

    /**
     * ????(Success/Failed)
     *
     * @type {string}
     * @memberof DatabaseBackup
     */
    status?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DatabaseBackup
     */
    error?: string | null;
}
