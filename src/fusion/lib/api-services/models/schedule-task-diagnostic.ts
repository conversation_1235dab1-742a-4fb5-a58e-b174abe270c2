/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ScheduleTaskStatusEnum } from './schedule-task-status-enum';
 /**
 * ?????? - ???????????
 *
 * @export
 * @interface ScheduleTaskDiagnostic
 */
export interface ScheduleTaskDiagnostic {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof ScheduleTaskDiagnostic
     */
    taskId?: number;

    /**
     * ?????
     *
     * @type {string}
     * @memberof ScheduleTaskDiagnostic
     */
    taskIdentifier?: string | null;

    /**
     * @type {ScheduleTaskStatusEnum}
     * @memberof ScheduleTaskDiagnostic
     */
    status?: ScheduleTaskStatusEnum;

    /**
     * ???????
     *
     * @type {boolean}
     * @memberof ScheduleTaskDiagnostic
     */
    existsInMemory?: boolean;

    /**
     * ?????????
     *
     * @type {boolean}
     * @memberof ScheduleTaskDiagnostic
     */
    isRegistered?: boolean;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof ScheduleTaskDiagnostic
     */
    lastExecuteTime?: Date | null;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof ScheduleTaskDiagnostic
     */
    nextExecuteTime?: Date | null;

    /**
     * ?????????
     *
     * @type {string}
     * @memberof ScheduleTaskDiagnostic
     */
    missedReason?: string | null;

    /**
     * ???????
     *
     * @type {string}
     * @memberof ScheduleTaskDiagnostic
     */
    schedulerHealth?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof ScheduleTaskDiagnostic
     */
    threadState?: string | null;

    /**
     * ??????
     *
     * @type {string}
     * @memberof ScheduleTaskDiagnostic
     */
    lastError?: string | null;
}
