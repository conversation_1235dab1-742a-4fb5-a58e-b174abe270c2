/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface RESTfulResultDictionaryStringObject
 */
export interface RESTfulResultDictionaryStringObject {

    /**
     * @type {number}
     * @memberof RESTfulResultDictionaryStringObject
     */
    statusCode?: number | null;

    /**
     * @type {{ [key: string]: any; }}
     * @memberof RESTfulResultDictionaryStringObject
     */
    data?: { [key: string]: any; } | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultDictionaryStringObject
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultDictionaryStringObject
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultDictionaryStringObject
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultDictionaryStringObject
     */
    timestamp?: number;
}
