/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

/**
 * ?????<br />&nbsp; Equal = 0<br />&nbsp; NotEqual = 1<br />&nbsp; GreaterThan = 2<br />&nbsp; GreaterThanOrEqual = 3<br />&nbsp; LessThan = 4<br />&nbsp; LessThanOrEqual = 5<br />&nbsp; Contains = 6<br />&nbsp; NotContains = 7<br />&nbsp; StartsWith = 8<br />&nbsp; EndsWith = 9<br />
 * @export
 * @enum {string}
 */
export enum ComparisonOperator {
    NUMBER_0 = 0,
    NUMBER_1 = 1,
    NUMBER_2 = 2,
    NUMBER_3 = 3,
    NUMBER_4 = 4,
    NUMBER_5 = 5,
    NUMBER_6 = 6,
    NUMBER_7 = 7,
    NUMBER_8 = 8,
    NUMBER_9 = 9
}

