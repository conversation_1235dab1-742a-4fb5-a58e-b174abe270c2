/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DeviceDriver } from './device-driver';
import { DeviceInfo } from './device-info';
import { DeviceLogLevelEnum } from './device-log-level-enum';
 /**
 * ????
 *
 * @export
 * @interface DeviceUpdateInput
 */
export interface DeviceUpdateInput {

    /**
     * ??Id
     *
     * @type {string}
     * @memberof DeviceUpdateInput
     */
    identifier: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceUpdateInput
     */
    name?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof DeviceUpdateInput
     */
    description?: string | null;

    /**
     * ??
     *
     * @type {{ [key: string]: any; }}
     * @memberof DeviceUpdateInput
     */
    deviceConfig?: { [key: string]: any; } | null;

    /**
     * @type {DeviceInfo}
     * @memberof DeviceUpdateInput
     */
    deviceInfo?: DeviceInfo;

    /**
     * @type {DeviceDriver}
     * @memberof DeviceUpdateInput
     */
    driver: DeviceDriver;

    /**
     * ??ID
     *
     * @type {number}
     * @memberof DeviceUpdateInput
     */
    channelId?: number;

    /**
     * ????
     *
     * @type {Array<string>}
     * @memberof DeviceUpdateInput
     */
    groupTags?: Array<string> | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceUpdateInput
     */
    location?: string | null;

    /**
     * @type {DeviceLogLevelEnum}
     * @memberof DeviceUpdateInput
     */
    logLevel?: DeviceLogLevelEnum;

    /**
     * ??ID
     *
     * @type {number}
     * @memberof DeviceUpdateInput
     */
    id: number;
}
