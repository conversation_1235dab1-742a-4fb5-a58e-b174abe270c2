/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ????
 *
 * @export
 * @interface EventAction
 */
export interface EventAction {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof EventAction
     */
    id?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventAction
     */
    actionType?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventAction
     */
    name?: string | null;

    /**
     * ???? - JSON?????????
     *
     * @type {string}
     * @memberof EventAction
     */
    parameters?: string | null;

    /**
     * ????
     *
     * @type {number}
     * @memberof EventAction
     */
    order?: number;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof EventAction
     */
    enabled?: boolean;
}
