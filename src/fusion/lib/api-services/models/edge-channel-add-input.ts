/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Parity } from './parity';
import { StopBits } from './stop-bits';
 /**
 * ??????
 *
 * @export
 * @interface EdgeChannelAddInput
 */
export interface EdgeChannelAddInput {

    /**
     * ????
     *
     * @type {string}
     * @memberof EdgeChannelAddInput
     */
    channelName?: string | null;

    /**
     * ?? :??;??
     *
     * @type {boolean}
     * @memberof EdgeChannelAddInput
     */
    enable: boolean;

    /**
     * ???
     *
     * @type {string}
     * @memberof EdgeChannelAddInput
     */
    serial: string;

    /**
     * ???
     *
     * @type {number}
     * @memberof EdgeChannelAddInput
     */
    baudRate: number;

    /**
     * ???
     *
     * @type {number}
     * @memberof EdgeChannelAddInput
     */
    dataBits: number;

    /**
     * @type {StopBits}
     * @memberof EdgeChannelAddInput
     */
    stop: StopBits;

    /**
     * @type {Parity}
     * @memberof EdgeChannelAddInput
     */
    checkout: Parity;

    /**
     * ????
     *
     * @type {number}
     * @memberof EdgeChannelAddInput
     */
    threadCount?: number;
}
