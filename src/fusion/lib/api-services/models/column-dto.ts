/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ?????
 *
 * @export
 * @interface ColumnDto
 */
export interface ColumnDto {

    /**
     * ???
     *
     * @type {string}
     * @memberof ColumnDto
     */
    name?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof ColumnDto
     */
    dataType?: string | null;

    /**
     * ??
     *
     * @type {number}
     * @memberof ColumnDto
     */
    length?: number | null;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof ColumnDto
     */
    isNullable?: boolean;

    /**
     * ???
     *
     * @type {string}
     * @memberof ColumnDto
     */
    defaultValue?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof ColumnDto
     */
    comment?: string | null;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof ColumnDto
     */
    isPrimaryKey?: boolean;
}
