/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ForwardTypeEnum } from './forward-type-enum';
import { OfflineStorageConfig } from './offline-storage-config';
import { RetryPolicy } from './retry-policy';
 /**
 * 
 *
 * @export
 * @interface ForwardConfigUpdateInput
 */
export interface ForwardConfigUpdateInput {

    /**
     * @type {string}
     * @memberof ForwardConfigUpdateInput
     */
    name: string;

    /**
     * @type {boolean}
     * @memberof ForwardConfigUpdateInput
     */
    enable?: boolean;

    /**
     * @type {ForwardTypeEnum}
     * @memberof ForwardConfigUpdateInput
     */
    type?: ForwardTypeEnum;

    /**
     * @type {string}
     * @memberof ForwardConfigUpdateInput
     */
    url: string;

    /**
     * @type {string}
     * @memberof ForwardConfigUpdateInput
     */
    username?: string | null;

    /**
     * @type {string}
     * @memberof ForwardConfigUpdateInput
     */
    password?: string | null;

    /**
     * @type {number}
     * @memberof ForwardConfigUpdateInput
     */
    timeout?: number;

    /**
     * @type {string}
     * @memberof ForwardConfigUpdateInput
     */
    forwardMode?: string | null;

    /**
     * @type {any}
     * @memberof ForwardConfigUpdateInput
     */
    customConfig?: any | null;

    /**
     * @type {number}
     * @memberof ForwardConfigUpdateInput
     */
    connectionTimeout?: number;

    /**
     * @type {number}
     * @memberof ForwardConfigUpdateInput
     */
    reconnectInterval?: number;

    /**
     * @type {OfflineStorageConfig}
     * @memberof ForwardConfigUpdateInput
     */
    offlineStorageConfig?: OfflineStorageConfig;

    /**
     * @type {RetryPolicy}
     * @memberof ForwardConfigUpdateInput
     */
    retryConfig?: RetryPolicy;

    /**
     * @type {number}
     * @memberof ForwardConfigUpdateInput
     */
    id: number;
}
