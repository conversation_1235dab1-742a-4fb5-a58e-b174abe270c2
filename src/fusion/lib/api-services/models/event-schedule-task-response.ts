/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ScheduleTaskStatusEnum } from './schedule-task-status-enum';
import { TimerExpressionTypeEnum } from './timer-expression-type-enum';
import { TriggerEventTypeEnum } from './trigger-event-type-enum';
 /**
 * ??????????
 *
 * @export
 * @interface EventScheduleTaskResponse
 */
export interface EventScheduleTaskResponse {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof EventScheduleTaskResponse
     */
    id?: number;

    /**
     * ????ID
     *
     * @type {number}
     * @memberof EventScheduleTaskResponse
     */
    deviceEventId?: number;

    /**
     * ??ID
     *
     * @type {number}
     * @memberof EventScheduleTaskResponse
     */
    deviceId?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventScheduleTaskResponse
     */
    taskName?: string | null;

    /**
     * ?????
     *
     * @type {string}
     * @memberof EventScheduleTaskResponse
     */
    taskIdentifier?: string | null;

    /**
     * @type {TriggerEventTypeEnum}
     * @memberof EventScheduleTaskResponse
     */
    taskType?: TriggerEventTypeEnum;

    /**
     * @type {ScheduleTaskStatusEnum}
     * @memberof EventScheduleTaskResponse
     */
    status?: ScheduleTaskStatusEnum;

    /**
     * @type {TimerExpressionTypeEnum}
     * @memberof EventScheduleTaskResponse
     */
    expressionType?: TimerExpressionTypeEnum;

    /**
     * ?????
     *
     * @type {string}
     * @memberof EventScheduleTaskResponse
     */
    expression?: string | null;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof EventScheduleTaskResponse
     */
    lastExecuteTime?: Date | null;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof EventScheduleTaskResponse
     */
    nextExecuteTime?: Date | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof EventScheduleTaskResponse
     */
    startTime?: Date | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof EventScheduleTaskResponse
     */
    endTime?: Date | null;

    /**
     * ????
     *
     * @type {number}
     * @memberof EventScheduleTaskResponse
     */
    executeCount?: number;

    /**
     * ??????
     *
     * @type {number}
     * @memberof EventScheduleTaskResponse
     */
    maxExecuteCount?: number;

    /**
     * ??????
     *
     * @type {number}
     * @memberof EventScheduleTaskResponse
     */
    missedCount?: number;

    /**
     * ??????
     *
     * @type {number}
     * @memberof EventScheduleTaskResponse
     */
    successCount?: number;

    /**
     * ??????
     *
     * @type {number}
     * @memberof EventScheduleTaskResponse
     */
    failureCount?: number;

    /**
     * ??????
     *
     * @type {string}
     * @memberof EventScheduleTaskResponse
     */
    lastExecuteResult?: string | null;
}
