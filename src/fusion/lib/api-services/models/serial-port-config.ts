/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Handshake } from './handshake';
import { Parity } from './parity';
import { StopBits } from './stop-bits';
 /**
 * ????
 *
 * @export
 * @interface SerialPortConfig
 */
export interface SerialPortConfig {

    /**
     * ????
     *
     * @type {string}
     * @memberof SerialPortConfig
     */
    portName: string;

    /**
     * ???
     *
     * @type {number}
     * @memberof SerialPortConfig
     */
    baudRate: number;

    /**
     * ???
     *
     * @type {number}
     * @memberof SerialPortConfig
     */
    dataBits?: number;

    /**
     * @type {StopBits}
     * @memberof SerialPortConfig
     */
    stopBits?: StopBits;

    /**
     * @type {Parity}
     * @memberof SerialPortConfig
     */
    parity?: Parity;

    /**
     * @type {Handshake}
     * @memberof SerialPortConfig
     */
    handshake?: Handshake;
}
