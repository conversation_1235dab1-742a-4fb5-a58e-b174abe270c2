/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ActivateInput } from '../models';
import { GenerateActivationCodeInput } from '../models';
import { RESTfulResultLicenseInfo } from '../models';
import { RESTfulResultListLicenseRecord } from '../models';
import { RESTfulResultString } from '../models';
/**
 * LicenseApi - axios parameter creator
 * @export
 */
export const LicenseApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????
         * @param {ActivateInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        activate: async (body?: ActivateInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/license/activate`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ?????(?????)
         * @param {GenerateActivationCodeInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generateActivationCode: async (body?: GenerateActivationCodeInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/license/generate-code`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLicenseInfo: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/license/info`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {string} [machineCode] ???
         * @param {boolean} [isActivated] ????
         * @param {string} [edition] ????
         * @param {string} [customer] ????
         * @param {Date} [activateStartTime] ??????
         * @param {Date} [activateEndTime] ??????
         * @param {number} [page] ????
         * @param {number} [pageSize] ????
         * @param {string} [field] ????
         * @param {string} [order] ????
         * @param {string} [descStr] ????
         * @param {string} [keyword] ???????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLicenseRecords: async (machineCode?: string, isActivated?: boolean, edition?: string, customer?: string, activateStartTime?: Date, activateEndTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, keyword?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/license/records`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (machineCode !== undefined) {
                localVarQueryParameter['MachineCode'] = machineCode;
            }

            if (isActivated !== undefined) {
                localVarQueryParameter['IsActivated'] = isActivated;
            }

            if (edition !== undefined) {
                localVarQueryParameter['Edition'] = edition;
            }

            if (customer !== undefined) {
                localVarQueryParameter['Customer'] = customer;
            }

            if (activateStartTime !== undefined) {
                localVarQueryParameter['ActivateStartTime'] = (activateStartTime as any instanceof Date) ?
                    (activateStartTime as any).toISOString() :
                    activateStartTime;
            }

            if (activateEndTime !== undefined) {
                localVarQueryParameter['ActivateEndTime'] = (activateEndTime as any instanceof Date) ?
                    (activateEndTime as any).toISOString() :
                    activateEndTime;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * LicenseApi - functional programming interface
 * @export
 */
export const LicenseApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????
         * @param {ActivateInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async activate(body?: ActivateInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultLicenseInfo>>> {
            const localVarAxiosArgs = await LicenseApiAxiosParamCreator(configuration).activate(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ?????(?????)
         * @param {GenerateActivationCodeInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async generateActivationCode(body?: GenerateActivationCodeInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultString>>> {
            const localVarAxiosArgs = await LicenseApiAxiosParamCreator(configuration).generateActivationCode(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLicenseInfo(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultLicenseInfo>>> {
            const localVarAxiosArgs = await LicenseApiAxiosParamCreator(configuration).getLicenseInfo(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {string} [machineCode] ???
         * @param {boolean} [isActivated] ????
         * @param {string} [edition] ????
         * @param {string} [customer] ????
         * @param {Date} [activateStartTime] ??????
         * @param {Date} [activateEndTime] ??????
         * @param {number} [page] ????
         * @param {number} [pageSize] ????
         * @param {string} [field] ????
         * @param {string} [order] ????
         * @param {string} [descStr] ????
         * @param {string} [keyword] ???????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLicenseRecords(machineCode?: string, isActivated?: boolean, edition?: string, customer?: string, activateStartTime?: Date, activateEndTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, keyword?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListLicenseRecord>>> {
            const localVarAxiosArgs = await LicenseApiAxiosParamCreator(configuration).getLicenseRecords(machineCode, isActivated, edition, customer, activateStartTime, activateEndTime, page, pageSize, field, order, descStr, keyword, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * LicenseApi - factory interface
 * @export
 */
export const LicenseApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ????
         * @param {ActivateInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async activate(body?: ActivateInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultLicenseInfo>> {
            return LicenseApiFp(configuration).activate(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ?????(?????)
         * @param {GenerateActivationCodeInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async generateActivationCode(body?: GenerateActivationCodeInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultString>> {
            return LicenseApiFp(configuration).generateActivationCode(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLicenseInfo(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultLicenseInfo>> {
            return LicenseApiFp(configuration).getLicenseInfo(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {string} [machineCode] ???
         * @param {boolean} [isActivated] ????
         * @param {string} [edition] ????
         * @param {string} [customer] ????
         * @param {Date} [activateStartTime] ??????
         * @param {Date} [activateEndTime] ??????
         * @param {number} [page] ????
         * @param {number} [pageSize] ????
         * @param {string} [field] ????
         * @param {string} [order] ????
         * @param {string} [descStr] ????
         * @param {string} [keyword] ???????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLicenseRecords(machineCode?: string, isActivated?: boolean, edition?: string, customer?: string, activateStartTime?: Date, activateEndTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, keyword?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListLicenseRecord>> {
            return LicenseApiFp(configuration).getLicenseRecords(machineCode, isActivated, edition, customer, activateStartTime, activateEndTime, page, pageSize, field, order, descStr, keyword, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * LicenseApi - object-oriented interface
 * @export
 * @class LicenseApi
 * @extends {BaseAPI}
 */
export class LicenseApi extends BaseAPI {
    /**
     * 
     * @summary ????
     * @param {ActivateInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LicenseApi
     */
    public async activate(body?: ActivateInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultLicenseInfo>> {
        return LicenseApiFp(this.configuration).activate(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ?????(?????)
     * @param {GenerateActivationCodeInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LicenseApi
     */
    public async generateActivationCode(body?: GenerateActivationCodeInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultString>> {
        return LicenseApiFp(this.configuration).generateActivationCode(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LicenseApi
     */
    public async getLicenseInfo(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultLicenseInfo>> {
        return LicenseApiFp(this.configuration).getLicenseInfo(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {string} [machineCode] ???
     * @param {boolean} [isActivated] ????
     * @param {string} [edition] ????
     * @param {string} [customer] ????
     * @param {Date} [activateStartTime] ??????
     * @param {Date} [activateEndTime] ??????
     * @param {number} [page] ????
     * @param {number} [pageSize] ????
     * @param {string} [field] ????
     * @param {string} [order] ????
     * @param {string} [descStr] ????
     * @param {string} [keyword] ???????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LicenseApi
     */
    public async getLicenseRecords(machineCode?: string, isActivated?: boolean, edition?: string, customer?: string, activateStartTime?: Date, activateEndTime?: Date, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, keyword?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListLicenseRecord>> {
        return LicenseApiFp(this.configuration).getLicenseRecords(machineCode, isActivated, edition, customer, activateStartTime, activateEndTime, page, pageSize, field, order, descStr, keyword, options).then((request) => request(this.axios, this.basePath));
    }
}
