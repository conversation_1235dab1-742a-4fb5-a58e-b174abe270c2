/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ForwardFailureRecordStatus } from '../models';
import { RESTfulResultBatchDeleteResult } from '../models';
import { RESTfulResultBoolean } from '../models';
import { RESTfulResultInt32 } from '../models';
import { RESTfulResultRetryStatistics } from '../models';
import { RESTfulResultSqlSugarPagedListForwardFailureRecord } from '../models';
/**
 * RetryManagementApi - axios parameter creator
 * @export
 */
export const RetryManagementApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????????
         * @param {Array<number>} [body] ??ID??
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRetryManagementRecordsBatchDeleteDelete: async (body?: Array<number>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/retry-management/records/batch-delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {Array<number>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRetryManagementRecordsBatchRetryPost: async (body?: Array<number>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/retry-management/records/batch-retry`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {boolean} [excludeRetrying] ???????????,???true
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRetryManagementRecordsClearDelete: async (excludeRetrying?: boolean, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/retry-management/records/clear`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (excludeRetrying !== undefined) {
                localVarQueryParameter['excludeRetrying'] = excludeRetrying;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????(??)
         * @param {number} [forwardConfigId] 
         * @param {ForwardFailureRecordStatus} [status] &lt;br /&gt;&amp;nbsp; Pending &#x3D; 0&lt;br /&gt;&amp;nbsp; Retrying &#x3D; 1&lt;br /&gt;
         * @param {string} [keyword] 
         * @param {number} [page] ????
         * @param {number} [pageSize] ????
         * @param {string} [field] ????
         * @param {string} [order] ????
         * @param {string} [descStr] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRetryManagementRecordsGet: async (forwardConfigId?: number, status?: ForwardFailureRecordStatus, keyword?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/retry-management/records`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (forwardConfigId !== undefined) {
                localVarQueryParameter['ForwardConfigId'] = forwardConfigId;
            }

            if (status !== undefined) {
                localVarQueryParameter['Status'] = status;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (field !== undefined) {
                localVarQueryParameter['Field'] = field;
            }

            if (order !== undefined) {
                localVarQueryParameter['Order'] = order;
            }

            if (descStr !== undefined) {
                localVarQueryParameter['DescStr'] = descStr;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} recordId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRetryManagementRecordsRecordIdRetryPost: async (recordId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'recordId' is not null or undefined
            if (recordId === null || recordId === undefined) {
                throw new RequiredError('recordId','Required parameter recordId was null or undefined when calling apiRetryManagementRecordsRecordIdRetryPost.');
            }
            const localVarPath = `/api/retry-management/records/{recordId}/retry`
                .replace(`{${"recordId"}}`, encodeURIComponent(String(recordId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiRetryManagementStatisticsGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/retry-management/statistics`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * RetryManagementApi - functional programming interface
 * @export
 */
export const RetryManagementApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????????
         * @param {Array<number>} [body] ??ID??
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsBatchDeleteDelete(body?: Array<number>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultBatchDeleteResult>>> {
            const localVarAxiosArgs = await RetryManagementApiAxiosParamCreator(configuration).apiRetryManagementRecordsBatchDeleteDelete(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {Array<number>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsBatchRetryPost(body?: Array<number>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultBoolean>>> {
            const localVarAxiosArgs = await RetryManagementApiAxiosParamCreator(configuration).apiRetryManagementRecordsBatchRetryPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {boolean} [excludeRetrying] ???????????,???true
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsClearDelete(excludeRetrying?: boolean, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultInt32>>> {
            const localVarAxiosArgs = await RetryManagementApiAxiosParamCreator(configuration).apiRetryManagementRecordsClearDelete(excludeRetrying, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????(??)
         * @param {number} [forwardConfigId] 
         * @param {ForwardFailureRecordStatus} [status] &lt;br /&gt;&amp;nbsp; Pending &#x3D; 0&lt;br /&gt;&amp;nbsp; Retrying &#x3D; 1&lt;br /&gt;
         * @param {string} [keyword] 
         * @param {number} [page] ????
         * @param {number} [pageSize] ????
         * @param {string} [field] ????
         * @param {string} [order] ????
         * @param {string} [descStr] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsGet(forwardConfigId?: number, status?: ForwardFailureRecordStatus, keyword?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultSqlSugarPagedListForwardFailureRecord>>> {
            const localVarAxiosArgs = await RetryManagementApiAxiosParamCreator(configuration).apiRetryManagementRecordsGet(forwardConfigId, status, keyword, page, pageSize, field, order, descStr, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} recordId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsRecordIdRetryPost(recordId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultBoolean>>> {
            const localVarAxiosArgs = await RetryManagementApiAxiosParamCreator(configuration).apiRetryManagementRecordsRecordIdRetryPost(recordId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementStatisticsGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultRetryStatistics>>> {
            const localVarAxiosArgs = await RetryManagementApiAxiosParamCreator(configuration).apiRetryManagementStatisticsGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * RetryManagementApi - factory interface
 * @export
 */
export const RetryManagementApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ????????
         * @param {Array<number>} [body] ??ID??
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsBatchDeleteDelete(body?: Array<number>, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultBatchDeleteResult>> {
            return RetryManagementApiFp(configuration).apiRetryManagementRecordsBatchDeleteDelete(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {Array<number>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsBatchRetryPost(body?: Array<number>, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultBoolean>> {
            return RetryManagementApiFp(configuration).apiRetryManagementRecordsBatchRetryPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {boolean} [excludeRetrying] ???????????,???true
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsClearDelete(excludeRetrying?: boolean, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultInt32>> {
            return RetryManagementApiFp(configuration).apiRetryManagementRecordsClearDelete(excludeRetrying, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????(??)
         * @param {number} [forwardConfigId] 
         * @param {ForwardFailureRecordStatus} [status] &lt;br /&gt;&amp;nbsp; Pending &#x3D; 0&lt;br /&gt;&amp;nbsp; Retrying &#x3D; 1&lt;br /&gt;
         * @param {string} [keyword] 
         * @param {number} [page] ????
         * @param {number} [pageSize] ????
         * @param {string} [field] ????
         * @param {string} [order] ????
         * @param {string} [descStr] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsGet(forwardConfigId?: number, status?: ForwardFailureRecordStatus, keyword?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultSqlSugarPagedListForwardFailureRecord>> {
            return RetryManagementApiFp(configuration).apiRetryManagementRecordsGet(forwardConfigId, status, keyword, page, pageSize, field, order, descStr, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} recordId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementRecordsRecordIdRetryPost(recordId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultBoolean>> {
            return RetryManagementApiFp(configuration).apiRetryManagementRecordsRecordIdRetryPost(recordId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiRetryManagementStatisticsGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultRetryStatistics>> {
            return RetryManagementApiFp(configuration).apiRetryManagementStatisticsGet(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * RetryManagementApi - object-oriented interface
 * @export
 * @class RetryManagementApi
 * @extends {BaseAPI}
 */
export class RetryManagementApi extends BaseAPI {
    /**
     * 
     * @summary ????????
     * @param {Array<number>} [body] ??ID??
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RetryManagementApi
     */
    public async apiRetryManagementRecordsBatchDeleteDelete(body?: Array<number>, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultBatchDeleteResult>> {
        return RetryManagementApiFp(this.configuration).apiRetryManagementRecordsBatchDeleteDelete(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {Array<number>} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RetryManagementApi
     */
    public async apiRetryManagementRecordsBatchRetryPost(body?: Array<number>, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultBoolean>> {
        return RetryManagementApiFp(this.configuration).apiRetryManagementRecordsBatchRetryPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {boolean} [excludeRetrying] ???????????,???true
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RetryManagementApi
     */
    public async apiRetryManagementRecordsClearDelete(excludeRetrying?: boolean, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultInt32>> {
        return RetryManagementApiFp(this.configuration).apiRetryManagementRecordsClearDelete(excludeRetrying, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????(??)
     * @param {number} [forwardConfigId] 
     * @param {ForwardFailureRecordStatus} [status] &lt;br /&gt;&amp;nbsp; Pending &#x3D; 0&lt;br /&gt;&amp;nbsp; Retrying &#x3D; 1&lt;br /&gt;
     * @param {string} [keyword] 
     * @param {number} [page] ????
     * @param {number} [pageSize] ????
     * @param {string} [field] ????
     * @param {string} [order] ????
     * @param {string} [descStr] ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RetryManagementApi
     */
    public async apiRetryManagementRecordsGet(forwardConfigId?: number, status?: ForwardFailureRecordStatus, keyword?: string, page?: number, pageSize?: number, field?: string, order?: string, descStr?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultSqlSugarPagedListForwardFailureRecord>> {
        return RetryManagementApiFp(this.configuration).apiRetryManagementRecordsGet(forwardConfigId, status, keyword, page, pageSize, field, order, descStr, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} recordId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RetryManagementApi
     */
    public async apiRetryManagementRecordsRecordIdRetryPost(recordId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultBoolean>> {
        return RetryManagementApiFp(this.configuration).apiRetryManagementRecordsRecordIdRetryPost(recordId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof RetryManagementApi
     */
    public async apiRetryManagementStatisticsGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultRetryStatistics>> {
        return RetryManagementApiFp(this.configuration).apiRetryManagementStatisticsGet(options).then((request) => request(this.axios, this.basePath));
    }
}
