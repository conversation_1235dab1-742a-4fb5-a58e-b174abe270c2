/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { NetworkNodeInfo } from '../models';
import { PingRequest } from '../models';
import { PortScanRequest } from '../models';
import { RESTfulResultListPingResult } from '../models';
import { RESTfulResultListPortScanResult } from '../models';
import { RESTfulResultNetworkAnalytics } from '../models';
import { RESTfulResultNetworkDiscoveryResult } from '../models';
import { RESTfulResultNetworkNodeInfo } from '../models';
import { RESTfulResultNetworkTopology } from '../models';
import { RESTfulResultPortScanResult } from '../models';
/**
 * NetworkTopologyApi - axios parameter creator
 * @export
 */
export const NetworkTopologyApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????????
         * @param {NetworkNodeInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        addNode: async (body?: NetworkNodeInfo, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/topology/node`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??Ping
         * @param {PingRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchPing: async (body?: PingRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/topology/ping`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {string} [target] 
         * @param {number} [port] 
         * @param {number} [timeout] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        checkPort: async (target?: string, port?: number, timeout?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/topology/check-port`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (target !== undefined) {
                localVarQueryParameter['target'] = target;
            }

            if (port !== undefined) {
                localVarQueryParameter['port'] = port;
            }

            if (timeout !== undefined) {
                localVarQueryParameter['timeout'] = timeout;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        discoverNetwork: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/topology/discover`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {Date} [startTime] 
         * @param {Date} [endTime] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNetworkAnalytics: async (startTime?: Date, endTime?: Date, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/topology/analytics`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['startTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['endTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ???????????
         * @param {string} nodeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNodeDetails: async (nodeId: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'nodeId' is not null or undefined
            if (nodeId === null || nodeId === undefined) {
                throw new RequiredError('nodeId','Required parameter nodeId was null or undefined when calling getNodeDetails.');
            }
            const localVarPath = `/api/system/network/topology/node/{nodeId}`
                .replace(`{${"nodeId"}}`, encodeURIComponent(String(nodeId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTopology: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/topology/topology`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {string} nodeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        removeNode: async (nodeId: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'nodeId' is not null or undefined
            if (nodeId === null || nodeId === undefined) {
                throw new RequiredError('nodeId','Required parameter nodeId was null or undefined when calling removeNode.');
            }
            const localVarPath = `/api/system/network/topology/node/{nodeId}`
                .replace(`{${"nodeId"}}`, encodeURIComponent(String(nodeId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {PortScanRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        scanPorts: async (body?: PortScanRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/topology/port-scan`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * NetworkTopologyApi - functional programming interface
 * @export
 */
export const NetworkTopologyApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????????
         * @param {NetworkNodeInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async addNode(body?: NetworkNodeInfo, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await NetworkTopologyApiAxiosParamCreator(configuration).addNode(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??Ping
         * @param {PingRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async batchPing(body?: PingRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListPingResult>>> {
            const localVarAxiosArgs = await NetworkTopologyApiAxiosParamCreator(configuration).batchPing(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {string} [target] 
         * @param {number} [port] 
         * @param {number} [timeout] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async checkPort(target?: string, port?: number, timeout?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultPortScanResult>>> {
            const localVarAxiosArgs = await NetworkTopologyApiAxiosParamCreator(configuration).checkPort(target, port, timeout, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async discoverNetwork(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultNetworkDiscoveryResult>>> {
            const localVarAxiosArgs = await NetworkTopologyApiAxiosParamCreator(configuration).discoverNetwork(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {Date} [startTime] 
         * @param {Date} [endTime] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworkAnalytics(startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultNetworkAnalytics>>> {
            const localVarAxiosArgs = await NetworkTopologyApiAxiosParamCreator(configuration).getNetworkAnalytics(startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ???????????
         * @param {string} nodeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNodeDetails(nodeId: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultNetworkNodeInfo>>> {
            const localVarAxiosArgs = await NetworkTopologyApiAxiosParamCreator(configuration).getNodeDetails(nodeId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getTopology(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultNetworkTopology>>> {
            const localVarAxiosArgs = await NetworkTopologyApiAxiosParamCreator(configuration).getTopology(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {string} nodeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async removeNode(nodeId: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await NetworkTopologyApiAxiosParamCreator(configuration).removeNode(nodeId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {PortScanRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async scanPorts(body?: PortScanRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListPortScanResult>>> {
            const localVarAxiosArgs = await NetworkTopologyApiAxiosParamCreator(configuration).scanPorts(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * NetworkTopologyApi - factory interface
 * @export
 */
export const NetworkTopologyApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ????????
         * @param {NetworkNodeInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async addNode(body?: NetworkNodeInfo, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return NetworkTopologyApiFp(configuration).addNode(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??Ping
         * @param {PingRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async batchPing(body?: PingRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListPingResult>> {
            return NetworkTopologyApiFp(configuration).batchPing(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {string} [target] 
         * @param {number} [port] 
         * @param {number} [timeout] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async checkPort(target?: string, port?: number, timeout?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultPortScanResult>> {
            return NetworkTopologyApiFp(configuration).checkPort(target, port, timeout, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async discoverNetwork(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultNetworkDiscoveryResult>> {
            return NetworkTopologyApiFp(configuration).discoverNetwork(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {Date} [startTime] 
         * @param {Date} [endTime] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNetworkAnalytics(startTime?: Date, endTime?: Date, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultNetworkAnalytics>> {
            return NetworkTopologyApiFp(configuration).getNetworkAnalytics(startTime, endTime, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ???????????
         * @param {string} nodeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNodeDetails(nodeId: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultNetworkNodeInfo>> {
            return NetworkTopologyApiFp(configuration).getNodeDetails(nodeId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getTopology(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultNetworkTopology>> {
            return NetworkTopologyApiFp(configuration).getTopology(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {string} nodeId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async removeNode(nodeId: string, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return NetworkTopologyApiFp(configuration).removeNode(nodeId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {PortScanRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async scanPorts(body?: PortScanRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListPortScanResult>> {
            return NetworkTopologyApiFp(configuration).scanPorts(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * NetworkTopologyApi - object-oriented interface
 * @export
 * @class NetworkTopologyApi
 * @extends {BaseAPI}
 */
export class NetworkTopologyApi extends BaseAPI {
    /**
     * 
     * @summary ????????
     * @param {NetworkNodeInfo} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkTopologyApi
     */
    public async addNode(body?: NetworkNodeInfo, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return NetworkTopologyApiFp(this.configuration).addNode(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??Ping
     * @param {PingRequest} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkTopologyApi
     */
    public async batchPing(body?: PingRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListPingResult>> {
        return NetworkTopologyApiFp(this.configuration).batchPing(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {string} [target] 
     * @param {number} [port] 
     * @param {number} [timeout] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkTopologyApi
     */
    public async checkPort(target?: string, port?: number, timeout?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultPortScanResult>> {
        return NetworkTopologyApiFp(this.configuration).checkPort(target, port, timeout, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkTopologyApi
     */
    public async discoverNetwork(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultNetworkDiscoveryResult>> {
        return NetworkTopologyApiFp(this.configuration).discoverNetwork(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {Date} [startTime] 
     * @param {Date} [endTime] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkTopologyApi
     */
    public async getNetworkAnalytics(startTime?: Date, endTime?: Date, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultNetworkAnalytics>> {
        return NetworkTopologyApiFp(this.configuration).getNetworkAnalytics(startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ???????????
     * @param {string} nodeId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkTopologyApi
     */
    public async getNodeDetails(nodeId: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultNetworkNodeInfo>> {
        return NetworkTopologyApiFp(this.configuration).getNodeDetails(nodeId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkTopologyApi
     */
    public async getTopology(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultNetworkTopology>> {
        return NetworkTopologyApiFp(this.configuration).getTopology(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {string} nodeId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkTopologyApi
     */
    public async removeNode(nodeId: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return NetworkTopologyApiFp(this.configuration).removeNode(nodeId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {PortScanRequest} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NetworkTopologyApi
     */
    public async scanPorts(body?: PortScanRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListPortScanResult>> {
        return NetworkTopologyApiFp(this.configuration).scanPorts(body, options).then((request) => request(this.axios, this.basePath));
    }
}
