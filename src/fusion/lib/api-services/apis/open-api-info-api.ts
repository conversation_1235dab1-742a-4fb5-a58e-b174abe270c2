/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { RESTfulResultListOpenApiInfoModel } from '../models';
/**
 * OpenApiInfoApi - axios parameter creator
 * @export
 */
export const OpenApiInfoApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????API??
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOpenApiInfo: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/openapi/info/openApiInfo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * OpenApiInfoApi - functional programming interface
 * @export
 */
export const OpenApiInfoApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????API??
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOpenApiInfo(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListOpenApiInfoModel>>> {
            const localVarAxiosArgs = await OpenApiInfoApiAxiosParamCreator(configuration).getOpenApiInfo(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * OpenApiInfoApi - factory interface
 * @export
 */
export const OpenApiInfoApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ??????API??
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOpenApiInfo(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListOpenApiInfoModel>> {
            return OpenApiInfoApiFp(configuration).getOpenApiInfo(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * OpenApiInfoApi - object-oriented interface
 * @export
 * @class OpenApiInfoApi
 * @extends {BaseAPI}
 */
export class OpenApiInfoApi extends BaseAPI {
    /**
     * 
     * @summary ??????API??
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof OpenApiInfoApi
     */
    public async getOpenApiInfo(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListOpenApiInfoModel>> {
        return OpenApiInfoApiFp(this.configuration).getOpenApiInfo(options).then((request) => request(this.axios, this.basePath));
    }
}
