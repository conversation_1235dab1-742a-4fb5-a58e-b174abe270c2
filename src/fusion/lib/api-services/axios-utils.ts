/**
 * 当前版本：v1.6.0
 * 使用描述：https://editor.swagger.io 代码生成 typescript-axios 辅组工具库
 * 依赖说明：适配 axios 版本：v0.21.4
 * 视频教程：https://www.bilibili.com/video/BV1EW4y1C71D
 *
 * v1.6.0 更新内容：
 * - 新增全局API错误处理机制
 * - 支持后端统一响应格式（statusCode、succeeded、errors、timestamp）
 * - 当succeeded=false时自动显示错误信息
 * - 保持向后兼容性
 */

import globalAxios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  CancelTokenSource,
} from 'axios'
import { Configuration } from '../api-services'
import { BASE_PATH, BaseAPI } from '../api-services/base'
import { toast } from '@/components/ui/use-toast'

// 扩展 AxiosRequestConfig 接口
declare module 'axios' {
  interface AxiosRequestConfig {
    // retry?: boolean
    // retryConfig?: RetryConfig
    silentError?: boolean
  }
}

// 定义标准API响应格式
export interface ApiResponse<T = any> {
  data?: T
  errors?: string | Record<string, any>
  message?: string
  success?: boolean
  // 新增字段：支持后端统一响应格式
  statusCode?: number
  succeeded?: boolean
  timestamp?: number
}

/**
 * 接口服务器配置
 */
export const serveConfig = new Configuration({
  basePath: import.meta.env.VITE_API_BASE_URL || import.meta.env.NEXT_PUBLIC_API_BASE_URL || 'http://nervecube.cn:5005',
})

// token 键定义
export const accessTokenKey = 'access-token'
export const refreshAccessTokenKey = `x-${accessTokenKey}`

// 清除 token
export const clearAccessTokens = () => {
  window.localStorage.removeItem(accessTokenKey)
  window.localStorage.removeItem(refreshAccessTokenKey)

  // 这里可以添加清除更多 Key =========================================
}

// 错误处理
export const throwError = (message: string) => {
  throw new Error(message)
}

/**
 * 处理业务逻辑错误
 * 当API返回succeeded=false时，显示错误信息给用户
 * @param responseData API响应数据
 * @returns 是否为业务逻辑错误
 */
const handleBusinessError = (responseData: any): boolean => {
  // 检查是否为标准的业务响应格式
  if (!responseData || typeof responseData !== 'object') {
    return false
  }

  // 检查是否明确标记为失败
  if (responseData.hasOwnProperty('succeeded') && responseData.succeeded === false) {
    // 提取错误信息
    let errorMessage = '操作失败'

    if (responseData.errors) {
      if (typeof responseData.errors === 'string') {
        errorMessage = responseData.errors
      } else if (typeof responseData.errors === 'object') {
        errorMessage = JSON.stringify(responseData.errors)
      }
    } else if (responseData.message) {
      errorMessage = responseData.message
    }

    // 显示错误提示
    toast({
      title: '操作失败',
      description: errorMessage,
      variant: 'destructive',
    })

    return true
  }

  return false
}

/**
 * axios 默认实例
 */
export const axiosInstance: AxiosInstance = globalAxios

/**
 * 获取axios实例
 * @returns axios实例
 */
export function getAxiosInstance(): AxiosInstance {
  return axiosInstance
}

// 这里可以配置 axios 更多选项 =========================================
axiosInstance.defaults.timeout = 1000 * 300 // 设置超时，默认 120 秒
axiosInstance.defaults.baseURL = import.meta.env.VITE_API_BASE_URL || import.meta.env.NEXT_PUBLIC_API_BASE_URL || 'http://nervecube.cn:5005' // 设置基础URL

// 请求取消令牌存储
const pendingRequests = new Map<string, CancelTokenSource>()

// 保存请求重试配置
// const retryMap = new Map<string, { count: number; config: RetryConfig }>()

// 存储API实例的缓存
const apiInstanceCache = new Map<string, BaseAPI>()

// API实例创建计数器
let apiInstanceCounter = 0

/**
 * 生成请求的唯一键
 */
const getRequestKey = (config: AxiosRequestConfig): string => {
  const { url, method, params, data } = config
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&')
}

/**
 * 添加请求取消令牌
 */
const addPendingRequest = (config: AxiosRequestConfig): void => {
  const requestKey = getRequestKey(config)
  if (config.cancelToken) return

  const source = globalAxios.CancelToken.source()
  config.cancelToken = source.token
  pendingRequests.set(requestKey, source)
}

/**
 * 移除请求取消令牌
 */
const removePendingRequest = (config: AxiosRequestConfig): void => {
  const requestKey = getRequestKey(config)
  if (pendingRequests.has(requestKey)) {
    pendingRequests.delete(requestKey)
  }
}

/**
 * 取消所有请求
 */
export const cancelAllRequests = (message = '操作已取消'): void => {
  pendingRequests.forEach((source) => {
    source.cancel(message)
  })
  pendingRequests.clear()
}

/**
 * 取消匹配的请求
 * @param urlPattern 要取消的URL模式（正则表达式）
 * @param message 取消消息
 */
export const cancelRequestsByUrlPattern = (
  urlPattern: RegExp,
  message = '操作已取消'
): void => {
  pendingRequests.forEach((source, key) => {
    if (urlPattern.test(key.split('&')[0])) {
      source.cancel(message)
      pendingRequests.delete(key)
    }
  })
}

// 添加连接状态管理
let connectionTimeoutId: NodeJS.Timeout | null = null
let isShowingConnectionError = false
let isShowingNotFoundError = false
let connectionAlertContainer: HTMLDivElement | null = null

// 显示连接错误并跳转
const showConnectionError = () => {
  if (!isShowingConnectionError) {
    isShowingConnectionError = true
    if (navigateFunction) {
      navigateFunction('/network-error') // 跳转到断连页面
    }
  }
}

// 检查是否为需要跳转的网络相关错误
const isNetworkRelatedError = (status: number): boolean => {
  // 502: Bad Gateway, 503: Service Unavailable, 504: Gateway Timeout
  return status === 502 || status === 503 || status === 504
}

// 清除连接错误状态
const clearConnectionError = () => {
  if (isShowingConnectionError) {
    isShowingConnectionError = false
    if (navigateFunction) {
      navigateFunction('/') // 返回之前的页面
    }
  }
}

// 添加404错误处理函数
const showNotFoundError = (message?: string) => {
  if (!isShowingNotFoundError) {
    // 判断当前路径是否在白名单中
    const isInWhitelist =
      typeof window !== 'undefined' &&
      (window.location.pathname.includes('/settings/config') ||
        window.location.pathname.includes('/admin/settings'))

    // 如果在白名单中，只显示toast而不重定向
    if (isInWhitelist) {
      toast({
        title: '资源未找到',
        description: '当前API配置可能有误，请检查API基础URL设置',
        variant: 'destructive',
      })
      return
    }

    isShowingNotFoundError = true
    if (navigateFunction) {
      // 如果有错误消息，则添加到URL参数中
      const errorParam = message ? `?error=${encodeURIComponent(message)}` : ''
      navigateFunction(`/not-found-error${errorParam}`)
    }
  }
}

// 清除404错误状态
const clearNotFoundError = () => {
  if (isShowingNotFoundError) {
    isShowingNotFoundError = false
  }
}

// axios 请求拦截
axiosInstance.interceptors.request.use(
  (conf) => {
    // 添加请求取消令牌
    addPendingRequest(conf)

    // 清除之前的连接超时计时器
    if (connectionTimeoutId) {
      clearTimeout(connectionTimeoutId)
    }

    // 设置新的连接超时提示
    connectionTimeoutId = setTimeout(() => {
      showConnectionError()
    }, 120000) // 120秒后显示连接提示

    // 检查并修复URL，确保使用正确的基础URL
    if (conf.url && !conf.url.startsWith('http')) {
      // 如果URL是相对路径，并且不是以 / 开头，添加 /
      if (!conf.url.startsWith('/')) {
        conf.url = '/' + conf.url
      }

      // 如果有baseURL，使用它；否则使用serveConfig.basePath
      const baseUrl = conf.baseURL || serveConfig.basePath

      // 如果URL中包含完整的旧地址模式，替换为新的baseURL
      if (conf.url.includes('nervecube.cn:5005')) {
        const urlPath = conf.url.split('nervecube.cn:5005')[1]
        conf.url = urlPath
      }

      conf.baseURL = baseUrl
    }

    // 获取本地的 token
    const accessToken = window.localStorage.getItem(accessTokenKey)
    if (accessToken) {
      // 将 token 添加到请求报文头中
      conf.headers!['Authorization'] = `Bearer ${accessToken}`

      // 判断 accessToken 是否过期
      const jwt: any = decryptJWT(accessToken)
      const exp = getJWTDate(jwt.exp as number)

      // token 已经过期
      if (new Date() >= exp) {
        // 获取刷新 token
        const refreshAccessToken = window.localStorage.getItem(
          refreshAccessTokenKey
        )

        // 携带刷新 token
        if (refreshAccessToken) {
          conf.headers!['X-Authorization'] = `Bearer ${refreshAccessToken}`
        }
      }
    }

    // 这里编写请求拦截代码 =========================================
    return conf
  },
  (error) => {
    showConnectionError()
    // 处理请求错误
    if (error.request) {
    }
    // 这里编写请求错误代码

    return Promise.reject(error)
  }
)

// 添加一个用于存储导航函数的变量
let navigateFunction: ((path: string) => void) | null = null

// 添加设置导航函数的方法
export const setNavigateFunction = (navigate: (path: string) => void) => {
  navigateFunction = navigate
}

// axios 响应拦截
axiosInstance.interceptors.response.use(
  (res) => {
    // 清除连接超时计时器
    if (connectionTimeoutId) {
      clearTimeout(connectionTimeoutId)
      connectionTimeoutId = null
    }
    clearConnectionError()
    clearNotFoundError()

    // 从待处理请求映射中移除
    removePendingRequest(res.config)

    // 检查并存储授权信息
    checkAndStoreAuthentication(res)

    // 处理业务逻辑错误 - 新增全局错误处理机制
    const responseData = res.data
    if (responseData && typeof responseData === 'object') {
      // 优先检查新的统一响应格式
      if (responseData.hasOwnProperty('succeeded')) {
        // 处理401未授权错误
        if (responseData.statusCode === 401 || responseData.errors === '401 Unauthorized') {
          clearAccessTokens()
          toast({
            title: '登录提示',
            description: '登录已过期，请重新登录。',
            variant: 'destructive',
          })
          if (navigateFunction) {
            navigateFunction('/login')
          }
          return res
        }

        // 处理其他业务逻辑错误
        if (handleBusinessError(responseData)) {
          // 业务逻辑错误已处理，不抛出异常，让调用方正常处理
          return res
        }
      }
      // 兼容旧的错误格式
      else if (responseData.hasOwnProperty('errors') && responseData.errors) {
        // 检查是否为401未授权错误（兼容旧格式）
        const isUnauthorized =
          responseData.errors === '401 Unauthorized' ||
          (responseData.hasOwnProperty('statusCode') && responseData.statusCode === 401)

        if (isUnauthorized) {
          clearAccessTokens()
          toast({
            title: '登录提示',
            description: '登录已过期，请重新登录。',
            variant: 'destructive',
          })
          if (navigateFunction) {
            navigateFunction('/login')
          }
        }

        throwError(
          !responseData.errors
            ? 'Request Error.'
            : typeof responseData.errors === 'string'
            ? responseData.errors
            : JSON.stringify(responseData.errors)
        )
        return res
      }
    }

    // 这里编写响应拦截代码 =========================================

    return res
  },
  (error) => {
    // 清除连接超时计时器
    if (connectionTimeoutId) {
      clearTimeout(connectionTimeoutId)
      connectionTimeoutId = null
    }

    // 如果请求被取消，不处理
    if (globalAxios.isCancel(error)) {
      return Promise.reject(error)
    }

    // 获取请求配置
    const config = error.config

    // 从待处理请求映射中移除
    if (config) {
      removePendingRequest(config)
    }

    if (error.response) {
      clearConnectionError() // 有响应就清除连接错误提示
      clearNotFoundError() // 清除可能存在的404错误状态

      // 获取响应对象并解析状态码
      const res = error.response
      const status: number = res.status

      // 检查并存储授权信息
      checkAndStoreAuthentication(res)

      // 检查 401 权限
      if (status === 401) {
        toast({
          title: '登录提示',
          description: '登录已过期，请重新登录。',
          variant: 'destructive',
        })
        if (navigateFunction) {
          navigateFunction('/login')
        }
        clearAccessTokens()
      }
      // 检查网络相关错误（502、503、504）
      else if (isNetworkRelatedError(status)) {
        const errorMessages = {
          502: '无法连接到服务器，可能是网关错误',
          503: '服务暂时不可用',
          504: '服务器响应超时',
        }

        toast({
          title: `服务器错误 ${status}`,
          description:
            errorMessages[status as keyof typeof errorMessages] ||
            '网络连接异常',
          variant: 'destructive',
        })
        showConnectionError() // 调用连接错误处理
      }
      // 处理404错误
      else if (status === 404) {
        // 检查是否来自配置页面的请求
        const isFromConfigPage =
          typeof window !== 'undefined' &&
          (window.location.pathname.includes('/settings/config') ||
            window.location.pathname.includes('/admin/settings'))

        // 只有非静默错误模式下才显示错误提示
        if (!config || !config.silentError) {
          // 显示错误toast提示
          toast({
            title: `资源未找到`,
            description: isFromConfigPage
              ? '当前API配置可能有误，请检查API基础URL设置'
              : res.data?.message || '请求的资源不存在或已被移除',
            variant: 'destructive',
          })

          // 如果不是配置页面，才跳转到404错误页面
          if (!isFromConfigPage) {
            const errorMessage =
              res.data?.message || res.statusText || '请求的资源不存在'
            showNotFoundError(errorMessage)
          }
        }
      } else if (!config || !config.silentError) {
        // 显示通用错误提示，除非设置了silentError
        toast({
          title: `请求错误 ${status}`,
          description: res.data?.message || res.statusText || '服务器发生错误',
          variant: 'destructive',
        })
      }
    } else if (error.request) {
      // 在检查网络错误前，清除可能的404错误状态
      clearNotFoundError()
      // 网络错误或服务器未响应
      showConnectionError()
    } else if (!config || !config.silentError) {
      toast({
        title: '请求失败',
        description: error.message || '发生未知网络错误',
        variant: 'destructive',
      })
    }

    return Promise.reject(error)
  }
)

/**
 * 检查并存储授权信息
 * @param res 响应对象
 */
export function checkAndStoreAuthentication(res: any): void {
  // 读取响应报文头 token 信息
  var accessToken = res.headers[accessTokenKey]
  var refreshAccessToken = res.headers[refreshAccessTokenKey]

  // 判断是否是无效 token
  if (accessToken === 'invalid_token') {
    clearAccessTokens()
  }
  // 判断是否存在刷新 token，如果存在则存储在本地
  else if (
    refreshAccessToken &&
    accessToken &&
    accessToken !== 'invalid_token'
  ) {
    window.localStorage.setItem(accessTokenKey, accessToken)
    window.localStorage.setItem(refreshAccessTokenKey, refreshAccessToken)
  }
}

/**
 * 包装 Promise 并返回 [Error, any]
 * @param promise Promise 方法
 * @param errorExt 自定义错误信息（拓展）
 * @returns [Error, any]
 */
export function feature<T, U = Error>(
  promise: Promise<T>,
  errorExt?: object
): Promise<[U, undefined] | [null, T]> {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[U, undefined]>((err: U) => {
      // 增强错误对象，提取更多有用信息
      let enhancedError: any = { ...err, ...errorExt };

      // 处理响应中的错误信息
      if ((err as any)?.response?.data) {
        const responseData = (err as any).response.data;

        // 优先使用新的统一响应格式
        if (responseData.hasOwnProperty('succeeded') && responseData.succeeded === false) {
          enhancedError = {
            ...err,
            message: typeof responseData.errors === 'string'
              ? responseData.errors
              : JSON.stringify(responseData.errors),
            statusCode: responseData.statusCode || (err as any).response?.status,
            timestamp: responseData.timestamp,
            ...errorExt,
          };
        }
        // 兼容旧的错误格式
        else if (responseData.errors) {
          enhancedError = {
            ...err,
            message: typeof responseData.errors === 'string'
              ? responseData.errors
              : JSON.stringify(responseData.errors),
            statusCode: (err as any).response?.status,
            ...errorExt,
          };
        }
      }

      return [enhancedError as U, undefined]
    })
}

/**
 * API请求的加载状态管理
 */
export class LoadingManager {
  private static loadingStates = new Map<string, boolean>()

  /**
   * 设置加载状态
   * @param key 请求标识符
   * @param isLoading 是否加载中
   */
  static setLoading(key: string, isLoading: boolean): void {
    this.loadingStates.set(key, isLoading)
  }

  /**
   * 获取加载状态
   * @param key 请求标识符
   * @returns 是否加载中
   */
  static isLoading(key: string): boolean {
    return this.loadingStates.get(key) || false
  }

  /**
   * 包装API请求并自动管理加载状态
   * @param key 请求标识符
   * @param promiseFn 返回Promise的函数
   * @returns 原始Promise结果
   */
  static async withLoading<T>(
    key: string,
    promiseFn: () => Promise<T>
  ): Promise<T> {
    try {
      this.setLoading(key, true)
      return await promiseFn()
    } finally {
      this.setLoading(key, false)
    }
  }
}

/**
 * 包装API请求并管理加载状态，返回feature格式的结果
 * @param key 请求标识符
 * @param promiseFn 返回Promise的函数
 * @returns feature格式的结果 [error, data]
 */
export async function withLoadingFeature<T, U = Error>(
  key: string,
  promiseFn: () => Promise<T>,
  errorExt?: object
): Promise<[U, undefined] | [null, T]> {
  try {
    LoadingManager.setLoading(key, true)
    return await feature<T, U>(promiseFn(), errorExt)
  } finally {
    LoadingManager.setLoading(key, false)
  }
}

/**
 * 获取/创建服务 API 实例
 * @param apiType BaseAPI 派生类型
 * @param configuration 服务器配置对象
 * @param basePath 服务器地址
 * @param axiosObject axios 实例
 * @returns 服务API 实例
 */
export function getAPI<T extends BaseAPI>(
  apiType: new (
    configuration?: Configuration,
    basePath?: string,
    axiosInstance?: AxiosInstance
  ) => T,
  configuration: Configuration = serveConfig,
  basePath: string = BASE_PATH,
  axiosObject: AxiosInstance = axiosInstance
) {
  // 创建缓存键
  const cacheKey = `${apiType.name}_${
    configuration.basePath || basePath
  }_${apiInstanceCounter}`

  // 检查缓存中是否存在
  if (apiInstanceCache.has(cacheKey)) {
    return apiInstanceCache.get(cacheKey) as T
  }

  // 创建新的API实例
  const instance = new apiType(configuration, basePath, axiosObject)

  // 存入缓存
  apiInstanceCache.set(cacheKey, instance)

  return instance
}

/**
 * 刷新所有API实例
 * 在API配置更改后调用此函数，强制重新创建实例
 */
export function refreshApiInstances() {
  // 清空API实例缓存
  apiInstanceCache.clear()

  // 增加计数器，确保新实例使用新配置
  apiInstanceCounter++
}

/**
 * 解密 JWT token 的信息
 * @param token jwt token 字符串
 * @returns <any>object
 */
export function decryptJWT(token: string): any {
  token = token.replace(/_/g, '/').replace(/-/g, '+')
  var json = decodeURIComponent(escape(window.atob(token.split('.')[1])))
  return JSON.parse(json)
}

/**
 * 将 JWT 时间戳转换成 Date
 * @description 主要针对 `exp`，`iat`，`nbf`
 * @param timestamp 时间戳
 * @returns Date 对象
 */
export function getJWTDate(timestamp: number): Date {
  return new Date(timestamp * 1000)
}

/**
 * 解析 token 授权信息
 * @returns 解密后的 token 对象
 */
export function getAccessInfo(): any {
  const accessToken = window.localStorage.getItem(accessTokenKey)

  if (!accessToken) {
    return null
  }

  try {
    const accessInfo = decryptJWT(accessToken)
    return accessInfo
  } catch {
    return null
  }
}

/**
 * 实现异步延迟
 * @param delay 延迟时间（毫秒）
 * @returns
 */
export function sleep(delay: number) {
  return new Promise((resolve) => setTimeout(resolve, delay))
}

/**
 * 更新API基础地址
 * 在用户修改配置后调用此函数
 */
export function updateApiBasePath(apiBaseUrl: string) {
  serveConfig.basePath = apiBaseUrl

  // 同时更新axios默认配置
  axiosInstance.defaults.baseURL = apiBaseUrl

  // 刷新所有API实例
  refreshApiInstances()
}

/**
 * 获取API扩展配置
 * @param config 请求配置
 * @returns 扩展的请求配置
 */
export function getRequestConfig(
  config: Partial<AxiosRequestConfig> = {}
): AxiosRequestConfig {
  return {
    ...config,
    // retry: config.retry === undefined ? true : config.retry,
    // retryConfig: config.retryConfig || defaultRetryConfig,
    silentError: config.silentError || false,
  }
}

/**
 * 扩展API实例，使其支持加载状态管理
 * 使用例子: const userApi = withLoadingAPI(getAPI(UserAPI), 'user')
 */
export function withLoadingAPI<T extends BaseAPI>(
  api: T,
  keyPrefix: string
): T {
  const handler = {
    get(target: T, prop: string) {
      const originalProp = target[prop as keyof T]

      if (typeof originalProp === 'function' && prop.startsWith('api')) {
        // 只代理以'api'开头的方法
        return function (...args: any[]) {
          const loadingKey = `${keyPrefix}.${prop}`
          return LoadingManager.withLoading(loadingKey, () => {
            return (originalProp as Function).apply(target, args)
          })
        }
      }

      return originalProp
    },
  }

  return new Proxy(api, handler)
}
