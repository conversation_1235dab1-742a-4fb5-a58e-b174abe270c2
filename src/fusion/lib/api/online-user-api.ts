import { feature, LoadingManager, axiosInstance } from '../api-services/axios-utils'
import { AxiosResponse } from 'axios'

// 在线用户数据类型
export interface OnlineUser {
  id: string
  userId: number
  username: string
  displayName: string
  role: string
  loginTime: string
  lastActivity: string
  ipAddress: string
  location: string
  device: 'desktop' | 'mobile' | 'tablet'
  browser: string
  os: string
  sessionId: string
  status: 'active' | 'idle' | 'away'
  connectionType: 'websocket' | 'http'
  isOnline: boolean
  userAgent?: string
}

// 在线用户统计类型
export interface OnlineUserStats {
  totalOnline: number
  activeUsers: number
  idleUsers: number
  awayUsers: number
  websocketConnections: number
  httpConnections: number
  newLoginsToday: number
  averageSessionTime: string
}

// 在线用户查询参数
export interface OnlineUserQueryParams {
  page?: number
  pageSize?: number
  username?: string
  ipAddress?: string
  location?: string
  status?: 'active' | 'idle' | 'away'
  connectionType?: 'websocket' | 'http'
}

// 强制下线参数
export interface KickUserInput {
  sessionId: string
  reason?: string
}

// 分页响应类型
export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}

// 加载状态键
export const LOADING_KEY = {
  ONLINE_USER_LIST: 'online-user-list',
  ONLINE_USER_STATS: 'online-user-stats',
  KICK_USER: 'kick-user',
  USER_DETAIL: 'user-detail',
} as const

/**
 * 在线用户管理API服务类
 */
class OnlineUserApiService {
  /**
   * 获取在线用户列表
   * @param params 查询参数
   * @returns 在线用户分页数据
   */
  async getOnlineUserList(params: OnlineUserQueryParams = {}): Promise<AxiosResponse<PagedResponse<OnlineUser>>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.ONLINE_USER_LIST, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysAuth/onlineUsers', { params })
      )

      if (error) {
        console.error('获取在线用户列表失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ONLINE_USER_LIST, false)
    }
  }

  /**
   * 获取在线用户统计信息
   * @returns 在线用户统计数据
   */
  async getOnlineUserStats(): Promise<AxiosResponse<OnlineUserStats>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.ONLINE_USER_STATS, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysAuth/onlineStats')
      )

      if (error) {
        console.error('获取在线用户统计失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ONLINE_USER_STATS, false)
    }
  }

  /**
   * 根据会话ID获取在线用户详情
   * @param sessionId 会话ID
   * @returns 在线用户详情
   */
  async getOnlineUserDetail(sessionId: string): Promise<AxiosResponse<OnlineUser>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.USER_DETAIL, true)
      const [error, response] = await feature(
        axiosInstance.get(`/api/sysAuth/onlineUsers/${sessionId}`)
      )

      if (error) {
        console.error('获取在线用户详情失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.USER_DETAIL, false)
    }
  }

  /**
   * 强制用户下线
   * @param input 强制下线参数
   * @returns 操作结果
   */
  async kickUser(input: KickUserInput): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.KICK_USER, true)
      const [error, response] = await feature(
        axiosInstance.post('/api/sysAuth/kickUser', input)
      )

      if (error) {
        console.error('强制用户下线失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.KICK_USER, false)
    }
  }

  /**
   * 批量强制用户下线
   * @param sessionIds 会话ID列表
   * @param reason 下线原因
   * @returns 操作结果
   */
  async kickUsers(sessionIds: string[], reason?: string): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.KICK_USER, true)
      const [error, response] = await feature(
        axiosInstance.post('/api/sysAuth/kickUsers', { sessionIds, reason })
      )

      if (error) {
        console.error('批量强制用户下线失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.KICK_USER, false)
    }
  }

  /**
   * 获取用户会话历史
   * @param userId 用户ID
   * @param days 查询天数，默认7天
   * @returns 会话历史数据
   */
  async getUserSessionHistory(userId: number, days: number = 7): Promise<AxiosResponse<OnlineUser[]>> {
    const [error, response] = await feature(
      axiosInstance.get(`/api/sysAuth/sessionHistory/${userId}`, { 
        params: { days } 
      })
    )

    if (error) {
      console.error('获取用户会话历史失败:', error)
      throw error
    }

    return response
  }

  /**
   * 获取系统登录统计
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 登录统计数据
   */
  async getLoginStats(startDate?: string, endDate?: string): Promise<AxiosResponse<any>> {
    const [error, response] = await feature(
      axiosInstance.get('/api/sysAuth/loginStats', { 
        params: { startDate, endDate } 
      })
    )

    if (error) {
      console.error('获取登录统计失败:', error)
      throw error
    }

    return response
  }

  /**
   * 检查用户是否在线
   * @param userId 用户ID
   * @returns 在线状态
   */
  async checkUserOnline(userId: number): Promise<AxiosResponse<boolean>> {
    const [error, response] = await feature(
      axiosInstance.get(`/api/sysAuth/checkOnline/${userId}`)
    )

    if (error) {
      console.error('检查用户在线状态失败:', error)
      throw error
    }

    return response
  }

  /**
   * 发送系统消息给在线用户
   * @param sessionIds 会话ID列表
   * @param message 消息内容
   * @param type 消息类型
   * @returns 发送结果
   */
  async sendMessageToUsers(
    sessionIds: string[], 
    message: string, 
    type: 'info' | 'warning' | 'error' = 'info'
  ): Promise<AxiosResponse<boolean>> {
    const [error, response] = await feature(
      axiosInstance.post('/api/sysAuth/sendMessage', { 
        sessionIds, 
        message, 
        type 
      })
    )

    if (error) {
      console.error('发送消息失败:', error)
      throw error
    }

    return response
  }

  /**
   * 获取用户设备信息统计
   * @returns 设备统计数据
   */
  async getDeviceStats(): Promise<AxiosResponse<any>> {
    const [error, response] = await feature(
      axiosInstance.get('/api/sysAuth/deviceStats')
    )

    if (error) {
      console.error('获取设备统计失败:', error)
      throw error
    }

    return response
  }
}

// 导出单例实例
export const onlineUserApi = new OnlineUserApiService()
export default onlineUserApi
