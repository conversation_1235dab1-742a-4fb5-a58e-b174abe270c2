import { feature, LoadingManager, axiosInstance } from '../api-services/axios-utils'
import { AxiosResponse } from 'axios'

// 角色数据类型
export interface SysRole {
  id: number
  name: string
  code: string
  description?: string
  status: boolean
  isDefault: boolean
  accountType?: string
  sort: number
  createTime: string
  updateTime?: string
}

// 角色查询参数
export interface RoleQueryParams {
  page?: number
  pageSize?: number
  name?: string
  code?: string
  accountType?: string
  status?: boolean
}

// 角色创建/更新输入
export interface RoleInput {
  name: string
  code: string
  description?: string
  status?: boolean
  accountType?: string
  sort?: number
}

// 角色菜单权限配置
export interface RoleMenuPermission {
  roleId: number
  menuIds: number[]
}

// 菜单权限项
export interface MenuPermissionItem {
  id: number
  name: string
  code: string
  parentId?: number
  children?: MenuPermissionItem[]
  checked: boolean
}

// 分页响应类型
export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}

// API响应类型
export interface ApiResponse<T> {
  data: T
  succeeded: boolean
  errors?: string
  message?: string
}

// 加载状态键
const LOADING_KEY = {
  ROLE_LIST: 'roleManagement.list',
  ROLE_CREATE: 'roleManagement.create',
  ROLE_UPDATE: 'roleManagement.update',
  ROLE_DELETE: 'roleManagement.delete',
  ROLE_TOGGLE_STATUS: 'roleManagement.toggleStatus',
  ROLE_PERMISSIONS: 'roleManagement.permissions',
  ROLE_ASSIGN_PERMISSIONS: 'roleManagement.assignPermissions',
}

/**
 * 角色管理API服务类
 */
class RoleManagementApiService {
  /**
   * 获取角色分页列表
   * @param params 查询参数
   * @returns 角色分页数据
   */
  async getRoleList(params: RoleQueryParams = {}): Promise<AxiosResponse<PagedResponse<SysRole>>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_LIST, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysRole/page', { params })
      )

      if (error) {
        console.error('获取角色列表失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_LIST, false)
    }
  }

  /**
   * 获取所有角色（不分页）
   * @returns 所有角色列表
   */
  async getAllRoles(): Promise<AxiosResponse<SysRole[]>> {
    const [error, response] = await feature(
      axiosInstance.get('/api/sysRole/list')
    )

    if (error) {
      console.error('获取所有角色失败:', error)
      throw error
    }

    return response
  }

  /**
   * 根据ID获取角色详情
   * @param id 角色ID
   * @returns 角色详情
   */
  async getRoleById(id: number): Promise<AxiosResponse<SysRole>> {
    const [error, response] = await feature(
      axiosInstance.get(`/api/sysRole/${id}`)
    )

    if (error) {
      console.error('获取角色详情失败:', error)
      throw error
    }

    return response
  }

  /**
   * 创建新角色
   * @param role 角色信息
   * @returns 创建结果
   */
  async createRole(role: RoleInput): Promise<AxiosResponse<number>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_CREATE, true)
      const [error, response] = await feature(
        axiosInstance.post('/api/sysRole', role)
      )

      if (error) {
        console.error('创建角色失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_CREATE, false)
    }
  }

  /**
   * 更新角色信息
   * @param id 角色ID
   * @param role 角色信息
   * @returns 更新结果
   */
  async updateRole(id: number, role: RoleInput): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_UPDATE, true)
      const [error, response] = await feature(
        axiosInstance.put(`/api/sysRole/${id}`, role)
      )

      if (error) {
        console.error('更新角色失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_UPDATE, false)
    }
  }

  /**
   * 删除角色
   * @param id 角色ID
   * @returns 删除结果
   */
  async deleteRole(id: number): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_DELETE, true)
      const [error, response] = await feature(
        axiosInstance.delete(`/api/sysRole/${id}`)
      )

      if (error) {
        console.error('删除角色失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_DELETE, false)
    }
  }

  /**
   * 切换角色状态
   * @param id 角色ID
   * @param status 新状态
   * @returns 更新结果
   */
  async toggleRoleStatus(id: number, status: boolean): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_TOGGLE_STATUS, true)
      const [error, response] = await feature(
        axiosInstance.patch(`/api/sysRole/${id}/status`, { status })
      )

      if (error) {
        console.error('切换角色状态失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_TOGGLE_STATUS, false)
    }
  }

  /**
   * 获取角色的菜单权限
   * @param roleId 角色ID
   * @returns 菜单权限列表
   */
  async getRolePermissions(roleId: number): Promise<AxiosResponse<number[]>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_PERMISSIONS, true)
      const [error, response] = await feature(
        axiosInstance.get(`/api/sysRole/${roleId}/menus`)
      )

      if (error) {
        console.error('获取角色权限失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_PERMISSIONS, false)
    }
  }

  /**
   * 为角色分配菜单权限
   * @param roleId 角色ID
   * @param menuIds 菜单ID列表
   * @returns 分配结果
   */
  async assignRolePermissions(roleId: number, menuIds: number[]): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_ASSIGN_PERMISSIONS, true)
      const [error, response] = await feature(
        axiosInstance.post(`/api/sysRole/${roleId}/menus`, menuIds)
      )

      if (error) {
        console.error('分配角色权限失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_ASSIGN_PERMISSIONS, false)
    }
  }

  /**
   * 检查是否正在加载角色列表
   * @returns 是否正在加载
   */
  isRoleListLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.ROLE_LIST)
  }

  /**
   * 检查是否正在创建角色
   * @returns 是否正在加载
   */
  isRoleCreateLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.ROLE_CREATE)
  }

  /**
   * 检查是否正在更新角色
   * @returns 是否正在加载
   */
  isRoleUpdateLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.ROLE_UPDATE)
  }

  /**
   * 检查是否正在删除角色
   * @returns 是否正在加载
   */
  isRoleDeleteLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.ROLE_DELETE)
  }

  /**
   * 检查是否正在切换角色状态
   * @returns 是否正在加载
   */
  isRoleToggleStatusLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.ROLE_TOGGLE_STATUS)
  }

  /**
   * 检查是否正在加载角色权限
   * @returns 是否正在加载
   */
  isRolePermissionsLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.ROLE_PERMISSIONS)
  }

  /**
   * 检查是否正在分配角色权限
   * @returns 是否正在加载
   */
  isRoleAssignPermissionsLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.ROLE_ASSIGN_PERMISSIONS)
  }

  /**
   * 获取指定API的加载状态
   * @param key 加载状态键
   * @returns 是否加载中
   */
  isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}

// 导出单例实例
export const roleManagementApi = new RoleManagementApiService()
