import { axiosInstance } from '../api-services/axios-utils'

/**
 * API可用性检查工具
 * 用于检查后端API是否可用，如果不可用则提供降级方案
 */
export class ApiChecker {
  private static checkedApis = new Map<string, boolean>()

  /**
   * 检查API是否可用
   * @param url API路径
   * @returns 是否可用
   */
  static async checkApi(url: string): Promise<boolean> {
    // 如果已经检查过，直接返回结果
    if (this.checkedApis.has(url)) {
      return this.checkedApis.get(url)!
    }

    try {
      // 尝试发送GET请求检查API是否存在（因为有些API不支持HEAD方法）
      const response = await axiosInstance.get(url)
      const isAvailable = response.status < 400
      this.checkedApis.set(url, isAvailable)
      return isAvailable
    } catch (error: any) {
      // 如果是404或其他错误，说明API不可用
      const isAvailable = error.response?.status !== 404
      this.checkedApis.set(url, isAvailable)
      if (!isAvailable) {
        console.warn(`API ${url} 不可用:`, error.message)
      }
      return isAvailable
    }
  }

  /**
   * 清除API检查缓存
   */
  static clearCache() {
    this.checkedApis.clear()
  }

  /**
   * 获取所有已检查的API状态
   */
  static getCheckedApis() {
    return new Map(this.checkedApis)
  }
}

/**
 * API可用性常量
 */
export const API_ENDPOINTS = {
  // 用户管理 - 已实现
  USER_LIST: '/api/sysUser/page',
  USER_CREATE: '/api/sysUser',
  USER_UPDATE: '/api/sysUser',
  USER_DELETE: '/api/sysUser',

  // 角色管理 - 已实现
  ROLE_LIST: '/api/sysRole/page',
  ROLE_CREATE: '/api/sysRole',
  ROLE_UPDATE: '/api/sysRole',
  ROLE_DELETE: '/api/sysRole',

  // 菜单管理 - 已实现
  MENU_LIST: '/api/sysMenu/page',
  MENU_TREE: '/api/sysMenu/tree',
  MENU_CREATE: '/api/sysMenu',
  MENU_UPDATE: '/api/sysMenu',
  MENU_DELETE: '/api/sysMenu',

  // 权限管理 - 已实现
  PERMISSION_TREE: '/api/SysPermission/tree',
  PERMISSION_LIST: '/api/SysPermission/page',
  PERMISSION_TEMPLATES: '/api/SysPermission/templates',

  // 在线用户 - 已实现
  ONLINE_USERS: '/api/SysAuth/onlineUsers',
  ONLINE_STATS: '/api/SysAuth/onlineStats',
  KICK_USER: '/api/SysAuth/kickUser',

  // 系统日志 - 已实现
  LOG_LIST: '/api/SysLog/page',
  LOG_STATS: '/api/SysLog/stats',
  LOG_EXPORT: '/api/SysLog/export',
} as const

/**
 * 检查所有API的可用性
 */
export async function checkAllApis() {
  const results: Record<string, boolean> = {}
  
  for (const [key, url] of Object.entries(API_ENDPOINTS)) {
    results[key] = await ApiChecker.checkApi(url)
  }
  
  return results
}

/**
 * 获取可用的API列表
 */
export async function getAvailableApis() {
  const results = await checkAllApis()
  return Object.entries(results)
    .filter(([_, available]) => available)
    .map(([key, _]) => key)
}

/**
 * 获取不可用的API列表
 */
export async function getUnavailableApis() {
  const results = await checkAllApis()
  return Object.entries(results)
    .filter(([_, available]) => !available)
    .map(([key, _]) => key)
}
