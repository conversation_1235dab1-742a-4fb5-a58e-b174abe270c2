import { feature, LoadingManager, axiosInstance } from '../api-services/axios-utils'
import { AxiosResponse } from 'axios'

// 菜单树节点接口
export interface MenuTreeNode {
  id: number
  name: string
  code: string
  path?: string
  icon?: string
  parentId?: number
  menuType: number
  menuTypeDesc: string
  sort: number
  status: boolean
  hidden: boolean
  createTime?: string
  children: MenuTreeNode[]
}

// 权限API响应类型
export interface UserMenusResponse {
  data: MenuTreeNode[]
  succeeded: boolean
  errors?: string
  message?: string
}

export interface UserPermissionsResponse {
  data: string[]
  succeeded: boolean
  errors?: string
  message?: string
}

// 加载状态键
const LOADING_KEY = {
  USER_MENUS: 'permission.userMenus',
  USER_PERMISSIONS: 'permission.userPermissions',
}

/**
 * 权限API服务类
 */
class PermissionApiService {
  /**
   * 获取当前用户菜单树
   * @returns 用户菜单树
   */
  async getUserMenus(): Promise<AxiosResponse<MenuTreeNode[]>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.USER_MENUS, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysAuth/userMenus')
      )

      if (error) {
        console.error('获取用户菜单失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.USER_MENUS, false)
    }
  }

  /**
   * 获取当前用户权限代码列表
   * @returns 用户权限代码列表
   */
  async getUserPermissions(): Promise<AxiosResponse<string[]>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.USER_PERMISSIONS, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysAuth/userPermissions')
      )

      if (error) {
        console.error('获取用户权限失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.USER_PERMISSIONS, false)
    }
  }

  /**
   * 检查是否正在加载用户菜单
   * @returns 是否正在加载
   */
  isUserMenusLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.USER_MENUS)
  }

  /**
   * 检查是否正在加载用户权限
   * @returns 是否正在加载
   */
  isUserPermissionsLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.USER_PERMISSIONS)
  }

  /**
   * 获取指定API的加载状态
   * @param key 加载状态键
   * @returns 是否加载中
   */
  isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}

// 导出单例实例
export const permissionApi = new PermissionApiService()
