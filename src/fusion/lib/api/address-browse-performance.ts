/**
 * 地址浏览性能监控和错误处理增强
 */

export interface PerformanceMetrics {
  operationType: string
  deviceId: number
  startTime: number
  endTime: number
  duration: number
  success: boolean
  errorMessage?: string
  cacheHit?: boolean
  dataSize?: number
}

export interface ErrorInfo {
  operationType: string
  deviceId: number
  error: Error
  timestamp: number
  context?: any
}

export class AddressBrowsePerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private errors: ErrorInfo[] = []
  private maxMetricsHistory = 1000
  private maxErrorHistory = 100

  /**
   * 开始性能监控
   */
  startOperation(operationType: string, deviceId: number): PerformanceTracker {
    return new PerformanceTracker(operationType, deviceId, this)
  }

  /**
   * 记录性能指标
   */
  recordMetrics(metrics: PerformanceMetrics): void {
    this.metrics.push(metrics)
    
    // 保持历史记录在限制范围内
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory)
    }

    // 输出性能日志
    if (metrics.duration > 1000) { // 超过1秒的操作
      console.warn(`慢操作检测: ${metrics.operationType} 耗时 ${metrics.duration}ms`, metrics)
    }
  }

  /**
   * 记录错误信息
   */
  recordError(errorInfo: ErrorInfo): void {
    this.errors.push(errorInfo)
    
    // 保持错误历史记录在限制范围内
    if (this.errors.length > this.maxErrorHistory) {
      this.errors = this.errors.slice(-this.maxErrorHistory)
    }

    console.error(`地址浏览错误: ${errorInfo.operationType}`, errorInfo)
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): {
    totalOperations: number
    averageDuration: number
    successRate: number
    cacheHitRate: number
    slowOperations: number
  } {
    if (this.metrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        successRate: 0,
        cacheHitRate: 0,
        slowOperations: 0
      }
    }

    const totalOperations = this.metrics.length
    const successfulOperations = this.metrics.filter(m => m.success).length
    const cacheHits = this.metrics.filter(m => m.cacheHit).length
    const slowOperations = this.metrics.filter(m => m.duration > 1000).length
    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0)

    return {
      totalOperations,
      averageDuration: totalDuration / totalOperations,
      successRate: (successfulOperations / totalOperations) * 100,
      cacheHitRate: (cacheHits / totalOperations) * 100,
      slowOperations
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    totalErrors: number
    errorsByType: Record<string, number>
    recentErrors: ErrorInfo[]
  } {
    const errorsByType: Record<string, number> = {}
    
    this.errors.forEach(error => {
      errorsByType[error.operationType] = (errorsByType[error.operationType] || 0) + 1
    })

    return {
      totalErrors: this.errors.length,
      errorsByType,
      recentErrors: this.errors.slice(-10) // 最近10个错误
    }
  }

  /**
   * 清除历史数据
   */
  clearHistory(): void {
    this.metrics = []
    this.errors = []
  }

  /**
   * 导出性能报告
   */
  exportReport(): {
    performance: ReturnType<typeof this.getPerformanceStats>
    errors: ReturnType<typeof this.getErrorStats>
    timestamp: number
  } {
    return {
      performance: this.getPerformanceStats(),
      errors: this.getErrorStats(),
      timestamp: Date.now()
    }
  }
}

/**
 * 性能跟踪器
 */
export class PerformanceTracker {
  private startTime: number
  private operationType: string
  private deviceId: number
  private monitor: AddressBrowsePerformanceMonitor
  private cacheHit = false
  private dataSize?: number

  constructor(operationType: string, deviceId: number, monitor: AddressBrowsePerformanceMonitor) {
    this.operationType = operationType
    this.deviceId = deviceId
    this.monitor = monitor
    this.startTime = performance.now()
  }

  /**
   * 标记缓存命中
   */
  markCacheHit(): void {
    this.cacheHit = true
  }

  /**
   * 设置数据大小
   */
  setDataSize(size: number): void {
    this.dataSize = size
  }

  /**
   * 完成操作（成功）
   */
  complete(): void {
    const endTime = performance.now()
    const duration = endTime - this.startTime

    this.monitor.recordMetrics({
      operationType: this.operationType,
      deviceId: this.deviceId,
      startTime: this.startTime,
      endTime,
      duration,
      success: true,
      cacheHit: this.cacheHit,
      dataSize: this.dataSize
    })
  }

  /**
   * 完成操作（失败）
   */
  fail(error: Error): void {
    const endTime = performance.now()
    const duration = endTime - this.startTime

    this.monitor.recordMetrics({
      operationType: this.operationType,
      deviceId: this.deviceId,
      startTime: this.startTime,
      endTime,
      duration,
      success: false,
      errorMessage: error.message,
      cacheHit: this.cacheHit,
      dataSize: this.dataSize
    })

    this.monitor.recordError({
      operationType: this.operationType,
      deviceId: this.deviceId,
      error,
      timestamp: Date.now()
    })
  }
}

/**
 * 重试机制
 */
export class RetryManager {
  private maxRetries: number
  private baseDelay: number
  private maxDelay: number

  constructor(maxRetries = 3, baseDelay = 1000, maxDelay = 10000) {
    this.maxRetries = maxRetries
    this.baseDelay = baseDelay
    this.maxDelay = maxDelay
  }

  /**
   * 执行带重试的操作
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    shouldRetry: (error: any) => boolean = () => true
  ): Promise<T> {
    let lastError: any
    
    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        if (attempt === this.maxRetries || !shouldRetry(error)) {
          throw error
        }

        const delay = Math.min(
          this.baseDelay * Math.pow(2, attempt),
          this.maxDelay
        )

        console.warn(
          `${operationName} 失败，${delay}ms后重试 (${attempt + 1}/${this.maxRetries})`,
          error
        )

        await this.sleep(delay)
      }
    }

    throw lastError
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * 全局性能监控实例
 */
export const addressBrowsePerformanceMonitor = new AddressBrowsePerformanceMonitor()

/**
 * 全局重试管理器
 */
export const retryManager = new RetryManager()

/**
 * 错误分类器
 */
export class ErrorClassifier {
  /**
   * 判断是否为网络错误
   */
  static isNetworkError(error: any): boolean {
    return error?.code === 'NETWORK_ERROR' || 
           error?.message?.includes('Network Error') ||
           error?.message?.includes('timeout')
  }

  /**
   * 判断是否为认证错误
   */
  static isAuthError(error: any): boolean {
    return error?.status === 401 || 
           error?.status === 403 ||
           error?.message?.includes('Unauthorized')
  }

  /**
   * 判断是否为服务器错误
   */
  static isServerError(error: any): boolean {
    return error?.status >= 500 && error?.status < 600
  }

  /**
   * 判断是否应该重试
   */
  static shouldRetry(error: any): boolean {
    // 网络错误和服务器错误可以重试，认证错误不重试
    return this.isNetworkError(error) || this.isServerError(error)
  }

  /**
   * 获取用户友好的错误消息
   */
  static getUserFriendlyMessage(error: any): string {
    if (this.isNetworkError(error)) {
      return '网络连接失败，请检查网络连接'
    }
    
    if (this.isAuthError(error)) {
      return '认证失败，请重新登录'
    }
    
    if (this.isServerError(error)) {
      return '服务器暂时不可用，请稍后重试'
    }
    
    return error?.message || '操作失败，请重试'
  }
}
