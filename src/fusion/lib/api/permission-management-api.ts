import { feature, LoadingManager, axiosInstance } from '../api-services/axios-utils'
import { AxiosResponse } from 'axios'

// 权限数据类型
export interface SysPermission {
  id: number
  name: string
  code: string
  type: 'module' | 'function' | 'data' | 'api'
  description?: string
  parentId?: number
  children?: SysPermission[]
  enabled: boolean
  visible: boolean
  sort: number
  createTime: string
  updateTime?: string
}

// 权限模板类型
export interface PermissionTemplate {
  id: number
  name: string
  description: string
  permissions: number[]
  type: 'system' | 'custom'
  createTime: string
  updateTime?: string
}

// 权限查询参数
export interface PermissionQueryParams {
  page?: number
  pageSize?: number
  name?: string
  code?: string
  type?: string
  parentId?: number
  enabled?: boolean
}

// 权限创建/更新输入
export interface PermissionInput {
  name: string
  code: string
  type: 'module' | 'function' | 'data' | 'api'
  description?: string
  parentId?: number
  enabled?: boolean
  visible?: boolean
  sort?: number
}

// 权限模板输入
export interface PermissionTemplateInput {
  name: string
  description: string
  permissions: number[]
  type?: 'system' | 'custom'
}

// 分页响应类型
export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}

// 加载状态键
export const LOADING_KEY = {
  PERMISSION_LIST: 'permission-list',
  PERMISSION_CREATE: 'permission-create',
  PERMISSION_UPDATE: 'permission-update',
  PERMISSION_DELETE: 'permission-delete',
  TEMPLATE_LIST: 'template-list',
  TEMPLATE_CREATE: 'template-create',
  TEMPLATE_UPDATE: 'template-update',
  TEMPLATE_DELETE: 'template-delete',
} as const

/**
 * 权限管理API服务类
 */
class PermissionManagementApiService {
  /**
   * 获取权限列表（树形结构）
   * @param params 查询参数
   * @returns 权限树形数据
   */
  async getPermissionTree(params: PermissionQueryParams = {}): Promise<AxiosResponse<SysPermission[]>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_LIST, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysPermission/tree', { params })
      )

      if (error) {
        console.error('获取权限树失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_LIST, false)
    }
  }

  /**
   * 获取权限分页列表
   * @param params 查询参数
   * @returns 权限分页数据
   */
  async getPermissionList(params: PermissionQueryParams = {}): Promise<AxiosResponse<PagedResponse<SysPermission>>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_LIST, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysPermission/page', { params })
      )

      if (error) {
        console.error('获取权限列表失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_LIST, false)
    }
  }

  /**
   * 根据ID获取权限详情
   * @param id 权限ID
   * @returns 权限详情
   */
  async getPermissionById(id: number): Promise<AxiosResponse<SysPermission>> {
    const [error, response] = await feature(
      axiosInstance.get(`/api/sysPermission/${id}`)
    )

    if (error) {
      console.error('获取权限详情失败:', error)
      throw error
    }

    return response
  }

  /**
   * 创建新权限
   * @param permission 权限信息
   * @returns 创建结果
   */
  async createPermission(permission: PermissionInput): Promise<AxiosResponse<number>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_CREATE, true)
      const [error, response] = await feature(
        axiosInstance.post('/api/sysPermission', permission)
      )

      if (error) {
        console.error('创建权限失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_CREATE, false)
    }
  }

  /**
   * 更新权限
   * @param id 权限ID
   * @param permission 权限信息
   * @returns 更新结果
   */
  async updatePermission(id: number, permission: PermissionInput): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_UPDATE, true)
      const [error, response] = await feature(
        axiosInstance.put(`/api/sysPermission/${id}`, permission)
      )

      if (error) {
        console.error('更新权限失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_UPDATE, false)
    }
  }

  /**
   * 删除权限
   * @param id 权限ID
   * @returns 删除结果
   */
  async deletePermission(id: number): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_DELETE, true)
      const [error, response] = await feature(
        axiosInstance.delete(`/api/sysPermission/${id}`)
      )

      if (error) {
        console.error('删除权限失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.PERMISSION_DELETE, false)
    }
  }

  /**
   * 获取权限模板列表
   * @returns 权限模板列表
   */
  async getPermissionTemplates(): Promise<AxiosResponse<PermissionTemplate[]>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_LIST, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysPermission/templates')
      )

      if (error) {
        console.error('获取权限模板失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_LIST, false)
    }
  }

  /**
   * 创建权限模板
   * @param template 模板信息
   * @returns 创建结果
   */
  async createPermissionTemplate(template: PermissionTemplateInput): Promise<AxiosResponse<number>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_CREATE, true)
      const [error, response] = await feature(
        axiosInstance.post('/api/sysPermission/templates', template)
      )

      if (error) {
        console.error('创建权限模板失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_CREATE, false)
    }
  }

  /**
   * 更新权限模板
   * @param id 模板ID
   * @param template 模板信息
   * @returns 更新结果
   */
  async updatePermissionTemplate(id: number, template: PermissionTemplateInput): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_UPDATE, true)
      const [error, response] = await feature(
        axiosInstance.put(`/api/sysPermission/templates/${id}`, template)
      )

      if (error) {
        console.error('更新权限模板失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_UPDATE, false)
    }
  }

  /**
   * 删除权限模板
   * @param id 模板ID
   * @returns 删除结果
   */
  async deletePermissionTemplate(id: number): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_DELETE, true)
      const [error, response] = await feature(
        axiosInstance.delete(`/api/sysPermission/templates/${id}`)
      )

      if (error) {
        console.error('删除权限模板失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.TEMPLATE_DELETE, false)
    }
  }
}

// 导出单例实例
export const permissionManagementApi = new PermissionManagementApiService()
export default permissionManagementApi
