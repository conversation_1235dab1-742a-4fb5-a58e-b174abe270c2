/**
 * 模拟数据服务
 * 
 * 当后端API未实现时，提供模拟数据
 * 保持与真实API相同的数据结构，便于后续无缝切换
 */

import { SysPermission, PermissionTemplate } from './permission-management-api'
import { OnlineUser, OnlineUserStats } from './online-user-api'
import { SystemLog, LogStats } from './system-log-api'

/**
 * 模拟权限数据
 */
export const mockPermissions: SysPermission[] = [
  {
    id: 1,
    name: '仪表盘',
    code: 'dashboard',
    type: 'module',
    description: '仪表盘模块访问权限',
    enabled: true,
    visible: true,
    sort: 1,
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00',
    children: [
      {
        id: 11,
        name: '查看仪表盘',
        code: 'dashboard:view',
        type: 'function',
        description: '查看仪表盘数据',
        parentId: 1,
        enabled: true,
        visible: true,
        sort: 1,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
      },
      {
        id: 12,
        name: '导出数据',
        code: 'dashboard:export',
        type: 'function',
        description: '导出仪表盘数据',
        parentId: 1,
        enabled: true,
        visible: true,
        sort: 2,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
      },
    ],
  },
  {
    id: 2,
    name: '数据采集',
    code: 'data-collection',
    type: 'module',
    description: '数据采集模块权限',
    enabled: true,
    visible: true,
    sort: 2,
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00',
    children: [
      {
        id: 21,
        name: '设备管理',
        code: 'devices:manage',
        type: 'function',
        description: '设备增删改查权限',
        parentId: 2,
        enabled: true,
        visible: true,
        sort: 1,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
      },
      {
        id: 22,
        name: '设备数据',
        code: 'devices:data',
        type: 'data',
        description: '设备数据访问权限',
        parentId: 2,
        enabled: true,
        visible: true,
        sort: 2,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
      },
    ],
  },
  {
    id: 3,
    name: '系统管理',
    code: 'system',
    type: 'module',
    description: '系统管理模块权限',
    enabled: true,
    visible: true,
    sort: 10,
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00',
    children: [
      {
        id: 31,
        name: '用户管理',
        code: 'system:users',
        type: 'function',
        description: '用户管理权限',
        parentId: 3,
        enabled: true,
        visible: true,
        sort: 1,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
      },
      {
        id: 32,
        name: '角色管理',
        code: 'system:roles',
        type: 'function',
        description: '角色管理权限',
        parentId: 3,
        enabled: true,
        visible: true,
        sort: 2,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
      },
      {
        id: 33,
        name: '权限配置',
        code: 'system:permissions',
        type: 'function',
        description: '权限配置管理',
        parentId: 3,
        enabled: true,
        visible: true,
        sort: 3,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
      },
    ],
  },
]

/**
 * 模拟权限模板数据
 */
export const mockPermissionTemplates: PermissionTemplate[] = [
  {
    id: 1,
    name: '超级管理员',
    description: '拥有所有权限的超级管理员模板',
    permissions: [1, 11, 12, 2, 21, 22, 3, 31, 32, 33],
    type: 'system',
    createTime: '2024-01-01 00:00:00',
  },
  {
    id: 2,
    name: '普通用户',
    description: '基础功能权限模板',
    permissions: [1, 11, 2, 22],
    type: 'system',
    createTime: '2024-01-01 00:00:00',
  },
  {
    id: 3,
    name: '设备操作员',
    description: '设备管理相关权限模板',
    permissions: [1, 11, 2, 21, 22],
    type: 'custom',
    createTime: '2024-03-15 10:30:00',
  },
]

/**
 * 模拟在线用户数据
 */
export const mockOnlineUsers: OnlineUser[] = [
  {
    id: '1',
    userId: 1,
    username: 'admin',
    displayName: '系统管理员',
    role: '系统管理员',
    loginTime: '2025-07-30 22:42:36',
    lastActivity: '2025-07-30 23:15:22',
    ipAddress: '::1',
    location: '本地',
    device: 'desktop',
    browser: 'Chrome *********',
    os: 'Windows 11',
    sessionId: 'sess_admin_20250730_224236',
    status: 'active',
    connectionType: 'websocket',
    isOnline: true,
  },
  {
    id: '2',
    userId: 2,
    username: 'operator1',
    displayName: '操作员001',
    role: '操作员',
    loginTime: '2025-07-30 23:10:15',
    lastActivity: '2025-07-30 23:14:45',
    ipAddress: '*************',
    location: '上海',
    device: 'mobile',
    browser: 'Safari 17.0',
    os: 'iOS 17.1',
    sessionId: 'sess_op1_20250730_231015',
    status: 'active',
    connectionType: 'websocket',
    isOnline: true,
  },
  {
    id: '3',
    userId: 3,
    username: 'viewer1',
    displayName: '观察员001',
    role: '观察员',
    loginTime: '2025-07-30 22:55:30',
    lastActivity: '2025-07-30 23:05:12',
    ipAddress: '*************',
    location: '北京',
    device: 'tablet',
    browser: 'Edge *********',
    os: 'Android 14',
    sessionId: 'sess_view1_20250730_225530',
    status: 'idle',
    connectionType: 'http',
    isOnline: true,
  },
  {
    id: '4',
    userId: 4,
    username: 'operator2',
    displayName: '操作员002',
    role: '操作员',
    loginTime: '2025-07-30 22:30:45',
    lastActivity: '2025-07-30 22:58:33',
    ipAddress: '*************',
    location: '深圳',
    device: 'desktop',
    browser: 'Firefox 126.0',
    os: 'Ubuntu 22.04',
    sessionId: 'sess_op2_20250730_223045',
    status: 'away',
    connectionType: 'websocket',
    isOnline: true,
  },
]

/**
 * 模拟在线用户统计数据
 */
export const mockOnlineUserStats: OnlineUserStats = {
  totalOnline: 4,
  activeUsers: 2,
  idleUsers: 1,
  awayUsers: 1,
  websocketConnections: 3,
  httpConnections: 1,
  newLoginsToday: 8,
  averageSessionTime: '2小时15分钟',
}

/**
 * 模拟系统日志数据
 */
export const mockSystemLogs: SystemLog[] = [
  {
    id: '1',
    timestamp: '2025-07-30 23:15:22',
    level: 'info',
    category: '用户管理',
    action: '用户登录',
    message: '用户 admin 成功登录系统',
    userId: 1,
    username: 'admin',
    ipAddress: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    module: 'auth',
    status: 'success',
    duration: 245,
    createTime: '2025-07-30 23:15:22',
  },
  {
    id: '2',
    timestamp: '2025-07-30 23:14:55',
    level: 'success',
    category: '权限管理',
    action: '权限配置',
    message: '角色 "操作员" 权限配置已更新',
    userId: 1,
    username: 'admin',
    ipAddress: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    module: 'permissions',
    status: 'success',
    duration: 156,
    createTime: '2025-07-30 23:14:55',
    details: {
      roleId: 'role_operator',
      changedPermissions: ['设备配置'],
      operation: 'add',
    },
  },
  {
    id: '3',
    timestamp: '2025-07-30 23:12:18',
    level: 'warn',
    category: '系统安全',
    action: '登录失败',
    message: '用户尝试使用错误密码登录',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X)',
    module: 'auth',
    status: 'failed',
    duration: 89,
    createTime: '2025-07-30 23:12:18',
    details: {
      attemptedUsername: 'admin',
      failureReason: 'invalid_password',
      attemptCount: 3,
    },
  },
  {
    id: '4',
    timestamp: '2025-07-30 23:10:33',
    level: 'info',
    category: '数据采集',
    action: '设备连接',
    message: '设备 PLC-001 已连接',
    userId: 2,
    username: 'operator1',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X)',
    module: 'devices',
    status: 'success',
    duration: 1234,
    createTime: '2025-07-30 23:10:33',
    details: {
      deviceId: 'PLC-001',
      deviceType: 'PLC',
      connectionType: 'TCP',
    },
  },
  {
    id: '5',
    timestamp: '2025-07-30 23:08:45',
    level: 'error',
    category: '系统错误',
    action: 'API调用',
    message: '获取用户列表失败',
    userId: 1,
    username: 'admin',
    ipAddress: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    module: 'api',
    status: 'failed',
    duration: 5000,
    createTime: '2025-07-30 23:08:45',
    details: {
      endpoint: '/api/users',
      errorCode: 'TIMEOUT',
      errorMessage: 'Request timeout after 5000ms',
    },
  },
]

/**
 * 模拟日志统计数据
 */
export const mockLogStats: LogStats = {
  totalLogs: 1247,
  todayLogs: 156,
  errorLogs: 23,
  warningLogs: 45,
  successLogs: 89,
  infoLogs: 78,
  debugLogs: 12,
  uniqueUsers: 8,
  topActions: [
    { action: '用户登录', count: 45 },
    { action: '数据查询', count: 32 },
    { action: '设备连接', count: 28 },
    { action: '权限配置', count: 15 },
    { action: '系统配置', count: 12 },
  ],
  topModules: [
    { module: 'auth', count: 89 },
    { module: 'devices', count: 67 },
    { module: 'permissions', count: 34 },
    { module: 'api', count: 23 },
    { module: 'system', count: 18 },
  ],
  hourlyStats: [
    { hour: 0, count: 12 },
    { hour: 1, count: 8 },
    { hour: 2, count: 5 },
    { hour: 3, count: 3 },
    { hour: 4, count: 2 },
    { hour: 5, count: 4 },
    { hour: 6, count: 8 },
    { hour: 7, count: 15 },
    { hour: 8, count: 23 },
    { hour: 9, count: 34 },
    { hour: 10, count: 45 },
    { hour: 11, count: 38 },
    { hour: 12, count: 42 },
    { hour: 13, count: 39 },
    { hour: 14, count: 41 },
    { hour: 15, count: 37 },
    { hour: 16, count: 35 },
    { hour: 17, count: 28 },
    { hour: 18, count: 22 },
    { hour: 19, count: 18 },
    { hour: 20, count: 15 },
    { hour: 21, count: 12 },
    { hour: 22, count: 9 },
    { hour: 23, count: 6 },
  ],
}

/**
 * 模拟API延迟
 */
export const simulateApiDelay = (ms: number = 800): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}
