import { feature, LoadingManager, axiosInstance } from '../api-services/axios-utils'
import { AxiosResponse } from 'axios'

// 菜单数据类型
export interface SysMenu {
  id: number
  name: string
  code: string
  path?: string
  icon?: string
  parentId?: number
  level: number
  sort: number
  status: boolean
  hidden: boolean
  menuType: string
  createTime: string
  updateTime?: string
  children?: SysMenu[]
}

// 菜单查询参数
export interface MenuQueryParams {
  page?: number
  pageSize?: number
  name?: string
  code?: string
  menuType?: string
  status?: boolean
  parentId?: number
}

// 菜单创建/更新输入
export interface MenuInput {
  name: string
  code: string
  path?: string
  icon?: string
  parentId?: number
  sort?: number
  status?: boolean
  hidden?: boolean
  menuType: string
}

// 菜单树节点
export interface MenuTreeNode {
  id: number
  name: string
  code: string
  path?: string
  icon?: string
  parentId?: number
  level: number
  sort: number
  status: boolean
  hidden: boolean
  menuType: string
  children: MenuTreeNode[]
  createTime: string
}

// 分页响应类型
export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}

// API响应类型
export interface ApiResponse<T> {
  data: T
  succeeded: boolean
  errors?: string
  message?: string
}

// 加载状态键
const LOADING_KEY = {
  MENU_LIST: 'menuManagement.list',
  MENU_TREE: 'menuManagement.tree',
  MENU_CREATE: 'menuManagement.create',
  MENU_UPDATE: 'menuManagement.update',
  MENU_DELETE: 'menuManagement.delete',
  MENU_TOGGLE_STATUS: 'menuManagement.toggleStatus',
  MENU_TOGGLE_HIDDEN: 'menuManagement.toggleHidden',
}

/**
 * 菜单管理API服务类
 */
class MenuManagementApiService {
  /**
   * 获取菜单分页列表
   * @param params 查询参数
   * @returns 菜单分页数据
   */
  async getMenuList(params: MenuQueryParams = {}): Promise<AxiosResponse<PagedResponse<SysMenu>>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_LIST, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysMenu/page', { params })
      )

      if (error) {
        console.error('获取菜单列表失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_LIST, false)
    }
  }

  /**
   * 获取菜单树结构
   * @returns 菜单树
   */
  async getMenuTree(): Promise<AxiosResponse<MenuTreeNode[]>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_TREE, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysMenu/tree')
      )

      if (error) {
        console.error('获取菜单树失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_TREE, false)
    }
  }

  /**
   * 获取所有菜单（不分页）
   * @returns 所有菜单列表
   */
  async getAllMenus(): Promise<AxiosResponse<SysMenu[]>> {
    const [error, response] = await feature(
      axiosInstance.get('/api/sysMenu/tree')
    )

    if (error) {
      console.error('获取所有菜单失败:', error)
      throw error
    }

    return response
  }

  /**
   * 根据ID获取菜单详情
   * @param id 菜单ID
   * @returns 菜单详情
   */
  async getMenuById(id: number): Promise<AxiosResponse<SysMenu>> {
    const [error, response] = await feature(
      axiosInstance.get(`/api/sysMenu/${id}`)
    )

    if (error) {
      console.error('获取菜单详情失败:', error)
      throw error
    }

    return response
  }

  /**
   * 创建新菜单
   * @param menu 菜单信息
   * @returns 创建结果
   */
  async createMenu(menu: MenuInput): Promise<AxiosResponse<number>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_CREATE, true)
      const [error, response] = await feature(
        axiosInstance.post('/api/sysMenu', menu)
      )

      if (error) {
        console.error('创建菜单失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_CREATE, false)
    }
  }

  /**
   * 更新菜单信息
   * @param id 菜单ID
   * @param menu 菜单信息
   * @returns 更新结果
   */
  async updateMenu(id: number, menu: MenuInput): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_UPDATE, true)
      const [error, response] = await feature(
        axiosInstance.put(`/api/sysMenu/${id}`, menu)
      )

      if (error) {
        console.error('更新菜单失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_UPDATE, false)
    }
  }

  /**
   * 删除菜单
   * @param id 菜单ID
   * @returns 删除结果
   */
  async deleteMenu(id: number): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_DELETE, true)
      const [error, response] = await feature(
        axiosInstance.delete(`/api/sysMenu/${id}`)
      )

      if (error) {
        console.error('删除菜单失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_DELETE, false)
    }
  }

  /**
   * 切换菜单状态
   * @param id 菜单ID
   * @param status 新状态
   * @returns 更新结果
   */
  async toggleMenuStatus(id: number, status: boolean): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_TOGGLE_STATUS, true)
      const [error, response] = await feature(
        axiosInstance.patch(`/api/sysMenu/${id}/status`, { status })
      )

      if (error) {
        console.error('切换菜单状态失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_TOGGLE_STATUS, false)
    }
  }

  /**
   * 切换菜单可见性
   * @param id 菜单ID
   * @param hidden 是否隐藏
   * @returns 更新结果
   */
  async toggleMenuHidden(id: number, hidden: boolean): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_TOGGLE_HIDDEN, true)
      const [error, response] = await feature(
        axiosInstance.patch(`/api/sysMenu/${id}/hidden`, { hidden })
      )

      if (error) {
        console.error('切换菜单可见性失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_TOGGLE_HIDDEN, false)
    }
  }

  /**
   * 获取父级菜单选项（用于下拉选择）
   * @returns 父级菜单选项
   */
  async getParentMenuOptions(): Promise<AxiosResponse<Array<{ id: number; name: string; level: number }>>> {
    const [error, response] = await feature(
      axiosInstance.get('/api/sysMenu/parentOptions')
    )

    if (error) {
      console.error('获取父级菜单选项失败:', error)
      throw error
    }

    return response
  }

  /**
   * 检查是否正在加载菜单列表
   * @returns 是否正在加载
   */
  isMenuListLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.MENU_LIST)
  }

  /**
   * 检查是否正在加载菜单树
   * @returns 是否正在加载
   */
  isMenuTreeLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.MENU_TREE)
  }

  /**
   * 检查是否正在创建菜单
   * @returns 是否正在加载
   */
  isMenuCreateLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.MENU_CREATE)
  }

  /**
   * 检查是否正在更新菜单
   * @returns 是否正在加载
   */
  isMenuUpdateLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.MENU_UPDATE)
  }

  /**
   * 检查是否正在删除菜单
   * @returns 是否正在加载
   */
  isMenuDeleteLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.MENU_DELETE)
  }

  /**
   * 检查是否正在切换菜单状态
   * @returns 是否正在加载
   */
  isMenuToggleStatusLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.MENU_TOGGLE_STATUS)
  }

  /**
   * 检查是否正在切换菜单可见性
   * @returns 是否正在加载
   */
  isMenuToggleHiddenLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.MENU_TOGGLE_HIDDEN)
  }

  /**
   * 获取指定API的加载状态
   * @param key 加载状态键
   * @returns 是否加载中
   */
  isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}

// 导出单例实例
export const menuManagementApi = new MenuManagementApiService()
