import { DeviceApi } from '@/lib/api-services/apis/device-api'
import { Configuration } from '@/lib/api-services/configuration'
import { AddressBrowseConfig, BrowseResult, BrowsableNode, AddressTemplate, TemplateParameter } from './device-api'
import {
  addressBrowsePerformanceMonitor,
  retryManager,
  ErrorClassifier
} from './address-browse-performance'

/**
 * 地址浏览API适配器
 * 将前端的地址浏览API调用适配到现有的后端API
 */
export class AddressBrowseAdapter {
  private deviceApi: DeviceApi
  private cache: Map<string, any> = new Map()
  private cacheTimeout = 5 * 60 * 1000 // 5分钟缓存

  constructor(configuration?: Configuration) {
    this.deviceApi = new DeviceApi(configuration)
  }

  /**
   * 获取地址浏览配置
   */
  async getAddressBrowseConfig(deviceId: number): Promise<AddressBrowseConfig> {
    const tracker = addressBrowsePerformanceMonitor.startOperation('getAddressBrowseConfig', deviceId)

    const cacheKey = `config_${deviceId}`
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      tracker.markCacheHit()
      tracker.complete()
      return cached
    }

    try {
      const config = await retryManager.executeWithRetry(
        () => this.generateConfigForDevice(deviceId),
        'getAddressBrowseConfig',
        ErrorClassifier.shouldRetry
      )

      this.setCache(cacheKey, config)
      tracker.setDataSize(JSON.stringify(config).length)
      tracker.complete()
      return config
    } catch (error) {
      tracker.fail(error as Error)
      throw new Error(ErrorClassifier.getUserFriendlyMessage(error))
    }
  }

  /**
   * 获取根节点
   */
  async getRootNodes(deviceId: number): Promise<BrowseResult> {
    const tracker = addressBrowsePerformanceMonitor.startOperation('getRootNodes', deviceId)

    const cacheKey = `root_${deviceId}`
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      tracker.markCacheHit()
      tracker.complete()
      return cached
    }

    try {
      const result = await retryManager.executeWithRetry(
        async () => {
          // 这里应该调用后端的OPC UA浏览API
          // 暂时使用模拟数据
          const mockResult: BrowseResult = {
            success: true,
            nodes: [
              {
                nodeId: 'ns=0;i=85',
                displayName: 'Objects',
                description: 'Objects文件夹',
                nodeClass: 'Object',
                hasChildren: true,
                canRead: false,
                canWrite: false
              },
              {
                nodeId: 'ns=0;i=86',
                displayName: 'Types',
                description: 'Types文件夹',
                nodeClass: 'ObjectType',
                hasChildren: true,
                canRead: false,
                canWrite: false
              },
              {
                nodeId: 'ns=0;i=87',
                displayName: 'Views',
                description: 'Views文件夹',
                nodeClass: 'View',
                hasChildren: true,
                canRead: false,
                canWrite: false
              }
            ],
            hasMore: false,
            totalCount: 3
          }
          return mockResult
        },
        'getRootNodes',
        ErrorClassifier.shouldRetry
      )

      this.setCache(cacheKey, result)
      tracker.setDataSize(result.nodes.length)
      tracker.complete()
      return result
    } catch (error) {
      tracker.fail(error as Error)
      return {
        success: false,
        errorMessage: ErrorClassifier.getUserFriendlyMessage(error),
        nodes: [],
        hasMore: false,
        totalCount: 0
      }
    }
  }

  /**
   * 获取子节点
   */
  async getChildNodes(deviceId: number, parentNodeId: string): Promise<BrowseResult> {
    const cacheKey = `children_${deviceId}_${parentNodeId}`
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      return cached
    }

    try {
      // 这里应该调用后端的OPC UA浏览API
      // 暂时使用模拟数据
      const result = await this.generateChildNodes(parentNodeId)
      
      this.setCache(cacheKey, result)
      return result
    } catch (error) {
      console.error('获取子节点失败:', error)
      return {
        success: false,
        errorMessage: error instanceof Error ? error.message : '获取子节点失败',
        nodes: [],
        hasMore: false,
        totalCount: 0
      }
    }
  }

  /**
   * 搜索节点
   */
  async searchNodes(deviceId: number, searchText: string, maxResults: number = 100): Promise<BrowseResult> {
    try {
      // 这里应该调用后端的搜索API
      // 暂时使用模拟数据
      const allNodes = await this.getAllNodesForSearch(deviceId)
      const filteredNodes = allNodes.filter(node => 
        node.displayName.toLowerCase().includes(searchText.toLowerCase()) ||
        node.nodeId.toLowerCase().includes(searchText.toLowerCase())
      ).slice(0, maxResults)

      return {
        success: true,
        nodes: filteredNodes,
        hasMore: filteredNodes.length >= maxResults,
        totalCount: filteredNodes.length
      }
    } catch (error) {
      console.error('搜索节点失败:', error)
      return {
        success: false,
        errorMessage: error instanceof Error ? error.message : '搜索节点失败',
        nodes: [],
        hasMore: false,
        totalCount: 0
      }
    }
  }

  /**
   * 验证节点
   */
  async validateNode(deviceId: number, nodeId: string): Promise<boolean> {
    try {
      // 这里应该调用后端的验证API
      // 暂时使用简单的格式验证
      const nodeIdPattern = /^(ns=\d+;)?[isgb]=.+$/
      return nodeIdPattern.test(nodeId)
    } catch (error) {
      console.error('验证节点失败:', error)
      return false
    }
  }

  /**
   * 根据设备生成配置
   */
  private async generateConfigForDevice(deviceId: number): Promise<AddressBrowseConfig> {
    // 这里应该根据设备的驱动类型返回不同的配置
    // 暂时假设都是OPC UA设备
    return {
      inputType: 'Helper',
      supportsDynamicBrowsing: true,
      requiresConnection: true,
      formatDescription: 'OPC UA NodeId格式：ns=命名空间索引;标识符类型=标识符值',
      validationPattern: '^(ns=\\d+;)?[isgb]=.+$',
      addressTemplates: this.getDefaultAddressTemplates(),
      searchable: true,
      maxSearchResults: 100,
      maxBrowseDepth: 10
    }
  }

  /**
   * 获取默认地址模板
   */
  private getDefaultAddressTemplates(): AddressTemplate[] {
    return [
      {
        category: '数值型NodeId',
        pattern: 'ns={namespace};i={identifier}',
        example: 'ns=2;i=1001',
        description: '使用命名空间索引和数值标识符',
        parameters: [
          { name: 'namespace', description: '命名空间索引', type: 'number', defaultValue: '2', required: true },
          { name: 'identifier', description: '数值标识符', type: 'number', defaultValue: '1001', required: true }
        ]
      },
      {
        category: '字符串型NodeId',
        pattern: 'ns={namespace};s={identifier}',
        example: 'ns=2;s=Temperature',
        description: '使用命名空间索引和字符串标识符',
        parameters: [
          { name: 'namespace', description: '命名空间索引', type: 'number', defaultValue: '2', required: true },
          { name: 'identifier', description: '字符串标识符', type: 'string', defaultValue: 'Temperature', required: true }
        ]
      },
      {
        category: 'GUID型NodeId',
        pattern: 'ns={namespace};g={identifier}',
        example: 'ns=2;g=09087e75-8e5e-499b-954f-f2a9603db28a',
        description: '使用命名空间索引和GUID标识符',
        parameters: [
          { name: 'namespace', description: '命名空间索引', type: 'number', defaultValue: '2', required: true },
          { name: 'identifier', description: 'GUID标识符', type: 'string', defaultValue: '09087e75-8e5e-499b-954f-f2a9603db28a', required: true }
        ]
      },
      {
        category: '简化数值型',
        pattern: 'i={identifier}',
        example: 'i=2258',
        description: '使用默认命名空间的数值标识符',
        parameters: [
          { name: 'identifier', description: '数值标识符', type: 'number', defaultValue: '2258', required: true }
        ]
      },
      {
        category: '简化字符串型',
        pattern: 's={identifier}',
        example: 's=MyVariable',
        description: '使用默认命名空间的字符串标识符',
        parameters: [
          { name: 'identifier', description: '字符串标识符', type: 'string', defaultValue: 'MyVariable', required: true }
        ]
      }
    ]
  }

  /**
   * 生成子节点（模拟数据）
   */
  private async generateChildNodes(parentNodeId: string): Promise<BrowseResult> {
    const childrenMap: Record<string, BrowsableNode[]> = {
      'ns=0;i=85': [ // Objects
        {
          nodeId: 'ns=0;i=2253',
          displayName: 'Server',
          description: '服务器对象',
          nodeClass: 'Object',
          hasChildren: true,
          canRead: false,
          canWrite: false
        },
        {
          nodeId: 'ns=2;s=Device1',
          displayName: 'Device1',
          description: '设备1',
          nodeClass: 'Object',
          hasChildren: true,
          canRead: false,
          canWrite: false
        }
      ],
      'ns=0;i=2253': [ // Server
        {
          nodeId: 'i=2258',
          displayName: 'ServerStatus',
          description: '服务器状态',
          nodeClass: 'Variable',
          hasChildren: false,
          canRead: true,
          canWrite: false,
          dataType: 'ServerStatusDataType'
        }
      ],
      'ns=2;s=Device1': [ // Device1
        {
          nodeId: 'ns=2;s=Device1.Temperature',
          displayName: 'Temperature',
          description: '温度传感器',
          nodeClass: 'Variable',
          hasChildren: false,
          canRead: true,
          canWrite: false,
          dataType: 'Float',
          value: 25.6
        },
        {
          nodeId: 'ns=2;s=Device1.Pressure',
          displayName: 'Pressure',
          description: '压力传感器',
          nodeClass: 'Variable',
          hasChildren: false,
          canRead: true,
          canWrite: false,
          dataType: 'Float',
          value: 1013.25
        }
      ]
    }

    const children = childrenMap[parentNodeId] || []
    return {
      success: true,
      nodes: children,
      hasMore: false,
      totalCount: children.length
    }
  }

  /**
   * 获取所有节点用于搜索（模拟数据）
   */
  private async getAllNodesForSearch(deviceId: number): Promise<BrowsableNode[]> {
    return [
      {
        nodeId: 'ns=2;s=Temperature',
        displayName: 'Temperature',
        description: '温度传感器',
        nodeClass: 'Variable',
        hasChildren: false,
        canRead: true,
        canWrite: false,
        dataType: 'Float',
        nodePath: 'Objects/Device1/Temperature'
      },
      {
        nodeId: 'ns=2;s=Pressure',
        displayName: 'Pressure',
        description: '压力传感器',
        nodeClass: 'Variable',
        hasChildren: false,
        canRead: true,
        canWrite: false,
        dataType: 'Float',
        nodePath: 'Objects/Device1/Pressure'
      },
      {
        nodeId: 'ns=2;s=TemperatureAlarm',
        displayName: 'TemperatureAlarm',
        description: '温度报警',
        nodeClass: 'Variable',
        hasChildren: false,
        canRead: true,
        canWrite: true,
        dataType: 'Boolean',
        nodePath: 'Objects/Alarms/TemperatureAlarm'
      }
    ]
  }

  /**
   * 缓存操作
   */
  private getFromCache(key: string): any {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }
    this.cache.delete(key)
    return null
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
  }
}
