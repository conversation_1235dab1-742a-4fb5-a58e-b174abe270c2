import { feature, LoadingManager, axiosInstance } from '../api-services/axios-utils'
import { AxiosResponse } from 'axios'

// 用户数据类型
export interface SysUser {
  id: number
  account: string
  name: string
  password?: string
  accountType: string
  status: boolean
  lastLoginTime?: string
  lastLoginIp?: string
  createTime: string
  updateTime?: string
}

// 用户查询参数
export interface UserQueryParams {
  page?: number
  pageSize?: number
  account?: string
  name?: string
  accountType?: string
  status?: boolean
}

// 用户创建/更新输入
export interface UserInput {
  account: string
  name: string
  password?: string
  accountType: string
  status?: boolean
}

// 分页响应类型
export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}

// API响应类型
export interface ApiResponse<T> {
  data: T
  succeeded: boolean
  errors?: string
  message?: string
}

// 加载状态键
const LOADING_KEY = {
  USER_LIST: 'userManagement.list',
  USER_CREATE: 'userManagement.create',
  USER_UPDATE: 'userManagement.update',
  USER_DELETE: 'userManagement.delete',
  USER_TOGGLE_STATUS: 'userManagement.toggleStatus',
}

/**
 * 用户管理API服务类
 */
class UserManagementApiService {
  /**
   * 获取用户分页列表
   * @param params 查询参数
   * @returns 用户分页数据
   */
  async getUserList(params: UserQueryParams = {}): Promise<AxiosResponse<PagedResponse<SysUser>>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.USER_LIST, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysUser/page', { params })
      )

      if (error) {
        console.error('获取用户列表失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.USER_LIST, false)
    }
  }

  /**
   * 根据ID获取用户详情
   * @param id 用户ID
   * @returns 用户详情
   */
  async getUserById(id: number): Promise<AxiosResponse<SysUser>> {
    const [error, response] = await feature(
      axiosInstance.get(`/api/sysUser/${id}`)
    )

    if (error) {
      console.error('获取用户详情失败:', error)
      throw error
    }

    return response
  }

  /**
   * 创建新用户
   * @param user 用户信息
   * @returns 创建结果
   */
  async createUser(user: UserInput): Promise<AxiosResponse<number>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.USER_CREATE, true)
      const [error, response] = await feature(
        axiosInstance.post('/api/sysUser', user)
      )

      if (error) {
        console.error('创建用户失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.USER_CREATE, false)
    }
  }

  /**
   * 更新用户信息
   * @param id 用户ID
   * @param user 用户信息
   * @returns 更新结果
   */
  async updateUser(id: number, user: UserInput): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.USER_UPDATE, true)
      const [error, response] = await feature(
        axiosInstance.put(`/api/sysUser/${id}`, user)
      )

      if (error) {
        console.error('更新用户失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.USER_UPDATE, false)
    }
  }

  /**
   * 删除用户
   * @param id 用户ID
   * @returns 删除结果
   */
  async deleteUser(id: number): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.USER_DELETE, true)
      const [error, response] = await feature(
        axiosInstance.delete(`/api/sysUser/${id}`)
      )

      if (error) {
        console.error('删除用户失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.USER_DELETE, false)
    }
  }

  /**
   * 切换用户状态
   * @param id 用户ID
   * @param status 新状态
   * @returns 更新结果
   */
  async toggleUserStatus(id: number, status: boolean): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.USER_TOGGLE_STATUS, true)
      const [error, response] = await feature(
        axiosInstance.patch(`/api/sysUser/${id}/status`, { status })
      )

      if (error) {
        console.error('切换用户状态失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.USER_TOGGLE_STATUS, false)
    }
  }

  /**
   * 重置用户密码
   * @param id 用户ID
   * @param newPassword 新密码
   * @returns 重置结果
   */
  async resetUserPassword(id: number, newPassword: string): Promise<AxiosResponse<boolean>> {
    const [error, response] = await feature(
      axiosInstance.patch(`/api/sysUser/${id}/password`, { password: newPassword })
    )

    if (error) {
      console.error('重置用户密码失败:', error)
      throw error
    }

    return response
  }

  /**
   * 检查是否正在加载用户列表
   * @returns 是否正在加载
   */
  isUserListLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.USER_LIST)
  }

  /**
   * 检查是否正在创建用户
   * @returns 是否正在加载
   */
  isUserCreateLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.USER_CREATE)
  }

  /**
   * 检查是否正在更新用户
   * @returns 是否正在加载
   */
  isUserUpdateLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.USER_UPDATE)
  }

  /**
   * 检查是否正在删除用户
   * @returns 是否正在加载
   */
  isUserDeleteLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.USER_DELETE)
  }

  /**
   * 检查是否正在切换用户状态
   * @returns 是否正在加载
   */
  isUserToggleStatusLoading(): boolean {
    return LoadingManager.isLoading(LOADING_KEY.USER_TOGGLE_STATUS)
  }

  /**
   * 获取指定API的加载状态
   * @param key 加载状态键
   * @returns 是否加载中
   */
  isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}

// 导出单例实例
export const userManagementApi = new UserManagementApiService()
