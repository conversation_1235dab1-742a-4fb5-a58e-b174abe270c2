import { feature, LoadingManager, axiosInstance } from '../api-services/axios-utils'
import { AxiosResponse } from 'axios'

// 系统日志数据类型
export interface SystemLog {
  id: string
  timestamp: string
  level: 'info' | 'warn' | 'error' | 'debug' | 'success'
  category: string
  action: string
  message: string
  userId?: number
  username?: string
  ipAddress: string
  userAgent?: string
  module: string
  details?: Record<string, any>
  duration?: number
  status: 'success' | 'failed' | 'pending'
  createTime: string
}

// 日志统计类型
export interface LogStats {
  totalLogs: number
  todayLogs: number
  errorLogs: number
  warningLogs: number
  successLogs: number
  infoLogs: number
  debugLogs: number
  uniqueUsers: number
  topActions: Array<{ action: string; count: number }>
  topModules: Array<{ module: string; count: number }>
  hourlyStats: Array<{ hour: number; count: number }>
}

// 日志查询参数
export interface LogQueryParams {
  page?: number
  pageSize?: number
  level?: 'info' | 'warn' | 'error' | 'debug' | 'success'
  category?: string
  action?: string
  module?: string
  userId?: number
  username?: string
  ipAddress?: string
  status?: 'success' | 'failed' | 'pending'
  startTime?: string
  endTime?: string
  keyword?: string
}

// 日志导出参数
export interface LogExportParams {
  format: 'csv' | 'excel' | 'json'
  level?: string
  category?: string
  startTime?: string
  endTime?: string
  maxRecords?: number
}

// 分页响应类型
export interface PagedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}

// 加载状态键
export const LOADING_KEY = {
  LOG_LIST: 'log-list',
  LOG_STATS: 'log-stats',
  LOG_EXPORT: 'log-export',
  LOG_DETAIL: 'log-detail',
  LOG_DELETE: 'log-delete',
  LOG_CLEAR: 'log-clear',
} as const

/**
 * 系统日志管理API服务类
 */
class SystemLogApiService {
  /**
   * 获取系统日志列表
   * @param params 查询参数
   * @returns 日志分页数据
   */
  async getLogList(params: LogQueryParams = {}): Promise<AxiosResponse<PagedResponse<SystemLog>>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOG_LIST, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysLog/page', { params })
      )

      if (error) {
        console.error('获取系统日志列表失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOG_LIST, false)
    }
  }

  /**
   * 获取日志统计信息
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 日志统计数据
   */
  async getLogStats(startTime?: string, endTime?: string): Promise<AxiosResponse<LogStats>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOG_STATS, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysLog/stats', { 
          params: { startTime, endTime } 
        })
      )

      if (error) {
        console.error('获取日志统计失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOG_STATS, false)
    }
  }

  /**
   * 根据ID获取日志详情
   * @param id 日志ID
   * @returns 日志详情
   */
  async getLogDetail(id: string): Promise<AxiosResponse<SystemLog>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOG_DETAIL, true)
      const [error, response] = await feature(
        axiosInstance.get(`/api/sysLog/${id}`)
      )

      if (error) {
        console.error('获取日志详情失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOG_DETAIL, false)
    }
  }

  /**
   * 导出日志
   * @param params 导出参数
   * @returns 导出文件流
   */
  async exportLogs(params: LogExportParams): Promise<AxiosResponse<Blob>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOG_EXPORT, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysLog/export', { 
          params,
          responseType: 'blob'
        })
      )

      if (error) {
        console.error('导出日志失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOG_EXPORT, false)
    }
  }

  /**
   * 删除日志
   * @param id 日志ID
   * @returns 删除结果
   */
  async deleteLog(id: string): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOG_DELETE, true)
      const [error, response] = await feature(
        axiosInstance.delete(`/api/sysLog/${id}`)
      )

      if (error) {
        console.error('删除日志失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOG_DELETE, false)
    }
  }

  /**
   * 批量删除日志
   * @param ids 日志ID列表
   * @returns 删除结果
   */
  async deleteLogs(ids: string[]): Promise<AxiosResponse<boolean>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOG_DELETE, true)
      const [error, response] = await feature(
        axiosInstance.post('/api/sysLog/batchDelete', { ids })
      )

      if (error) {
        console.error('批量删除日志失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOG_DELETE, false)
    }
  }

  /**
   * 清理日志
   * @param beforeDate 清理此日期之前的日志
   * @param level 日志级别，可选
   * @returns 清理结果
   */
  async clearLogs(beforeDate: string, level?: string): Promise<AxiosResponse<{ deletedCount: number }>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOG_CLEAR, true)
      const [error, response] = await feature(
        axiosInstance.post('/api/sysLog/clear', { beforeDate, level })
      )

      if (error) {
        console.error('清理日志失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOG_CLEAR, false)
    }
  }

  /**
   * 获取日志分类列表
   * @returns 分类列表
   */
  async getLogCategories(): Promise<AxiosResponse<string[]>> {
    const [error, response] = await feature(
      axiosInstance.get('/api/sysLog/categories')
    )

    if (error) {
      console.error('获取日志分类失败:', error)
      throw error
    }

    return response
  }

  /**
   * 获取日志模块列表
   * @returns 模块列表
   */
  async getLogModules(): Promise<AxiosResponse<string[]>> {
    const [error, response] = await feature(
      axiosInstance.get('/api/sysLog/modules')
    )

    if (error) {
      console.error('获取日志模块失败:', error)
      throw error
    }

    return response
  }

  /**
   * 获取日志操作列表
   * @returns 操作列表
   */
  async getLogActions(): Promise<AxiosResponse<string[]>> {
    const [error, response] = await feature(
      axiosInstance.get('/api/sysLog/actions')
    )

    if (error) {
      console.error('获取日志操作失败:', error)
      throw error
    }

    return response
  }

  /**
   * 获取实时日志流
   * @param level 日志级别过滤
   * @param module 模块过滤
   * @returns EventSource实例
   */
  createLogStream(level?: string, module?: string): EventSource {
    const params = new URLSearchParams()
    if (level) params.append('level', level)
    if (module) params.append('module', module)
    
    const url = `/api/sysLog/stream?${params.toString()}`
    return new EventSource(url)
  }

  /**
   * 搜索日志
   * @param keyword 关键词
   * @param params 其他查询参数
   * @returns 搜索结果
   */
  async searchLogs(keyword: string, params: Omit<LogQueryParams, 'keyword'> = {}): Promise<AxiosResponse<PagedResponse<SystemLog>>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOG_LIST, true)
      const [error, response] = await feature(
        axiosInstance.get('/api/sysLog/search', { 
          params: { ...params, keyword } 
        })
      )

      if (error) {
        console.error('搜索日志失败:', error)
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOG_LIST, false)
    }
  }

  /**
   * 获取用户操作日志
   * @param userId 用户ID
   * @param params 查询参数
   * @returns 用户操作日志
   */
  async getUserLogs(userId: number, params: Omit<LogQueryParams, 'userId'> = {}): Promise<AxiosResponse<PagedResponse<SystemLog>>> {
    const [error, response] = await feature(
      axiosInstance.get(`/api/sysLog/user/${userId}`, { params })
    )

    if (error) {
      console.error('获取用户日志失败:', error)
      throw error
    }

    return response
  }
}

// 导出单例实例
export const systemLogApi = new SystemLogApiService()
export default systemLogApi
