/**
 * WebSocket测试工具UI组件
 *
 * 提供WebSocket连接、消息发送和接收功能的用户界面
 * 支持多种数据格式和连接状态管理
 */

import {
  useWebSocketTester,
  type WebSocketMessage,
} from '@/hooks/use-websocket-tester'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import {
  Play,
  Square,
  Send,
  Trash,
  Download,
  ArrowDown,
  ArrowUp,
  Activity,
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { cn } from '@/lib/utils'

export function WebSocketTester() {
  const {
    // 连接设置
    url,
    setUrl,
    protocols,
    setProtocols,
    autoReconnect,
    setAutoReconnect,
    reconnectInterval,
    setReconnectInterval,
    reconnectAttempts,
    setReconnectAttempts,

    // 连接状态
    isConnected,
    connectionStatus,
    connectionError,

    // 消息设置
    messageToSend,
    setMessageToSend,
    messageFormat,
    setMessageFormat,

    // 消息历史
    messages,

    // 方法
    connect,
    disconnect,
    sendMessage,
    clearMessages,
    exportMessages,
    formatMessage,
  } = useWebSocketTester()

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* 连接设置卡片 */}
      <Card>
        <CardHeader className="py-4">
          <CardTitle>WebSocket连接设置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="url">WebSocket URL</Label>
            <Input
              id="url"
              placeholder="例如: ws://echo.websocket.org"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              disabled={isConnected}
              className="h-10"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="protocols">协议 (可选，用逗号分隔)</Label>
            <Input
              id="protocols"
              placeholder="例如: v1.protocol.example, v2.protocol.example"
              value={protocols}
              onChange={(e) => setProtocols(e.target.value)}
              disabled={isConnected}
              className="h-10"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="autoReconnect"
                checked={autoReconnect}
                onCheckedChange={setAutoReconnect}
                disabled={isConnected}
              />
              <Label htmlFor="autoReconnect">自动重连</Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="reconnectInterval">重连间隔 (毫秒)</Label>
              <Input
                id="reconnectInterval"
                type="number"
                min="100"
                step="100"
                value={reconnectInterval}
                onChange={(e) => setReconnectInterval(Number(e.target.value))}
                disabled={isConnected || !autoReconnect}
                className="h-10"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reconnectAttempts">最大重连次数</Label>
              <Input
                id="reconnectAttempts"
                type="number"
                min="1"
                step="1"
                value={reconnectAttempts}
                onChange={(e) => setReconnectAttempts(Number(e.target.value))}
                disabled={isConnected || !autoReconnect}
                className="h-10"
              />
            </div>
          </div>
        </CardContent>
        <div className="p-4 border-t flex justify-between items-center">
          <div>
            {!isConnected ? (
              <Button
                size="lg"
                onClick={connect}
                disabled={!url}
                className="h-10">
                <Play className="mr-2 h-4 w-4" />
                连接
              </Button>
            ) : (
              <Button
                variant="destructive"
                size="lg"
                onClick={disconnect}
                className="h-10">
                <Square className="mr-2 h-4 w-4" />
                断开
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Activity
              className={cn(
                'h-5 w-5',
                isConnected ? 'text-green-500' : 'text-muted-foreground'
              )}
            />
            <span className="text-sm">{connectionStatus}</span>
          </div>
        </div>
      </Card>

      {/* 错误提示 */}
      {connectionError && (
        <Alert variant="destructive">
          <AlertDescription>{connectionError}</AlertDescription>
        </Alert>
      )}

      {/* 主要功能区 */}
      <Tabs defaultValue="send" className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="send">发送消息</TabsTrigger>
          <TabsTrigger value="messages">消息历史</TabsTrigger>
        </TabsList>

        {/* 发送消息选项卡 */}
        <TabsContent value="send" className="space-y-4 mt-2">
          <Card>
            <CardContent className="pt-6 space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="messageToSend">消息内容</Label>
                  <Select
                    value={messageFormat}
                    onValueChange={(value) =>
                      setMessageFormat(value as 'text' | 'json' | 'binary')
                    }
                    disabled={!isConnected}>
                    <SelectTrigger className="w-32 h-8">
                      <SelectValue placeholder="选择格式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">文本</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                      <SelectItem value="binary">二进制</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Textarea
                  id="messageToSend"
                  placeholder="输入要发送的消息内容..."
                  value={messageToSend}
                  onChange={(e) => setMessageToSend(e.target.value)}
                  disabled={!isConnected}
                  className="min-h-[200px] font-mono"
                />
              </div>

              <Button
                className="w-full"
                onClick={sendMessage}
                disabled={!isConnected || !messageToSend}>
                <Send className="mr-2 h-4 w-4" />
                发送消息
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 消息历史选项卡 */}
        <TabsContent value="messages" className="space-y-4 mt-2">
          <Card>
            <CardHeader className="py-4 flex flex-row items-center justify-between">
              <CardTitle>消息历史</CardTitle>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportMessages}
                  disabled={messages.length === 0}>
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearMessages}
                  disabled={messages.length === 0}>
                  <Trash className="h-4 w-4 mr-2" />
                  清空
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {messages.length === 0 ? (
                <div className="text-center text-muted-foreground py-4">
                  暂无消息
                </div>
              ) : (
                <ScrollArea className="h-[400px]">
                  <div className="space-y-3">
                    {messages.map((message) => (
                      <Card key={message.id} className="overflow-hidden">
                        <div
                          className={cn(
                            'px-4 py-2',
                            message.direction === 'send'
                              ? 'bg-blue-50 dark:bg-blue-950'
                              : 'bg-green-50 dark:bg-green-950'
                          )}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              {message.direction === 'send' ? (
                                <ArrowUp className="h-4 w-4 mr-2 text-blue-500" />
                              ) : (
                                <ArrowDown className="h-4 w-4 mr-2 text-green-500" />
                              )}
                              <span className="font-medium">
                                {message.direction === 'send'
                                  ? '已发送'
                                  : '已接收'}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline">
                                {message.format === 'text'
                                  ? '文本'
                                  : message.format === 'json'
                                  ? 'JSON'
                                  : '二进制'}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {formatDistanceToNow(message.timestamp, {
                                  addSuffix: true,
                                })}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="p-4">
                          <pre className="whitespace-pre-wrap text-sm font-mono bg-muted p-2 rounded-md">
                            {message.format === 'json'
                              ? formatMessage(message.data, 'json')
                              : message.data}
                          </pre>
                        </div>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
