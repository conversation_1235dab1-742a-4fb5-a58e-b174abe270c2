import { JsonFormatter } from './json-formatter'
import { TimestampConverter } from './timestamp-converter'
import { HttpTester } from './http-tester'
import { MqttClient } from './mqtt-client'
import { CommandTerminal } from './command-terminal'

export type ToolType =
  | 'json-formatter'
  | 'timestamp-converter'
  | 'http-test'
  | 'mqtt-test'
  | 'command-terminal'
  | 'tcp-test'
  | 'websocket-test'
  | 'ping-test'
  | 'port-scan'
  | 'screenshot'
  | 'calculator'
  | 'data-converter'
  | 'data-finder'

interface ToolManagerProps {
  activeTool: ToolType | null
  onClose: () => void
}

export function ToolManager({ activeTool, onClose }: ToolManagerProps) {
  // 根据activeTool渲染对应的工具组件
  return (
    <>
      <JsonFormatter open={activeTool === 'json-formatter'} onClose={onClose} />

      <TimestampConverter
        open={activeTool === 'timestamp-converter'}
        onClose={onClose}
      />

      <HttpTester open={activeTool === 'http-test'} onClose={onClose} />

      <MqttClient open={activeTool === 'mqtt-test'} onClose={onClose} />

      <CommandTerminal
        open={activeTool === 'command-terminal'}
        onClose={onClose}
      />

      {/* 其他工具组件可以在这里添加 */}
    </>
  )
}
