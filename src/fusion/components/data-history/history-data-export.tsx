import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Download,
  Loader2,
  FileSpreadsheet,
  FileJson,
  FileText,
} from 'lucide-react'
import { format } from 'date-fns'
import type { HistoryQueryCriteria } from './types'

interface HistoryDataExportProps {
  data: any[]
  columns: string[]
  criteria: HistoryQueryCriteria | null
}

export function HistoryDataExport({
  data,
  columns,
  criteria,
}: HistoryDataExportProps) {
  const [isExporting, setIsExporting] = useState(false)

  // 导出为CSV
  const exportToCsv = () => {
    setIsExporting(true)

    try {
      // 创建CSV内容
      let csvContent = columns.join(',') + '\n'

      data.forEach((row) => {
        const rowValues = columns.map((column) => {
          const value = row[column]

          // 处理特殊情况
          if (value === undefined || value === null) return ''
          if (column === 'timestamp')
            return format(new Date(value), 'yyyy-MM-dd HH:mm:ss')
          if (typeof value === 'string' && value.includes(','))
            return `"${value}"`

          return value
        })

        csvContent += rowValues.join(',') + '\n'
      })

      // 创建Blob并下载
      downloadFile(
        csvContent,
        `历史数据_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`,
        'text/csv;charset=utf-8;'
      )
    } catch (error) {
      console.error('导出CSV失败:', error)
    } finally {
      setIsExporting(false)
    }
  }

  // 导出为Excel (实际上是CSV，但用Excel打开)
  const exportToExcel = () => {
    setIsExporting(true)

    try {
      // 创建CSV内容，添加BOM以便Excel正确识别中文
      let csvContent = '\uFEFF' + columns.join(',') + '\n'

      data.forEach((row) => {
        const rowValues = columns.map((column) => {
          const value = row[column]

          // 处理特殊情况
          if (value === undefined || value === null) return ''
          if (column === 'timestamp')
            return format(new Date(value), 'yyyy-MM-dd HH:mm:ss')
          if (typeof value === 'string' && value.includes(','))
            return `"${value}"`

          return value
        })

        csvContent += rowValues.join(',') + '\n'
      })

      // 创建Blob并下载
      downloadFile(
        csvContent,
        `历史数据_${format(new Date(), 'yyyyMMdd_HHmmss')}.xlsx`,
        'application/vnd.ms-excel;charset=utf-8;'
      )
    } catch (error) {
      console.error('导出Excel失败:', error)
    } finally {
      setIsExporting(false)
    }
  }

  // 导出为JSON
  const exportToJson = () => {
    setIsExporting(true)

    try {
      // 创建JSON内容
      const jsonData = {
        criteria,
        exportTime: new Date().toISOString(),
        columns,
        data,
      }

      const jsonContent = JSON.stringify(jsonData, null, 2)

      // 创建Blob并下载
      downloadFile(
        jsonContent,
        `历史数据_${format(new Date(), 'yyyyMMdd_HHmmss')}.json`,
        'application/json;charset=utf-8;'
      )
    } catch (error) {
      console.error('导出JSON失败:', error)
    } finally {
      setIsExporting(false)
    }
  }

  // 通用下载文件函数
  const downloadFile = (
    content: string,
    fileName: string,
    mimeType: string
  ) => {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" disabled={isExporting || data.length === 0}>
          {isExporting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              导出中...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              导出数据
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={exportToExcel}>
          <FileSpreadsheet className="mr-2 h-4 w-4" />
          导出为Excel
        </DropdownMenuItem>
        <DropdownMenuItem onClick={exportToCsv}>
          <FileText className="mr-2 h-4 w-4" />
          导出为CSV
        </DropdownMenuItem>
        <DropdownMenuItem onClick={exportToJson}>
          <FileJson className="mr-2 h-4 w-4" />
          导出为JSON
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
