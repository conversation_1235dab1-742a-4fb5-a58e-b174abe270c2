import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Plus, Trash2, Edit, Save } from 'lucide-react'

export function HttpConfig() {
  const [baseUrl, setBaseUrl] = useState('https://api.example.com')
  const [authType, setAuthType] = useState('none')
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [apiKey, setApiKey] = useState('')
  const [apiKeyHeader, setApiKeyHeader] = useState('X-API-Key')
  const [timeout, setTimeout] = useState('30000')
  const [endpoints, setEndpoints] = useState([
    {
      id: '1',
      name: '实时数据',
      path: '/data/realtime',
      method: 'POST',
      template:
        '{"deviceId":"{{deviceId}}","value":"{{value}}","timestamp":"{{timestamp}}"}',
    },
    {
      id: '2',
      name: '历史数据',
      path: '/data/history',
      method: 'POST',
      template: '{"deviceId":"{{deviceId}}","history":"{{history}}"}',
    },
    {
      id: '3',
      name: '设备状态',
      path: '/device/status',
      method: 'PUT',
      template: '{"deviceId":"{{deviceId}}","status":"{{status}}"}',
    },
  ])
  const [editingEndpoint, setEditingEndpoint] = useState<string | null>(null)
  const [newEndpoint, setNewEndpoint] = useState({
    name: '',
    path: '',
    method: 'POST',
    template: '',
  })

  const handleAddEndpoint = () => {
    if (newEndpoint.name && newEndpoint.path) {
      setEndpoints([
        ...endpoints,
        {
          id: Date.now().toString(),
          ...newEndpoint,
        },
      ])
      setNewEndpoint({
        name: '',
        path: '',
        method: 'POST',
        template: '',
      })
    }
  }

  const handleDeleteEndpoint = (id: string) => {
    setEndpoints(endpoints.filter((endpoint) => endpoint.id !== id))
  }

  const handleEditEndpoint = (id: string) => {
    setEditingEndpoint(id)
  }

  const handleSaveEndpoint = (id: string, updatedEndpoint: any) => {
    setEndpoints(
      endpoints.map((endpoint) =>
        endpoint.id === id ? { ...endpoint, ...updatedEndpoint } : endpoint
      )
    )
    setEditingEndpoint(null)
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="baseUrl">基础 URL</Label>
            <Input
              id="baseUrl"
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              placeholder="例如: https://api.example.com"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="timeout">超时时间 (毫秒)</Label>
            <Input
              id="timeout"
              value={timeout}
              onChange={(e) => setTimeout(e.target.value)}
              placeholder="例如: 30000"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="authType">认证方式</Label>
            <Select value={authType} onValueChange={setAuthType}>
              <SelectTrigger>
                <SelectValue placeholder="选择认证方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">无认证</SelectItem>
                <SelectItem value="basic">基本认证</SelectItem>
                <SelectItem value="apikey">API Key</SelectItem>
                <SelectItem value="bearer">Bearer Token</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {authType === 'basic' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="username">用户名</Label>
                <Input
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="认证用户名"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">密码</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="认证密码"
                />
              </div>
            </>
          )}

          {authType === 'apikey' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="apiKeyHeader">API Key 头名称</Label>
                <Input
                  id="apiKeyHeader"
                  value={apiKeyHeader}
                  onChange={(e) => setApiKeyHeader(e.target.value)}
                  placeholder="例如: X-API-Key"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <Input
                  id="apiKey"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="您的 API Key"
                />
              </div>
            </>
          )}

          {authType === 'bearer' && (
            <div className="space-y-2">
              <Label htmlFor="bearerToken">Bearer Token</Label>
              <Input
                id="bearerToken"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="您的 Bearer Token"
              />
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">端点配置</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setNewEndpoint({
                name: '',
                path: '',
                method: 'POST',
                template: '',
              })
            }}>
            <Plus className="h-4 w-4 mr-2" />
            添加端点
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>添加新端点</CardTitle>
            <CardDescription>配置 HTTP 请求端点</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="endpointName">端点名称</Label>
                <Input
                  id="endpointName"
                  value={newEndpoint.name}
                  onChange={(e) =>
                    setNewEndpoint({ ...newEndpoint, name: e.target.value })
                  }
                  placeholder="例如: 实时数据"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endpointPath">路径</Label>
                <Input
                  id="endpointPath"
                  value={newEndpoint.path}
                  onChange={(e) =>
                    setNewEndpoint({ ...newEndpoint, path: e.target.value })
                  }
                  placeholder="例如: /data/realtime"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endpointMethod">HTTP 方法</Label>
                <Select
                  value={newEndpoint.method}
                  onValueChange={(value) =>
                    setNewEndpoint({ ...newEndpoint, method: value })
                  }>
                  <SelectTrigger>
                    <SelectValue placeholder="选择 HTTP 方法" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GET">GET</SelectItem>
                    <SelectItem value="POST">POST</SelectItem>
                    <SelectItem value="PUT">PUT</SelectItem>
                    <SelectItem value="DELETE">DELETE</SelectItem>
                    <SelectItem value="PATCH">PATCH</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="endpointTemplate">数据模板 (JSON)</Label>
                <Textarea
                  id="endpointTemplate"
                  value={newEndpoint.template}
                  onChange={(e) =>
                    setNewEndpoint({ ...newEndpoint, template: e.target.value })
                  }
                  placeholder='{"deviceId":"{{deviceId}}","value":"{{value}}","timestamp":"{{timestamp}}"}'
                  className="font-mono text-sm h-24"
                />
                <p className="text-xs text-muted-foreground">
                  使用 {'{{变量名}}'} 作为占位符，例如 {'{{deviceId}}'},{' '}
                  {'{{value}}'}, {'{{timestamp}}'}
                </p>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleAddEndpoint}>添加端点</Button>
          </CardFooter>
        </Card>

        <div className="space-y-4">
          <h4 className="font-medium">已配置端点</h4>

          {endpoints.length === 0 ? (
            <p className="text-muted-foreground">暂无配置的端点</p>
          ) : (
            <div className="space-y-4">
              {endpoints.map((endpoint) => (
                <Card key={endpoint.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-base">
                        {endpoint.name}
                      </CardTitle>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditEndpoint(endpoint.id)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteEndpoint(endpoint.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <CardDescription>
                      {endpoint.method} {baseUrl}
                      {endpoint.path}
                    </CardDescription>
                  </CardHeader>

                  {editingEndpoint === endpoint.id ? (
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor={`edit-name-${endpoint.id}`}>
                              端点名称
                            </Label>
                            <Input
                              id={`edit-name-${endpoint.id}`}
                              defaultValue={endpoint.name}
                              onChange={(e) => {
                                const updatedEndpoint = {
                                  ...endpoint,
                                  name: e.target.value,
                                }
                                handleSaveEndpoint(endpoint.id, updatedEndpoint)
                              }}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor={`edit-path-${endpoint.id}`}>
                              路径
                            </Label>
                            <Input
                              id={`edit-path-${endpoint.id}`}
                              defaultValue={endpoint.path}
                              onChange={(e) => {
                                const updatedEndpoint = {
                                  ...endpoint,
                                  path: e.target.value,
                                }
                                handleSaveEndpoint(endpoint.id, updatedEndpoint)
                              }}
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor={`edit-template-${endpoint.id}`}>
                            数据模板
                          </Label>
                          <Textarea
                            id={`edit-template-${endpoint.id}`}
                            defaultValue={endpoint.template}
                            className="font-mono text-sm h-24"
                            onChange={(e) => {
                              const updatedEndpoint = {
                                ...endpoint,
                                template: e.target.value,
                              }
                              handleSaveEndpoint(endpoint.id, updatedEndpoint)
                            }}
                          />
                        </div>

                        <Button onClick={() => setEditingEndpoint(null)}>
                          <Save className="h-4 w-4 mr-2" />
                          保存
                        </Button>
                      </div>
                    </CardContent>
                  ) : (
                    endpoint.template && (
                      <CardContent className="pt-0">
                        <div className="bg-muted p-2 rounded-md">
                          <pre className="text-xs font-mono overflow-x-auto whitespace-pre-wrap">
                            {endpoint.template}
                          </pre>
                        </div>
                      </CardContent>
                    )
                  )}
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
