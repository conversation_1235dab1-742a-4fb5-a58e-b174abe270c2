import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { lazy, Suspense } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Download,
  Trash2,
  Search,
  Filter,
  Pause,
  Play,
  AlertCircle,
  Info,
  AlertTriangle,
  CheckCircle2,
  RefreshCw,
  WifiOff,
  Wifi,
  XCircle,
  Activity,
  List,
  FileText,
  Clock,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { Alert, AlertDescription } from '@/components/ui/alert'
import * as XLSX from 'xlsx'
import {
  useSignalR,
  useSignalREvent,
  HubConnectionState,
  mapHubConnectionState,
} from '@/lib/signalr/signalr-context'

// 替换动态导入
const MonacoEditor = lazy(() => import('@monaco-editor/react'))

// 日志级别类型
export type LogLevel = 'info' | 'warning' | 'error' | 'success' | 'debug'

// 日志条目接口
export interface LogEntry {
  id: string
  timestamp: string
  level: LogLevel
  message: string
  protocol?: string
  details?: string
  source?: string
  target?: string
  dataSize?: number
}

// 显示模式类型
type DisplayMode = 'structured' | 'text'

// 安全的时间格式化工具函数
const formatSafeTimestamp = (timestamp: string | undefined | null): string => {
  if (!timestamp) {
    return new Date().toLocaleString()
  }

  try {
    const date = new Date(timestamp)
    if (isNaN(date.getTime())) {
      // 如果时间戳无效，返回当前时间
      return new Date().toLocaleString()
    }
    return date.toLocaleString()
  } catch (error) {
    // 发生任何错误时，返回当前时间
    console.warn('时间格式化失败:', error)
    return new Date().toLocaleString()
  }
}

// 实时当前时间显示组件
const CurrentTimeDisplay: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  return (
    <div className="flex items-center text-sm text-muted-foreground">
      <Clock className="h-4 w-4 mr-1" />
      <span>当前时间: {currentTime.toLocaleString()}</span>
    </div>
  )
}

// 日志格式化工具函数
const formatLogEntryToText = (log: LogEntry): string => {
  const timestamp = formatSafeTimestamp(log.timestamp)
  const level = (log.level || 'info').toUpperCase().padEnd(7)
  const protocol = log.protocol ? `[${log.protocol}]` : ''
  const dataSize = log.dataSize ? ` (${log.dataSize} bytes)` : ''
  const source = log.source ? ` ${log.source}` : ''
  const target = log.target ? ` → ${log.target}` : ''

  let logLine = `[${timestamp}] [${level}] ${protocol}${source}${target} ${
    log.message || ''
  }${dataSize}`

  if (log.details) {
    logLine += `\n    详情: ${log.details}`
  }

  return logLine
}

// 批量格式化日志
const formatLogsToText = (logs: LogEntry[]): string => {
  return logs.map(formatLogEntryToText).join('\n')
}

// 获取日志级别的颜色标记（用于Monaco Editor主题）
const getLogLevelColor = (level: LogLevel): string => {
  switch (level) {
    case 'error':
      return '#ef4444' // red-500
    case 'warning':
      return '#f59e0b' // amber-500
    case 'success':
      return '#10b981' // emerald-500
    case 'info':
      return '#3b82f6' // blue-500
    case 'debug':
      return '#6b7280' // gray-500
    default:
      return '#6b7280'
  }
}

// 验证和规范化日志数据
const normalizeLogEntry = (rawLog: any): LogEntry => {
  return {
    id:
      rawLog.id ||
      `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: rawLog.timestamp || new Date().toISOString(),
    level: (rawLog.level as LogLevel) || 'info',
    message: rawLog.message || '未知消息',
    protocol: rawLog.protocol || undefined,
    details: rawLog.details || undefined,
    source: rawLog.source || undefined,
    target: rawLog.target || undefined,
    dataSize: rawLog.dataSize || undefined,
  }
}

// 高性能日志查看器组件
interface HighPerformanceLogViewerProps {
  logs: LogEntry[]
  searchTerm?: string
  autoScroll?: boolean
  height?: string
}

const HighPerformanceLogViewer: React.FC<HighPerformanceLogViewerProps> = ({
  logs,
  searchTerm = '',
  autoScroll = true,
  height = '384px',
}) => {
  const editorRef = useRef<any>(null)
  const [editorContent, setEditorContent] = useState('')

  // 更新编辑器内容
  useEffect(() => {
    const content = formatLogsToText(logs)
    setEditorContent(content)
  }, [logs])

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && editorRef.current && logs.length > 0) {
      try {
        // 滚动到文档底部
        const model = editorRef.current.getModel()
        if (model) {
          const lineCount = model.getLineCount()
          editorRef.current.revealLine(lineCount)
          editorRef.current.setPosition({ lineNumber: lineCount, column: 1 })
        }
      } catch (error) {
        console.warn('自动滚动失败:', error)
      }
    }
  }, [logs, autoScroll])

  // 处理搜索
  useEffect(() => {
    if (searchTerm && editorRef.current) {
      try {
        // 使用Monaco的查找功能
        const findController = editorRef.current.getContribution(
          'editor.contrib.findController'
        )
        if (findController) {
          findController.start({
            searchString: searchTerm,
            isRegex: false,
            matchCase: false,
            wholeWord: false,
          })
        }
      } catch (error) {
        console.warn('搜索功能初始化失败:', error)
      }
    }
  }, [searchTerm])

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor

    // 配置编辑器主题和样式
    monaco.editor.defineTheme('logTheme', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'log.error', foreground: 'ef4444', fontStyle: 'bold' },
        { token: 'log.warning', foreground: 'f59e0b', fontStyle: 'bold' },
        { token: 'log.success', foreground: '10b981', fontStyle: 'bold' },
        { token: 'log.info', foreground: '3b82f6' },
        { token: 'log.debug', foreground: '6b7280' },
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
      },
    })

    monaco.editor.setTheme('logTheme')

    // 禁用不需要的功能
    editor.updateOptions({
      readOnly: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      fontSize: 12,
      lineNumbers: 'on',
      folding: false,
      links: false,
      selectOnLineNumbers: false,
      contextmenu: false,
    })
  }

  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center h-96 bg-muted">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p>正在加载编辑器...</p>
          </div>
        </div>
      }>
      <MonacoEditor
        height={height}
        language="plaintext"
        value={editorContent}
        onMount={handleEditorDidMount}
        options={{
          readOnly: true,
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          wordWrap: 'on',
          fontSize: 12,
          lineNumbers: 'on',
          folding: false,
          links: false,
          selectOnLineNumbers: false,
          contextmenu: false,
          automaticLayout: true,
        }}
      />
    </Suspense>
  )
}

interface ForwardingRealTimeLogsProps {
  configId: number
}

// 虚拟滚动的行高
const ROW_HEIGHT = 80
const CONTAINER_HEIGHT = 600

export function ForwardingRealTimeLogs({
  configId,
}: ForwardingRealTimeLogsProps) {
  // 状态管理
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([])
  const [isPaused, setIsPaused] = useState(false)
  const [autoScroll, setAutoScroll] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [levelFilter, setLevelFilter] = useState<string>('all')
  const [displayMode, setDisplayMode] = useState<DisplayMode>('structured')

  // 引用
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // 使用SignalR连接
  const { connectionState, reconnect, sendMessage, connection } = useSignalR()

  // 订阅转发日志事件
  const logEventName = `${configId}_mqtt_forward_logs`

  // 显式订阅转发日志主题
  useEffect(() => {
    const subscribeToTopic = async () => {
      if (connection && connection.state === 'Connected') {
        try {
          await sendMessage('SubscribeTopic', logEventName)
          console.log(`已订阅转发日志主题: ${logEventName}`)
        } catch (error) {
          console.error(`订阅转发日志主题失败: ${logEventName}`, error)
        }
      } else {
        console.warn('SignalR连接未就绪，无法订阅主题:', connection?.state)
      }
    }

    subscribeToTopic()

    // 组件卸载时取消订阅
    return () => {
      if (connection && connection.state === 'Connected') {
        sendMessage('UnsubscribeTopic', logEventName)
          .then(() => console.log(`已取消订阅转发日志主题: ${logEventName}`))
          .catch((err) =>
            console.error(`取消订阅转发日志主题失败: ${logEventName}`, err)
          )
      }
    }
  }, [connection, connectionState, sendMessage, logEventName])

  useSignalREvent<LogEntry>(
    logEventName,
    useCallback(
      (logData: LogEntry) => {
        if (!isPaused) {
          setLogs((prevLogs) => {
            // 规范化和验证日志数据
            const normalizedLog = normalizeLogEntry(logData)
            const newLogs = [...prevLogs, normalizedLog]
            // 保留最新的1000条日志以优化性能
            return newLogs.slice(-1000)
          })
        }
      },
      [isPaused]
    ),
    [configId, isPaused]
  )

  // 过滤日志
  useEffect(() => {
    let filtered = logs

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(
        (log) =>
          log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.protocol?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.details?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // 级别过滤
    if (levelFilter !== 'all') {
      filtered = filtered.filter((log) => log.level === levelFilter)
    }

    setFilteredLogs(filtered)
  }, [logs, searchTerm, levelFilter])

  // 自动滚动到底部
  useEffect(() => {
    if (
      autoScroll &&
      !isPaused &&
      displayMode === 'structured' &&
      scrollAreaRef.current
    ) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [filteredLogs, autoScroll, isPaused, displayMode])

  // 优化：当日志数量过多时自动切换到文本模式
  useEffect(() => {
    if (logs.length > 500 && displayMode === 'structured') {
      toast({
        title: '性能优化提示',
        description: '日志数量较多，建议切换到文本模式以获得更好的性能',
        duration: 5000,
      })
    }
  }, [logs.length, displayMode])

  // 清除日志
  const clearLogs = useCallback(() => {
    setLogs([])
    setFilteredLogs([])
    toast({
      title: '日志已清除',
      description: '所有日志记录已清空',
    })
  }, [])

  // 导出日志
  const exportLogs = useCallback(
    (format: 'txt' | 'json' | 'xlsx') => {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
      const filename = `forwarding-logs-${configId}-${timestamp}`

      switch (format) {
        case 'txt':
          const txtContent = filteredLogs
            .map(
              (log) =>
                `[${formatSafeTimestamp(
                  log.timestamp
                )}] [${log.level.toUpperCase()}] [${log.protocol}] ${
                  log.message
                }${log.details ? '\n' + log.details : ''}`
            )
            .join('\n\n')

          const txtBlob = new Blob([txtContent], { type: 'text/plain' })
          downloadFile(txtBlob, `${filename}.txt`)
          break

        case 'json':
          const jsonContent = JSON.stringify(filteredLogs, null, 2)
          const jsonBlob = new Blob([jsonContent], { type: 'application/json' })
          downloadFile(jsonBlob, `${filename}.json`)
          break

        case 'xlsx':
          const wsData = filteredLogs.map((log) => ({
            时间: formatSafeTimestamp(log.timestamp),
            级别: log.level,
            协议: log.protocol,
            消息: log.message,
            来源: log.source,
            目标: log.target,
            数据大小: log.dataSize,
            详情: log.details,
          }))

          const ws = XLSX.utils.json_to_sheet(wsData)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, '转发日志')
          XLSX.writeFile(wb, `${filename}.xlsx`)
          break
      }

      toast({
        title: '导出成功',
        description: `日志已导出为${format.toUpperCase()}格式`,
      })
    },
    [filteredLogs, configId]
  )

  // 下载文件辅助函数
  const downloadFile = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 获取日志级别样式
  const getLevelStyle = (level: LogLevel) => {
    switch (level) {
      case 'error':
        return 'bg-red-100 text-red-800 border-red-300'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 'success':
        return 'bg-green-100 text-green-800 border-green-300'
      case 'info':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case 'debug':
        return 'bg-gray-100 text-gray-800 border-gray-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  // 获取日志级别图标
  const getLevelIcon = (level: LogLevel) => {
    switch (level) {
      case 'error':
        return <XCircle className="h-4 w-4" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />
      case 'success':
        return <CheckCircle2 className="h-4 w-4" />
      case 'info':
        return <Info className="h-4 w-4" />
      case 'debug':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  // 连接状态显示
  const renderConnectionStatus = () => {
    const getStatusInfo = () => {
      // 将connectionState字符串转换为HubConnectionState对应的值
      switch (connectionState) {
        case 'connected':
          return {
            icon: <Wifi className="h-4 w-4" />,
            text: '已连接',
            className: 'bg-green-100 text-green-800 border-green-300',
          }
        case 'connecting':
          return {
            icon: <RefreshCw className="h-4 w-4 animate-spin" />,
            text: '连接中',
            className: 'bg-yellow-100 text-yellow-800 border-yellow-300',
          }
        case 'reconnecting':
          return {
            icon: <RefreshCw className="h-4 w-4 animate-spin" />,
            text: '重连中',
            className: 'bg-yellow-100 text-yellow-800 border-yellow-300',
          }
        case 'disconnected':
        default:
          return {
            icon: <WifiOff className="h-4 w-4" />,
            text: '未连接',
            className: 'bg-gray-100 text-gray-800 border-gray-300',
          }
      }
    }

    const { icon, text, className } = getStatusInfo()

    return (
      <Badge variant="outline" className={className}>
        {icon}
        <span className="ml-1">{text}</span>
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            实时转发日志
            {displayMode === 'text' && (
              <Badge variant="secondary" className="ml-2 text-xs">
                高性能模式
              </Badge>
            )}
            <span className="ml-2 text-sm font-normal text-muted-foreground">
              (订阅主题: {logEventName})
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <CurrentTimeDisplay />
            {renderConnectionStatus()}
            <Badge variant="outline">
              {filteredLogs.length} / {logs.length} 条记录
            </Badge>
          </div>
        </CardTitle>
        {displayMode === 'text' && (
          <p className="text-sm text-muted-foreground">
            文本模式使用Monaco
            Editor提供高性能日志显示，适合查看大量日志数据。使用Ctrl+F进行快速搜索。
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 连接错误提示 */}
        {connectionState === 'disconnected' && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              SignalR连接失败，正在尝试重新连接...
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={() => reconnect()}>
                手动重连
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* 控制栏 */}
        <div className="flex flex-wrap gap-4 items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div className="flex flex-wrap gap-2 items-center">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索日志..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 w-64"
              />
            </div>

            <Select value={levelFilter} onValueChange={setLevelFilter}>
              <SelectTrigger className="w-32">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部级别</SelectItem>
                <SelectItem value="error">错误</SelectItem>
                <SelectItem value="warning">警告</SelectItem>
                <SelectItem value="info">信息</SelectItem>
                <SelectItem value="success">成功</SelectItem>
                <SelectItem value="debug">调试</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsPaused(!isPaused)}
              className={isPaused ? 'text-green-600' : 'text-orange-600'}>
              {isPaused ? (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  继续
                </>
              ) : (
                <>
                  <Pause className="h-4 w-4 mr-2" />
                  暂停
                </>
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoScroll(!autoScroll)}>
              自动滚动: {autoScroll ? '开' : '关'}
            </Button>

            <div className="flex border rounded-md">
              <Button
                variant={displayMode === 'structured' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setDisplayMode('structured')}
                className="rounded-r-none">
                <List className="h-4 w-4 mr-2" />
                结构化
              </Button>
              <Button
                variant={displayMode === 'text' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setDisplayMode('text')}
                className="rounded-l-none">
                <FileText className="h-4 w-4 mr-2" />
                文本
              </Button>
            </div>
          </div>

          <div className="flex gap-2">
            <Select onValueChange={(value) => exportLogs(value as any)}>
              <SelectTrigger className="w-auto">
                <Download className="h-4 w-4 mr-2" />
                导出
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="txt">导出为 TXT</SelectItem>
                <SelectItem value="json">导出为 JSON</SelectItem>
                <SelectItem value="xlsx">导出为 Excel</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm" onClick={clearLogs}>
              <Trash2 className="h-4 w-4 mr-2" />
              清除
            </Button>
          </div>
        </div>

        {/* 日志显示区域 - 支持双模式切换 */}
        {filteredLogs.length === 0 ? (
          <div className="border rounded-lg">
            <div className="flex items-center justify-center py-12 text-muted-foreground h-96">
              <div className="text-center">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>等待日志数据...</p>
                <p className="text-sm mt-1">确保转发配置已启用且连接正常</p>
              </div>
            </div>
          </div>
        ) : displayMode === 'text' ? (
          /* 文本模式 - 使用Monaco Editor高性能显示 */
          <HighPerformanceLogViewer
            logs={filteredLogs}
            searchTerm={searchTerm}
            autoScroll={autoScroll && !isPaused}
            height="384px"
          />
        ) : (
          /* 结构化模式 - 原有的详细显示 */
          <div className="border rounded-lg">
            <ScrollArea ref={scrollAreaRef} className="h-96">
              <div className="p-4 space-y-3">
                {filteredLogs.map((log, index) => (
                  <div
                    key={log.id}
                    className="flex items-start gap-3 p-3 border rounded-lg hover:bg-muted/30 transition-colors">
                    <div className="flex items-center mt-1">
                      <Badge
                        variant="outline"
                        className={`${getLevelStyle(log.level)} text-xs`}>
                        {getLevelIcon(log.level)}
                        <span className="ml-1 uppercase">{log.level}</span>
                      </Badge>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium">
                          {log.message}
                        </span>
                        {log.protocol && (
                          <Badge variant="secondary" className="text-xs">
                            {log.protocol}
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{formatSafeTimestamp(log.timestamp)}</span>
                        {log.source && <span>来源: {log.source}</span>}
                        {log.target && <span>目标: {log.target}</span>}
                        {log.dataSize && (
                          <span>大小: {log.dataSize} bytes</span>
                        )}
                      </div>

                      {log.details && (
                        <details className="mt-2">
                          <summary className="text-xs text-blue-600 cursor-pointer hover:text-blue-800">
                            查看详情
                          </summary>
                          <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                            {log.details}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* 状态栏 */}
        <div className="flex items-center justify-between text-sm text-muted-foreground pt-2 border-t">
          <div className="flex items-center gap-4">
            <span>连接状态: {renderConnectionStatus()}</span>
            <span>日志缓冲: {logs.length}/1000 条</span>
            <span>
              显示模式:{' '}
              {displayMode === 'structured' ? '结构化视图' : '文本视图'}
            </span>
            {displayMode === 'text' && (
              <span className="text-blue-600">高性能模式已启用</span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {isPaused && (
              <Badge variant="outline" className="text-orange-600">
                已暂停接收
              </Badge>
            )}
            <span>订阅: {logEventName}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
