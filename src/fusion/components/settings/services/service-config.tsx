import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useServiceConfig } from '@/hooks/use-services'
import { Loader2, Save, RefreshCw } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { ConfigFieldDefinition } from '@/lib/api/service-manager-api'
import { serviceManagerApi } from '@/lib/api/service-manager-api'

interface ServiceConfigProps {
  serviceId: string
}

interface DynamicFieldProps {
  field: ConfigFieldDefinition
  value: any
  onChange: (value: any) => void
  disabled?: boolean
}

// 动态字段渲染组件
function DynamicField({
  field,
  value,
  onChange,
  disabled = false,
}: DynamicFieldProps) {
  const renderField = () => {
    switch (field.type) {
      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Switch
              id={field.name}
              checked={Boolean(value)}
              onCheckedChange={onChange}
              disabled={disabled}
            />
            <Label htmlFor={field.name} className="text-sm font-medium">
              {field.description}
            </Label>
          </div>
        )

      case 'number':
        return (
          <div className="space-y-2">
            <Label htmlFor={field.name} className="text-sm font-medium">
              {field.description}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="number"
              value={value || ''}
              onChange={(e) =>
                onChange(parseInt(e.target.value) || field.defaultValue)
              }
              disabled={disabled}
              placeholder={`默认值: ${field.defaultValue}`}
            />
          </div>
        )

      case 'enum':
        return (
          <div className="space-y-2">
            <Label htmlFor={field.name} className="text-sm font-medium">
              {field.description}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Select
              value={value || field.defaultValue}
              onValueChange={onChange}
              disabled={disabled}>
              <SelectTrigger>
                <SelectValue placeholder={`选择${field.description}`} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )

      default: // string
        return (
          <div className="space-y-2">
            <Label htmlFor={field.name} className="text-sm font-medium">
              {field.description}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="text"
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              disabled={disabled}
              placeholder={`默认值: ${field.defaultValue}`}
            />
          </div>
        )
    }
  }

  return (
    <div className="space-y-1">
      {renderField()}
      {field.extendDescription && (
        <p className="text-xs text-muted-foreground">
          {field.extendDescription}
        </p>
      )}
      {field.validationRules && field.validationRules.length > 0 && (
        <div className="text-xs text-muted-foreground">
          {field.validationRules.map((rule, index) => (
            <div key={index}>{rule.message}</div>
          ))}
        </div>
      )}
    </div>
  )
}

export function ServiceConfig({ serviceId }: ServiceConfigProps) {
  const {
    config,
    loading,
    saving,
    error,
    updateConfig,
    updateServiceEnabled,
    saveConfig,
    refreshConfig,
  } = useServiceConfig(serviceId)

  const [localConfig, setLocalConfig] = useState<Record<string, any>>({})
  const [hasChanges, setHasChanges] = useState(false)

  // 同步远程配置到本地状态
  useEffect(() => {
    if (config) {
      setLocalConfig(config.config)
      setHasChanges(false)
    }
  }, [config])

  // 处理字段值变更
  const handleFieldChange = (fieldName: string, value: any) => {
    const newConfig = { ...localConfig, [fieldName]: value }
    setLocalConfig(newConfig)
    setHasChanges(true)
  }

  // 处理服务启用状态变更
  const handleEnabledChange = async (enabled: boolean) => {
    try {
      // 立即调用API更新服务状态
      const success = await serviceManagerApi.setServiceStatus(
        serviceId,
        enabled
      )

      if (success) {
        updateServiceEnabled(enabled)
        toast({
          title: enabled ? '服务已启用' : '服务已禁用',
          description: `${config?.serviceName} 状态已更新`,
        })
      } else {
        toast({
          title: '操作失败',
          description: '服务状态更新失败，请稍后重试',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('切换服务状态失败:', error)
      toast({
        title: '操作失败',
        description: '服务状态更新失败，请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 保存配置
  const handleSave = async () => {
    try {
      // 构建完整的配置对象，包含启用状态
      const fullConfig = {
        ...localConfig,
        IsEnabled: config?.enabled?.toString() || 'false',
      }

      const success = await saveConfig(fullConfig)
      if (success) {
        setHasChanges(false)
        toast({
          title: '保存成功',
          description: '服务配置已更新',
        })
      }
    } catch (error) {
      toast({
        title: '保存失败',
        description: '配置保存失败，请重试',
        variant: 'destructive',
      })
    }
  }

  // 重置配置
  const handleReset = () => {
    if (config) {
      setLocalConfig(config.config)
      setHasChanges(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载配置中...</span>
        </CardContent>
      </Card>
    )
  }

  if (error || !config) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center">
            <p className="text-muted-foreground mb-4">
              {error || '无法加载服务配置'}
            </p>
            <Button onClick={refreshConfig} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const sortedFields = [...config.fieldDefinitions].sort(
    (a, b) => a.order - b.order
  )

  return (
    <div className="space-y-6">
      {/* 服务基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle>服务基本设置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="service-enabled"
              checked={config.enabled}
              onCheckedChange={handleEnabledChange}
            />
            <Label htmlFor="service-enabled" className="text-sm font-medium">
              启用服务
            </Label>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">服务名称:</span>{' '}
              {config.serviceName}
            </div>
            <div>
              <span className="font-medium">运行状态:</span>
              <span
                className={`ml-2 px-2 py-1 rounded text-xs ${
                  config.isRunning
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                {config.isRunning ? '运行中' : '已停止'}
              </span>
            </div>
            {config.version && (
              <div>
                <span className="font-medium">版本:</span> {config.version}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 动态配置字段 */}
      {sortedFields.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>服务配置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {sortedFields.map((field) => (
                <DynamicField
                  key={field.name}
                  field={field}
                  value={localConfig[field.name]}
                  onChange={(value) => handleFieldChange(field.name, value)}
                  disabled={saving}
                />
              ))}
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={!hasChanges || saving}>
                重置
              </Button>
              <Button onClick={handleSave} disabled={!hasChanges || saving}>
                {saving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    保存配置
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {sortedFields.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">该服务暂无可配置项</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
