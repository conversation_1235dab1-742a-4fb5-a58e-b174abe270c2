import { useEffect, useRef } from 'react'
import * as echarts from 'echarts'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

export function ApiStats() {
  const chartRef = useRef(null)

  useEffect(() => {
    // 确保DOM元素已经渲染
    if (chartRef.current) {
      // 初始化ECharts实例
      const chart = echarts.init(chartRef.current)

      // 生成过去7天的日期
      const dates = Array.from({ length: 7 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - (6 - i))
        return date.toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric',
        })
      })

      // 模拟API调用数据
      const callData = [120, 132, 101, 134, 90, 230, 210]
      const successRateData = [98, 97, 99, 99, 96, 98, 99]

      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999',
            },
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        legend: {
          data: ['API调用次数', '成功率'],
        },
        xAxis: [
          {
            type: 'category',
            data: dates,
            axisPointer: {
              type: 'shadow',
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '调用次数',
            min: 0,
            axisLabel: {
              formatter: '{value}',
            },
          },
          {
            type: 'value',
            name: '成功率',
            min: 90,
            max: 100,
            axisLabel: {
              formatter: '{value}%',
            },
          },
        ],
        series: [
          {
            name: 'API调用次数',
            type: 'bar',
            data: callData,
          },
          {
            name: '成功率',
            type: 'line',
            yAxisIndex: 1,
            data: successRateData,
            smooth: true,
            lineStyle: {
              width: 3,
              color: '#10b981',
            },
            itemStyle: {
              color: '#10b981',
            },
          },
        ],
      }

      // 渲染图表
      chart.setOption(option)

      // 响应窗口大小变化
      const handleResize = () => {
        chart.resize()
      }
      window.addEventListener('resize', handleResize)

      // 清理函数
      return () => {
        chart.dispose()
        window.removeEventListener('resize', handleResize)
      }
    }
  }, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle>API调用统计</CardTitle>
        <CardDescription>过去7天的API调用次数和成功率</CardDescription>
      </CardHeader>
      <CardContent>
        <div ref={chartRef} style={{ height: '300px', width: '100%' }} />
      </CardContent>
    </Card>
  )
}
