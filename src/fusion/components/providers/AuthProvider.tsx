import { ReactNode, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { setNavigateFunction } from '@/lib/api-services/axios-utils'
import { checkUserPermissionAndApplyRestrictions } from '@/lib/utils/dev-tools-blocker'
import { PermissionProvider } from '@/components/providers/permission-provider'

interface AuthProviderProps {
  children: ReactNode
}

/**
 * 认证提供者组件
 * 用于在应用启动时设置axios的导航函数，以便在401响应时自动跳转
 * 同时集成权限管理系统
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const navigate = useNavigate()

  useEffect(() => {
    // 设置axios的导航函数
    setNavigateFunction((path: string) => {
      navigate(path)
    })

    // 监听存储变化，当用户登录状态改变时重新检查权限
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'access-token') {
        // 延迟检查，确保用户信息已更新
        setTimeout(() => {
          checkUserPermissionAndApplyRestrictions()
        }, 500)
      }
    }

    // 监听localStorage变化
    window.addEventListener('storage', handleStorageChange)

    // 组件挂载时也检查一次权限（处理页面刷新的情况）
    setTimeout(() => {
      checkUserPermissionAndApplyRestrictions()
    }, 1000)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [navigate])

  return (
    <PermissionProvider>
      {children}
    </PermissionProvider>
  )
}
