import React, { createContext, useContext, ReactNode } from 'react'
import { usePermission } from '@/lib/hooks/use-permission'
import { MenuTreeNode } from '@/lib/api/permission-api'

/**
 * 权限上下文接口
 */
interface PermissionContextType {
  // 状态
  permissions: string[]
  userMenus: MenuTreeNode[]
  isLoading: boolean
  error: string | null
  
  // 权限检查方法
  hasPermission: (permissionCode: string) => boolean
  hasMenuPermission: (menuCode: string) => boolean
  hasModulePermission: (moduleCode: string) => boolean
  hasPathPermission: (path: string) => boolean
  
  // 菜单过滤方法
  getAccessibleMenus: (menus: MenuTreeNode[]) => MenuTreeNode[]
  
  // 刷新方法
  refreshPermissions: () => Promise<void>
  
  // 清除方法
  clearPermissions: () => void
}

/**
 * 权限上下文
 */
const PermissionContext = createContext<PermissionContextType | undefined>(undefined)

/**
 * 权限Provider属性接口
 */
interface PermissionProviderProps {
  children: ReactNode
}

/**
 * 权限Provider组件
 * 为整个应用提供权限管理功能
 */
export function PermissionProvider({ children }: PermissionProviderProps) {
  const permissionData = usePermission()

  return (
    <PermissionContext.Provider value={permissionData}>
      {children}
    </PermissionContext.Provider>
  )
}

/**
 * 使用权限上下文的Hook
 * @returns 权限上下文数据
 */
export function usePermissionContext(): PermissionContextType {
  const context = useContext(PermissionContext)
  
  if (context === undefined) {
    throw new Error('usePermissionContext must be used within a PermissionProvider')
  }
  
  return context
}

/**
 * 权限检查组件属性接口
 */
interface PermissionGuardProps {
  children: ReactNode
  permission?: string
  menuCode?: string
  moduleCode?: string
  path?: string
  fallback?: ReactNode
  requireAll?: boolean
}

/**
 * 权限守卫组件
 * 根据权限控制子组件的显示
 */
export function PermissionGuard({
  children,
  permission,
  menuCode,
  moduleCode,
  path,
  fallback = null,
  requireAll = false,
}: PermissionGuardProps) {
  const { hasPermission, hasMenuPermission, hasModulePermission, hasPathPermission } = usePermissionContext()

  // 收集所有需要检查的权限
  const checks: boolean[] = []

  if (permission) {
    checks.push(hasPermission(permission))
  }

  if (menuCode) {
    checks.push(hasMenuPermission(menuCode))
  }

  if (moduleCode) {
    checks.push(hasModulePermission(moduleCode))
  }

  if (path) {
    checks.push(hasPathPermission(path))
  }

  // 如果没有指定任何权限检查，默认允许访问
  if (checks.length === 0) {
    return <>{children}</>
  }

  // 根据requireAll参数决定检查逻辑
  const hasAccess = requireAll 
    ? checks.every(check => check)  // 需要所有权限
    : checks.some(check => check)   // 只需要其中一个权限

  return hasAccess ? <>{children}</> : <>{fallback}</>
}

/**
 * 权限检查Hook（简化版）
 * 直接返回权限检查结果，适用于条件渲染
 */
export function usePermissionCheck() {
  const { hasPermission, hasMenuPermission, hasModulePermission, hasPathPermission } = usePermissionContext()

  return {
    hasPermission,
    hasMenuPermission,
    hasModulePermission,
    hasPathPermission,
  }
}
