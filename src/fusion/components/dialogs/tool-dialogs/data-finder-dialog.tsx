/**
 * 数据查找工具对话框组件
 *
 * 复用debug-tools中的数据查找工具，提供给floating-assistant使用
 * 该组件封装了数据查找功能，以对话框的形式展示
 */

import { Search } from 'lucide-react'
import { DataFinder } from '@/components/debug-tools/data-finder'
import { BaseToolDialog } from './base-tool-dialog'
import { memo, useState, useEffect } from 'react'

// 使用 memo 包装 DataFinder 组件以避免不必要的重新渲染
const MemoizedDataFinder = memo(DataFinder)

interface DataFinderDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function DataFinderDialog({
  open,
  onOpenChange,
}: DataFinderDialogProps) {
  // 使用状态来控制组件的渲染，避免在对话框关闭时仍然渲染组件
  const [shouldRender, setShouldRender] = useState(false)

  // 当对话框打开状态变化时，更新渲染状态
  useEffect(() => {
    if (open) {
      setShouldRender(true)
    } else {
      // 延迟移除组件，确保关闭动画完成
      const timer = setTimeout(() => {
        setShouldRender(false)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [open])

  return (
    <BaseToolDialog
      open={open}
      onOpenChange={onOpenChange}
      title="数据查找工具"
      icon={<Search className="h-5 w-5" />}
      description="在二进制数据中查找特定值，支持多种数据类型和格式"
      toolPath="/debug-tools/data-finder">
      {shouldRender && <MemoizedDataFinder />}
    </BaseToolDialog>
  )
}
