import React, { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { usePermissionContext } from '@/components/providers/permission-provider'
import { AccessDenied } from '@/components/ui/access-denied'

/**
 * 受保护路由组件属性接口
 */
interface ProtectedRouteProps {
  children: ReactNode
  permission?: string
  menuCode?: string
  moduleCode?: string
  fallback?: ReactNode
  redirectTo?: string
  requireAuth?: boolean
}

/**
 * 受保护路由组件
 * 根据权限控制路由访问
 */
export function ProtectedRoute({
  children,
  permission,
  menuCode,
  moduleCode,
  fallback,
  redirectTo,
  requireAuth = true,
}: ProtectedRouteProps) {
  const location = useLocation()
  const { 
    hasPermission, 
    hasMenuPermission, 
    hasModulePermission, 
    hasPathPermission,
    isLoading 
  } = usePermissionContext()

  // 检查是否需要认证
  if (requireAuth) {
    const accessToken = window.localStorage.getItem('access-token')
    if (!accessToken) {
      return <Navigate to="/login" state={{ from: location }} replace />
    }
  }

  // 如果正在加载权限数据，显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  // 权限检查
  let hasAccess = true

  if (permission) {
    hasAccess = hasPermission(permission)
  } else if (menuCode) {
    hasAccess = hasMenuPermission(menuCode)
  } else if (moduleCode) {
    hasAccess = hasModulePermission(moduleCode)
  } else {
    // 如果没有指定具体权限，使用路径权限检查
    hasAccess = hasPathPermission(location.pathname)
  }

  // 如果没有权限
  if (!hasAccess) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />
    }
    
    if (fallback) {
      return <>{fallback}</>
    }
    
    return <AccessDenied />
  }

  return <>{children}</>
}

/**
 * 路由权限检查Hook
 * 用于在组件内部检查当前路由权限
 */
export function useRoutePermission() {
  const location = useLocation()
  const { hasPathPermission, isLoading } = usePermissionContext()

  const hasAccess = hasPathPermission(location.pathname)

  return {
    hasAccess,
    isLoading,
    currentPath: location.pathname,
  }
}
