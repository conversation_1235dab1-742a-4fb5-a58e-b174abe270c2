import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { NodeConfigProps } from '../types'

export default function DataChangeTriggerConfig({
  selectedNode,
  onPropertyChange,
}: NodeConfigProps) {
  return (
    <>
      <div>
        <Label htmlFor="tag-id">点位选择</Label>
        <Select
          value={selectedNode.data?.tagId || ''}
          onValueChange={(value) => onPropertyChange('tagId', value)}>
          <SelectTrigger id="tag-id">
            <SelectValue placeholder="选择点位" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="tag-001">温度传感器</SelectItem>
            <SelectItem value="tag-002">压力传感器</SelectItem>
            <SelectItem value="tag-003">运行状态</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="change-type">变化类型</Label>
        <Select
          value={selectedNode.data?.changeType || 'any'}
          onValueChange={(value) => onPropertyChange('changeType', value)}>
          <SelectTrigger id="change-type">
            <SelectValue placeholder="选择变化类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">任何变化</SelectItem>
            <SelectItem value="increase">增加</SelectItem>
            <SelectItem value="decrease">减少</SelectItem>
            <SelectItem value="threshold">超过阈值</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {selectedNode.data?.changeType === 'threshold' && (
        <div>
          <Label htmlFor="threshold">阈值</Label>
          <Input
            id="threshold"
            type="number"
            value={selectedNode.data?.threshold || '0'}
            onChange={(e) => onPropertyChange('threshold', e.target.value)}
          />
        </div>
      )}
    </>
  )
}
