import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { NodeConfigProps } from '../types'

export default function DeviceEventTriggerConfig({
  selectedNode,
  onPropertyChange,
}: NodeConfigProps) {
  return (
    <>
      <div>
        <Label htmlFor="device-id">设备选择</Label>
        <Select
          value={selectedNode.data?.deviceId || ''}
          onValueChange={(value) => onPropertyChange('deviceId', value)}>
          <SelectTrigger id="device-id">
            <SelectValue placeholder="选择设备" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="dev-001">温度传感器 A1</SelectItem>
            <SelectItem value="dev-002">压力传感器 B2</SelectItem>
            <SelectItem value="dev-003">流量计 C3</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="event-type">事件类型</Label>
        <Select
          value={selectedNode.data?.eventType || 'status'}
          onValueChange={(value) => onPropertyChange('eventType', value)}>
          <SelectTrigger id="event-type">
            <SelectValue placeholder="选择事件类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="status">状态变化</SelectItem>
            <SelectItem value="online">上线</SelectItem>
            <SelectItem value="offline">离线</SelectItem>
            <SelectItem value="error">错误</SelectItem>
            <SelectItem value="warning">警告</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </>
  )
}
