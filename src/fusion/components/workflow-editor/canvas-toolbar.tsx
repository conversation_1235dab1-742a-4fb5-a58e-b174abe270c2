import { Button } from '@/components/ui/button'
import { Undo, Redo, ZoomIn, ZoomOut } from 'lucide-react'

interface CanvasToolbarProps {
  zoom: number
  onZoomIn: () => void
  onZoomOut: () => void
  onUndo: () => void
  onRedo: () => void
}

export default function CanvasToolbar({
  zoom,
  onZoomIn,
  onZoomOut,
  onUndo,
  onRedo,
}: CanvasToolbarProps) {
  return (
    <div className="border-b p-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={onUndo}>
            <Undo className="mr-1 h-4 w-4" /> 撤销
          </Button>
          <Button variant="outline" size="sm" onClick={onRedo}>
            <Redo className="mr-1 h-4 w-4" /> 重做
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={onZoomOut}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <span className="text-sm">{zoom}%</span>
          <Button variant="ghost" size="icon" onClick={onZoomIn}>
            <ZoomIn className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
