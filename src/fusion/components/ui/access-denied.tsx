import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ShieldX, ArrowLeft, Home } from 'lucide-react'

/**
 * 访问拒绝组件属性接口
 */
interface AccessDeniedProps {
  title?: string
  description?: string
  showBackButton?: boolean
  showHomeButton?: boolean
  className?: string
}

/**
 * 访问拒绝组件
 * 当用户没有权限访问某个页面或功能时显示
 */
export function AccessDenied({
  title = '访问被拒绝',
  description = '您没有权限访问此页面或功能。如需访问，请联系系统管理员。',
  showBackButton = true,
  showHomeButton = true,
  className = '',
}: AccessDeniedProps) {
  const navigate = useNavigate()

  const handleGoBack = () => {
    navigate(-1)
  }

  const handleGoHome = () => {
    navigate('/dashboard')
  }

  return (
    <div className={`flex items-center justify-center min-h-screen p-4 ${className}`}>
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <ShieldX className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-xl font-semibold text-red-600 dark:text-red-400">
            {title}
          </CardTitle>
          <CardDescription className="text-center">
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col gap-2 sm:flex-row">
            {showBackButton && (
              <Button
                variant="outline"
                onClick={handleGoBack}
                className="flex-1"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回上页
              </Button>
            )}
            {showHomeButton && (
              <Button
                onClick={handleGoHome}
                className="flex-1"
              >
                <Home className="mr-2 h-4 w-4" />
                回到首页
              </Button>
            )}
          </div>
          
          <div className="text-center text-sm text-muted-foreground">
            <p>错误代码: 403</p>
            <p className="mt-1">
              如果您认为这是一个错误，请联系技术支持。
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * 简化版访问拒绝组件
 * 用于内联显示，不占用整个页面
 */
export function InlineAccessDenied({
  message = '您没有权限访问此功能',
  className = '',
}: {
  message?: string
  className?: string
}) {
  return (
    <div className={`flex items-center justify-center p-8 ${className}`}>
      <div className="text-center">
        <ShieldX className="mx-auto h-12 w-12 text-red-500 mb-4" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}
