import type * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80 dark:bg-primary/70 dark:text-primary-foreground dark:hover:bg-primary/60",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 dark:bg-secondary/30 dark:text-secondary-foreground dark:hover:bg-secondary/40",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 dark:bg-red-800/70 dark:text-red-200 dark:hover:bg-red-700/80 dark:border dark:border-red-600/30",
        outline: "text-foreground dark:text-gray-200 dark:border-gray-600",
        success:
          "border-transparent bg-green-100 text-green-800 dark:bg-green-800/70 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-700/80 dark:border dark:border-green-600/30",
        warning:
          "border-transparent bg-yellow-100 text-yellow-800 dark:bg-yellow-800/70 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-700/80 dark:border dark:border-yellow-600/30",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
)

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return <div className={cn(badgeVariants({ variant }), className)} {...props} />
}

export { Badge, badgeVariants }
