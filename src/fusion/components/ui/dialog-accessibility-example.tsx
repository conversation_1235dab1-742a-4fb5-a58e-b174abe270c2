/**
 * Dialog Accessibility Example
 * 
 * This file demonstrates proper accessibility practices for dialog components,
 * including the use of VisuallyHidden for DialogTitle when needed.
 */

import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { VisuallyHidden } from '@/components/ui/visually-hidden'

/**
 * Example 1: Standard dialog with visible title
 * This is the recommended approach for most dialogs
 */
export function StandardDialog() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setOpen(true)}>
        Open Standard Dialog
      </Button>
      
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Action</DialogTitle>
            <DialogDescription>
              Are you sure you want to proceed with this action?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setOpen(false)}>
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

/**
 * Example 2: Dialog with hidden title for accessibility
 * Use this when the dialog's purpose is clear from context
 * but you still need a title for screen readers
 */
export function DialogWithHiddenTitle() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setOpen(true)}>
        Open Dialog with Hidden Title
      </Button>
      
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <VisuallyHidden>
              <DialogTitle>User Settings</DialogTitle>
            </VisuallyHidden>
            <DialogDescription>
              Configure your preferences below
            </DialogDescription>
          </DialogHeader>
          
          {/* Dialog content would go here */}
          <div className="py-4">
            <p>Settings form would be here...</p>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setOpen(false)}>
              Save Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

/**
 * Example 3: Dialog with complex header layout
 * Shows how to use VisuallyHidden with custom header layouts
 */
export function DialogWithComplexHeader() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setOpen(true)}>
        Open Complex Header Dialog
      </Button>
      
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold">Custom Header</h2>
                <p className="text-sm text-muted-foreground">
                  With additional information
                </p>
              </div>
              <Button variant="ghost" size="sm">
                Action
              </Button>
            </div>
            
            {/* Hidden title for accessibility */}
            <VisuallyHidden>
              <DialogTitle>Custom Header Dialog</DialogTitle>
            </VisuallyHidden>
            
            <DialogDescription>
              This dialog demonstrates a custom header layout while maintaining accessibility
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <p>Dialog content...</p>
          </div>
          
          <DialogFooter>
            <Button onClick={() => setOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

/**
 * ❌ INCORRECT: Dialog without DialogTitle
 * This example shows what NOT to do - it will cause accessibility issues
 */
export function IncorrectDialog() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setOpen(true)}>
        ❌ Incorrect Dialog (Missing Title)
      </Button>
      
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            {/* ❌ Missing DialogTitle - this is not accessible */}
            <DialogDescription>
              This dialog is missing a DialogTitle and will cause accessibility warnings
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <p>This dialog is not properly accessible to screen readers.</p>
          </div>
          
          <DialogFooter>
            <Button onClick={() => setOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

/**
 * Demo component that shows all examples
 */
export function DialogAccessibilityDemo() {
  return (
    <div className="space-y-4 p-6">
      <h1 className="text-2xl font-bold">Dialog Accessibility Examples</h1>
      
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">✅ Correct Examples</h2>
        <div className="flex gap-2">
          <StandardDialog />
          <DialogWithHiddenTitle />
          <DialogWithComplexHeader />
        </div>
      </div>
      
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">❌ Incorrect Example</h2>
        <div className="flex gap-2">
          <IncorrectDialog />
        </div>
        <p className="text-sm text-muted-foreground">
          The incorrect example will show accessibility warnings in the console
        </p>
      </div>
    </div>
  )
}
