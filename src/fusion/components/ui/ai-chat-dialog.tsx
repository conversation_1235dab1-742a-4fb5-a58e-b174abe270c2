import type React from 'react'

import { useState, useRef, useEffect, useCallback } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Send,
  Copy,
  Bot,
  User,
  Trash2,
  Download,
  Paperclip,
  X,
  FileText,
  ImageIcon,
  Code,
  File,
  Loader2,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'

interface Message {
  id: string
  content: string
  sender: 'user' | 'ai'
  timestamp: Date
  isTyping?: boolean
  attachments?: FileAttachment[]
}

interface FileAttachment {
  id: string
  name: string
  size: number
  type: string
  content?: string
  url?: string
  ocrText?: string
  isProcessingOCR?: boolean
}

interface AIChatDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title?: string
  description?: string
  sendMessage?: (message: string) => Promise<string>
  suggestions?: string[]
}

const STORAGE_KEY = 'ai-chat-history'
const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

export function AIChatDialog({
  open,
  onOpenChange,
  title = 'AI 智能助手',
  description = '询问相关问题，获取AI建议',
  sendMessage,
  suggestions = [
    '如何优化这个配置？',
    '帮我分析这个错误',
    '生成相关代码示例',
    '解释这个概念',
  ],
}: AIChatDialogProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [typingText, setTypingText] = useState('')
  const [attachments, setAttachments] = useState<FileAttachment[]>([])
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 加载历史记录
  useEffect(() => {
    if (open) {
      loadChatHistory()
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [open])

  // 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    })
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages, typingText, scrollToBottom])

  // 保存聊天历史
  const saveChatHistory = useCallback((newMessages: Message[]) => {
    try {
      const messagesToSave = newMessages.map((msg) => ({
        ...msg,
        attachments: msg.attachments?.map((att) => ({
          ...att,
          content: undefined,
          url: undefined,
        })),
      }))
      localStorage.setItem(STORAGE_KEY, JSON.stringify(messagesToSave))
    } catch (error) {
      console.error('Failed to save chat history:', error)
    }
  }, [])

  // 加载聊天历史
  const loadChatHistory = useCallback(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY)
      if (saved) {
        const parsedMessages = JSON.parse(saved).map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp),
        }))
        setMessages(parsedMessages)
      } else {
        const welcomeMessage: Message = {
          id: 'welcome',
          content:
            description ||
            '你好！我是 AI 智能助手 🤖\n\n我可以帮助你解决各种技术问题和分析文件内容。有什么我可以帮助你的吗？',
          sender: 'ai',
          timestamp: new Date(),
        }
        setMessages([welcomeMessage])
      }
    } catch (error) {
      console.error('Failed to load chat history:', error)
      setMessages([])
    }
  }, [description])

  // 清空聊天记录
  const clearChatHistory = useCallback(() => {
    setMessages([])
    setAttachments([])
    localStorage.removeItem(STORAGE_KEY)
    toast({
      title: '聊天记录已清空',
      description: '所有对话历史已被删除',
    })
  }, [])

  // 导出聊天记录
  const exportChatHistory = useCallback(() => {
    const exportData = {
      exportTime: new Date().toISOString(),
      messages: messages.map((msg) => ({
        ...msg,
        timestamp: msg.timestamp.toISOString(),
      })),
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ai-chat-history-${
      new Date().toISOString().split('T')[0]
    }.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: '导出成功',
      description: '聊天记录已导出到文件',
    })
  }, [messages])

  // OCR 文字识别 (模拟实现)
  const performOCR = useCallback(async (imageUrl: string): Promise<string> => {
    // 模拟 OCR 处理延迟
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 模拟 OCR 结果 - 在实际应用中，这里会调用真实的 OCR API
    const mockOCRResults = [
      `// JavaScript 代码示例
function calculateSum(a, b) {
  if (typeof a !== 'number' || typeof b !== 'number') {
    throw new Error('参数必须是数字');
  }
  return a + b;
}

const result = calculateSum(10, 20);
console.log('结果:', result);`,

      `{
  "device": {
    "name": "温度传感器",
    "protocol": "ModbusTCP",
    "host": "*************",
    "port": 502,
    "registers": [
      {
        "address": 40001,
        "type": "holding",
        "dataType": "float32"
      }
    ]
  }
}`,

      `import React, { useState, useEffect } from 'react';

const DataDisplay = ({ deviceId }) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDeviceData(deviceId)
      .then(setData)
      .finally(() => setLoading(false));
  }, [deviceId]);

  if (loading) return <div>Loading...</div>;
  
  return (
    <div className="data-container">
      <h2>设备数据</h2>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
};`,

      `SELECT d.device_name, 
       t.tag_name,
       AVG(h.value) as avg_value,
       MAX(h.value) as max_value,
       MIN(h.value) as min_value
FROM devices d
JOIN tags t ON d.device_id = t.device_id
JOIN history_data h ON t.tag_id = h.tag_id
WHERE h.timestamp >= NOW() - INTERVAL 1 DAY
GROUP BY d.device_id, t.tag_id
ORDER BY d.device_name, t.tag_name;`,

      `错误信息：
连接超时 - 无法连接到设备 *************:502
检查项目：
1. 网络连接是否正常
2. 设备IP地址是否正确
3. 端口502是否开放
4. 防火墙设置是否阻止连接

解决方案：
- 使用 ping 命令测试网络连通性
- 使用 telnet ************* 502 测试端口
- 检查设备配置和网络设置`,
    ]

    // 随机返回一个模拟结果
    return mockOCRResults[Math.floor(Math.random() * mockOCRResults.length)]
  }, [])

  // 文件上传处理
  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || [])

      files.forEach(async (file) => {
        if (file.size > MAX_FILE_SIZE) {
          toast({
            title: '文件过大',
            description: `文件 ${file.name} 超过 10MB 限制`,
            variant: 'destructive',
          })
          return
        }

        const fileAttachment: FileAttachment = {
          id: `file-${Date.now()}-${Math.random()}`,
          name: file.name,
          size: file.size,
          type: file.type,
        }

        // 读取文件内容
        const reader = new FileReader()

        if (file.type.startsWith('image/')) {
          reader.onload = async (e) => {
            fileAttachment.url = e.target?.result as string
            fileAttachment.isProcessingOCR = true

            // 先添加文件到附件列表
            setAttachments((prev) => [...prev, fileAttachment])

            try {
              // 执行 OCR 识别
              const ocrText = await performOCR(fileAttachment.url)

              // 更新附件的 OCR 结果
              setAttachments((prev) =>
                prev.map((att) =>
                  att.id === fileAttachment.id
                    ? { ...att, ocrText, isProcessingOCR: false }
                    : att
                )
              )

              toast({
                title: 'OCR 识别完成',
                description: `已成功识别图片 ${file.name} 中的文字内容`,
              })
            } catch (error) {
              console.error('OCR 处理失败:', error)
              setAttachments((prev) =>
                prev.map((att) =>
                  att.id === fileAttachment.id
                    ? { ...att, isProcessingOCR: false }
                    : att
                )
              )
              toast({
                title: 'OCR 识别失败',
                description: '图片文字识别过程中出现错误',
                variant: 'destructive',
              })
            }
          }
          reader.readAsDataURL(file)
        } else {
          reader.onload = (e) => {
            fileAttachment.content = e.target?.result as string
            setAttachments((prev) => [...prev, fileAttachment])
          }
          reader.readAsText(file)
        }
      })

      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    },
    [performOCR]
  )

  // 移除附件
  const removeAttachment = useCallback((attachmentId: string) => {
    setAttachments((prev) => prev.filter((att) => att.id !== attachmentId))
  }, [])

  // 获取文件图标
  const getFileIcon = (fileName: string, fileType: string) => {
    if (fileType.startsWith('image/')) return <ImageIcon className="h-4 w-4" />
    if (fileName.match(/\.(js|ts|jsx|tsx|py|java|cpp|c|php|rb|go|rs)$/i))
      return <Code className="h-4 w-4" />
    if (fileName.match(/\.(txt|md|json|xml|yaml|yml|csv|ini|conf)$/i))
      return <FileText className="h-4 w-4" />
    return <File className="h-4 w-4" />
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return (
      Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    )
  }

  // 打字机效果
  const typeMessage = useCallback(async (text: string, messageId: string) => {
    setTypingText('')

    // 优化打字机效果 - 如果文本很长，直接显示，避免卡顿
    if (text.length > 500) {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId
            ? { ...msg, content: text, isTyping: false }
            : msg
        )
      )
      return
    }

    // 对于较短文本，使用更快的打字机效果
    const words = text.split('')
    const speed = Math.max(5, Math.min(20, 2000 / words.length)) // 动态调整速度

    for (let i = 0; i <= words.length; i++) {
      const currentText = words.slice(0, i).join('')
      setTypingText(currentText)

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId
            ? { ...msg, content: currentText, isTyping: i < words.length }
            : msg
        )
      )

      await new Promise((resolve) => setTimeout(resolve, speed))
    }

    setTypingText('')
  }, [])

  const handleSend = async () => {
    if ((!inputValue.trim() && attachments.length === 0) || isLoading) return

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: inputValue.trim(),
      sender: 'user',
      timestamp: new Date(),
      attachments: attachments.length > 0 ? [...attachments] : undefined,
    }

    const newMessages = [...messages, userMessage]
    setMessages(newMessages)
    setInputValue('')
    setAttachments([])
    setIsLoading(true)

    const aiMessageId = `ai-${Date.now()}`
    const aiMessage: Message = {
      id: aiMessageId,
      content: '',
      sender: 'ai',
      timestamp: new Date(),
      isTyping: true,
    }

    setMessages((prev) => [...prev, aiMessage])

    try {
      await new Promise((resolve) => setTimeout(resolve, 400)) // 减少延迟

      const response = await generateAiResponse(inputValue.trim(), attachments)
      await typeMessage(response, aiMessageId)

      const finalMessages = [
        ...newMessages,
        { ...aiMessage, content: response, isTyping: false },
      ]
      saveChatHistory(finalMessages)
    } catch (error) {
      console.error('Error generating response:', error)
      const errorMessage = '抱歉，处理您的请求时出现了错误。请稍后再试。'

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === aiMessageId
            ? { ...msg, content: errorMessage, isTyping: false }
            : msg
        )
      )

      toast({
        title: '错误',
        description: 'AI 回复失败，请重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const generateAiResponse = async (
    userInput: string,
    fileAttachments: FileAttachment[]
  ): Promise<string> => {
    // 构建完整的消息内容，包含文件信息和用户输入
    let fullMessage = ''

    // 如果有文件附件，先处理文件信息
    if (fileAttachments.length > 0) {
      fullMessage += '用户上传了以下文件：\n\n'

      fileAttachments.forEach((file, index) => {
        fullMessage += `文件 ${index + 1}: ${file.name}\n`
        fullMessage += `大小: ${formatFileSize(file.size)}\n`
        fullMessage += `类型: ${file.type || '未知'}\n`

        if (file.type.startsWith('image/') && file.ocrText) {
          fullMessage += `OCR识别内容:\n${file.ocrText}\n`
        } else if (file.content) {
          const content =
            file.content.length > 2000
              ? file.content.substring(0, 2000) + '\n...(内容已截断)'
              : file.content
          fullMessage += `文件内容:\n${content}\n`
        }
        fullMessage += '\n'
      })
    }

    // 添加用户的问题
    if (userInput) {
      fullMessage += `用户问题: ${userInput}\n\n`
    }

    // 如果提供了sendMessage函数，调用真实API
    if (sendMessage) {
      try {
        const response = await sendMessage(fullMessage)
        return response
      } catch (error) {
        console.error('API调用失败:', error)
        return '抱歉，AI服务暂时不可用，请稍后再试。'
      }
    }

    // 如果没有提供sendMessage函数，返回基本的模拟回复
    if (fileAttachments.length > 0) {
      let response = '我已经分析了您上传的文件：\n\n'

      fileAttachments.forEach((file, index) => {
        response += `**文件 ${index + 1}: ${file.name}**\n`
        response += `• 大小: ${formatFileSize(file.size)}\n`
        response += `• 类型: ${file.type || '未知'}\n\n`

        if (file.type.startsWith('image/')) {
          if (file.ocrText) {
            response += `**OCR 识别结果：**\n`

            // 检测是否包含代码
            const codePatterns = [
              /function\s+\w+\s*\(/,
              /const\s+\w+\s*=/,
              /import\s+.*from/,
              /class\s+\w+/,
              /def\s+\w+\s*\(/,
              /SELECT\s+.*FROM/i,
              /\{[\s\S]*\}/,
              /if\s*$$.*$$\s*\{/,
            ]

            const isCode = codePatterns.some((pattern) =>
              pattern.test(file.ocrText!)
            )

            if (isCode) {
              // 尝试检测编程语言
              let language = 'text'
              if (
                file.ocrText.includes('function') ||
                file.ocrText.includes('const') ||
                file.ocrText.includes('import')
              ) {
                language = 'javascript'
              } else if (
                file.ocrText.includes('def ') ||
                file.ocrText.includes('import ')
              ) {
                language = 'python'
              } else if (
                file.ocrText.includes('SELECT') ||
                file.ocrText.includes('FROM')
              ) {
                language = 'sql'
              } else if (
                file.ocrText.includes('{') &&
                file.ocrText.includes('}')
              ) {
                language = 'json'
              }

              response += `识别到代码内容 (${language})：\n\n`
              response += `\`\`\`${language}\n${file.ocrText}\n\`\`\`\n\n`

              response += `**代码分析：**\n`
              if (language === 'javascript') {
                response += `• 检测到 JavaScript 代码\n`
                if (file.ocrText.includes('function'))
                  response += `• 包含函数定义\n`
                if (
                  file.ocrText.includes('const') ||
                  file.ocrText.includes('let')
                )
                  response += `• 使用现代 ES6+ 语法\n`
                if (
                  file.ocrText.includes('async') ||
                  file.ocrText.includes('await')
                )
                  response += `• 包含异步操作\n`
                response += `• 建议检查错误处理和类型验证\n`
              } else if (language === 'python') {
                response += `• 检测到 Python 代码\n`
                if (file.ocrText.includes('def '))
                  response += `• 包含函数定义\n`
                if (file.ocrText.includes('class '))
                  response += `• 包含类定义\n`
                response += `• 建议遵循 PEP 8 代码规范\n`
              } else if (language === 'sql') {
                response += `• 检测到 SQL 查询语句\n`
                response += `• 建议检查查询性能和索引优化\n`
              }
            } else {
              response += `识别到文本内容：\n\n`
              response += `\`\`\`\n${file.ocrText}\n\`\`\`\n\n`

              if (
                file.ocrText.includes('错误') ||
                file.ocrText.includes('Error') ||
                file.ocrText.includes('异常')
              ) {
                response += `**错误信息分析：**\n`
                response += `• 检测到错误或异常信息\n`
                response += `• 建议检查相关配置和网络连接\n`
                response += `• 可以提供具体的故障排查步骤\n`
              }
            }
          } else {
            response += `这是一个图片文件。OCR 文字识别功能可以帮助提取图片中的文字和代码内容。\n\n`
          }
        } else if (file.content) {
          // 原有的文件内容分析逻辑
          const content = file.content.substring(0, 1000)

          if (file.name.match(/\.(json)$/i)) {
            try {
              JSON.parse(file.content)
              response += `**JSON 文件分析：**\n`
              response += `• 格式正确，JSON 结构有效\n`
              response += `• 文件大小: ${formatFileSize(file.size)}\n`

              if (
                content.includes('"device"') ||
                content.includes('"protocol"')
              ) {
                response += `• 检测到设备配置相关内容\n`
                response += `• 建议检查协议参数和地址配置\n`
              }

              response += `\n\`\`\`json\n${content}${
                file.content.length > 1000 ? '\n...(内容已截断)' : ''
              }\n\`\`\`\n\n`
            } catch (e) {
              response += `**JSON 格式错误：**\n• 文件包含语法错误，请检查格式\n\n`
            }
          } else if (file.name.match(/\.(js|ts|jsx|tsx)$/i)) {
            response += `**JavaScript/TypeScript 代码分析：**\n`

            if (content.includes('function') || content.includes('=>')) {
              response += `• 检测到函数定义\n`
            }
            if (content.includes('import') || content.includes('require')) {
              response += `• 包含模块导入\n`
            }
            if (content.includes('async') || content.includes('await')) {
              response += `• 使用异步编程模式\n`
            }

            response += `\n\`\`\`javascript\n${content}${
              file.content.length > 1000 ? '\n...(内容已截断)' : ''
            }\n\`\`\`\n\n`
            response += `**建议：**\n• 检查代码规范和最佳实践\n• 考虑添加错误处理\n• 优化性能和可读性\n\n`
          }
        }
      })

      if (userInput) {
        response += `**您的问题：** ${userInput}\n\n`
        response += `基于上传的文件和您的问题，我建议：\n`
        response += `• 仔细检查文件中的配置参数\n`
        response += `• 验证数据格式和结构\n`
        response += `• 如需具体分析，请告诉我关注点\n`
      }

      return response
    }

    // 原有的文本回复逻辑 - 作为fallback
    const lowerInput = userInput.toLowerCase()

    if (
      lowerInput.includes('ocr') ||
      lowerInput.includes('图片') ||
      lowerInput.includes('识别')
    ) {
      return `关于 OCR 图片文字识别功能：

**功能特点：**
• 自动识别图片中的文字内容
• 智能检测代码片段和编程语言
• 支持多种图片格式 (.jpg, .png, .gif, .svg)
• 提供代码格式化和语法高亮

**支持识别的内容：**
• JavaScript/TypeScript 代码
• Python 代码
• SQL 查询语句
• JSON 配置文件
• 错误信息和日志
• 技术文档和说明

**使用方法：**
1. 点击回形针图标 📎
2. 选择包含文字或代码的图片
3. 等待 OCR 自动识别完成
4. AI 会分析识别结果并提供建议

**最佳实践：**
• 确保图片清晰，文字可读
• 避免复杂背景和干扰元素
• 代码截图建议使用深色主题
• 单次上传文件不超过 10MB

试试上传一张包含代码或文字的图片，我来帮你分析！`
    }

    if (lowerInput.includes('代码') || lowerInput.includes('code')) {
      return `我来帮你分析代码！支持多种方式：

**1. 直接上传代码文件：**
• 支持 .js, .ts, .py, .java, .cpp 等
• 自动语法检查和优化建议
• 代码规范和最佳实践分析

**2. 上传代码截图：**
• OCR 自动识别图片中的代码
• 智能检测编程语言
• 提供格式化和语法高亮

**3. 直接粘贴代码：**
• 在对话框中直接输入代码
• 实时分析和建议

**代码分析示例：**

\`\`\`javascript
// 优化前：回调地狱
function fetchData(callback) {
  getData((data) => {
    processData(data, (result) => {
      saveResult(result, callback);
    });
  });
}

// 优化后：使用 async/await
async function fetchData() {
  try {
    const data = await getData();
    const result = await processData(data);
    return await saveResult(result);
  } catch (error) {
    console.error('处理失败:', error);
    throw error;
  }
}
\`\`\`

你可以上传代码文件或截图，我会帮你进行详细分析！`
    }

    return `我理解你的问题："${userInput}"

**我可以帮你分析：**
• 上传代码文件进行代码审查
• 分析配置文件的正确性
• 检查数据文件的格式
• **OCR 识别图片中的文字和代码** 📸
• 解读技术文档和错误信息

**支持的文件类型：**
• 代码文件 (.js, .ts, .py, .java, .cpp)
• 配置文件 (.json, .xml, .yaml, .ini)
• 数据文件 (.csv, .txt, .md)
• 图片文件 (.jpg, .png, .svg) - **支持 OCR**

**OCR 识别功能：**
• 自动识别图片中的代码和文字
• 智能检测编程语言类型
• 提供代码分析和优化建议
• 支持错误信息和日志分析

请上传文件或图片，我将给出针对性的分析和建议！`
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: '已复制',
      description: '内容已复制到剪贴板',
    })
  }

  const renderMessageContent = (content: string) => {
    if (!content) return null

    const normalizedContent = content
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')

    // 优化：使用更高效的正则表达式
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g
    const parts = normalizedContent.split(codeBlockRegex)
    const elements = []

    for (let i = 0; i < parts.length; i += 3) {
      // 文本部分
      if (parts[i]) {
        const textContent = parts[i]
        const textParts = textContent.split('\n')
        elements.push(
          <div key={`text-${i}`} className="whitespace-pre-wrap">
            {textParts.map((line, lineIndex) => (
              <div key={lineIndex}>
                {line.split(/(\*\*.*?\*\*|• .*?)/).map((part, partIndex) => {
                  if (part.startsWith('**') && part.endsWith('**')) {
                    return (
                      <strong
                        key={partIndex}
                        className="font-semibold text-gray-900">
                        {part.slice(2, -2)}
                      </strong>
                    )
                  }
                  if (part.startsWith('• ')) {
                    return (
                      <div
                        key={partIndex}
                        className="flex items-start gap-2 my-1">
                        <span className="text-blue-500 mt-1">•</span>
                        <span>{part.slice(2)}</span>
                      </div>
                    )
                  }
                  // 支持行内代码
                  if (part.includes('`') && !part.includes('```')) {
                    return part
                      .split(/(`[^`]+`)/)
                      .map((codePart, codeIndex) => {
                        if (
                          codePart.startsWith('`') &&
                          codePart.endsWith('`')
                        ) {
                          return (
                            <code
                              key={codeIndex}
                              className="bg-gray-100 px-1 rounded text-sm font-mono">
                              {codePart.slice(1, -1)}
                            </code>
                          )
                        }
                        return codePart
                      })
                  }
                  return part
                })}
              </div>
            ))}
          </div>
        )
      }

      // 代码块部分
      if (i + 2 < parts.length && parts[i + 2]) {
        const language = parts[i + 1] || 'javascript' // 默认为JavaScript
        const code = parts[i + 2]

        // 语言别名处理
        const languageMap: { [key: string]: string } = {
          js: 'javascript',
          ts: 'typescript',
          py: 'python',
          rb: 'ruby',
          cpp: 'c++',
          c: 'c',
          java: 'java',
          php: 'php',
          go: 'go',
          rs: 'rust',
          sh: 'bash',
          sql: 'sql',
          html: 'html',
          css: 'css',
          json: 'json',
          xml: 'xml',
          yaml: 'yaml',
          yml: 'yaml',
        }

        const displayLanguage =
          languageMap[language.toLowerCase()] || language || 'javascript'

        elements.push(
          <div key={`code-${i}`} className="my-4 relative group">
            <div className="flex items-center justify-between bg-gray-800 text-white px-4 py-2 rounded-t-lg">
              <span className="text-sm font-medium">{displayLanguage}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(code)}
                className="text-white hover:bg-gray-700 opacity-0 group-hover:opacity-100 transition-opacity">
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-b-lg overflow-x-auto max-h-96">
              <code className="text-sm font-mono leading-relaxed">{code}</code>
            </pre>
          </div>
        )
      }
    }

    return <div className="space-y-2">{elements}</div>
  }

  const renderAttachments = (attachments?: FileAttachment[]) => {
    if (!attachments || attachments.length === 0) return null

    return (
      <div className="mt-2 space-y-2">
        {attachments.map((attachment) => (
          <div
            key={attachment.id}
            className="flex items-center gap-2 p-2 bg-gray-100 rounded-lg">
            {getFileIcon(attachment.name, attachment.type)}
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium truncate">
                {attachment.name}
              </div>
              <div className="text-xs text-gray-500">
                {formatFileSize(attachment.size)}
                {attachment.isProcessingOCR && (
                  <span className="ml-2 text-blue-500">• OCR 识别中...</span>
                )}
                {attachment.ocrText && (
                  <span className="ml-2 text-green-500">• 已识别文字</span>
                )}
              </div>
            </div>
            {attachment.url && (
              <img
                src={attachment.url || '/placeholder.svg'}
                alt={attachment.name}
                className="w-8 h-8 object-cover rounded"
              />
            )}
            {attachment.isProcessingOCR && (
              <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
            )}
          </div>
        ))}
      </div>
    )
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  // 应用建议到输入框
  const applySuggestion = (suggestion: string) => {
    setInputValue(suggestion)
    setTimeout(() => {
      inputRef.current?.focus()
    }, 100)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl w-[90vw] h-[80vh] max-h-[800px] flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              {title}
              <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                OCR 识别
              </span>
            </DialogTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={exportChatHistory}
                disabled={messages.length === 0}
                title="导出聊天记录">
                <Download className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearChatHistory}
                disabled={messages.length === 0}
                title="清空聊天记录">
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 flex flex-col min-h-0">
          <ScrollArea className="flex-1 px-6" ref={scrollAreaRef}>
            <div className="space-y-4 py-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-3 ${
                    message.sender === 'user' ? 'justify-end' : 'justify-start'
                  }`}>
                  {message.sender === 'ai' && (
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 mt-1">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                  )}

                  <div
                    className={`max-w-[80%] rounded-lg p-4 ${
                      message.sender === 'user'
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                        : 'bg-gray-50 text-gray-900 border border-gray-200'
                    }`}>
                    <div className="text-sm">
                      {message.sender === 'user' ? (
                        <>
                          <div className="whitespace-pre-wrap">
                            {message.content}
                          </div>
                          {renderAttachments(message.attachments)}
                        </>
                      ) : (
                        renderMessageContent(message.content)
                      )}
                      {message.isTyping && (
                        <span className="inline-block w-2 h-4 ml-1 bg-gray-400 animate-pulse rounded" />
                      )}
                    </div>
                    <div
                      className={`text-xs mt-2 ${
                        message.sender === 'user'
                          ? 'text-blue-100'
                          : 'text-gray-500'
                      }`}>
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </div>

                  {message.sender === 'user' && (
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center flex-shrink-0 mt-1">
                      <User className="h-4 w-4 text-white" />
                    </div>
                  )}
                </div>
              ))}

              {isLoading && (
                <div className="flex gap-3 justify-start">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                          style={{ animationDelay: '0.1s' }}></div>
                        <div
                          className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                          style={{ animationDelay: '0.2s' }}></div>
                      </div>
                      <span className="text-sm text-gray-500">
                        {attachments.length > 0
                          ? 'AI 正在分析文件...'
                          : 'AI 正在思考...'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* 建议按钮区域 - 只在初始状态显示 */}
          {messages.length <= 1 && suggestions.length > 0 && (
            <div className="px-6 py-4 border-t border-b bg-gray-50">
              <div className="text-sm text-gray-600 mb-3">
                💡 试试这些问题：
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {suggestions.map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => applySuggestion(suggestion)}
                    className="justify-start text-left h-auto py-2 px-3 text-sm">
                    {suggestion}
                  </Button>
                ))}
              </div>
            </div>
          )}

          <div className="px-6 py-4 border-t bg-gray-50">
            {/* 附件预览 */}
            {attachments.length > 0 && (
              <div className="mb-3 space-y-2">
                {attachments.map((attachment) => (
                  <div
                    key={attachment.id}
                    className="flex items-center gap-2 p-2 bg-white border rounded-lg">
                    {getFileIcon(attachment.name, attachment.type)}
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium truncate">
                        {attachment.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatFileSize(attachment.size)}
                        {attachment.isProcessingOCR && (
                          <span className="ml-2 text-blue-500">
                            • OCR 识别中...
                          </span>
                        )}
                        {attachment.ocrText && (
                          <span className="ml-2 text-green-500">
                            • 已识别文字
                          </span>
                        )}
                      </div>
                    </div>
                    {attachment.url && (
                      <img
                        src={attachment.url || '/placeholder.svg'}
                        alt={attachment.name}
                        className="w-8 h-8 object-cover rounded"
                      />
                    )}
                    {attachment.isProcessingOCR ? (
                      <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAttachment(attachment.id)}
                        className="text-gray-500 hover:text-red-500">
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            )}

            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={isLoading}
                className="px-3"
                title="上传文件 (支持 OCR 图片识别)">
                <Paperclip className="h-4 w-4" />
              </Button>
              <Input
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="输入你的问题或上传图片进行 OCR 识别..."
                onKeyPress={handleKeyPress}
                disabled={isLoading}
                className="flex-1 bg-white"
              />
              <Button
                onClick={handleSend}
                disabled={
                  isLoading || (!inputValue.trim() && attachments.length === 0)
                }
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700">
                <Send className="h-4 w-4" />
              </Button>
            </div>
            <div className="text-xs text-gray-500 mt-2">
              按 Enter 发送，支持上传图片进行 OCR 文字识别 (最大 10MB)
            </div>
          </div>
        </div>

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".txt,.md,.json,.xml,.yaml,.yml,.ini,.conf,.js,.ts,.jsx,.tsx,.py,.java,.cpp,.c,.php,.rb,.go,.rs,.csv,.jpg,.jpeg,.png,.gif,.svg"
          onChange={handleFileUpload}
          className="hidden"
        />
      </DialogContent>
    </Dialog>
  )
}
