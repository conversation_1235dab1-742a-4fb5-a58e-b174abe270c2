import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Search,
  Filter,
  Download,
  Calendar,
  Clock,
  User,
  Monitor,
  Shield,
  Workflow,
  Server,
} from 'lucide-react'

interface ActivityLogItem {
  id: string
  type: 'login' | 'workflow' | 'device' | 'system' | 'security'
  action: string
  timestamp: string
  details: string
  ip?: string
  device?: string
  browser?: string
}

export function ActivityLog() {
  const [loading, setLoading] = useState(true)
  const [activities, setActivities] = useState<ActivityLogItem[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')

  useEffect(() => {
    const fetchActivities = async () => {
      setLoading(true)
      try {
        // 模拟API请求
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // 模拟活动日志数据
        const mockActivities: ActivityLogItem[] = [
          {
            id: 'act-001',
            type: 'login',
            action: '登录成功',
            timestamp: '2025-04-21 09:15:32',
            details: '通过用户名密码登录系统',
            ip: '*************',
            device: 'Windows 10',
            browser: 'Chrome 120.0.0',
          },
          {
            id: 'act-002',
            type: 'workflow',
            action: '创建工作流',
            timestamp: '2025-04-21 10:23:45',
            details: "创建了新工作流 '客户数据同步'",
          },
          {
            id: 'act-003',
            type: 'device',
            action: '添加设备',
            timestamp: '2025-04-20 14:05:12',
            details: "添加了新设备 '温度传感器 A1'",
          },
          {
            id: 'act-004',
            type: 'system',
            action: '修改系统设置',
            timestamp: '2025-04-20 11:30:18',
            details: '修改了系统网络配置',
          },
          {
            id: 'act-005',
            type: 'security',
            action: '修改密码',
            timestamp: '2025-04-19 16:42:09',
            details: '成功修改了账户密码',
            ip: '*************',
          },
          {
            id: 'act-006',
            type: 'login',
            action: '登录失败',
            timestamp: '2025-04-19 08:10:23',
            details: '密码错误，登录失败',
            ip: '************',
            device: 'Unknown',
            browser: 'Unknown',
          },
          {
            id: 'act-007',
            type: 'workflow',
            action: '执行工作流',
            timestamp: '2025-04-18 15:20:37',
            details: "手动触发执行工作流 '订单处理流程'",
          },
          {
            id: 'act-008',
            type: 'device',
            action: '设备配置更新',
            timestamp: '2025-04-18 13:15:42',
            details: "更新了设备 '湿度传感器 B2' 的配置",
          },
          {
            id: 'act-009',
            type: 'security',
            action: '启用双因素认证',
            timestamp: '2025-04-17 10:05:19',
            details: '为账户启用了双因素认证',
            ip: '*************',
          },
          {
            id: 'act-010',
            type: 'system',
            action: '系统更新',
            timestamp: '2025-04-16 09:30:00',
            details: '系统自动更新到版本 v2.3.1',
          },
        ]

        setActivities(mockActivities)
      } catch (error) {
        console.error('获取活动日志失败:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchActivities()
  }, [])

  // 过滤活动日志
  const filteredActivities = activities.filter((activity) => {
    // 搜索过滤
    const matchesSearch =
      searchQuery === '' ||
      activity.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.details.toLowerCase().includes(searchQuery.toLowerCase())

    // 类型过滤
    const matchesType = typeFilter === 'all' || activity.type === typeFilter

    // 日期过滤
    let matchesDate = true
    const activityDate = new Date(activity.timestamp)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    const lastWeek = new Date(today)
    lastWeek.setDate(lastWeek.getDate() - 7)
    const lastMonth = new Date(today)
    lastMonth.setMonth(lastMonth.getMonth() - 1)

    if (dateFilter === 'today') {
      matchesDate = activityDate.toDateString() === today.toDateString()
    } else if (dateFilter === 'yesterday') {
      matchesDate = activityDate.toDateString() === yesterday.toDateString()
    } else if (dateFilter === 'week') {
      matchesDate = activityDate >= lastWeek
    } else if (dateFilter === 'month') {
      matchesDate = activityDate >= lastMonth
    }

    return matchesSearch && matchesType && matchesDate
  })

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <User className="h-4 w-4" />
      case 'workflow':
        return <Workflow className="h-4 w-4" />
      case 'device':
        return <Server className="h-4 w-4" />
      case 'system':
        return <Monitor className="h-4 w-4" />
      case 'security':
        return <Shield className="h-4 w-4" />
      default:
        return <User className="h-4 w-4" />
    }
  }

  const getActivityTypeBadge = (type: string) => {
    switch (type) {
      case 'login':
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 hover:bg-blue-50">
            登录
          </Badge>
        )
      case 'workflow':
        return (
          <Badge
            variant="outline"
            className="bg-purple-50 text-purple-700 hover:bg-purple-50">
            工作流
          </Badge>
        )
      case 'device':
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 hover:bg-green-50">
            设备
          </Badge>
        )
      case 'system':
        return (
          <Badge
            variant="outline"
            className="bg-amber-50 text-amber-700 hover:bg-amber-50">
            系统
          </Badge>
        )
      case 'security':
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 hover:bg-red-50">
            安全
          </Badge>
        )
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>活动日志</CardTitle>
        <CardDescription>查看您的账户活动历史记录</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索活动..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[130px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="活动类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                <SelectItem value="login">登录</SelectItem>
                <SelectItem value="workflow">工作流</SelectItem>
                <SelectItem value="device">设备</SelectItem>
                <SelectItem value="system">系统</SelectItem>
                <SelectItem value="security">安全</SelectItem>
              </SelectContent>
            </Select>
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-[130px]">
                <Calendar className="mr-2 h-4 w-4" />
                <SelectValue placeholder="时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有时间</SelectItem>
                <SelectItem value="today">今天</SelectItem>
                <SelectItem value="yesterday">昨天</SelectItem>
                <SelectItem value="week">最近一周</SelectItem>
                <SelectItem value="month">最近一个月</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList className="w-full">
            <TabsTrigger value="all" className="flex-1">
              所有活动
            </TabsTrigger>
            <TabsTrigger value="login" className="flex-1">
              登录活动
            </TabsTrigger>
            <TabsTrigger value="security" className="flex-1">
              安全活动
            </TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="mt-4">
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : filteredActivities.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                未找到匹配的活动记录
              </div>
            ) : (
              <div className="space-y-4">
                {filteredActivities.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                    <div className="mr-4 mt-1">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        {getActivityIcon(activity.type)}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-1">
                        <div className="font-medium flex items-center gap-2">
                          {activity.action}
                          {getActivityTypeBadge(activity.type)}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center gap-1">
                          <Calendar className="h-3.5 w-3.5" />
                          <span>{activity.timestamp}</span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {activity.details}
                      </p>
                      {activity.ip && (
                        <div className="mt-2 text-xs text-muted-foreground">
                          <span className="inline-block mr-4">
                            IP: {activity.ip}
                          </span>
                          {activity.device && (
                            <span className="inline-block mr-4">
                              设备: {activity.device}
                            </span>
                          )}
                          {activity.browser && (
                            <span className="inline-block">
                              浏览器: {activity.browser}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
          <TabsContent value="login" className="mt-4">
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredActivities
                  .filter((activity) => activity.type === 'login')
                  .map((activity) => (
                    <div
                      key={activity.id}
                      className="flex border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <div className="mr-4 mt-1">
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <User className="h-4 w-4" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-1">
                          <div className="font-medium">{activity.action}</div>
                          <div className="text-sm text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3.5 w-3.5" />
                            <span>{activity.timestamp}</span>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {activity.details}
                        </p>
                        {activity.ip && (
                          <div className="mt-2 text-xs text-muted-foreground">
                            <span className="inline-block mr-4">
                              IP: {activity.ip}
                            </span>
                            {activity.device && (
                              <span className="inline-block mr-4">
                                设备: {activity.device}
                              </span>
                            )}
                            {activity.browser && (
                              <span className="inline-block">
                                浏览器: {activity.browser}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </TabsContent>
          <TabsContent value="security" className="mt-4">
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredActivities
                  .filter((activity) => activity.type === 'security')
                  .map((activity) => (
                    <div
                      key={activity.id}
                      className="flex border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <div className="mr-4 mt-1">
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <Shield className="h-4 w-4" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-1">
                          <div className="font-medium">{activity.action}</div>
                          <div className="text-sm text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3.5 w-3.5" />
                            <span>{activity.timestamp}</span>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {activity.details}
                        </p>
                        {activity.ip && (
                          <div className="mt-2 text-xs text-muted-foreground">
                            <span className="inline-block mr-4">
                              IP: {activity.ip}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
