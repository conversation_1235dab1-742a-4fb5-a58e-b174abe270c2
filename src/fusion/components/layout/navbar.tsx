import { DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { useConfigStore } from '@/lib/config/config-store'
import { useTheme } from 'next-themes'
import { useLockScreen } from '@/lib/lock-screen/lock-screen-context'

import { Link } from 'react-router-dom'
import { Bell, Menu, User, LogOut, Lock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { useState, useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { X, ChevronDown, LayoutDashboard } from 'lucide-react'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  Server,
  Database,
  Activity,
  Workflow,
  BarChart2,
  Settings,
  List,
  BellRing,
  CalendarClock,
  Share,
  Info,
  RefreshCw,
  FileText,
  Key,
  Sliders,
  BarChart,
  AlertTriangle,
  Clock,
  TrendingUp,
  HardDrive,
  Plus,
  FileCode,
  Monitor,
  Users,
  Shield,
  UserCheck,
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { FusionTrackLogo } from '@/components/fusion-track-logo'

export function Navbar() {
  const location = useLocation()
  const pathname = location.pathname
  const [openSubmenus, setOpenSubmenus] = useState<Record<string, boolean>>({})
  const [openModules, setOpenModules] = useState<string[]>([])
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false)
  const { theme } = useTheme()
  const { config } = useConfigStore()
  const { lockScreen } = useLockScreen()

  // 获取当前主题下的logo
  const getLogo = () => {
    if (theme === 'dark' && config.darkLogoUrl) {
      return config.darkLogoUrl
    }
    return config.logoUrl
  }

  // 通知状态
  const [notifications, setNotifications] = useState<
    Array<{
      id: number
      title: string
      message: string
      time: string
      read: boolean
      type: 'critical' | 'success' | 'info'
    }>
  >([])

  // 标记通知为已读
  const markAsRead = (id: number) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    )
  }

  // 标记所有通知为已读
  const markAllAsRead = () => {
    setNotifications(
      notifications.map((notification) => ({ ...notification, read: true }))
    )
  }

  // 计算未读通知数量
  const unreadCount = notifications.filter(
    (notification) => !notification.read
  ).length

  // 主导航菜单
  const navigation = [
    { name: '仪表盘', to: '/dashboard', icon: LayoutDashboard },
    {
      name: '数据采集',
      to: '/devices/data-collection',
      icon: Server,
      submenu: [
        { name: '设备列表', to: '/devices', icon: List },
        { name: '设备报警', to: '/devices/alarms', icon: BellRing, inDevelopment: true },
        { name: '设备事件', to: '/devices/events', icon: CalendarClock, inDevelopment: true },
      ],
    },
    {
      name: '数据转发',
      to: '/data-forwarding',
      icon: Share,
      submenu: [
        { name: '配置管理', to: '/data-forwarding', icon: Settings },
        {
          name: '数据统计',
          to: '/data-forwarding/statistics',
          icon: TrendingUp,
        },
        {
          name: '离线数据',
          to: '/data-forwarding/offline-data',
          icon: HardDrive,
        },
        { name: '新增配置', to: '/data-forwarding/add', icon: Plus },
      ],
    },
    {
      name: '工作流编排',
      to: '/workflows',
      icon: Workflow,
      submenu: [
        { name: '工作流管理', to: '/workflows', icon: Workflow },
        { name: '模板管理', to: '/workflows/templates', icon: FileCode },
        { name: '监控中心', to: '/workflows/monitoring', icon: Monitor },
      ],
    },
    {
      name: '任务中心',
      to: '/task-center',
      icon: CalendarClock,
      submenu: [
        { name: '任务概览', to: '/task-center', icon: LayoutDashboard },
        { name: '定时任务', to: '/task-center/scheduled-tasks', icon: Clock },
        { name: '任务配置', to: '/task-center/task-config', icon: Settings },
      ],
    },
    {
      name: '数据监控',
      to: '/monitoring',
      icon: Activity,
      submenu: [
        { name: '监控仪表盘', to: '/monitoring', icon: LayoutDashboard },
        { name: '数据库管理', to: '/monitoring/database', icon: Database },
      ],
    },
    { name: '数据分析', to: '/analytics', icon: BarChart2 },
    {
      to: '/data-history',
      name: '历史数据',
      icon: BarChart,
    },
    {
      name: '权限管理',
      to: '/system',
      icon: Shield,
      submenu: [
        { name: '用户管理', to: '/system/users', icon: Users },
        { name: '角色管理', to: '/system/roles', icon: Shield },
        { name: '菜单管理', to: '/system/menus', icon: Menu },
        { name: '权限配置', to: '/system/permissions', icon: Key },
        { name: '在线用户', to: '/system/online-users', icon: UserCheck },
        { name: '系统日志', to: '/system/logs', icon: FileText },
      ],
    },
    {
      name: '系统设置',
      to: '/settings',
      icon: Settings,
      submenu: [
        { name: '网络配置', to: '/settings/network', icon: Share },
        { name: '系统信息', to: '/settings/info', icon: Info },
        { name: '系统更新', to: '/settings/update', icon: RefreshCw },
        { name: '系统日志', to: '/settings/logs', icon: FileText },
        { name: '系统配置', to: '/settings/config', icon: Settings },
        { name: '系统授权', to: '/settings/auth', icon: Key },
        { name: '参数配置', to: '/settings/params', icon: Sliders },
        { name: '系统服务', to: '/settings/services', icon: Server },
        { name: '品牌设置', to: '/settings/branding', icon: Settings },
      ],
    },
  ]

  // 确定当前活动的主模块
  const getCurrentModule = () => {
    // 始终包含仪表盘
    const dashboardModule = navigation.find(
      (item) => item.name === '仪表盘'
    ) || {
      name: '仪表盘',
      to: '/dashboard',
      icon: LayoutDashboard,
    }

    // 查找当前路径匹配的模块
    const currentPathModule = navigation.find((item) => {
      // 首先检查主模块路径
      if (pathname?.startsWith(item.to) && item.to !== '/dashboard') {
        return true
      }

      // 然后检查子菜单路径
      if (item.submenu) {
        return item.submenu.some(
          (subItem) =>
            pathname === subItem.to || pathname?.startsWith(`${subItem.to}/`)
        )
      }

      return false
    })

    return currentPathModule || dashboardModule
  }

  const currentModule = getCurrentModule()

  // 添加或移除打开的模块
  const toggleOpenModule = (moduleName: string) => {
    setOpenModules((prev) =>
      prev.includes(moduleName)
        ? prev.filter((name) => name !== moduleName)
        : [...prev, moduleName]
    )
  }

  // 切换子菜单的展开/折叠状态
  const toggleSubmenu = (name: string) => {
    setOpenSubmenus((prev) => ({
      ...prev,
      [name]: !prev[name],
    }))
  }

  // 当路径变化时，自动展开当前活动的子菜单
  useEffect(() => {
    navigation.forEach((item) => {
      if (item.submenu && pathname?.startsWith(item.to)) {
        setOpenSubmenus((prev) => ({
          ...prev,
          [item.name]: true,
        }))
      }
    })
  }, [pathname])

  const cn = (...inputs: any[]) => {
    return inputs.filter(Boolean).join(' ')
  }

  // 渲染Logo
  const renderLogo = () => {
    const logoUrl = getLogo()

    // 如果有自定义logo，使用img标签显示
    if (logoUrl && logoUrl !== '/logo.svg' && logoUrl !== '/logo-dark.svg') {
      return (
        <div className="h-8 w-auto relative">
          <img
            src={logoUrl || '/placeholder.svg'}
            alt={config.systemName || '系统Logo'}
            className="h-8 w-auto object-contain"
          />
        </div>
      )
    }

    // 否则使用默认的FusionTrackLogo组件
    return <FusionTrackLogo size="md" />
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="w-full px-4 flex h-16 items-center justify-between">
        <div className="flex items-center">
          <div className="mr-4 flex md:hidden">
            <Sheet open={isMobileNavOpen} onOpenChange={setIsMobileNavOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">打开菜单</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[240px] sm:w-[300px]">
                <div className="flex h-full flex-col">
                  <div className="flex items-center border-b py-4">
                    <Link
                      to="/"
                      className="flex items-center gap-2 font-semibold"
                      onClick={() => setIsMobileNavOpen(false)}>
                      {renderLogo()}
                      <span className="text-sm">
                        {config.systemName || '工业边缘网关'}
                      </span>
                    </Link>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="ml-auto"
                      onClick={() => setIsMobileNavOpen(false)}>
                      <X className="h-5 w-5" />
                      <span className="sr-only">关闭菜单</span>
                    </Button>
                  </div>
                  <nav className="flex-1 overflow-auto py-4">
                    <ul className="space-y-2 px-2">
                      {navigation.map((item) => {
                        const isActive = pathname?.startsWith(item.to)

                        if (item.submenu) {
                          return (
                            <li key={item.name}>
                              <Collapsible
                                open={openSubmenus[item.name]}
                                onOpenChange={() => toggleSubmenu(item.name)}>
                                <CollapsibleTrigger asChild>
                                  <button
                                    className={cn(
                                      'flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-medium',
                                      isActive
                                        ? 'bg-primary text-primary-foreground'
                                        : 'hover:bg-muted'
                                    )}>
                                    <div className="flex items-center">
                                      <item.icon className="mr-3 h-5 w-5" />
                                      {item.name}
                                    </div>
                                    <ChevronDown
                                      className={cn(
                                        'h-4 w-4 transition-transform',
                                        openSubmenus[item.name]
                                          ? 'rotate-180'
                                          : ''
                                      )}
                                    />
                                  </button>
                                </CollapsibleTrigger>
                                <CollapsibleContent>
                                  <ul className="mt-1 space-y-1 pl-9">
                                    {item.submenu.map((subItem) => {
                                      const isSubActive =
                                        pathname === subItem.to ||
                                        pathname?.startsWith(`${subItem.to}/`)

                                      // 如果是开发中状态，渲染禁用的项目
                                      if (subItem.inDevelopment) {
                                        return (
                                          <li key={subItem.name}>
                                            <div
                                              className={cn(
                                                'flex items-center gap-2 rounded-md px-3 py-2 text-sm cursor-not-allowed opacity-60',
                                                'text-muted-foreground'
                                              )}>
                                              {subItem.icon && (
                                                <subItem.icon className="h-4 w-4" />
                                              )}
                                              <span>{subItem.name}</span>
                                              <Badge variant="secondary" className="ml-auto text-xs">
                                                开发中
                                              </Badge>
                                            </div>
                                          </li>
                                        )
                                      }

                                      return (
                                        <li key={subItem.name}>
                                          <Link
                                            to={subItem.to}
                                            className={cn(
                                              'flex items-center gap-2 rounded-md px-3 py-2 text-sm',
                                              isSubActive
                                                ? 'bg-primary/10 font-medium text-primary'
                                                : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                                            )}
                                            onClick={() =>
                                              setIsMobileNavOpen(false)
                                            }>
                                            {subItem.icon && (
                                              <subItem.icon className="h-4 w-4" />
                                            )}
                                            {subItem.name}
                                          </Link>
                                        </li>
                                      )
                                    })}
                                  </ul>
                                </CollapsibleContent>
                              </Collapsible>
                            </li>
                          )
                        }

                        return (
                          <li key={item.name}>
                            <Link
                              to={item.to}
                              className={cn(
                                'flex items-center rounded-md px-3 py-2 text-sm font-medium',
                                isActive
                                  ? 'bg-primary text-primary-foreground'
                                  : 'hover:bg-muted'
                              )}
                              onClick={() => setIsMobileNavOpen(false)}>
                              <item.icon className="mr-3 h-5 w-5" />
                              {item.name}
                            </Link>
                          </li>
                        )
                      })}
                    </ul>
                  </nav>
                </div>
              </SheetContent>
            </Sheet>
          </div>
          <div className="flex items-center space-x-2">
            <Link to="/" className="flex items-center space-x-2">
              {renderLogo()}
              <span className="hidden md:inline-block font-medium text-sm">
                {config.systemName || '工业边缘网关'}
              </span>
            </Link>
          </div>
        </div>
        <nav className="hidden flex-1 items-center space-x-1 md:flex md:ml-6">
          {/* 仪表盘始终显示*/}
          {navigation.find((item) => item.name === '仪表盘') && (
            <Link
              to="/dashboard"
              className={cn(
                'flex items-center rounded-md px-3 py-2 text-sm font-medium',
                pathname === '/dashboard'
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-muted'
              )}>
              <LayoutDashboard className="mr-2 h-4 w-4" />
              仪表盘
            </Link>
          )}

          {/* 当前模块的菜单项 */}
          {currentModule && currentModule.name !== '仪表板' && (
            <>
              <div className="flex items-center">
                <Link
                  to={currentModule.to}
                  className={cn(
                    'flex items-center rounded-md px-3 py-2 text-sm font-medium',
                    pathname === currentModule.to ||
                      (currentModule.submenu &&
                        currentModule.submenu.some(
                          (subItem) =>
                            pathname === subItem.to ||
                            pathname?.startsWith(`${subItem.to}/`)
                        ))
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-muted'
                  )}>
                  <currentModule.icon className="mr-2 h-4 w-4" />
                  {currentModule.name}
                </Link>
              </div>

              {/* 平铺展示子菜单 */}
              {currentModule.submenu && (
                <div className="flex items-center ml-2 border-l pl-2">
                  {currentModule.submenu.map((subItem) => {
                    const isSubActive =
                      pathname === subItem.to ||
                      pathname?.startsWith(`${subItem.to}/`)

                    // 如果是开发中状态，渲染禁用的项目
                    if (subItem.inDevelopment) {
                      return (
                        <div
                          key={subItem.name}
                          className={cn(
                            'flex items-center rounded-md px-2 py-1 text-xs font-medium mx-1 cursor-not-allowed opacity-60',
                            'text-muted-foreground'
                          )}>
                          {subItem.icon && (
                            <subItem.icon className="mr-1 h-3 w-3" />
                          )}
                          <span className="mr-1">{subItem.name}</span>
                          <Badge variant="secondary" className="text-[10px] px-1 py-0">
                            开发中
                          </Badge>
                        </div>
                      )
                    }

                    return (
                      <Link
                        key={subItem.name}
                        to={subItem.to}
                        className={cn(
                          'flex items-center rounded-md px-2 py-1 text-xs font-medium mx-1',
                          isSubActive
                            ? 'bg-primary/10 text-primary'
                            : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                        )}>
                        {subItem.icon && (
                          <subItem.icon className="mr-1 h-3 w-3" />
                        )}
                        {subItem.name}
                      </Link>
                    )
                  })}
                </div>
              )}
            </>
          )}

          {/* 用户打开的其他模块 */}
          {openModules.map((moduleName) => {
            const module = navigation.find((item) => item.name === moduleName)
            if (
              !module ||
              module.name === currentModule.name ||
              module.name === '仪表板'
            )
              return null

            // 修复：检查主模块路径或子菜单路径
            const isActive =
              pathname?.startsWith(module.to) ||
              (module.submenu &&
                module.submenu.some(
                  (subItem) =>
                    pathname === subItem.to ||
                    pathname?.startsWith(`${subItem.to}/`)
                ))

            return (
              <div key={module.name} className="relative group">
                <div className="flex items-center">
                  <Link
                    to={module.to}
                    className={cn(
                      'flex items-center rounded-md px-3 py-2 text-sm font-medium',
                      isActive
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-muted'
                    )}>
                    <module.icon className="mr-2 h-4 w-4" />
                    {module.name}
                  </Link>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 p-0 h-5 w-5 rounded-full opacity-70 hover:opacity-100"
                    onClick={() => toggleOpenModule(module.name)}>
                    <X className="h-3 w-3" />
                    <span className="sr-only">关闭</span>
                  </Button>
                </div>

                {/* 平铺展示子菜单 */}
                {module.submenu && isActive && (
                  <div className="flex items-center ml-2 border-l pl-2">
                    {module.submenu.map((subItem) => {
                      const isSubActive =
                        pathname === subItem.to ||
                        pathname?.startsWith(`${subItem.to}/`)

                      // 如果是开发中状态，渲染禁用的项目
                      if (subItem.inDevelopment) {
                        return (
                          <div
                            key={subItem.name}
                            className={cn(
                              'flex items-center rounded-md px-2 py-1 text-xs font-medium mx-1 cursor-not-allowed opacity-60',
                              'text-muted-foreground'
                            )}>
                            {subItem.icon && (
                              <subItem.icon className="mr-1 h-3 w-3" />
                            )}
                            <span className="mr-1">{subItem.name}</span>
                            <Badge variant="secondary" className="text-[10px] px-1 py-0">
                              开发中
                            </Badge>
                          </div>
                        )
                      }

                      return (
                        <Link
                          key={subItem.name}
                          to={subItem.to}
                          className={cn(
                            'flex items-center rounded-md px-2 py-1 text-xs font-medium mx-1',
                            isSubActive
                              ? 'bg-primary/10 text-primary'
                              : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                          )}>
                          {subItem.icon && (
                            <subItem.icon className="mr-1 h-3 w-3" />
                          )}
                          {subItem.name}
                        </Link>
                      )
                    })}
                  </div>
                )}
              </div>
            )
          })}

          {/* 更多模块下拉菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="ml-2">
                <span className="mr-1">更多模块</span>
                <ChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>选择模块</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {navigation.map((item) => {
                // 排除已显示的模块
                if (
                  item.name === currentModule?.name ||
                  item.name === '仪表板' ||
                  openModules.includes(item.name)
                )
                  return null

                return (
                  <DropdownMenuItem
                    key={item.name}
                    onClick={(e) => {
                      e.preventDefault() // 阻止默认行为，不进行导航
                      toggleOpenModule(item.name)
                    }}
                    className="flex items-center">
                    <item.icon className="mr-2 h-4 w-4" />
                    {item.name}
                  </DropdownMenuItem>
                )
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </nav>
        <div className="ml-auto flex items-center space-x-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <Badge
                    className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-[10px]"
                    variant="destructive">
                    {unreadCount}
                  </Badge>
                )}
                <span className="sr-only">通知</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel className="flex items-center justify-between">
                <span>通知</span>
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={markAllAsRead}
                    className="h-8 text-xs">
                    全部标为已读
                  </Button>
                )}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              {notifications.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  暂无通知
                </div>
              ) : (
                notifications.map((notification) => (
                  <DropdownMenuItem
                    key={notification.id}
                    className="p-0 focus:bg-transparent"
                    onSelect={(e) => e.preventDefault()}>
                    <div
                      className={`w-full p-3 hover:bg-accent cursor-pointer ${
                        !notification.read ? 'bg-accent/50' : ''
                      }`}
                      onClick={() => markAsRead(notification.id)}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-2">
                          <div
                            className={`mt-0.5 rounded-full p-1 ${
                              notification.type === 'critical'
                                ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300'
                                : notification.type === 'success'
                                ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300'
                                : 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                            }`}>
                            <AlertTriangle className="h-3 w-3" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">
                              {notification.title}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {notification.message}
                            </p>
                          </div>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {notification.time}
                        </span>
                      </div>
                    </div>
                  </DropdownMenuItem>
                ))
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link
                  to="/notifications"
                  className="w-full text-center cursor-pointer">
                  <span className="mx-auto">查看全部通知</span>
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* 锁定按钮 */}
          <Button
            variant="ghost"
            size="icon"
            onClick={lockScreen}
            title="锁定屏幕"
            className="hidden md:flex">
            <Lock className="h-5 w-5" />
            <span className="sr-only">锁定屏幕</span>
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full">
                <User className="h-5 w-5" />
                <span className="sr-only">用户菜单</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>我的账户</DropdownMenuLabel>
              <DropdownMenuItem>
                <Link to="/profile" className="flex w-full">
                  个人资料
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link to="/settings" className="flex w-full">
                  设置
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={lockScreen}>
                <div className="flex items-center w-full">
                  <Lock className="mr-2 h-4 w-4" />
                  锁定屏幕
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link to="/login" className="flex w-full items-center">
                  <LogOut className="mr-2 h-4 w-4" />
                  退出登录
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}

function MobileSidebar({ onClose }: { onClose: () => void }) {
  const cn = (...inputs: any[]) => {
    return inputs.filter(Boolean).join(' ')
  }
  const location = useLocation()
  const pathname = location.pathname
  const [openSubmenus, setOpenSubmenus] = useState<Record<string, boolean>>({})
  const navigation = [
    { name: '仪表板', to: '/dashboard', icon: LayoutDashboard },
    {
      name: '数据采集',
      to: '/data-collection',
      icon: Server,
      submenu: [
        { name: '设备列表', to: '/devices', icon: List },
        { name: '设备报警', to: '/devices/alarms', icon: BellRing, inDevelopment: true },
        { name: '设备事件', to: '/devices/events', icon: CalendarClock, inDevelopment: true },
      ],
    },
    {
      name: '数据转发',
      to: '/data-forwarding',
      icon: Share,
      submenu: [
        { name: '配置管理', to: '/data-forwarding', icon: Settings },
        {
          name: '数据统计',
          to: '/data-forwarding/statistics',
          icon: TrendingUp,
        },
        {
          name: '离线数据',
          to: '/data-forwarding/offline-data',
          icon: HardDrive,
        },
        { name: '新增配置', to: '/data-forwarding/add', icon: Plus },
      ],
    },
    {
      name: '工作流编排',
      to: '/workflows',
      icon: Workflow,
      submenu: [
        { name: '工作流管理', to: '/workflows', icon: Workflow },
        { name: '模板库', to: '/workflows/templates', icon: FileCode },
        { name: '监控中心', to: '/workflows/monitoring', icon: Monitor },
      ],
    },
    {
      name: '任务中心',
      to: '/task-center',
      icon: CalendarClock,
      submenu: [
        { name: '任务概览', to: '/task-center', icon: LayoutDashboard },
        { name: '定时任务', to: '/task-center/scheduled-tasks', icon: Clock },
        { name: '任务配置', to: '/task-center/task-config', icon: Settings },
      ],
    },
    {
      name: '数据监控',
      to: '/monitoring',
      icon: Activity,
      submenu: [
        { name: '监控仪表板', to: '/monitoring', icon: LayoutDashboard },
        { name: '数据库管理', to: '/monitoring/database', icon: Database },
      ],
    },
    { name: '数据分析', to: '/analytics', icon: BarChart2 },
    {
      to: '/data-history',
      name: '历史数据',
      icon: BarChart,
    },
    {
      name: '权限管理',
      to: '/system',
      icon: Shield,
      submenu: [
        { name: '用户管理', to: '/system/users', icon: Users },
        { name: '角色管理', to: '/system/roles', icon: Shield },
        { name: '菜单管理', to: '/system/menus', icon: Menu },
        { name: '权限配置', to: '/system/permissions', icon: Key },
        { name: '在线用户', to: '/system/online-users', icon: UserCheck },
        { name: '系统日志', to: '/system/logs', icon: FileText },
      ],
    },
    {
      name: '系统设置',
      to: '/settings',
      icon: Settings,
      submenu: [
        { name: '网络配置', to: '/settings/network', icon: Share },
        { name: '系统信息', to: '/settings/info', icon: Info },
        { name: '系统更新', to: '/settings/update', icon: RefreshCw },
        { name: '系统日志', to: '/settings/logs', icon: FileText },
        { name: '系统配置', to: '/settings/config', icon: Settings },
        { name: '系统授权', to: '/settings/auth', icon: Key },
        { name: '参数配置', to: '/settings/params', icon: Sliders },
        { name: '系统服务', to: '/settings/services', icon: Server },
      ],
    },
  ]
  const toggleSubmenu = (name: string) => {
    setOpenSubmenus((prev) => ({
      ...prev,
      [name]: !prev[name],
    }))
  }
  useEffect(() => {
    navigation.forEach((item) => {
      if (item.submenu && pathname?.startsWith(item.to)) {
        setOpenSubmenus((prev) => ({
          ...prev,
          [item.name]: true,
        }))
      }
    })
  }, [pathname])
  return (
    <div className="flex h-full flex-col overflow-hidden">
      <div className="flex h-14 items-center border-b px-4">
        <Link
          to="/"
          className="flex items-center gap-2 font-semibold"
          onClick={onClose}>
          <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-3 w-3 text-primary-foreground">
              <path d="M18 6H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h13l4-3.5L18 6Z" />
              <path d="M12 13v8" />
              <path d="M5 13v6a2 2 0 0 0 2 2h8" />
            </svg>
          </div>
          <span>工作流编排平台</span>
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid gap-1 px-2">
          {/* 这里可以复用Sidebar组件的导航逻辑，但为了简化，我们直接使用一个简化版�?*/}
          <Link
            to="/dashboard"
            className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent"
            onClick={onClose}>
            <LayoutDashboard className="h-4 w-4" />
            <span>仪表板</span>
          </Link>
          {/* 添加更多导航项... */}
        </nav>
      </div>
    </div>
  )
}

// 导入必要的图标
