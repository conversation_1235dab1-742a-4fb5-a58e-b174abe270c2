import { Clock, Zap, Variable } from 'lucide-react'
import { <PERSON><PERSON>, Position } from 'reactflow'

// 节点数据类型定义
interface TriggerNodeData {
  label: string
  type: string
  enabled?: boolean
  scheduleType?: string
  interval?: string
  cronExpression?: string
  variables?: any[]
  [key: string]: any
}

// 组件属性类型
interface TriggerNodeProps {
  data: TriggerNodeData
  isConnectable: boolean
}

export function TriggerNode({ data, isConnectable }: TriggerNodeProps) {
  // Placeholder for Position until it's loaded
  const positionValue: Record<string, string> = {
    Bottom: 'bottom',
    Top: 'top',
    Left: 'left',
    Right: 'right',
  }

  // Get the actual Position value when available
  const getPosition = (pos: string) => {
    if (Position && Position[pos as keyof typeof Position]) {
      return Position[pos as keyof typeof Position]
    }
    return positionValue[pos] || 'bottom'
  }

  // 根据节点类型选择图标
  const getIcon = () => {
    switch (data.type) {
      case 'schedule':
        return <Clock className="h-5 w-5" />
      case 'webhook':
        return <Zap className="h-5 w-5" />
      default:
        return <Clock className="h-5 w-5" />
    }
  }

  // 获取节点描述
  const getDescription = () => {
    if (data.type === 'schedule') {
      if (data.scheduleType === 'interval') {
        return `每 ${data.interval || '60'} 分钟`
      } else if (data.scheduleType === 'cron') {
        return `Cron: ${data.cronExpression || '0 0 * * *'}`
      }
      return '定时触发'
    } else if (data.type === 'webhook') {
      return 'HTTP Webhook'
    }
    return '触发器'
  }

  // 检查节点是否有变量
  const hasVariables = data.variables && data.variables.length > 0

  return (
    <div
      className={`rounded-lg border-2 ${
        data.enabled === false
          ? 'border-gray-300 bg-gray-50'
          : 'border-blue-500 bg-white'
      } p-3 shadow-md`}>
      <div className="flex items-center gap-2">
        <div
          className={`flex h-8 w-8 items-center justify-center rounded-full ${
            data.enabled === false
              ? 'bg-gray-200 text-gray-500'
              : 'bg-blue-100 text-blue-500'
          }`}>
          {getIcon()}
        </div>
        <div>
          <div className="text-sm font-medium">{data.label}</div>
          <div className="text-xs text-gray-500">{getDescription()}</div>
        </div>
      </div>

      {/* 显示变量指示器 */}
      {hasVariables && (
        <div className="mt-2 flex items-center gap-1">
          <Variable className="h-3 w-3 text-gray-500" />
          <span className="text-xs text-gray-500">
            {data.variables?.length || 0} 个变量
          </span>
        </div>
      )}

      {/* 只有输出连接点 */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="out"
        isConnectable={isConnectable}
        className={`h-3 w-3 ${
          data.enabled === false ? 'bg-gray-400' : 'bg-blue-500'
        }`}
      />
    </div>
  )
}

export default TriggerNode
