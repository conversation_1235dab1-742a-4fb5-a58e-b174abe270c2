import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Trash2, Edit, FolderPlus, Tag } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import type { DeviceTag } from '@/lib/api/tag-api'

interface TagGroup {
  id: string
  name: string
  description?: string
  color?: string
  tagIds: string[]
}

interface TagGroupManagerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  deviceId: string
  tags: DeviceTag[]
  onGroupsUpdated?: () => void
}

export function TagGroupManager({
  open,
  onOpenChange,
  deviceId,
  tags,
  onGroupsUpdated,
}: TagGroupManagerProps) {
  const [groups, setGroups] = useState<TagGroup[]>([])
  const [activeTab, setActiveTab] = useState('groups')
  const [selectedGroup, setSelectedGroup] = useState<TagGroup | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isEditing, setIsEditing] = useState(false)

  // 新建/编辑组表单
  const [groupName, setGroupName] = useState('')
  const [groupDescription, setGroupDescription] = useState('')
  const [groupColor, setGroupColor] = useState('#3b82f6')
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([])

  // 加载分组
  const loadGroups = async () => {
    setIsLoading(true)
    try {
      // 在实际应用中使用API调用
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 800))

      // 模拟分组数据
      const mockGroups: TagGroup[] = [
        {
          id: 'group1',
          name: '温度传感器',
          description: '所有温度相关的点位',
          color: '#ef4444',
          tagIds: tags
            .filter(
              (t) => t.name.includes('temp') || t.name.includes('temperature')
            )
            .map((t) => t.id),
        },
        {
          id: 'group2',
          name: '压力传感器',
          description: '所有压力相关的点位',
          color: '#3b82f6',
          tagIds: tags
            .filter((t) => t.name.includes('pressure'))
            .map((t) => t.id),
        },
        {
          id: 'group3',
          name: '状态指示器',
          description: '所有状态指示相关的点位',
          color: '#10b981',
          tagIds: tags.filter((t) => t.dataType === 'Boolean').map((t) => t.id),
        },
      ]

      setGroups(mockGroups)
    } catch (error) {
      console.error('加载分组失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载点位分组',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    if (open) {
      loadGroups()
      resetForm()
    }
  }, [open, tags])

  // 重置表单
  const resetForm = () => {
    setGroupName('')
    setGroupDescription('')
    setGroupColor('#3b82f6')
    setSelectedTagIds([])
    setIsEditing(false)
    setSelectedGroup(null)
  }

  // 处理编辑分组
  const handleEditGroup = (group: TagGroup) => {
    setSelectedGroup(group)
    setGroupName(group.name)
    setGroupDescription(group.description || '')
    setGroupColor(group.color || '#3b82f6')
    setSelectedTagIds(group.tagIds)
    setIsEditing(true)
    setActiveTab('edit')
  }

  // 处理删除分组
  const handleDeleteGroup = async (groupId: string) => {
    if (!confirm('确定要删除此分组吗？')) return

    setIsLoading(true)
    try {
      // 在实际应用中使用API调用
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 800))

      setGroups(groups.filter((g) => g.id !== groupId))

      toast({
        title: '删除成功',
        description: '已成功删除分组',
      })

      if (onGroupsUpdated) {
        onGroupsUpdated()
      }
    } catch (error) {
      console.error('删除分组失败:', error)
      toast({
        title: '删除失败',
        description: '无法删除分组',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理保存分组
  const handleSaveGroup = async () => {
    if (!groupName.trim()) {
      toast({
        title: '验证失败',
        description: '分组名称不能为空',
        variant: 'destructive',
      })
      return
    }

    if (selectedTagIds.length === 0) {
      toast({
        title: '验证失败',
        description: '请至少选择一个点位',
        variant: 'destructive',
      })
      return
    }

    setIsLoading(true)
    try {
      // 在实际应用中使用API调用
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 800))

      if (isEditing && selectedGroup) {
        // 更新分组
        const updatedGroups = groups.map((g) =>
          g.id === selectedGroup.id
            ? {
                ...g,
                name: groupName,
                description: groupDescription,
                color: groupColor,
                tagIds: selectedTagIds,
              }
            : g
        )
        setGroups(updatedGroups)

        toast({
          title: '更新成功',
          description: '已成功更新分组',
        })
      } else {
        // 创建新分组
        const newGroup: TagGroup = {
          id: `group${Date.now()}`,
          name: groupName,
          description: groupDescription,
          color: groupColor,
          tagIds: selectedTagIds,
        }

        setGroups([...groups, newGroup])

        toast({
          title: '创建成功',
          description: '已成功创建分组',
        })
      }

      resetForm()
      setActiveTab('groups')

      if (onGroupsUpdated) {
        onGroupsUpdated()
      }
    } catch (error) {
      console.error('保存分组失败:', error)
      toast({
        title: '保存失败',
        description: '无法保存分组',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 获取分组中的点位
  const getGroupTags = (group: TagGroup) => {
    return tags.filter((tag) => group.tagIds.includes(tag.id))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-3xl max-h-[90vh] overflow-hidden flex flex-col"
        backgroundOpacity={0.3}>
        <DialogHeader>
          <DialogTitle>点位分组管理</DialogTitle>
          <DialogDescription>
            创建和管理点位分组，方便组织和查看相关点位
          </DialogDescription>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid grid-cols-2">
            <TabsTrigger value="groups">分组列表</TabsTrigger>
            <TabsTrigger value="edit">
              {isEditing ? '编辑分组' : '新建分组'}
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-auto p-1">
            <TabsContent value="groups" className="mt-0 h-full">
              <div className="space-y-4 p-2">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">设备分组</h3>
                  <Button
                    onClick={() => {
                      resetForm()
                      setActiveTab('edit')
                    }}>
                    <FolderPlus className="mr-2 h-4 w-4" />
                    新建分组
                  </Button>
                </div>

                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                  </div>
                ) : groups.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-gray-500 border rounded-lg">
                    <FolderPlus className="h-12 w-12 mb-4 text-gray-300" />
                    <p>暂无分组</p>
                    <p className="text-sm mt-2">点击"新建分组"创建第一个分组</p>
                  </div>
                ) : (
                  <div className="grid gap-4">
                    {groups.map((group) => {
                      const groupTags = getGroupTags(group)

                      return (
                        <Card key={group.id}>
                          <CardHeader className="pb-2">
                            <div className="flex items-start justify-between">
                              <div className="flex items-center gap-2">
                                <div
                                  className="w-4 h-4 rounded-full"
                                  style={{
                                    backgroundColor: group.color,
                                  }}></div>
                                <CardTitle className="text-base">
                                  {group.name}
                                </CardTitle>
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleEditGroup(group)}>
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteGroup(group.id)}>
                                  <Trash2 className="h-4 w-4 text-red-500" />
                                </Button>
                              </div>
                            </div>
                            {group.description && (
                              <p className="text-sm text-gray-500">
                                {group.description}
                              </p>
                            )}
                          </CardHeader>
                          <CardContent>
                            <div className="flex flex-wrap gap-2">
                              {groupTags.length === 0 ? (
                                <p className="text-sm text-gray-500">无点位</p>
                              ) : (
                                groupTags.map((tag) => (
                                  <Badge
                                    key={tag.id}
                                    variant="outline"
                                    className="flex items-center gap-1">
                                    <Tag className="h-3 w-3" />
                                    {tag.name}
                                  </Badge>
                                ))
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="edit" className="mt-0 h-full">
              <div className="space-y-4 p-2">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="groupName" className="required">
                      分组名称
                    </Label>
                    <Input
                      id="groupName"
                      value={groupName}
                      onChange={(e) => setGroupName(e.target.value)}
                      placeholder="输入分组名称"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="groupDescription">分组描述</Label>
                    <Input
                      id="groupDescription"
                      value={groupDescription}
                      onChange={(e) => setGroupDescription(e.target.value)}
                      placeholder="输入分组描述（可选）"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="groupColor">分组颜色</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="groupColor"
                        type="color"
                        value={groupColor}
                        onChange={(e) => setGroupColor(e.target.value)}
                        className="w-12 h-10 p-1"
                      />
                      <div
                        className="w-10 h-10 rounded-md border"
                        style={{ backgroundColor: groupColor }}></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="required">选择点位</Label>
                    <div className="border rounded-md p-4 max-h-[300px] overflow-y-auto">
                      <div className="space-y-2">
                        {tags.length === 0 ? (
                          <p className="text-sm text-gray-500">无可用点位</p>
                        ) : (
                          <>
                            <div className="flex items-center justify-between pb-2 border-b">
                              <span className="text-sm font-medium">
                                点位名称
                              </span>
                              <span className="text-sm font-medium">
                                数据类型
                              </span>
                            </div>
                            {tags.map((tag) => (
                              <div
                                key={tag.id}
                                className="flex items-center justify-between py-1">
                                <div className="flex items-center gap-2">
                                  <Checkbox
                                    id={`tag-${tag.id}`}
                                    checked={selectedTagIds.includes(tag.id)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        setSelectedTagIds([
                                          ...selectedTagIds,
                                          tag.id,
                                        ])
                                      } else {
                                        setSelectedTagIds(
                                          selectedTagIds.filter(
                                            (id) => id !== tag.id
                                          )
                                        )
                                      }
                                    }}
                                  />
                                  <Label
                                    htmlFor={`tag-${tag.id}`}
                                    className="cursor-pointer">
                                    {tag.name}
                                  </Label>
                                </div>
                                <Badge variant="outline">{tag.dataType}</Badge>
                              </div>
                            ))}
                          </>
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>已选择 {selectedTagIds.length} 个点位</span>
                      {selectedTagIds.length > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedTagIds([])}
                          className="h-auto p-0 text-red-500">
                          清空选择
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>

        <DialogFooter className="mt-4">
          {activeTab === 'edit' ? (
            <>
              <Button
                variant="outline"
                onClick={() => {
                  setActiveTab('groups')
                  resetForm()
                }}
                disabled={isLoading}>
                取消
              </Button>
              <Button onClick={handleSaveGroup} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    保存中...
                  </>
                ) : isEditing ? (
                  '更新分组'
                ) : (
                  '创建分组'
                )}
              </Button>
            </>
          ) : (
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
