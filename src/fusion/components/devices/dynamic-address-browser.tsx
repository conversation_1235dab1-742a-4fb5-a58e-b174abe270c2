import React, { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Search, 
  ChevronRight, 
  ChevronDown, 
  Folder, 
  FileText, 
  Settings, 
  Copy,
  Check,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { DeviceService, AddressBrowseConfig, BrowsableNode, BrowseResult, AddressTemplate } from '@/lib/api/device-api'

interface DynamicAddressBrowserProps {
  deviceId: number
  value?: string
  onChange?: (value: string) => void
  onSelect?: (node: BrowsableNode) => void
  className?: string
}

interface TreeNodeState {
  [nodeId: string]: {
    expanded: boolean
    children?: BrowsableNode[]
    loading: boolean
  }
}

export function DynamicAddressBrowser({
  deviceId,
  value,
  onChange,
  onSelect,
  className
}: DynamicAddressBrowserProps) {
  const [config, setConfig] = useState<AddressBrowseConfig | null>(null)
  const [rootNodes, setRootNodes] = useState<BrowsableNode[]>([])
  const [treeState, setTreeState] = useState<TreeNodeState>({})
  const [searchText, setSearchText] = useState('')
  const [searchResults, setSearchResults] = useState<BrowsableNode[]>([])
  const [selectedNodeId, setSelectedNodeId] = useState<string>(value || '')
  const [loading, setLoading] = useState(false)
  const [searchLoading, setSearchLoading] = useState(false)
  const [copiedTemplate, setCopiedTemplate] = useState<string | null>(null)

  // 加载地址浏览配置
  useEffect(() => {
    loadConfig()
  }, [deviceId])

  // 加载根节点
  useEffect(() => {
    if (config?.supportsDynamicBrowsing) {
      loadRootNodes()
    }
  }, [config])

  const loadConfig = async () => {
    try {
      setLoading(true)
      const response = await DeviceService.getAddressBrowseConfig(deviceId)
      if (response.code === 200 && response.data) {
        setConfig(response.data)
      } else {
        toast({
          title: '加载失败',
          description: response.msg || '无法加载地址浏览配置',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('加载地址浏览配置失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载地址浏览配置',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadRootNodes = async () => {
    try {
      setLoading(true)
      const response = await DeviceService.getRootNodes(deviceId)
      if (response.code === 200 && response.data?.success) {
        setRootNodes(response.data.nodes)
      } else {
        toast({
          title: '加载失败',
          description: response.data?.errorMessage || response.msg || '无法加载根节点',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('加载根节点失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载根节点',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadChildNodes = async (parentNodeId: string) => {
    try {
      setTreeState(prev => ({
        ...prev,
        [parentNodeId]: { ...prev[parentNodeId], loading: true }
      }))

      const response = await DeviceService.getChildNodes(deviceId, parentNodeId)
      if (response.code === 200 && response.data?.success) {
        setTreeState(prev => ({
          ...prev,
          [parentNodeId]: {
            ...prev[parentNodeId],
            children: response.data!.nodes,
            loading: false,
            expanded: true
          }
        }))
      } else {
        toast({
          title: '加载失败',
          description: response.data?.errorMessage || response.msg || '无法加载子节点',
          variant: 'destructive'
        })
        setTreeState(prev => ({
          ...prev,
          [parentNodeId]: { ...prev[parentNodeId], loading: false }
        }))
      }
    } catch (error) {
      console.error('加载子节点失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载子节点',
        variant: 'destructive'
      })
      setTreeState(prev => ({
        ...prev,
        [parentNodeId]: { ...prev[parentNodeId], loading: false }
      }))
    }
  }

  const handleNodeToggle = (node: BrowsableNode) => {
    const nodeState = treeState[node.nodeId]
    
    if (nodeState?.expanded) {
      // 折叠节点
      setTreeState(prev => ({
        ...prev,
        [node.nodeId]: { ...prev[node.nodeId], expanded: false }
      }))
    } else if (nodeState?.children) {
      // 展开已加载的节点
      setTreeState(prev => ({
        ...prev,
        [node.nodeId]: { ...prev[node.nodeId], expanded: true }
      }))
    } else if (node.hasChildren) {
      // 加载并展开节点
      loadChildNodes(node.nodeId)
    }
  }

  const handleNodeSelect = (node: BrowsableNode) => {
    setSelectedNodeId(node.nodeId)
    onChange?.(node.nodeId)
    onSelect?.(node)
  }

  const handleSearch = async () => {
    if (!searchText.trim()) {
      setSearchResults([])
      return
    }

    try {
      setSearchLoading(true)
      const response = await DeviceService.searchNodes(deviceId, searchText.trim())
      if (response.code === 200 && response.data?.success) {
        setSearchResults(response.data.nodes)
      } else {
        toast({
          title: '搜索失败',
          description: response.data?.errorMessage || response.msg || '搜索节点失败',
          variant: 'destructive'
        })
        setSearchResults([])
      }
    } catch (error) {
      console.error('搜索节点失败:', error)
      toast({
        title: '搜索失败',
        description: '搜索节点失败',
        variant: 'destructive'
      })
      setSearchResults([])
    } finally {
      setSearchLoading(false)
    }
  }

  const copyTemplate = (template: AddressTemplate) => {
    navigator.clipboard.writeText(template.example)
    setCopiedTemplate(template.category)
    setTimeout(() => setCopiedTemplate(null), 2000)
    toast({
      title: '已复制',
      description: `地址模板 "${template.example}" 已复制到剪贴板`
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>加载地址浏览器...</span>
      </div>
    )
  }

  if (!config) {
    return (
      <div className="flex items-center justify-center p-8 text-muted-foreground">
        <AlertCircle className="h-6 w-6 mr-2" />
        <span>无法加载地址浏览配置</span>
      </div>
    )
  }

  return (
    <div className={className}>
      <Tabs defaultValue="browse" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="browse" disabled={!config.supportsDynamicBrowsing}>
            浏览
          </TabsTrigger>
          <TabsTrigger value="search" disabled={!config.searchable}>
            搜索
          </TabsTrigger>
          <TabsTrigger value="templates">
            模板
          </TabsTrigger>
        </TabsList>

        <TabsContent value="browse" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">节点浏览</CardTitle>
              <CardDescription>
                {config.requiresConnection && (
                  <Badge variant="outline" className="mr-2">
                    需要连接
                  </Badge>
                )}
                浏览服务器地址空间
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <TreeView
                  nodes={rootNodes}
                  treeState={treeState}
                  selectedNodeId={selectedNodeId}
                  onNodeToggle={handleNodeToggle}
                  onNodeSelect={handleNodeSelect}
                />
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">节点搜索</CardTitle>
              <CardDescription>
                按名称搜索节点，最多显示 {config.maxSearchResults} 个结果
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <Input
                  placeholder="输入搜索关键词..."
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button onClick={handleSearch} disabled={searchLoading}>
                  {searchLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              <ScrollArea className="h-[300px]">
                {searchResults.length > 0 ? (
                  <div className="space-y-2">
                    {searchResults.map((node) => (
                      <div
                        key={node.nodeId}
                        className={`p-3 border rounded-lg cursor-pointer hover:bg-accent ${
                          selectedNodeId === node.nodeId ? 'bg-accent border-primary' : ''
                        }`}
                        onClick={() => handleNodeSelect(node)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4" />
                            <span className="font-medium">{node.displayName}</span>
                          </div>
                          <Badge variant="secondary">{node.nodeClass}</Badge>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {node.nodeId}
                        </div>
                        {node.nodePath && (
                          <div className="text-xs text-muted-foreground">
                            路径: {node.nodePath}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : searchText && !searchLoading ? (
                  <div className="text-center text-muted-foreground py-8">
                    未找到匹配的节点
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    输入关键词开始搜索
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">地址模板</CardTitle>
              <CardDescription>
                {config.formatDescription}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {config.addressTemplates.map((template, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{template.category}</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyTemplate(template)}
                    >
                      {copiedTemplate === template.category ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <Label className="text-xs">格式:</Label>
                      <code className="block text-sm bg-muted p-2 rounded mt-1">
                        {template.pattern}
                      </code>
                    </div>
                    <div>
                      <Label className="text-xs">示例:</Label>
                      <code className="block text-sm bg-muted p-2 rounded mt-1">
                        {template.example}
                      </code>
                    </div>
                    <div>
                      <Label className="text-xs">说明:</Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        {template.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// 树形视图组件
interface TreeViewProps {
  nodes: BrowsableNode[]
  treeState: TreeNodeState
  selectedNodeId: string
  onNodeToggle: (node: BrowsableNode) => void
  onNodeSelect: (node: BrowsableNode) => void
  level?: number
}

function TreeView({
  nodes,
  treeState,
  selectedNodeId,
  onNodeToggle,
  onNodeSelect,
  level = 0
}: TreeViewProps) {
  if (nodes.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        {level === 0 ? '暂无节点数据' : '暂无子节点'}
      </div>
    )
  }

  return (
    <div className="space-y-1">
      {nodes.map((node) => (
        <TreeNode
          key={node.nodeId}
          node={node}
          treeState={treeState}
          selectedNodeId={selectedNodeId}
          onNodeToggle={onNodeToggle}
          onNodeSelect={onNodeSelect}
          level={level}
        />
      ))}
    </div>
  )
}

// 树形节点组件
interface TreeNodeProps {
  node: BrowsableNode
  treeState: TreeNodeState
  selectedNodeId: string
  onNodeToggle: (node: BrowsableNode) => void
  onNodeSelect: (node: BrowsableNode) => void
  level: number
}

function TreeNode({
  node,
  treeState,
  selectedNodeId,
  onNodeToggle,
  onNodeSelect,
  level
}: TreeNodeProps) {
  const nodeState = treeState[node.nodeId]
  const isExpanded = nodeState?.expanded || false
  const isLoading = nodeState?.loading || false
  const children = nodeState?.children || []
  const isSelected = selectedNodeId === node.nodeId

  const getNodeIcon = (node: BrowsableNode) => {
    if (node.nodeClass === 'Object' || node.nodeClass === 'ObjectType') {
      return <Folder className="h-4 w-4" />
    }
    return <FileText className="h-4 w-4" />
  }

  const getNodeBadge = (node: BrowsableNode) => {
    if (node.nodeClass === 'Variable') {
      return (
        <div className="flex items-center space-x-1">
          <Badge variant="secondary" className="text-xs">
            {node.dataType || 'Variable'}
          </Badge>
          {node.canRead && (
            <Badge variant="outline" className="text-xs">
              R
            </Badge>
          )}
          {node.canWrite && (
            <Badge variant="outline" className="text-xs">
              W
            </Badge>
          )}
        </div>
      )
    }
    return (
      <Badge variant="secondary" className="text-xs">
        {node.nodeClass}
      </Badge>
    )
  }

  return (
    <div>
      <div
        className={`flex items-center space-x-2 p-2 rounded-lg cursor-pointer hover:bg-accent ${
          isSelected ? 'bg-accent border border-primary' : ''
        }`}
        style={{ paddingLeft: `${level * 20 + 8}px` }}
        onClick={() => onNodeSelect(node)}
      >
        {/* 展开/折叠按钮 */}
        <div className="flex-shrink-0 w-4 h-4">
          {node.hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="w-4 h-4 p-0"
              onClick={(e) => {
                e.stopPropagation()
                onNodeToggle(node)
              }}
            >
              {isLoading ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>

        {/* 节点图标 */}
        <div className="flex-shrink-0">
          {getNodeIcon(node)}
        </div>

        {/* 节点信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <span className="font-medium truncate">{node.displayName}</span>
            {getNodeBadge(node)}
          </div>
          <div className="text-xs text-muted-foreground truncate">
            {node.nodeId}
          </div>
          {node.description && (
            <div className="text-xs text-muted-foreground truncate">
              {node.description}
            </div>
          )}
          {node.value !== undefined && (
            <div className="text-xs text-blue-600 truncate">
              值: {String(node.value)}
            </div>
          )}
        </div>
      </div>

      {/* 子节点 */}
      {isExpanded && children.length > 0 && (
        <TreeView
          nodes={children}
          treeState={treeState}
          selectedNodeId={selectedNodeId}
          onNodeToggle={onNodeToggle}
          onNodeSelect={onNodeSelect}
          level={level + 1}
        />
      )}
    </div>
  )
}
