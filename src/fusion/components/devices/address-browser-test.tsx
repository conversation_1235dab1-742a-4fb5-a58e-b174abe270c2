import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { SmartAddressField } from './smart-address-input'
import { DynamicAddressBrowser } from './dynamic-address-browser'
import { OpcUaAddressDialog } from './opcua-address-dialog'
import { AddressBrowseStatus, LoadingOverlay, AddressBrowseErrorBoundary } from './address-browse-status'
import { BrowsableNode, DeviceService } from '@/lib/api/device-api'
import { addressBrowsePerformanceMonitor } from '@/lib/api/address-browse-performance'

export function AddressBrowserTest() {
  const [selectedAddress1, setSelectedAddress1] = useState('')
  const [selectedAddress2, setSelectedAddress2] = useState('')
  const [selectedAddress3, setSelectedAddress3] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedNode, setSelectedNode] = useState<BrowsableNode | null>(null)
  const [isConnected, setIsConnected] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [performanceReport, setPerformanceReport] = useState<any>(null)

  // 模拟设备ID
  const mockDeviceId = 1

  const handleNodeSelect = (node: BrowsableNode) => {
    setSelectedNode(node)
  }

  const handleDialogAddressSelected = (nodeId: string, browseName: string) => {
    setSelectedAddress3(nodeId)
  }

  const handleRetry = async () => {
    setIsLoading(true)
    try {
      // 模拟重试操作
      await new Promise(resolve => setTimeout(resolve, 1000))
      // 这里可以重新加载数据
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearCache = () => {
    DeviceService.clearAddressBrowseCache()
    // 可以添加成功提示
  }

  const handleToggleConnection = () => {
    setIsConnected(!isConnected)
  }

  const handleGeneratePerformanceReport = () => {
    const report = addressBrowsePerformanceMonitor.exportReport()
    setPerformanceReport(report)
  }

  const handlePreloadData = async () => {
    setIsLoading(true)
    try {
      await DeviceService.preloadAddressBrowseData(mockDeviceId)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AddressBrowseErrorBoundary>
      <div className="container mx-auto p-6 space-y-6 relative">
        <LoadingOverlay isLoading={isLoading} message="处理中..." />

        <div className="text-center">
          <h1 className="text-3xl font-bold">OPC UA 动态地址浏览测试</h1>
          <p className="text-muted-foreground mt-2">
            测试动态地址浏览功能的各个组件
          </p>
        </div>

        {/* 控制面板 */}
        <Card>
          <CardHeader>
            <CardTitle>测试控制面板</CardTitle>
            <CardDescription>
              控制测试环境和查看性能数据
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={isConnected ? "default" : "outline"}
                size="sm"
                onClick={handleToggleConnection}
              >
                {isConnected ? '断开连接' : '连接设备'}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handlePreloadData}
                disabled={!isConnected}
              >
                预加载数据
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleGeneratePerformanceReport}
              >
                生成性能报告
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleClearCache}
              >
                清除所有缓存
              </Button>
            </div>

            {performanceReport && (
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">性能报告</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="text-muted-foreground">总操作数</div>
                    <div className="font-medium">{performanceReport.performance.totalOperations}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">平均响应时间</div>
                    <div className="font-medium">{performanceReport.performance.averageDuration.toFixed(0)}ms</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">成功率</div>
                    <div className="font-medium">{performanceReport.performance.successRate.toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">缓存命中率</div>
                    <div className="font-medium">{performanceReport.performance.cacheHitRate.toFixed(1)}%</div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 状态监控 */}
        <AddressBrowseStatus
          deviceId={mockDeviceId}
          isConnected={isConnected}
          onRetry={handleRetry}
          onClearCache={handleClearCache}
        />
        {/* 智能地址输入组件测试 */}
        <Card>
          <CardHeader>
            <CardTitle>智能地址输入组件</CardTitle>
            <CardDescription>
              集成了地址验证和浏览功能的输入组件
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <SmartAddressField
              deviceId={mockDeviceId}
              label="OPC UA 地址"
              placeholder="请输入或选择地址"
              value={selectedAddress1}
              onChange={setSelectedAddress1}
              description="支持动态浏览和地址验证"
              required
            />
            
            <div className="text-sm">
              <strong>当前值:</strong> 
              <code className="ml-2 bg-muted px-2 py-1 rounded">
                {selectedAddress1 || '(空)'}
              </code>
            </div>
          </CardContent>
        </Card>

        {/* 表单字段测试 */}
        <Card>
          <CardHeader>
            <CardTitle>表单字段测试</CardTitle>
            <CardDescription>
              在表单中使用智能地址输入
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <SmartAddressField
              deviceId={mockDeviceId}
              label="设备地址"
              placeholder="输入设备采集点地址"
              value={selectedAddress2}
              onChange={setSelectedAddress2}
              error={selectedAddress2 === 'error' ? '地址格式错误' : undefined}
            />
            
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setSelectedAddress2('ns=2;s=Temperature')}
              >
                设置示例地址
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setSelectedAddress2('error')}
              >
                触发错误
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setSelectedAddress2('')}
              >
                清空
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 动态地址浏览器测试 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>动态地址浏览器</CardTitle>
            <CardDescription>
              完整的地址浏览功能，包含树形浏览、搜索和模板
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DynamicAddressBrowser
              deviceId={mockDeviceId}
              onSelect={handleNodeSelect}
              className="border rounded-lg p-4"
            />
            
            {selectedNode && (
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">选中的节点:</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>节点ID:</strong> {selectedNode.nodeId}
                  </div>
                  <div>
                    <strong>显示名称:</strong> {selectedNode.displayName}
                  </div>
                  <div>
                    <strong>节点类型:</strong> 
                    <Badge variant="secondary" className="ml-2">
                      {selectedNode.nodeClass}
                    </Badge>
                  </div>
                  <div>
                    <strong>数据类型:</strong> {selectedNode.dataType || 'N/A'}
                  </div>
                  <div>
                    <strong>可读:</strong> 
                    <Badge variant={selectedNode.canRead ? 'default' : 'secondary'} className="ml-2">
                      {selectedNode.canRead ? '是' : '否'}
                    </Badge>
                  </div>
                  <div>
                    <strong>可写:</strong> 
                    <Badge variant={selectedNode.canWrite ? 'default' : 'secondary'} className="ml-2">
                      {selectedNode.canWrite ? '是' : '否'}
                    </Badge>
                  </div>
                  {selectedNode.description && (
                    <div className="col-span-2">
                      <strong>描述:</strong> {selectedNode.description}
                    </div>
                  )}
                  {selectedNode.value !== undefined && (
                    <div className="col-span-2">
                      <strong>当前值:</strong> 
                      <code className="ml-2 bg-background px-2 py-1 rounded">
                        {String(selectedNode.value)}
                      </code>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 对话框测试 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>OPC UA 地址对话框</CardTitle>
            <CardDescription>
              弹窗形式的地址浏览器
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4">
              <Button onClick={() => setIsDialogOpen(true)}>
                打开地址浏览对话框
              </Button>
              <div className="text-sm">
                <strong>选中地址:</strong> 
                <code className="ml-2 bg-muted px-2 py-1 rounded">
                  {selectedAddress3 || '(未选择)'}
                </code>
              </div>
            </div>
            
            <OpcUaAddressDialog
              open={isDialogOpen}
              onOpenChange={setIsDialogOpen}
              deviceId={mockDeviceId}
              onAddressSelected={handleDialogAddressSelected}
            />
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* 功能说明 */}
      <Card>
        <CardHeader>
          <CardTitle>功能说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h4 className="font-medium mb-2">🌳 树形浏览</h4>
              <p className="text-sm text-muted-foreground">
                以树形结构展示OPC UA服务器的地址空间，支持懒加载和展开/折叠
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">🔍 智能搜索</h4>
              <p className="text-sm text-muted-foreground">
                按节点名称搜索，快速定位目标节点，支持模糊匹配
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">📋 地址模板</h4>
              <p className="text-sm text-muted-foreground">
                提供常用的地址格式模板，帮助用户快速构建正确的NodeId
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">✅ 地址验证</h4>
              <p className="text-sm text-muted-foreground">
                实时验证地址格式和节点存在性，提供即时反馈
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">🎯 智能输入</h4>
              <p className="text-sm text-muted-foreground">
                根据协议类型自动选择合适的输入方式和验证规则
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">🔗 无缝集成</h4>
              <p className="text-sm text-muted-foreground">
                与现有表单组件无缝集成，保持一致的用户体验
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 新增功能说明 */}
      <Card>
        <CardHeader>
          <CardTitle>新增功能</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">📊 性能监控</h4>
              <p className="text-sm text-muted-foreground">
                实时监控API调用性能，包括响应时间、成功率、缓存命中率等指标
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">🔄 智能重试</h4>
              <p className="text-sm text-muted-foreground">
                自动重试失败的操作，支持指数退避策略和错误分类
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">💾 智能缓存</h4>
              <p className="text-sm text-muted-foreground">
                自动缓存浏览结果，减少重复请求，提升用户体验
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">🛡️ 错误处理</h4>
              <p className="text-sm text-muted-foreground">
                完善的错误边界和用户友好的错误提示，提高系统稳定性
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </AddressBrowseErrorBoundary>
  )
}
