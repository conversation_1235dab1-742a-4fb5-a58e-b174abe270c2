import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Settings, Save, RotateCcw, Download, Upload } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

interface AddressBrowseSettings {
  // 缓存设置
  cacheEnabled: boolean
  cacheTimeout: number // 分钟
  maxCacheSize: number // MB
  
  // 性能设置
  maxRetries: number
  retryDelay: number // 毫秒
  requestTimeout: number // 毫秒
  maxConcurrentRequests: number
  
  // 浏览设置
  maxSearchResults: number
  maxBrowseDepth: number
  autoExpandDepth: number
  
  // UI设置
  showPerformanceStats: boolean
  showErrorDetails: boolean
  enableAnimations: boolean
  compactMode: boolean
  
  // 高级设置
  debugMode: boolean
  logLevel: 'error' | 'warn' | 'info' | 'debug'
  enableTelemetry: boolean
}

const defaultSettings: AddressBrowseSettings = {
  cacheEnabled: true,
  cacheTimeout: 5,
  maxCacheSize: 10,
  maxRetries: 3,
  retryDelay: 1000,
  requestTimeout: 10000,
  maxConcurrentRequests: 5,
  maxSearchResults: 100,
  maxBrowseDepth: 10,
  autoExpandDepth: 2,
  showPerformanceStats: true,
  showErrorDetails: false,
  enableAnimations: true,
  compactMode: false,
  debugMode: false,
  logLevel: 'warn',
  enableTelemetry: true
}

interface AddressBrowseSettingsProps {
  onSettingsChange?: (settings: AddressBrowseSettings) => void
  className?: string
}

export function AddressBrowseSettingsPanel({
  onSettingsChange,
  className
}: AddressBrowseSettingsProps) {
  const [settings, setSettings] = useState<AddressBrowseSettings>(defaultSettings)
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    // 从localStorage加载设置
    const savedSettings = localStorage.getItem('addressBrowseSettings')
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings)
        setSettings({ ...defaultSettings, ...parsed })
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    }
  }, [])

  const handleSettingChange = <K extends keyof AddressBrowseSettings>(
    key: K,
    value: AddressBrowseSettings[K]
  ) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    setHasChanges(true)
    onSettingsChange?.(newSettings)
  }

  const handleSave = () => {
    try {
      localStorage.setItem('addressBrowseSettings', JSON.stringify(settings))
      setHasChanges(false)
      toast({
        title: '设置已保存',
        description: '地址浏览设置已成功保存'
      })
    } catch (error) {
      toast({
        title: '保存失败',
        description: '无法保存设置，请重试',
        variant: 'destructive'
      })
    }
  }

  const handleReset = () => {
    setSettings(defaultSettings)
    setHasChanges(true)
    toast({
      title: '设置已重置',
      description: '所有设置已恢复为默认值'
    })
  }

  const handleExport = () => {
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'address-browse-settings.json'
    link.click()
    URL.revokeObjectURL(url)
  }

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string)
        setSettings({ ...defaultSettings, ...imported })
        setHasChanges(true)
        toast({
          title: '设置已导入',
          description: '设置文件导入成功'
        })
      } catch (error) {
        toast({
          title: '导入失败',
          description: '设置文件格式无效',
          variant: 'destructive'
        })
      }
    }
    reader.readAsText(file)
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                地址浏览设置
              </CardTitle>
              <CardDescription>
                自定义地址浏览器的行为和性能参数
              </CardDescription>
            </div>
            {hasChanges && (
              <Badge variant="outline" className="text-orange-600">
                有未保存的更改
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="cache" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="cache">缓存</TabsTrigger>
              <TabsTrigger value="performance">性能</TabsTrigger>
              <TabsTrigger value="ui">界面</TabsTrigger>
              <TabsTrigger value="advanced">高级</TabsTrigger>
            </TabsList>

            <TabsContent value="cache" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="cache-enabled">启用缓存</Label>
                  <Switch
                    id="cache-enabled"
                    checked={settings.cacheEnabled}
                    onCheckedChange={(checked) => handleSettingChange('cacheEnabled', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>缓存超时时间: {settings.cacheTimeout} 分钟</Label>
                  <Slider
                    value={[settings.cacheTimeout]}
                    onValueChange={([value]) => handleSettingChange('cacheTimeout', value)}
                    max={60}
                    min={1}
                    step={1}
                    disabled={!settings.cacheEnabled}
                  />
                </div>

                <div className="space-y-2">
                  <Label>最大缓存大小: {settings.maxCacheSize} MB</Label>
                  <Slider
                    value={[settings.maxCacheSize]}
                    onValueChange={([value]) => handleSettingChange('maxCacheSize', value)}
                    max={100}
                    min={1}
                    step={1}
                    disabled={!settings.cacheEnabled}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max-retries">最大重试次数</Label>
                  <Input
                    id="max-retries"
                    type="number"
                    value={settings.maxRetries}
                    onChange={(e) => handleSettingChange('maxRetries', parseInt(e.target.value) || 0)}
                    min={0}
                    max={10}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="retry-delay">重试延迟 (毫秒)</Label>
                  <Input
                    id="retry-delay"
                    type="number"
                    value={settings.retryDelay}
                    onChange={(e) => handleSettingChange('retryDelay', parseInt(e.target.value) || 0)}
                    min={100}
                    max={10000}
                    step={100}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="request-timeout">请求超时 (毫秒)</Label>
                  <Input
                    id="request-timeout"
                    type="number"
                    value={settings.requestTimeout}
                    onChange={(e) => handleSettingChange('requestTimeout', parseInt(e.target.value) || 0)}
                    min={1000}
                    max={60000}
                    step={1000}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-concurrent">最大并发请求</Label>
                  <Input
                    id="max-concurrent"
                    type="number"
                    value={settings.maxConcurrentRequests}
                    onChange={(e) => handleSettingChange('maxConcurrentRequests', parseInt(e.target.value) || 1)}
                    min={1}
                    max={20}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="ui" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-stats">显示性能统计</Label>
                  <Switch
                    id="show-stats"
                    checked={settings.showPerformanceStats}
                    onCheckedChange={(checked) => handleSettingChange('showPerformanceStats', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-errors">显示错误详情</Label>
                  <Switch
                    id="show-errors"
                    checked={settings.showErrorDetails}
                    onCheckedChange={(checked) => handleSettingChange('showErrorDetails', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="animations">启用动画</Label>
                  <Switch
                    id="animations"
                    checked={settings.enableAnimations}
                    onCheckedChange={(checked) => handleSettingChange('enableAnimations', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="compact-mode">紧凑模式</Label>
                  <Switch
                    id="compact-mode"
                    checked={settings.compactMode}
                    onCheckedChange={(checked) => handleSettingChange('compactMode', checked)}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="debug-mode">调试模式</Label>
                  <Switch
                    id="debug-mode"
                    checked={settings.debugMode}
                    onCheckedChange={(checked) => handleSettingChange('debugMode', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="log-level">日志级别</Label>
                  <Select
                    value={settings.logLevel}
                    onValueChange={(value: any) => handleSettingChange('logLevel', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="error">Error</SelectItem>
                      <SelectItem value="warn">Warning</SelectItem>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="debug">Debug</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="telemetry">启用遥测</Label>
                  <Switch
                    id="telemetry"
                    checked={settings.enableTelemetry}
                    onCheckedChange={(checked) => handleSettingChange('enableTelemetry', checked)}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <Separator className="my-6" />

          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4 mr-1" />
                导出
              </Button>
              <div>
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImport}
                  className="hidden"
                  id="import-settings"
                />
                <Button variant="outline" size="sm" asChild>
                  <label htmlFor="import-settings" className="cursor-pointer">
                    <Upload className="h-4 w-4 mr-1" />
                    导入
                  </label>
                </Button>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleReset}>
                <RotateCcw className="h-4 w-4 mr-1" />
                重置
              </Button>
              <Button onClick={handleSave} disabled={!hasChanges}>
                <Save className="h-4 w-4 mr-1" />
                保存
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
