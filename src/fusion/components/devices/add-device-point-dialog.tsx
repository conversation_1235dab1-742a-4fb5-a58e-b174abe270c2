import { useState, useEffect, useRef } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/components/ui/use-toast'
import { Search, Code, Plus, X } from 'lucide-react'
import { TagService } from '@/lib/api/tag-api'
import { DriverService } from '@/lib/api/driver-api'
import { SmartAddressField } from './smart-address-input'
import { DeviceVariableAddInput } from '@/lib/api-services/models/device-variable-add-input'
import { DeviceVariableEditInput } from '@/lib/api-services/models/device-variable-edit-input'
import { ValueSourceEnum } from '@/lib/api-services/models/value-source-enum'
import { SendTypeEnum } from '@/lib/api-services/models/send-type-enum'
import { ProtectTypeEnum } from '@/lib/api-services/models/protect-type-enum'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'

// 表单状态接口
export interface PointFormState {
  name: string
  displayName: string
  dataSource: string
  dataType: string // 数据转换类型：Number, Integer, Boolean等，最终映射到API的transitionType
  decimalPlaces: number
  unit: string
  description: string
  uploadMethod: string
  uploadInterval: number // 定时上报周期，单位秒
  archiveTime: number // 强制归档时间，单位秒
  collectionInterval: number
  min: string
  max: string
  accessRight: string
  address: string
  functionCode: string
  registerType: string
  registerAddress: number
  protocolDataType: string // 协议特定数据类型：int16, float等，最终映射到API的dataType
  tags: string[]
  processingScript: string
  transitionType: number
  actionOrder: number
  valueMapping?: Record<string, string>
  currentMappingValue?: string
  currentMappingMeaning?: string
  // 驱动配置相关字段
  encoding?: string
  method?: string
  readLength?: number
  [key: string]: any
}

// 属性配置接口
interface PropertyConfig {
  code: string
  name: string
  value: string
  defaultValue: string
  type: string
  display: boolean
  displayExpress: string
  order: number
  required?: boolean
}

// 组件属性接口
export interface AddDevicePointDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  deviceId: string
  deviceIdentifier?: string
  deviceName?: string
  protocol?: string
  defaultValues?: Partial<PointFormState>
  onPointAdded?: (newPoint: any) => void
  onSubmitting?: (isSubmitting: boolean) => void
  onScriptOpen?: (scriptContent: string, mode: string) => void
  onScriptSave?: (scriptContent: string) => void // 新增：脚本保存回调
  editMode?: boolean
  tagToEdit?: any
  onTagUpdated?: (updatedTag: any) => void
  protocolConfig?: PropertyConfig[]
}

// 默认表单值
const defaultFormState: PointFormState = {
  name: '',
  displayName: '',
  dataSource: 'device',
  dataType: 'Number', // 数据转换类型
  decimalPlaces: 2,
  unit: '',
  description: '',
  uploadMethod: '总是上报',
  uploadInterval: 60, // 定时上报周期，默认60秒
  archiveTime: 3600, // 强制归档时间，默认1小时
  collectionInterval: 0,
  min: '',
  max: '',
  accessRight: 'OnlyRead', // 使用协议配置期望的值
  address: '',
  functionCode: '3',
  registerType: 'HoldingRegister',
  registerAddress: 0,
  protocolDataType: 'Float', // 协议数据类型
  tags: [],
  processingScript: '',
  transitionType: 0,
  actionOrder: 0,
  // 驱动配置相关字段
  encoding: '',
  method: 'read',
  readLength: 1,
}

export function AddDevicePointDialog({
  open,
  onOpenChange,
  deviceId,
  deviceName = '',
  protocol = '',
  defaultValues = {},
  onPointAdded,
  onSubmitting,
  onScriptOpen,
  onScriptSave, // 新增：脚本保存回调
  editMode = false,
  tagToEdit = null,
  onTagUpdated,
  protocolConfig = [],
}: AddDevicePointDialogProps) {
  // 组件内部状态
  const [formState, setFormState] = useState<PointFormState>({
    ...defaultFormState,
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [newTagInput, setNewTagInput] = useState('')
  // 添加协议配置状态
  const [propertyConfig, setPropertyConfig] = useState<PropertyConfig[]>([])
  const [isLoadingConfig, setIsLoadingConfig] = useState(false)
  // 添加展开面板状态
  const [expandedSections, setExpandedSections] = useState<string[]>(['basic'])

  // 标记初始化状态
  const initialized = useRef(false)
  const prevOpenRef = useRef(open)
  const configLoadedRef = useRef(false)

  // 首次打开对话框时的初始化逻辑
  useEffect(() => {
    // 只在对话框打开状态变化时执行
    if (prevOpenRef.current !== open) {
      prevOpenRef.current = open

      if (open) {
        // 对话框打开时，重置初始化标志
        initialized.current = false
        configLoadedRef.current = false
      } else {
        // 对话框关闭时重置错误状态
        setFormErrors({})
      }
    }

    // 只在对话框打开且未初始化时执行初始化逻辑
    if (open && !initialized.current) {
      initialized.current = true

      // 设置初始表单状态
      if (editMode && tagToEdit) {
        const fillFromTag = { ...defaultFormState }

        // 从tagToEdit中复制数据到表单状态
        fillFromTag.name = tagToEdit.identifier || tagToEdit.name || '' // 使用identifier作为识别名
        fillFromTag.displayName = tagToEdit.displayName || tagToEdit.name || ''

        // 处理数据来源 - 优先使用valueSource，然后回退到dataSourceType
        if (tagToEdit.valueSource !== undefined) {
          if (typeof tagToEdit.valueSource === 'string') {
            switch (tagToEdit.valueSource) {
              case 'Read':
                fillFromTag.dataSource = 'device'
                break
              case 'Calculate':
                fillFromTag.dataSource = 'script'
                break
              case 'Static':
                fillFromTag.dataSource = 'static'
                break
              case 'Virtual':
                fillFromTag.dataSource = 'virtual'
                break
              default:
                fillFromTag.dataSource = 'device'
            }
          } else {
            // 处理数字类型的valueSource（枚举值）
            switch (Number(tagToEdit.valueSource)) {
              case 0: // ValueSourceEnum.NUMBER_0 - Read
                fillFromTag.dataSource = 'device'
                break
              case 1: // ValueSourceEnum.NUMBER_1 - Calculate
                fillFromTag.dataSource = 'script'
                break
              case 2: // ValueSourceEnum.NUMBER_2 - Static
                fillFromTag.dataSource = 'static'
                break
              case 3: // ValueSourceEnum.NUMBER_3 - Virtual
                fillFromTag.dataSource = 'virtual'
                break
              default:
                fillFromTag.dataSource = 'device'
            }
          }
        } else if (tagToEdit.dataSourceType) {
          fillFromTag.dataSource = tagToEdit.dataSourceType
        } else {
          fillFromTag.dataSource = 'device'
        }

        // 处理上报方式 - 处理sendType字段
        if (tagToEdit.sendType !== undefined) {
          if (typeof tagToEdit.sendType === 'string') {
            switch (tagToEdit.sendType) {
              case 'Always':
                fillFromTag.uploadMethod = '总是上报'
                break
              case 'Never':
                fillFromTag.uploadMethod = '从不上报'
                break
              case 'Changed':
                fillFromTag.uploadMethod = '变化上报'
                break
              default:
                fillFromTag.uploadMethod = '总是上报'
            }
          } else {
            // 处理数字类型的sendType（枚举值）
            switch (Number(tagToEdit.sendType)) {
              case 1: // SendTypeEnum.NUMBER_1 - Always
                fillFromTag.uploadMethod = '总是上报'
                break
              case 2: // SendTypeEnum.NUMBER_2 - Never
                fillFromTag.uploadMethod = '永不上报'
                break
              case 3: // SendTypeEnum.NUMBER_3 - Changed
                fillFromTag.uploadMethod = '变化上报'
                break
              case 4: // SendTypeEnum.NUMBER_4 - Timed
                fillFromTag.uploadMethod = '定时上报'
                break
              default:
                fillFromTag.uploadMethod = '总是上报'
            }
          }
        } else if (tagToEdit.uploadMethod) {
          fillFromTag.uploadMethod = tagToEdit.uploadMethod
        } else {
          fillFromTag.uploadMethod = '总是上报'
        }

        // 处理定时上报周期
        if (tagToEdit.uploadInterval !== undefined) {
          fillFromTag.uploadInterval = tagToEdit.uploadInterval
        } else {
          fillFromTag.uploadInterval = 60 // 默认60秒
        }

        // 处理强制归档时间
        if (tagToEdit.archiveTime !== undefined) {
          fillFromTag.archiveTime = tagToEdit.archiveTime
        } else {
          fillFromTag.archiveTime = 3600 // 默认1小时
        }

        // 处理读写权限 - 处理protectType字段
        // 注意：这里要存储协议配置期望的值，而不是中文显示值
        if (tagToEdit.protectType !== undefined) {
          if (typeof tagToEdit.protectType === 'string') {
            // 直接使用API返回的值，这样与协议配置匹配
            fillFromTag.accessRight = tagToEdit.protectType
          } else {
            // 处理数字类型的protectType（枚举值）
            switch (Number(tagToEdit.protectType)) {
              case 1: // ProtectTypeEnum.NUMBER_1 - OnlyRead
                fillFromTag.accessRight = 'OnlyRead'
                break
              case 2: // ProtectTypeEnum.NUMBER_2 - ReadAndWrite
                fillFromTag.accessRight = 'ReadAndWrite'
                break
              case 3: // ProtectTypeEnum.NUMBER_3 - OnlyWrite
                fillFromTag.accessRight = 'OnlyWrite'
                break
              default:
                fillFromTag.accessRight = 'OnlyRead'
            }
          }
        } else if (tagToEdit.readWrite) {
          // 兼容旧格式
          switch (tagToEdit.readWrite) {
            case 'R':
            case 'Read':
              fillFromTag.accessRight = 'OnlyRead'
              break
            case 'W':
            case 'Write':
              fillFromTag.accessRight = 'OnlyWrite'
              break
            case 'RW':
            case 'ReadWrite':
              fillFromTag.accessRight = 'ReadAndWrite'
              break
            default:
              fillFromTag.accessRight = 'OnlyRead'
          }
        } else {
          fillFromTag.accessRight = 'OnlyRead'
        }

        // 处理其他字段
        fillFromTag.dataType =
          tagToEdit.transitionType || tagToEdit.dataType || 'Number'
        fillFromTag.protocolDataType = tagToEdit.dataType || ''
        fillFromTag.description = tagToEdit.description || ''
        fillFromTag.unit = tagToEdit.unit || ''
        fillFromTag.tags = tagToEdit.tags || []
        fillFromTag.processingScript =
          tagToEdit.content || tagToEdit.processingScript || ''
        fillFromTag.collectionInterval = tagToEdit.period || 0
        fillFromTag.decimalPlaces = tagToEdit.length || 2
        fillFromTag.actionOrder = tagToEdit.actionOrder || 0

        // 处理地址字段
        fillFromTag.address =
          tagToEdit.registerAddress || tagToEdit.address || ''
        // registerAddress 需要转换为数字类型
        const regAddr = tagToEdit.registerAddress || tagToEdit.address || '0'
        fillFromTag.registerAddress = parseInt(regAddr.toString()) || 0

        // 处理编码、方法、读取长度等驱动配置字段
        fillFromTag.encoding = tagToEdit.encoding || ''
        fillFromTag.method = tagToEdit.method || 'read'
        fillFromTag.readLength = tagToEdit.readLength || 1

        // 确保驱动配置字段正确映射（这些字段名可能与协议配置中的字段名一致）
        // 根据API响应，确保所有驱动相关字段都被正确设置
        if (tagToEdit.protocolDataType) {
          fillFromTag.protocolDataType = tagToEdit.protocolDataType
        } else if (tagToEdit.dataType) {
          fillFromTag.protocolDataType = tagToEdit.dataType
        }
        if (tagToEdit.encoding) fillFromTag.encoding = tagToEdit.encoding
        if (tagToEdit.method) fillFromTag.method = tagToEdit.method
        if (tagToEdit.readLength !== undefined) fillFromTag.readLength = tagToEdit.readLength

        // 处理自定义值映射字段
        fillFromTag.valueMapping = tagToEdit.custom || {}

        console.log('🔧 [编辑模式] 设置表单状态，详情数据:', {
          tagToEdit: tagToEdit,
          fillFromTag: fillFromTag,
          protocolDataType: fillFromTag.protocolDataType,
          encoding: fillFromTag.encoding,
          method: fillFromTag.method,
          registerAddress: fillFromTag.registerAddress,
          readLength: fillFromTag.readLength
        })

        setFormState(fillFromTag)

        console.log('🔧 [编辑模式] 即将调用 initializeConfigWithFormState')
        // 编辑模式下，传入当前表单状态来初始化配置，避免协议默认值覆盖实际值
        initializeConfigWithFormState(fillFromTag)
      } else {
        // 新增模式：只设置默认表单状态，defaultValues 由单独的 useEffect 处理
        setFormState({ ...defaultFormState })

        // 新增模式下立即初始化配置
        initializeConfig()
      }
    }
  }, [open, editMode, tagToEdit, protocol])

  // 处理 defaultValues 变化的 useEffect
  useEffect(() => {
    // 只在非编辑模式下，且对话框打开时处理 defaultValues
    if (!editMode && open && defaultValues && Object.keys(defaultValues).length > 0) {
      setFormState(prev => ({ ...prev, ...defaultValues }))
    }
  }, [editMode, open, JSON.stringify(defaultValues)]) // 使用 JSON.stringify 来比较对象内容

  // 添加监听defaultValues.processingScript变化的useEffect
  useEffect(() => {
    // 只在非编辑模式下，且对话框打开时更新脚本内容
    if (!editMode && open && defaultValues?.processingScript !== undefined) {
      setFormState((prev) => ({
        ...prev,
        processingScript: defaultValues.processingScript || '',
      }))
    }
  }, [defaultValues?.processingScript, editMode, open])

  // 配置初始化函数
  const initializeConfig = () => {
    // 防止重复加载
    if (configLoadedRef.current) return
    configLoadedRef.current = true

    // 如果有传入的协议配置，直接使用
    if (protocolConfig.length > 0) {
      setPropertyConfig(protocolConfig)
      processPropertyDefaults(protocolConfig)
    }
    // 如果有协议但没有传入配置，则通过API加载
    else if (protocol && !isLoadingConfig) {
      loadProtocolConfig()
    } else {
      // 确保将加载状态设置为false，避免一直显示加载中
      setIsLoadingConfig(false)
    }
  }

  // 带表单状态的配置初始化函数（用于编辑模式）
  const initializeConfigWithFormState = (currentFormState: PointFormState) => {
    console.log('🔧 [initializeConfigWithFormState] 开始执行', {
      configLoaded: configLoadedRef.current,
      protocolConfigLength: protocolConfig.length,
      protocol: protocol,
      isLoadingConfig: isLoadingConfig,
      currentFormState: {
        protocolDataType: currentFormState.protocolDataType,
        encoding: currentFormState.encoding,
        method: currentFormState.method,
        registerAddress: currentFormState.registerAddress,
        readLength: currentFormState.readLength
      }
    })

    // 防止重复加载
    if (configLoadedRef.current) {
      console.log('🔧 [initializeConfigWithFormState] 配置已加载，跳过')
      return
    }
    configLoadedRef.current = true

    // 如果有传入的协议配置，直接使用
    if (protocolConfig.length > 0) {
      console.log('🔧 [initializeConfigWithFormState] 使用传入的协议配置')
      setPropertyConfig(protocolConfig)
      processPropertyDefaults(protocolConfig, currentFormState)
    }
    // 如果有协议但没有传入配置，则通过API加载
    else if (protocol && !isLoadingConfig) {
      console.log('🔧 [initializeConfigWithFormState] 通过API加载协议配置')
      loadProtocolConfigWithFormState(currentFormState)
    } else {
      console.log('🔧 [initializeConfigWithFormState] 无需加载配置')
      // 确保将加载状态设置为false，避免一直显示加载中
      setIsLoadingConfig(false)
    }
  }

  // 处理属性的默认值
  const processPropertyDefaults = (config: PropertyConfig[], currentFormState?: PointFormState) => {
    console.log('🔧 [processPropertyDefaults] 开始处理', {
      editMode: editMode,
      configLength: config.length,
      currentFormState: currentFormState ? {
        protocolDataType: currentFormState.protocolDataType,
        encoding: currentFormState.encoding,
        method: currentFormState.method,
        registerAddress: currentFormState.registerAddress,
        readLength: currentFormState.readLength
      } : null,
      formState: {
        protocolDataType: formState.protocolDataType,
        encoding: formState.encoding,
        method: formState.method,
        registerAddress: formState.registerAddress,
        readLength: formState.readLength
      }
    })

    const dynamicDefaults: Record<string, any> = {}

    // 使用传入的表单状态或当前的表单状态
    const stateToCheck = currentFormState || formState

    config.forEach((prop) => {
      // 映射协议配置字段名到表单状态字段名
      const fieldMapping: Record<string, string> = {
        'RegisterAddress': 'registerAddress',
        'DataType': 'protocolDataType',
        'Encoding': 'encoding',
        'ReadLength': 'readLength',
        'ProtectType': 'accessRight', // 这个字段映射到访问权限
        'Method': 'method'
      }

      const formFieldName = fieldMapping[prop.code] || prop.code
      const existingValue = stateToCheck[formFieldName]
      const hasValue = existingValue !== undefined && existingValue !== ''

      console.log(`🔧 [processPropertyDefaults] 处理字段 ${prop.code} -> ${formFieldName}:`, {
        editMode: editMode,
        existingValue: existingValue,
        hasValue: hasValue,
        propDefaultValue: prop.defaultValue,
        propValue: prop.value,
        willSkip: editMode && hasValue
      })

      // 在编辑模式下，如果表单状态中已经有该字段的值，则不使用协议默认值覆盖
      if (editMode && hasValue) {
        console.log(`🔧 [processPropertyDefaults] 跳过字段 ${prop.code} -> ${formFieldName}，保持现有值: ${existingValue}`)
        return // 跳过，保持现有值
      }

      // 对于select类型，处理方式不同
      if (prop.type === 'select') {
        try {
          // 尝试从defaultValue解析选项
          let options: Record<string, string> = {}
          let defaultSelected = ''

          // 首先尝试从defaultValue解析选项
          if (
            prop.defaultValue &&
            typeof prop.defaultValue === 'string' &&
            prop.defaultValue.startsWith('{')
          ) {
            options = JSON.parse(prop.defaultValue) as Record<string, string>
            // 如果value有值，使用value作为默认选中项
            defaultSelected = prop.value || Object.values(options)[0] || ''
          }
          // 如果defaultValue不是选项列表，则从value解析选项
          else if (
            prop.value &&
            typeof prop.value === 'string' &&
            prop.value.startsWith('{')
          ) {
            options = JSON.parse(prop.value) as Record<string, string>
            // 使用defaultValue作为默认选中项
            defaultSelected =
              (prop.defaultValue as string) || Object.values(options)[0] || ''
          }
          // 如果都不是选项列表，使用默认值
          else {
            defaultSelected = prop.value || (prop.defaultValue as string) || ''
          }

          // 设置默认选中值
          dynamicDefaults[prop.code] = defaultSelected
        } catch (e) {
          console.error(`处理默认值错误 (${prop.name}):`, e)
          dynamicDefaults[prop.code] = prop.value || ''
        }
      } else {
        // 非select类型，直接使用defaultValue或value
        dynamicDefaults[prop.code] = prop.defaultValue || prop.value || ''
      }
    })

    console.log('🔧 [processPropertyDefaults] 动态默认值:', dynamicDefaults)

    // 更新表单状态，合并动态属性默认值
    // 在编辑模式下，只设置那些表单中还没有值的字段
    setFormState((prev) => {
      console.log('🔧 [processPropertyDefaults] setFormState 回调执行', {
        prev: {
          protocolDataType: prev.protocolDataType,
          encoding: prev.encoding,
          method: prev.method,
          registerAddress: prev.registerAddress,
          readLength: prev.readLength
        },
        dynamicDefaults: dynamicDefaults,
        editMode: editMode
      })

      const newState = { ...prev }

      // 只在新增模式下或字段为空时才设置默认值
      Object.keys(dynamicDefaults).forEach(key => {
        // 映射协议配置字段名到表单状态字段名
        const fieldMapping: Record<string, string> = {
          'RegisterAddress': 'registerAddress',
          'DataType': 'protocolDataType',
          'Encoding': 'encoding',
          'ReadLength': 'readLength',
          'ProtectType': 'accessRight',
          'Method': 'method'
        }

        const formFieldName = fieldMapping[key] || key
        const shouldSet = !editMode || prev[formFieldName] === undefined || prev[formFieldName] === ''
        console.log(`🔧 [processPropertyDefaults] 字段 ${key} -> ${formFieldName}: shouldSet=${shouldSet}, prevValue=${prev[formFieldName]}, defaultValue=${dynamicDefaults[key]}`)

        if (shouldSet) {
          newState[formFieldName] = dynamicDefaults[key]
        }
      })

      console.log('🔧 [processPropertyDefaults] 最终状态:', {
        protocolDataType: newState.protocolDataType,
        encoding: newState.encoding,
        method: newState.method,
        registerAddress: newState.registerAddress,
        readLength: newState.readLength
      })

      return newState
    })
  }

  // 加载协议配置 - 移至组件外部
  const loadProtocolConfig = () => {
    setIsLoadingConfig(true)

    // 如果没有协议名称，直接返回
    if (!protocol) {
      setIsLoadingConfig(false)
      return
    }

    // 调用API加载协议详情
    DriverService.getProtocolDetail(protocol)
      .then((response) => {
        if (response.code === 200 && response.data) {
          // 获取协议的属性配置
          const propConfig = response.data.propertyConfiguration || []
          // 转换为组件使用的PropertyConfig格式
          const convertedConfig: PropertyConfig[] = propConfig.map(
            (config) => ({
              code: config.code || '',
              name: config.name || '',
              value: config.value || '',
              defaultValue: config.defaultValue || '',
              type: config.type || 'text',
              display: config.display || false,
              displayExpress: config.displayExpress || '',
              order: config.order || 0,
            })
          )

          // 更新配置状态
          setPropertyConfig(convertedConfig)

          // 设置默认值
          if (convertedConfig.length > 0) {
            processPropertyDefaults(convertedConfig)
          }
        } else {
          // 处理错误情况
          console.error('加载协议配置失败:', response.msg)
          toast({
            title: '加载失败',
            description: '无法加载协议配置，请重试',
            variant: 'destructive',
          })
        }
      })
      .catch((error) => {
        console.error('加载协议配置异常:', error)
        toast({
          title: '加载失败',
          description: '加载协议配置时发生错误',
          variant: 'destructive',
        })
      })
      .finally(() => {
        setIsLoadingConfig(false)
      })
  }

  // 带表单状态的加载协议配置函数（用于编辑模式）
  const loadProtocolConfigWithFormState = (currentFormState: PointFormState) => {
    setIsLoadingConfig(true)

    // 如果没有协议名称，直接返回
    if (!protocol) {
      setIsLoadingConfig(false)
      return
    }

    // 调用API加载协议详情
    DriverService.getProtocolDetail(protocol)
      .then((response) => {
        if (response.code === 200 && response.data) {
          // 获取协议的属性配置
          const propConfig = response.data.propertyConfiguration || []
          // 转换为组件使用的PropertyConfig格式
          const convertedConfig: PropertyConfig[] = propConfig.map(
            (config) => ({
              code: config.code || '',
              name: config.name || '',
              value: config.value || '',
              defaultValue: config.defaultValue || '',
              type: config.type || 'text',
              display: config.display || false,
              displayExpress: config.displayExpress || '',
              order: config.order || 0,
            })
          )

          // 更新配置状态
          setPropertyConfig(convertedConfig)

          // 设置默认值，传入当前表单状态
          if (convertedConfig.length > 0) {
            processPropertyDefaults(convertedConfig, currentFormState)
          }
        } else {
          // 处理错误情况
          console.error('加载协议配置失败:', response.msg)
          toast({
            title: '加载失败',
            description: '无法加载协议配置，请重试',
            variant: 'destructive',
          })
        }
      })
      .catch((error) => {
        console.error('加载协议配置异常:', error)
        toast({
          title: '加载失败',
          description: '加载协议配置时发生错误',
          variant: 'destructive',
        })
      })
      .finally(() => {
        setIsLoadingConfig(false)
      })
  }

  // 解析属性配置的选项
  const parsePropertyOptions = (optionsStr: string): Record<string, string> => {
    try {
      return JSON.parse(optionsStr) as Record<string, string>
    } catch (e) {
      console.error('解析选项错误:', e)
      return {}
    }
  }

  // 检查属性是否应该显示
  const shouldPropertyDisplay = (property: PropertyConfig) => {
    // 如果display为false且没有displayExpress，则不显示
    if (!property.display && !property.displayExpress) return false

    // 如果display为true且没有displayExpress，则显示
    if (property.display && !property.displayExpress) return true

    // 如果有displayExpress，则根据表达式判断
    if (property.displayExpress) {
      try {
        const displayCondition = JSON.parse(property.displayExpress)

        // 检查每个条件
        for (const [field, values] of Object.entries(displayCondition)) {
          const currentValue = formState[field]
          // 如果字段值在条件值数组中，则显示
          if (Array.isArray(values) && values.includes(currentValue)) {
            return true
          }
        }

        // 如果没有匹配的条件，则使用display值
        return property.display
      } catch (e) {
        console.error('解析显示条件错误:', e)
        return property.display
      }
    }

    return property.display
  }

  // 表单字段变更处理
  const handleFormChange = (field: string, value: any) => {
    // 创建新的表单状态
    let newValue = value

    // 过滤空格 - 对字符串类型的值处理
    if (typeof value === 'string') {
      // 识别名特殊处理：只允许字母开头，只能包含字母、数字和下划线
      if (field === 'name') {
        // 先移除所有空格
        let filteredValue = value.replace(/\s/g, '')

        // 然后验证格式：字母开头，只包含字母、数字和下划线
        if (filteredValue) {
          // 如果第一个字符不是字母，强制转为a
          if (!/^[a-zA-Z]/.test(filteredValue)) {
            filteredValue = 'a' + filteredValue
          }

          // 移除除字母、数字和下划线外的所有字符
          filteredValue = filteredValue.replace(/[^a-zA-Z0-9_]/g, '')
        }

        newValue = filteredValue
      } else if (field === 'processingScript') {
        // 脚本内容特殊处理：保持原始格式，不进行任何字符过滤
        newValue = value
      } else {
        // 其他字段只移除首尾空格
        newValue = value.trim()
      }
    }

    // 创建新的表单状态
    const newFormState = {
      ...formState,
      [field]: newValue,
    }

    // 特殊处理：当数据来源改变时
    if (field === 'dataSource') {
      // 如果切换到设备读取模式，并且协议配置为空，则尝试加载配置
      if (value === 'device') {
        // 自动展开驱动配置面板
        if (!expandedSections.includes('driver')) {
          setExpandedSections([...expandedSections, 'driver'])
        }

        // 如果协议配置未加载，加载协议配置
        if (propertyConfig.length === 0 && protocol) {
          configLoadedRef.current = false
          initializeConfig()
        }
      } else {
        // 如果切换到非设备读取模式，清空驱动配置相关字段
        newFormState.address = ''
      }
    }

    // 更新表单状态
    setFormState(newFormState)

    // 清除该字段的错误（如果有）
    if (formErrors[field]) {
      setFormErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // 处理脚本编辑器打开
  const handleScriptEditorOpen = () => {
    if (onScriptOpen) {
      onScriptOpen(formState.processingScript || '', 'point')
    }
  }

  // 处理脚本保存
  const handleScriptSave = (scriptContent: string) => {
    // 直接更新表单状态，不经过handleFormChange的字符过滤
    setFormState((prev) => ({
      ...prev,
      processingScript: scriptContent, // 保持脚本内容的原始格式
    }))

    // 清除脚本字段的错误（如果有）
    if (formErrors.processingScript) {
      setFormErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors.processingScript
        return newErrors
      })
    }

    // 调用外部回调（如果提供）
    if (onScriptSave) {
      onScriptSave(scriptContent)
    }
  }

  // 表单验证 - 只验证基本字段和条件字段
  const validateForm = () => {
    const errors: Record<string, string> = {}

    // 基本信息验证
    if (!formState.name) {
      errors.name = '识别名不能为空'
    } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(formState.name)) {
      errors.name = '识别名必须以字母开头，只能包含字母、数字和下划线'
    }

    if (!formState.displayName) {
      errors.displayName = '属性名不能为空'
    }

    // 当数据来源为设备读取时，验证地址
    if (formState.dataSource === 'device') {
      // 映射协议配置字段名到表单状态字段名
      const getFormFieldName = (propCode: string): string => {
        const fieldMapping: Record<string, string> = {
          'RegisterAddress': 'registerAddress',
          'DataType': 'protocolDataType',
          'Encoding': 'encoding',
          'ReadLength': 'readLength',
          'ProtectType': 'accessRight',
          'Method': 'method'
        }
        return fieldMapping[propCode] || propCode
      }

      // 从属性配置中查找地址字段
      const addressField = propertyConfig.find(
        (config) =>
          config.code === 'RegisterAddress' ||
          config.code.toLowerCase().includes('address')
      )

      // 如果找到地址字段，检查其值
      if (addressField) {
        const formFieldName = getFormFieldName(addressField.code)
        const addressValue = formState[formFieldName]
        if (!addressValue || String(addressValue).trim() === '') {
          errors[formFieldName] = '设备读取模式下地址不能为空'
        }
      } else {
        // 如果没有找到地址字段，则检查通用地址字段
        const address = formState.address || formState.readAddress
        if (!address || address.trim() === '') {
          if (formState.readAddress !== undefined) {
            errors.readAddress = '读取地址不能为空'
          } else {
            errors.address = '地址不能为空'
          }
        }
      }
    }

    // 当数据来源为脚本计算时，验证脚本优先级
    if (formState.dataSource === 'script') {
      if (formState.actionOrder === undefined || formState.actionOrder < 0) {
        errors.actionOrder = '脚本优先级不能为空且必须大于等于0'
      }

      // 脚本内容也是必须的
      if (
        !formState.processingScript ||
        formState.processingScript.trim() === ''
      ) {
        errors.processingScript = '脚本计算模式下脚本内容不能为空'
      }
    }

    // 设置错误状态
    setFormErrors(errors)

    // 返回验证结果
    return Object.keys(errors).length === 0
  }

  // 计算表单完成度
  const getFormCompleteness = () => {
    // 只检查基本的必填字段：识别名和属性名
    const basicFieldsComplete = !!formState.name && !!formState.displayName

    // 直接使用basicFieldsComplete作为总体完成状态
    // 动态驱动配置不参与进度统计
    const isComplete = basicFieldsComplete

    // 进度直接为100%或0%
    const percentage = basicFieldsComplete ? 100 : 0

    return {
      percentage,
      isComplete,
      basicFieldsComplete,
    }
  }

  // 仅用于显示面板状态的辅助函数，不影响进度计算
  const getSectionStatus = (section: string) => {
    switch (section) {
      case 'basic':
        // 基本信息面板：只检查真正必填的字段（识别名和属性名）
        const basicRequired = ['name', 'displayName']
        const basicFilled = basicRequired.every((field) => !!formState[field])
        return {
          complete: basicFilled,
          badge: basicFilled ? (
            <Badge
              variant="outline"
              className="ml-2 bg-green-50 text-green-700 border-green-200 text-xs">
              已完成
            </Badge>
          ) : (
            <Badge
              variant="outline"
              className="ml-2 bg-amber-50 text-amber-700 border-amber-200 text-xs">
              待填写
            </Badge>
          ),
        }

      case 'tags':
        // 标签管理：完全可选
        return {
          complete: true,
          badge:
            formState.tags.length > 0 ? (
              <Badge
                variant="outline"
                className="ml-2 bg-blue-50 text-blue-700 border-blue-200 text-xs">
                {formState.tags.length}个标签
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="ml-2 bg-gray-50 text-gray-500 border-gray-200 text-xs">
                可选
              </Badge>
            ),
        }

      case 'advanced':
        // 高级配置：可选
        return {
          complete: true,
          badge: formState.processingScript ? (
            <Badge
              variant="outline"
              className="ml-2 bg-violet-50 text-violet-700 border-violet-200 text-xs">
              已配置脚本
            </Badge>
          ) : (
            <Badge
              variant="outline"
              className="ml-2 bg-gray-50 text-gray-500 border-gray-200 text-xs">
              可选
            </Badge>
          ),
        }

      case 'driver':
        // 驱动配置：完全可选
        return {
          complete: true,
          badge: (
            <Badge
              variant="outline"
              className="ml-2 bg-gray-50 text-gray-500 border-gray-200 text-xs">
              可选
            </Badge>
          ),
        }

      default:
        return { complete: true, badge: null }
    }
  }

  // 获取表单完成状态
  const completeness = getFormCompleteness()

  // 修改保存逻辑，对接真实API
  const handleSave = async () => {
    // 验证表单
    if (!validateForm()) {
      toast({
        title: '验证失败',
        description: '请填写必填字段',
        variant: 'destructive',
      })
      return
    }

    // 设置提交状态
    setIsSubmitting(true)
    if (onSubmitting) onSubmitting(true)

    try {
      console.log('💾 [handleSave] 准备保存数据', {
        editMode: editMode,
        collectionInterval: formState.collectionInterval,
        collectionIntervalType: typeof formState.collectionInterval,
        period: formState.collectionInterval !== undefined ? formState.collectionInterval : 0,
        formState: {
          collectionInterval: formState.collectionInterval,
          uploadInterval: formState.uploadInterval,
          uploadMethod: formState.uploadMethod
        }
      })

      // 构建点位数据 - 直接使用DeviceVariableAddInput接口
      const pointData: DeviceVariableAddInput = {
        // 必填字段
        deviceId: Number(deviceId),
        identifier: formState.name, // 使用name作为identifier
        name: formState.displayName, // 显示名称作为name

        // 设置数据来源
        valueSource:
          formState.dataSource === 'device'
            ? ValueSourceEnum.NUMBER_0 // 设备读取 Read = 0
            : formState.dataSource === 'script'
            ? ValueSourceEnum.NUMBER_1 // 统计计算 Calculate = 1
            : formState.dataSource === 'static'
            ? ValueSourceEnum.NUMBER_2 // 自然数据 Static = 2
            : ValueSourceEnum.NUMBER_3, // 虚拟属性 Fictitious = 3

        // 设置上报方式
        sendType:
          formState.uploadMethod === '变化上报'
            ? SendTypeEnum.NUMBER_3 // 变化上报 Changed = 3
            : formState.uploadMethod === '总是上报'
            ? SendTypeEnum.NUMBER_1 // 总是上报 Always = 1
            : formState.uploadMethod === '定时上报'
            ? SendTypeEnum.NUMBER_4 // 定时上报 Timed = 4
            : formState.uploadMethod === '永不上报'
            ? SendTypeEnum.NUMBER_2 // 永不上报 Never = 2
            : SendTypeEnum.NUMBER_1, // 默认总是上报

        // 可选字段
        tags: formState.tags || [], // 标签
        description: formState.description || '', // 描述
        period: formState.collectionInterval !== undefined ? formState.collectionInterval : 0, // 采集周期
        uploadInterval: formState.uploadInterval || 60, // 定时上报周期，单位秒
        archiveTime: formState.uploadMethod === '变化上报'
          ? formState.archiveTime || 3600
          : null, // 只在变化上报时设置强制归档时间
        length:
          formState.dataType === 'Number'
            ? formState.decimalPlaces || 2
            : undefined, // 小数位数仅适用于Number类型
        content: formState.processingScript || '', // 处理脚本
        transitionType: formState.dataType || 'Number', // 转换类型，使用dataType的值设置transitionType

        // 根据读写权限设置保护类型
        protectType:
          formState.accessRight === '只读'
            ? ProtectTypeEnum.NUMBER_1 // 仅可读 OnlyRead = 1
            : formState.accessRight === '只写'
            ? ProtectTypeEnum.NUMBER_3 // 仅可写 OnlyWrite = 3
            : ProtectTypeEnum.NUMBER_2, // 可读可写 ReadAndWrite = 2

        // 自定义字段对象
        custom: formState.valueMapping,
      }

      // 添加执行优先级字段 (actionOrder) - 仅在脚本计算模式下
      if (formState.dataSource === 'script') {
        pointData.actionOrder = formState.actionOrder || 0
      }

      // 处理协议特定的驱动配置
      if (propertyConfig.length > 0) {
        // 映射协议配置字段名到表单状态字段名
        const getFormFieldName = (propCode: string): string => {
          const fieldMapping: Record<string, string> = {
            'RegisterAddress': 'registerAddress',
            'DataType': 'protocolDataType',
            'Encoding': 'encoding',
            'ReadLength': 'readLength',
            'ProtectType': 'accessRight',
            'Method': 'method'
          }
          return fieldMapping[propCode] || propCode
        }

        propertyConfig.forEach((config) => {
          const formFieldName = getFormFieldName(config.code)
          const value = formState[formFieldName]
          if (value !== undefined) {
            switch (config.code) {
              case 'ProtectType':
                // 处理字符串到枚举值的映射
                if (typeof value === 'string') {
                  // 使用类型安全的映射方法
                  if (value === 'OnlyRead') {
                    pointData.protectType = ProtectTypeEnum.NUMBER_1
                  } else if (value === 'ReadAndWrite') {
                    pointData.protectType = ProtectTypeEnum.NUMBER_2
                  } else if (value === 'OnlyWrite') {
                    pointData.protectType = ProtectTypeEnum.NUMBER_3
                  }
                  // 如果没有匹配，保持原来的值
                } else {
                  // 处理数值类型的值
                  const numValue = Number(value?.value ?? value)
                  if (!isNaN(numValue)) {
                    if (numValue === 1) {
                      pointData.protectType = ProtectTypeEnum.NUMBER_1
                    } else if (numValue === 2) {
                      pointData.protectType = ProtectTypeEnum.NUMBER_2
                    } else if (numValue === 3) {
                      pointData.protectType = ProtectTypeEnum.NUMBER_3
                    }
                  }
                }
                break
              case 'DataType':
                pointData.dataType = value?.value ?? value // 协议数据类型映射到API的dataType
                break
              case 'Encoding':
                pointData.encoding = value?.value ?? value
                break
              case 'RegisterAddress':
                pointData.registerAddress = value?.value ?? value
                break
              case 'ReadLength':
                pointData.readLength = Number(value?.value ?? value)
                break
              case 'Method':
                pointData.method = value?.value ?? value
                break
              default:
                break
            }
          }
        })
      }

      // 处理表单中的protocolDataType字段，映射到API的dataType
      if (formState.protocolDataType && !pointData.dataType) {
        pointData.dataType = formState.protocolDataType
      }

      // 确保在设备读取模式下有方法字段
      if (formState.dataSource === 'device' && !pointData.method) {
        pointData.method = 'read' // 如果没有设置方法，使用默认的read方法
      }

      // 确保在设备读取模式下有地址
      if (
        formState.dataSource === 'device' &&
        (!pointData.registerAddress ||
          pointData.registerAddress.toString().trim() === '')
      ) {
        // 如果地址为空，尝试从formState中获取
        if (protocol === 'ModbusTCP' && formState.readAddress) {
          pointData.registerAddress = formState.readAddress
        } else if (formState.address) {
          pointData.registerAddress = formState.address
        }

        // 如果仍然为空，改变数据来源为虚拟点位
        if (
          !pointData.registerAddress ||
          pointData.registerAddress.toString().trim() === ''
        ) {
          pointData.valueSource = ValueSourceEnum.NUMBER_3 // 虚拟属性 Fictitious = 3
        }
      }

      console.log('💾 [handleSave] 构建的 pointData:', {
        period: pointData.period,
        uploadInterval: pointData.uploadInterval,
        collectionInterval: formState.collectionInterval,
        editMode: editMode
      })

      console.log('准备保存点位数据:', JSON.stringify(pointData, null, 2))

      let success = false

      if (editMode && tagToEdit) {
        // 编辑模式：更新现有点位
        const updatedTag: DeviceVariableEditInput = {
          ...pointData,
          id: tagToEdit.id,
          deviceId: Number(deviceId),
          // 编辑模式下保留原始标识符
          identifier: tagToEdit.identifier || tagToEdit.name || formState.name,
          // 使用新的字段值
          name: formState.displayName,
          transitionType: formState.dataType || 'Number',
          // 确保所有必需字段都存在
          sendType: pointData.sendType,
          valueSource: pointData.valueSource,
          protectType: pointData.protectType,
          content: formState.processingScript || '',
          period: formState.collectionInterval !== undefined ? formState.collectionInterval : 0,
          uploadInterval: formState.uploadInterval || 60, // 定时上报周期，单位秒
          archiveTime: formState.uploadMethod === '变化上报'
            ? formState.archiveTime || 3600
            : null, // 只在变化上报时设置强制归档时间
          length:
            formState.dataType === 'Number'
              ? formState.decimalPlaces || 2
              : undefined,
          description: formState.description || '',
          tags: formState.tags || [],
          actionOrder:
            formState.dataSource === 'script'
              ? formState.actionOrder || 0
              : undefined,
          // 驱动配置相关字段
          dataType: formState.protocolDataType || undefined,
          encoding: formState.encoding || undefined,
          method:
            formState.method ||
            (formState.dataSource === 'device' ? 'read' : undefined),
          registerAddress:
            (formState.address || formState.registerAddress)?.toString() ||
            undefined,
          readLength: formState.readLength || undefined,
          // 自定义字段
          custom: formState.valueMapping || undefined,
        }

        console.log('💾 [handleSave] 编辑模式 updatedTag:', {
          period: updatedTag.period,
          uploadInterval: updatedTag.uploadInterval,
          collectionInterval: formState.collectionInterval,
          fullUpdatedTag: updatedTag
        })

        // 调用API更新点位
        success = await TagService.editTag(updatedTag)
      } else {
        // 添加模式：创建新点位 - 直接传入DeviceVariableAddInput类型
        success = await TagService.addTag(pointData)
      }

      if (success) {
        if (editMode && tagToEdit && onTagUpdated) {
          // 编辑模式下，调用更新回调
          onTagUpdated({
            ...tagToEdit,
            ...pointData,
            name: formState.displayName,
            identifier: tagToEdit.identifier || formState.name,
          })
          toast({
            title: '更新成功',
            description: `点位 ${formState.displayName || '未命名'} 已成功更新`,
          })
        } else if (!editMode && onPointAdded) {
          // 新增模式下，调用添加回调
          onPointAdded(pointData)
          toast({
            title: '添加成功',
            description: `点位 ${formState.displayName || '未命名'} 已成功添加`,
          })
        } else {
          // 没有回调但操作成功的情况
          toast({
            title: editMode ? '更新成功' : '添加成功',
            description: `点位 ${formState.displayName || '未命名'} 已${
              editMode ? '更新' : '添加'
            }成功`,
          })
        }
      } else {
        throw new Error('保存失败')
      }

      // 关闭对话框
      onOpenChange(false)
    } catch (error: any) {
      console.error(editMode ? '更新失败' : '添加失败:', error)

      // 提取API错误信息
      let errorMessage =
        error.message || (editMode ? '无法更新点位' : '无法添加新点位')

      // 检查是否有API返回的详细错误信息
      if (error.response?.data?.msg) {
        errorMessage = error.response.data.msg
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.response?.data?.errors) {
        // 处理错误数组
        if (Array.isArray(error.response.data.errors)) {
          errorMessage = error.response.data.errors.join(', ')
        } else if (typeof error.response.data.errors === 'object') {
          // 处理错误对象
          const errorObj = error.response.data.errors
          const errorMessages = Object.keys(errorObj).map(
            (key) => `${key}: ${errorObj[key]}`
          )
          errorMessage = errorMessages.join('; ')
        }
      } else if (error.response?.statusText) {
        errorMessage = `${error.response.status} - ${error.response.statusText}`
      }

      // 如果存在错误详情字段 (比如ValidationErrors)
      if (error.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors
        if (typeof validationErrors === 'object') {
          const errorMessages = Object.keys(validationErrors).map(
            (key) => `${key}: ${validationErrors[key]}`
          )
          errorMessage = errorMessages.join('; ')
        }
      }

      toast({
        title: editMode ? '更新失败' : '添加失败',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
      if (onSubmitting) onSubmitting(false)
    }
  }

  // 处理自定义标签添加
  const handleAddCustomTag = () => {
    const trimmedTag = newTagInput.trim()
    if (trimmedTag && !formState.tags.includes(trimmedTag)) {
      handleFormChange('tags', [...formState.tags, trimmedTag])
      setNewTagInput('') // 清空输入
    }
  }

  // 渲染动态属性字段
  const renderDynamicProperty = (property: PropertyConfig) => {
    // 如果不应该显示，返回null
    if (!shouldPropertyDisplay(property)) return null

    // 映射协议配置字段名到表单状态字段名
    const getFormFieldName = (propCode: string): string => {
      const fieldMapping: Record<string, string> = {
        'RegisterAddress': 'registerAddress',
        'DataType': 'protocolDataType',
        'Encoding': 'encoding',
        'ReadLength': 'readLength',
        'ProtectType': 'accessRight',
        'Method': 'method'
      }
      return fieldMapping[propCode] || propCode
    }

    const formFieldName = getFormFieldName(property.code)

    // 根据类型渲染不同的表单控件
    switch (property.type) {
      case 'select':
        // 修改：尝试从defaultValue解析选项，如果解析失败再尝试从value解析
        let options: Record<string, string> = {}
        try {
          // 首先尝试从defaultValue解析选项
          if (
            property.defaultValue &&
            typeof property.defaultValue === 'string' &&
            property.defaultValue.startsWith('{')
          ) {
            options = JSON.parse(property.defaultValue) as Record<
              string,
              string
            >
          }
          // 如果defaultValue不是有效的JSON或为空，则尝试从value解析
          else if (property.value && typeof property.value === 'string') {
            options = parsePropertyOptions(property.value)
          }
        } catch (e) {
          console.error(`解析选项错误 (${property.name}):`, e)
          options = {}
        }

        // 设置默认值：如果value存在且不是JSON对象，则使用value作为默认值
        const defaultValue =
          property.value &&
          typeof property.value === 'string' &&
          !property.value.startsWith('{')
            ? property.value
            : Object.values(options)[0] || ''

        return (
          <div className="space-y-2" key={property.code}>
            <Label htmlFor={property.code}>{property.name}</Label>
            <Select
              value={formState[formFieldName] || defaultValue}
              onValueChange={(value) => handleFormChange(formFieldName, value)}>
              <SelectTrigger id={property.code}>
                <SelectValue placeholder={`选择${property.name}`} />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(options).map(([label, value]) => (
                  <SelectItem key={String(value)} value={String(value)}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )

      case 'text':
      default:
        return (
          <div className="space-y-2" key={property.code}>
            <Label htmlFor={property.code}>{property.name}</Label>
            <Input
              id={property.code}
              placeholder={`输入${property.name}`}
              value={formState[formFieldName] || property.defaultValue}
              onChange={(e) => handleFormChange(formFieldName, e.target.value)}
            />
          </div>
        )
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        onOpenChange(open)
        if (!open) {
          // 重置表单状态和错误
          setFormErrors({})
        }
      }}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] overflow-auto bg-white border-2 border-gray-300 shadow-xl"
        backgroundOpacity={0.9}>
        <DialogHeader>
          <DialogTitle>{editMode ? '编辑点位' : '新增点位'}</DialogTitle>
          <DialogDescription>
            {editMode
              ? `编辑设备 ${deviceName} 的点位 ${tagToEdit?.name || ''}`
              : `为设备 ${deviceName} 添加新的数据点位`}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-muted-foreground mb-4">
            请填写点位信息，带 <span className="text-red-500">*</span>{' '}
            的字段为必填项
          </p>

          <Accordion
            type="multiple"
            value={expandedSections}
            onValueChange={setExpandedSections}
            className="w-full space-y-4">
            {/* 基本信息面板 */}
            <AccordionItem
              value="basic"
              className="border rounded-md shadow-sm">
              <AccordionTrigger className="px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-t-md">
                <div className="flex items-center">
                  <span className="text-base font-medium">基本信息</span>
                  {getSectionStatus('basic').badge}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pt-4 pb-2">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="flex items-center">
                        识别名 <span className="text-red-500 ml-1">*</span>
                        {formErrors.name && (
                          <span className="text-red-500 text-xs ml-2">
                            {formErrors.name}
                          </span>
                        )}
                      </Label>
                      <div className="flex gap-2">
                        <Input
                          id="name"
                          placeholder="输入识别名，保证唯一性"
                          className="flex-1"
                          value={formState.name}
                          onChange={(e) =>
                            handleFormChange('name', e.target.value)
                          }
                          disabled={editMode} // 编辑模式下禁用识别名修改
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="displayName"
                        className="flex items-center">
                        属性名 <span className="text-red-500 ml-1">*</span>
                        {formErrors.displayName && (
                          <span className="text-red-500 text-xs ml-2">
                            {formErrors.displayName}
                          </span>
                        )}
                      </Label>
                      <div className="flex gap-2">
                        <Input
                          id="displayName"
                          placeholder="输入属性名，允许重复"
                          className="flex-1"
                          value={formState.displayName}
                          onChange={(e) =>
                            handleFormChange('displayName', e.target.value)
                          }
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="dataSource">
                        数据来源 <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        defaultValue={formState.dataSource}
                        value={formState.dataSource}
                        onValueChange={(value) =>
                          handleFormChange('dataSource', value)
                        }
                        disabled={editMode}>
                        {' '}
                        {/* 编辑模式下禁用数据来源修改 */}
                        <SelectTrigger id="dataSource">
                          <SelectValue placeholder="选择数据来源" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="device">设备读取</SelectItem>
                          <SelectItem value="script">脚本计算</SelectItem>
                          <SelectItem value="static">静态属性</SelectItem>
                          <SelectItem value="virtual">虚拟点位</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="dataType">
                        数据类型 <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={formState.dataType}
                        onValueChange={(value) =>
                          handleFormChange('dataType', value)
                        }
                        disabled={editMode}>
                        {' '}
                        {/* 编辑模式下禁用数据类型修改 */}
                        <SelectTrigger
                          id="dataType"
                          className="border border-gray-200 bg-white hover:border-primary focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors">
                          <SelectValue placeholder="选择数据类型" />
                        </SelectTrigger>
                        <SelectContent className="max-h-80 p-1 border border-gray-200 shadow-lg bg-white min-w-[320px]">
                          <div className="space-y-1">
                            <SelectItem
                              value="Number"
                              className="p-0 m-0 data-[highlighted]:bg-blue-50 focus:bg-blue-50 rounded-lg">
                              <div className="flex items-center w-full p-3 rounded-lg hover:bg-blue-50 transition-colors cursor-pointer">
                                <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
                                  <span className="text-blue-600 font-bold text-xs">
                                    123
                                  </span>
                                </div>
                                <div className="flex-grow min-w-0">
                                  <p className="font-semibold text-gray-900 text-sm">
                                    Number
                                  </p>
                                  <p className="text-xs text-gray-500 truncate">
                                    数值类型，支持小数
                                  </p>
                                </div>
                              </div>
                            </SelectItem>

                            <SelectItem
                              value="Integer"
                              className="p-0 m-0 data-[highlighted]:bg-green-50 focus:bg-green-50 rounded-lg">
                              <div className="flex items-center w-full p-3 rounded-lg hover:bg-green-50 transition-colors cursor-pointer">
                                <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center mr-3">
                                  <span className="text-green-600 font-bold text-xs">
                                    42
                                  </span>
                                </div>
                                <div className="flex-grow min-w-0">
                                  <p className="font-semibold text-gray-900 text-sm">
                                    Integer
                                  </p>
                                  <p className="text-xs text-gray-500 truncate">
                                    整数类型
                                  </p>
                                </div>
                              </div>
                            </SelectItem>

                            <SelectItem
                              value="Boolean"
                              className="p-0 m-0 data-[highlighted]:bg-yellow-50 focus:bg-yellow-50 rounded-lg">
                              <div className="flex items-center w-full p-3 rounded-lg hover:bg-yellow-50 transition-colors cursor-pointer">
                                <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-yellow-100 flex items-center justify-center mr-3">
                                  <span className="text-yellow-600 font-bold text-xs">
                                    T/F
                                  </span>
                                </div>
                                <div className="flex-grow min-w-0">
                                  <p className="font-semibold text-gray-900 text-sm">
                                    Boolean
                                  </p>
                                  <p className="text-xs text-gray-500 truncate">
                                    布尔类型
                                  </p>
                                </div>
                              </div>
                            </SelectItem>

                            <SelectItem
                              value="String"
                              className="p-0 m-0 data-[highlighted]:bg-red-50 focus:bg-red-50 rounded-lg">
                              <div className="flex items-center w-full p-3 rounded-lg hover:bg-red-50 transition-colors cursor-pointer">
                                <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-red-100 flex items-center justify-center mr-3">
                                  <span className="text-red-600 font-bold text-xs">
                                    Abc
                                  </span>
                                </div>
                                <div className="flex-grow min-w-0">
                                  <p className="font-semibold text-gray-900 text-sm">
                                    String
                                  </p>
                                  <p className="text-xs text-gray-500 truncate">
                                    字符串类型
                                  </p>
                                </div>
                              </div>
                            </SelectItem>

                            <SelectItem
                              value="Object"
                              className="p-0 m-0 data-[highlighted]:bg-purple-50 focus:bg-purple-50 rounded-lg">
                              <div className="flex items-center w-full p-3 rounded-lg hover:bg-purple-50 transition-colors cursor-pointer">
                                <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center mr-3">
                                  <span className="text-purple-600 font-bold text-xs">
                                    {}
                                  </span>
                                </div>
                                <div className="flex-grow min-w-0">
                                  <p className="font-semibold text-gray-900 text-sm">
                                    Object
                                  </p>
                                  <p className="text-xs text-gray-500 truncate">
                                    对象类型
                                  </p>
                                </div>
                              </div>
                            </SelectItem>

                            <SelectItem
                              value="Array"
                              className="p-0 m-0 data-[highlighted]:bg-teal-50 focus:bg-teal-50 rounded-md">
                              <div className="flex items-center w-full p-3 rounded-md hover:bg-teal-50 transition-colors cursor-pointer">
                                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-teal-100 flex items-center justify-center mr-3">
                                  <span className="text-teal-600 font-semibold text-sm">
                                    [ ]
                                  </span>
                                </div>
                                <div className="flex-grow min-w-0">
                                  <p className="font-medium text-gray-900 text-sm">
                                    Array
                                  </p>
                                  <p className="text-xs text-gray-500 truncate">
                                    数组类型
                                  </p>
                                </div>
                              </div>
                            </SelectItem>
                          </div>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* 脚本优先级和小数位数的布局，将它们放在同一行 */}
                  {formState.dataSource === 'script' && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="actionOrder">
                          脚本优先级{' '}
                          <span className="text-gray-500 text-xs">
                            (数值越小优先级越高，0为最高)
                          </span>
                        </Label>
                        <div className="flex items-center">
                          <Input
                            id="actionOrder"
                            type="number"
                            min="0"
                            value={formState.actionOrder}
                            onChange={(e) =>
                              handleFormChange(
                                'actionOrder',
                                parseInt(e.target.value) || 0
                              )
                            }
                            className="w-24"
                          />
                          <span className="ml-2">级别</span>
                        </div>
                      </div>

                      {/* 小数位数只在数据类型为Number时显示，放在脚本优先级的右侧 */}
                      {formState.dataType === 'Number' && (
                        <div className="space-y-2">
                          <Label htmlFor="decimalPlaces">小数位数</Label>
                          <div className="flex items-center">
                            <Input
                              id="decimalPlaces"
                              type="number"
                              min="0"
                              max="10"
                              value={formState.decimalPlaces}
                              onChange={(e) =>
                                handleFormChange(
                                  'decimalPlaces',
                                  parseInt(e.target.value) || 0
                                )
                              }
                              className="w-24"
                            />
                            <span className="ml-2">位</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* 当不是脚本计算且数据类型是Number时，单独显示小数位数 */}
                  {formState.dataSource !== 'script' &&
                    formState.dataType === 'Number' && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="unit">单位</Label>
                          <Input
                            id="unit"
                            placeholder="例如: °C, kg, m³"
                            value={formState.unit}
                            onChange={(e) =>
                              handleFormChange('unit', e.target.value)
                            }
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="decimalPlaces">小数位数</Label>
                          <div className="flex items-center">
                            <Input
                              id="decimalPlaces"
                              type="number"
                              min="0"
                              max="10"
                              value={formState.decimalPlaces}
                              onChange={(e) =>
                                handleFormChange(
                                  'decimalPlaces',
                                  parseInt(e.target.value) || 0
                                )
                              }
                              className="w-24"
                            />
                            <span className="ml-2">位</span>
                          </div>
                        </div>
                      </div>
                    )}

                  {/* 当不是脚本计算且数据类型不是Number时，只显示单位 */}
                  {formState.dataSource !== 'script' &&
                    formState.dataType !== 'Number' && (
                      <div className="space-y-2">
                        <Label htmlFor="unit">单位</Label>
                        <Input
                          id="unit"
                          placeholder="例如: °C, kg, m³"
                          value={formState.unit}
                          onChange={(e) =>
                            handleFormChange('unit', e.target.value)
                          }
                        />
                      </div>
                    )}

                  {/* 完全重写值映射规则部分 */}
                  {formState.dataType === 'Integer' && (
                    <div className="space-y-3 border border-gray-200 rounded-md p-4 bg-gray-50">
                      <Label
                        htmlFor="valueMapping"
                        className="flex items-center justify-between mb-2">
                        <span className="font-medium">值映射规则</span>
                        <span className="text-xs text-gray-500">
                          (可选) 数值对应的实际含义
                        </span>
                      </Label>

                      {/* 使用表单元素和原生按钮实现 */}
                      <div className="flex gap-3 items-end">
                        <div className="w-1/3">
                          <Label
                            htmlFor="mappingValue"
                            className="text-xs text-gray-600 mb-1 block">
                            数值
                          </Label>
                          <input
                            id="mappingValue"
                            type="text"
                            placeholder="例如: 0"
                            value={formState.currentMappingValue || ''}
                            onChange={(e) =>
                              handleFormChange(
                                'currentMappingValue',
                                e.target.value
                              )
                            }
                            className="w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 h-9 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                          />
                        </div>
                        <div className="flex-1">
                          <Label
                            htmlFor="mappingMeaning"
                            className="text-xs text-gray-600 mb-1 block">
                            含义
                          </Label>
                          <input
                            id="mappingMeaning"
                            type="text"
                            placeholder="例如: 关闭"
                            value={formState.currentMappingMeaning || ''}
                            onChange={(e) =>
                              handleFormChange(
                                'currentMappingMeaning',
                                e.target.value
                              )
                            }
                            className="w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 h-9 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                          />
                        </div>
                        <button
                          type="button"
                          className="h-9 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-md"
                          onClick={(e) => {
                            e.preventDefault() // 阻止任何默认行为

                            // 确保我们有有效的输入
                            if (
                              !formState.currentMappingValue ||
                              !formState.currentMappingMeaning
                            ) {
                              return
                            }

                            // 创建新的状态
                            const currentMappings = {
                              ...(formState.valueMapping || {}),
                            }
                            currentMappings[formState.currentMappingValue] =
                              formState.currentMappingMeaning

                            // 直接更新组件状态
                            setFormState({
                              ...formState,
                              valueMapping: currentMappings,
                              currentMappingValue: '', // 清空数值输入框
                            })
                          }}>
                          添加
                        </button>
                      </div>

                      {/* 显示已添加的映射 */}
                      {formState.valueMapping &&
                        Object.keys(formState.valueMapping).length > 0 && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <Label className="text-xs text-gray-600 mb-2 block">
                              已添加的映射
                            </Label>
                            <div className="flex flex-wrap gap-2">
                              {Object.entries(formState.valueMapping).map(
                                ([value, meaning]) => (
                                  <div
                                    key={value}
                                    className="bg-white border border-gray-200 rounded-md px-2 py-1 text-xs flex items-center">
                                    <span className="font-medium mr-1">
                                      {value}:
                                    </span>
                                    <span className="text-gray-600">
                                      {String(meaning)}
                                    </span>
                                    <button
                                      type="button"
                                      className="ml-2 text-gray-400 hover:text-red-500"
                                      onClick={(e) => {
                                        e.preventDefault()

                                        // 创建新的映射对象并删除当前项
                                        const newMapping = {
                                          ...formState.valueMapping,
                                        }
                                        delete newMapping[value]

                                        // 直接更新组件状态
                                        setFormState({
                                          ...formState,
                                          valueMapping: newMapping,
                                        })
                                      }}>
                                      <X className="h-3 w-3" />
                                    </button>
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="description">描述</Label>
                    <Textarea
                      id="description"
                      placeholder="请输入点位描述"
                      className="min-h-[80px]"
                      value={formState.description}
                      onChange={(e) =>
                        handleFormChange('description', e.target.value)
                      }
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 标签管理面板 */}
            <AccordionItem value="tags" className="border rounded-md shadow-sm">
              <AccordionTrigger className="px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-t-md">
                <div className="flex items-center">
                  <span className="text-base font-medium">标签管理</span>
                  {getSectionStatus('tags').badge}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pt-4 pb-2">
                <div className="space-y-2">
                  <Label htmlFor="tags">标签 (可多选)</Label>
                  <div className="border rounded-md p-3">
                    {/* 预设标签 */}
                    <div className="flex flex-wrap gap-2 mb-3">
                      {[
                        {
                          name: '温度',
                          color: 'bg-red-500/90 hover:bg-red-500',
                        },
                        {
                          name: '压力',
                          color: 'bg-blue-500/90 hover:bg-blue-500',
                        },
                        {
                          name: '湿度',
                          color: 'bg-sky-500/90 hover:bg-sky-500',
                        },
                        {
                          name: '流量',
                          color: 'bg-cyan-500/90 hover:bg-cyan-500',
                        },
                        {
                          name: '液位',
                          color: 'bg-teal-500/90 hover:bg-teal-500',
                        },
                        {
                          name: '速度',
                          color: 'bg-violet-500/90 hover:bg-violet-500',
                        },
                        {
                          name: '电压',
                          color: 'bg-yellow-500/90 hover:bg-yellow-500',
                        },
                        {
                          name: '电流',
                          color: 'bg-orange-500/90 hover:bg-orange-500',
                        },
                        {
                          name: '功率',
                          color: 'bg-pink-500/90 hover:bg-pink-500',
                        },
                        {
                          name: '能耗',
                          color: 'bg-green-500/90 hover:bg-green-500',
                        },
                      ].map((tag) => (
                        <Badge
                          key={tag.name}
                          variant={
                            formState.tags.includes(tag.name)
                              ? 'default'
                              : 'outline'
                          }
                          className={`cursor-pointer text-sm px-3 py-1 transition-all ${
                            formState.tags.includes(tag.name)
                              ? `${tag.color} text-white`
                              : 'bg-background hover:bg-muted/30 hover:border-muted-foreground/50'
                          }`}
                          onClick={() => {
                            if (formState.tags.includes(tag.name)) {
                              handleFormChange(
                                'tags',
                                formState.tags.filter((t) => t !== tag.name)
                              )
                            } else {
                              handleFormChange('tags', [
                                ...formState.tags,
                                tag.name,
                              ])
                            }
                          }}>
                          {tag.name}
                          {formState.tags.includes(tag.name) && (
                            <X className="ml-1.5 h-3.5 w-3.5 opacity-70" />
                          )}
                        </Badge>
                      ))}
                    </div>

                    {/* 自定义标签添加 */}
                    <div className="flex items-center mt-4 border-t pt-3">
                      <Input
                        value={newTagInput}
                        onChange={(e) => setNewTagInput(e.target.value)}
                        placeholder="添加自定义标签"
                        className="flex-1 h-9 rounded-l-md rounded-r-none border-r-0"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            handleAddCustomTag()
                          }
                        }}
                      />
                      <Button
                        variant="default"
                        size="sm"
                        className="h-9 rounded-l-none"
                        onClick={handleAddCustomTag}
                        disabled={!newTagInput.trim()}>
                        <Plus className="h-4 w-4 mr-1" />
                        添加
                      </Button>
                    </div>

                    {/* 自定义标签显示 */}
                    {formState.tags.some(
                      (tag) =>
                        ![
                          '温度',
                          '压力',
                          '湿度',
                          '流量',
                          '液位',
                          '速度',
                          '电压',
                          '电流',
                          '功率',
                          '能耗',
                        ].includes(tag)
                    ) && (
                      <div className="mt-3 border-t pt-3">
                        <div className="text-sm text-muted-foreground mb-2">
                          自定义标签:
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {formState.tags
                            .filter(
                              (tag) =>
                                ![
                                  '温度',
                                  '压力',
                                  '湿度',
                                  '流量',
                                  '液位',
                                  '速度',
                                  '电压',
                                  '电流',
                                  '功率',
                                  '能耗',
                                ].includes(tag)
                            )
                            .map((tag) => (
                              <Badge
                                key={tag}
                                variant="secondary"
                                className="cursor-pointer flex items-center text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-800">
                                {tag}
                                <button
                                  className="ml-1.5 rounded-full hover:bg-muted/40 p-0.5"
                                  onClick={() =>
                                    handleFormChange(
                                      'tags',
                                      formState.tags.filter((t) => t !== tag)
                                    )
                                  }>
                                  <X className="h-3.5 w-3.5" />
                                </button>
                              </Badge>
                            ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            {/* 高级配置面板 */}
            <AccordionItem
              value="advanced"
              className="border rounded-md shadow-sm">
              <AccordionTrigger className="px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-t-md">
                <div className="flex items-center">
                  <span className="text-base font-medium">高级配置</span>
                  {getSectionStatus('advanced').badge}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pt-4 pb-2">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="uploadMethod">
                      上报方式 <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formState.uploadMethod}
                      onValueChange={(value) =>
                        handleFormChange('uploadMethod', value)
                      }>
                      <SelectTrigger id="uploadMethod">
                        <SelectValue placeholder="选择上报方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="总是上报">总是上报</SelectItem>
                        <SelectItem value="变化上报">变化上报</SelectItem>
                        <SelectItem value="定时上报">定时上报</SelectItem>
                        <SelectItem value="永不上报">永不上报</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="collectionInterval">
                        采集间隔 <span className="text-red-500">*</span>
                      </Label>
                      <div className="flex items-center">
                        <Input
                          id="collectionInterval"
                          type="number"
                          min="0"
                          value={formState.collectionInterval}
                          onChange={(e) =>
                            handleFormChange(
                              'collectionInterval',
                              parseInt(e.target.value) || 0
                            )
                          }
                          className="w-20"
                        />
                        <span className="ml-1 text-sm">毫秒</span>
                      </div>
                    </div>

                    {/* 定时上报周期配置 */}
                    {formState.uploadMethod === '定时上报' && (
                      <div className="space-y-2">
                        <Label htmlFor="uploadInterval">
                          定时上报周期 <span className="text-red-500">*</span>
                        </Label>
                        <div className="flex items-center">
                          <Input
                            id="uploadInterval"
                            type="number"
                            min="1"
                            value={formState.uploadInterval}
                            onChange={(e) =>
                              handleFormChange(
                                'uploadInterval',
                                parseInt(e.target.value) || 60
                              )
                            }
                            className="w-20"
                          />
                          <span className="ml-1 text-sm">秒</span>
                        </div>
                        <p className="text-xs text-gray-500">
                          设置定时上报的时间间隔，单位为秒
                        </p>
                      </div>
                    )}

                    {/* 强制归档时间配置 - 只在变化上报时显示 */}
                    {formState.uploadMethod === '变化上报' && (
                      <div className="space-y-2">
                        <Label htmlFor="archiveTime">
                          强制归档时间 <span className="text-red-500">*</span>
                        </Label>
                        <div className="flex items-center">
                          <Input
                            id="archiveTime"
                            type="number"
                            min="1"
                            value={formState.archiveTime}
                            onChange={(e) =>
                              handleFormChange(
                                'archiveTime',
                                parseInt(e.target.value) || 3600
                              )
                            }
                            className="w-20"
                          />
                          <span className="ml-1 text-sm">秒</span>
                        </div>
                        <p className="text-xs text-gray-500">
                          设置强制归档的时间间隔，单位为秒
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="dataProcessing">
                        数据处理
                        {formState.dataSource === 'script' && (
                          <span className="text-red-500 ml-1">*</span>
                        )}
                        {formState.dataSource === 'script' && (
                          <span className="text-amber-500 text-xs ml-2">
                            (脚本计算模式下必填)
                          </span>
                        )}
                      </Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleScriptEditorOpen}>
                        <Code className="h-4 w-4 mr-2" />
                        编辑脚本
                      </Button>
                    </div>
                    <Textarea
                      id="dataProcessing"
                      value={formState.processingScript}
                      onChange={(e) =>
                        handleFormChange('processingScript', e.target.value)
                      }
                      placeholder=""
                      className={`min-h-[120px] font-mono ${
                        formErrors.processingScript ? 'border-red-500' : ''
                      }`}
                    />
                    {formErrors.processingScript && (
                      <p className="text-red-500 text-xs mt-1">
                        {formErrors.processingScript}
                      </p>
                    )}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* 驱动配置面板 - 只在数据来源为设备读取时显示 */}
            {formState.dataSource === 'device' && (
              <AccordionItem
                value="driver"
                className="border rounded-md shadow-sm">
                <AccordionTrigger className="px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-t-md">
                  <div className="flex items-center">
                    <span className="text-base font-medium">驱动配置</span>
                    {getSectionStatus('driver').badge}
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pt-4 pb-2">
                  {isLoadingConfig ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="flex items-center">
                        <svg
                          className="animate-spin -ml-1 mr-2 h-5 w-5 text-primary"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24">
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        加载协议配置中...
                      </div>
                    </div>
                  ) : propertyConfig.length > 0 ? (
                    // 动态渲染配置项
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      {[...propertyConfig]
                        .sort((a, b) => a.order - b.order)
                        .map(renderDynamicProperty)}
                    </div>
                  ) : (
                    // 无配置或加载失败时显示的内容
                    <div className="space-y-4">
                      <div className="bg-amber-50 p-4 rounded-md flex flex-col items-center">
                        <p className="text-amber-800 mb-2">
                          未找到协议特定配置或加载失败
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            configLoadedRef.current = false
                            initializeConfig()
                          }}
                          className="mt-2">
                          <svg
                            className="mr-2 h-4 w-4"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                            />
                          </svg>
                          重新加载
                        </Button>
                      </div>

                      {/* 根据协议类型显示不同的地址字段 */}
                      {protocol === 'ModbusTCP' ? (
                        <>
                          <div className="space-y-2 mb-4">
                            <Label
                              htmlFor="readWriteAccess"
                              className="flex items-center">
                              读写权限
                            </Label>
                            <Select
                              value={formState.readWriteAccess || '仅可读'}
                              onValueChange={(value) =>
                                handleFormChange('readWriteAccess', value)
                              }>
                              <SelectTrigger id="readWriteAccess">
                                <SelectValue placeholder="选择读写权限" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="仅可读">仅可读</SelectItem>
                                <SelectItem value="仅可写">仅可写</SelectItem>
                                <SelectItem value="可读可写">
                                  可读可写
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2 mb-4">
                            <Label
                              htmlFor="readAddress"
                              className="flex items-center">
                              读取地址
                              {formErrors.readAddress && (
                                <span className="text-red-500 text-xs ml-2">
                                  {formErrors.readAddress}
                                </span>
                              )}
                            </Label>
                            <Input
                              id="readAddress"
                              placeholder="输入读取地址"
                              value={formState.readAddress || ''}
                              onChange={(e) =>
                                handleFormChange('readAddress', e.target.value)
                              }
                            />
                          </div>

                          <div className="space-y-2 mb-4">
                            <Label
                              htmlFor="protocolDataType"
                              className="flex items-center">
                              协议数据类型
                            </Label>
                            <Select
                              value={formState.protocolDataType || 'int16'}
                              onValueChange={(value) =>
                                handleFormChange('protocolDataType', value)
                              }>
                              <SelectTrigger id="protocolDataType">
                                <SelectValue placeholder="选择协议数据类型" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="int16">int16</SelectItem>
                                <SelectItem value="uint16">uint16</SelectItem>
                                <SelectItem value="int32">int32</SelectItem>
                                <SelectItem value="uint32">uint32</SelectItem>
                                <SelectItem value="float">float</SelectItem>
                                <SelectItem value="double">double</SelectItem>
                                <SelectItem value="boolean">boolean</SelectItem>
                                <SelectItem value="string">string</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </>
                      ) : (
                        <div className="space-y-2 mb-4">
                          <SmartAddressField
                            deviceId= {deviceId}
                            label="地址"
                            placeholder="输入采集点地址"
                            value={formState.address || ''}
                            onChange={(value) => handleFormChange('address', value)}
                            error={formErrors.address}
                            required={formState.dataSource === 'device'}
                            description="设备采集点的地址标识"
                          />
                        </div>
                      )}
                    </div>
                  )}
                </AccordionContent>
              </AccordionItem>
            )}
          </Accordion>
        </div>

        <DialogFooter className="pt-2 border-t">
          <div className="flex items-center w-full justify-between">
            <div className="flex items-center">
              {/* 表单完成度指示器 */}
              <div className="flex items-center space-x-2 text-sm">
                <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className={`h-full ${
                      completeness.percentage === 100
                        ? 'bg-green-500'
                        : 'bg-blue-500'
                    }`}
                    style={{ width: `${completeness.percentage}%` }}
                  />
                </div>
                <span
                  className={`${
                    completeness.percentage === 100
                      ? 'text-green-600'
                      : 'text-gray-600'
                  }`}>
                  {completeness.percentage}% 完成
                </span>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}>
                取消
              </Button>

              {/* 主操作按钮 */}
              <Button
                type="button"
                variant={completeness.isComplete ? 'default' : 'outline'}
                onClick={handleSave}
                disabled={isSubmitting}
                className={`${
                  completeness.isComplete
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'text-amber-700 border-amber-200 bg-amber-50 hover:bg-amber-100'
                }`}>
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24">
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    处理中...
                  </span>
                ) : completeness.isComplete ? (
                  editMode ? (
                    '保存更改'
                  ) : (
                    '添加点位'
                  )
                ) : (
                  '填写必填项'
                )}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
