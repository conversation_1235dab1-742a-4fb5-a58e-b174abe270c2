import { useState, useEffect, useRef } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select'
import {
  Loader2,
  RefreshCw,
  HelpCircle,
  ChevronUp,
  ChevronDown,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import type { Device } from '@/lib/api/device-api'
import { DriverService, ProtocolInfo, ProtocolType } from '@/lib/api/driver-api'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { feature, getAPI } from '@/lib/api-services/axios-utils'
import { DeviceApi } from '@/lib/api-services/apis/device-api'
import { AxiosResponse } from 'axios'
import {
  RESTfulResultInt64,
  DeviceAddInput,
  DeviceUpdateInput,
  DeviceDriver,
  DeviceInfo,
} from '@/lib/api-services/models'
import { ChannelApi } from '@/lib/api-services/apis/channel-api'
import {
  EdgeChannel,
  EdgeChannelAddInput,
  BaseIdInputListInt64,
  StopBits,
  Parity,
} from '@/lib/api-services/models'
// 导入通道适配器模块
import { convertChannelToDisplay } from '@/lib/channel-adapter'

// 扩展Device接口，添加额外的属性
interface ExtendedDevice extends Device {
  identifier?: string // 添加 identifier 属性
  description?: string
  allowWrite?: boolean
}

interface AddDeviceDialogProps {
  onDeviceAdded?: (device: ExtendedDevice) => void
  onDeviceUpdated?: (device: ExtendedDevice) => void
  editMode?: boolean
  deviceToEdit?: ExtendedDevice | null
}

// 协议配置接口
interface ProtocolConfig {
  [key: string]: any
}

// ModbusTCP配置
interface ModbusTCPConfig extends ProtocolConfig {
  ipAddress: string
  port: string
  stationNumber: string
  bigEndian: boolean
  swapWords: boolean
  checkConsistency: boolean
  heartbeat: string
  timeout: string
}

// 串口配置
interface SerialConfig extends ProtocolConfig {
  serialChannelId: string
  baudRate: string
  dataBits: string
  stopBits: string
  parity: string
  flowControl: string
  timeout: string
}

// 添加串口通道表单接口
interface SerialChannelForm {
  id?: number
  channelName: string
  serial: string
  baudRate: number
  dataBits: number
  stop: string
  checkout: string
  threadCount?: number
  enable?: boolean
}

// 上报方式选项
const reportModes = [
  {
    value: 'immediate',
    label: '始终上报',
    description: '设备数据变化时立即上报到平台',
  },
  {
    value: 'change',
    label: '数据变化时上报',
    description: '仅当数据发生变化时才上报到平台',
  },
  {
    value: 'never',
    label: '从不上报',
    description: '不将设备数据上报到平台',
  },
  {
    value: 'onDemand',
    label: '按需上报',
    description: '根据需求上报数据到平台',
  },
  {
    value: 'onDemandAlways',
    label: '按需上报(设备关机依然上报)',
    description: '即使设备关机，也能按需上报数据到平台',
  },
]

export function AddDeviceDialog({
  onDeviceAdded,
  onDeviceUpdated,
  editMode = false,
  deviceToEdit = null,
}: AddDeviceDialogProps) {
  // 基本信息
  const [deviceId, setDeviceId] = useState('')
  const [deviceName, setDeviceName] = useState('')
  const [description, setDescription] = useState('')
  const [location, setLocation] = useState('')
  const [enabled, setEnabled] = useState(true)
  const [protocol, setProtocol] = useState<ProtocolType>('ModbusTCP')
  const [filteredProtocols, setFilteredProtocols] = useState<{
    [key: string]: ProtocolInfo[]
  }>({})

  // 配置信息
  const [protocolConfig, setProtocolConfig] = useState<ProtocolConfig>({})
  const [collectInterval, setCollectInterval] = useState('1000')
  const [retryInterval, setRetryInterval] = useState('0')
  const [reconnectTimeout, setReconnectTimeout] = useState('30000')
  const [reportMode, setReportMode] = useState('immediate')
  const [saveHistory, setSaveHistory] = useState(true)
  const [allowWrite, setAllowWrite] = useState(false)

  // 串口通道
  const [serialChannels, setSerialChannels] = useState<EdgeChannel[]>([])
  const [loadingChannels, setLoadingChannels] = useState(false)

  // 协议相关状态
  const [protocols, setProtocols] = useState<ProtocolInfo[]>([])
  const [groupedProtocols, setGroupedProtocols] = useState<{
    [key: string]: ProtocolInfo[]
  }>({})
  const [loadingProtocols, setLoadingProtocols] = useState(false)
  const [protocolDetail, setProtocolDetail] = useState<any>(null)
  const [loadingProtocolDetail, setLoadingProtocolDetail] = useState(false)

  // 提交状态
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 用于控制各部分折叠状态的状态
  const [sectionsState, setSectionsState] = useState({
    basicInfo: true,
    connectionConfig: true,
    advancedSettings: true,
  })

  // 展开/折叠区段
  const toggleSection = (section: keyof typeof sectionsState) => {
    setSectionsState((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  // 加载串口通道
  const loadSerialChannels = async () => {
    setLoadingChannels(true)
    try {
      // 调用真实API获取串口通道列表
      const [error, response] = await feature(getAPI(ChannelApi).page(1, 100))

      if (error) {
        throw error
      }

      if (response.data && response.data.data && response.data.data.items) {
        // 直接使用API返回的通道数据
        setSerialChannels(response.data.data.items)

        // 如果已经有通道数据，并且serialChannelId尚未设置，则默认选择第一个可用通道
        if (response.data.data.items.length > 0) {
          const availableChannel = response.data.data.items.find(
            (channel) => channel.deviceCount === 0
          )
          if (availableChannel && !protocolConfig.serialChannelId) {
            setTimeout(() => {
              handleSerialChannelChange(availableChannel.id?.toString() || '')
            }, 0)
          }
        }
      } else {
        setSerialChannels([])
      }
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载串口通道列表',
        variant: 'destructive',
      })
      setSerialChannels([])
    } finally {
      setLoadingChannels(false)
    }
  }

  // 处理串口通道选择，自动填充其他字段
  const handleSerialChannelChange = (channelId: string) => {
    if (!channelId) return

    // 查找选中的通道
    const selectedChannel = serialChannels.find(
      (channel) => channel.id?.toString() === channelId
    )

    if (selectedChannel) {
      // 转换为显示格式
      const displayChannel = convertChannelToDisplay(selectedChannel)

      // 更新串口配置（先更新通道ID）
      updateProtocolConfig('serialChannelId', channelId)

      // 创建一个函数来同时更新多个可能的字段标识符
      const updateFieldWithAlternatives = (
        baseField: string,
        value: string | number
      ) => {
        // 尝试直接使用驱动API返回的标识符格式（首字母大写）
        updateProtocolConfig(
          baseField.charAt(0).toUpperCase() + baseField.slice(1),
          value
        )
        // 全小写格式
        updateProtocolConfig(baseField.toLowerCase(), value)
        // 全大写格式
        updateProtocolConfig(baseField.toUpperCase(), value)
      }

      // 自动填充所有通道相关字段 - 使用各种可能的字段标识符
      // 串口名称
      updateFieldWithAlternatives('port', displayChannel.port)
      updateFieldWithAlternatives('portName', displayChannel.port)
      updateFieldWithAlternatives('serialPort', displayChannel.port)
      updateFieldWithAlternatives('serial', displayChannel.port)
      updateFieldWithAlternatives('comPort', displayChannel.port)

      // 波特率
      updateFieldWithAlternatives(
        'baudRate',
        displayChannel.baudRate.toString()
      )
      // 数据位
      updateFieldWithAlternatives(
        'dataBits',
        displayChannel.dataBits.toString()
      )
      // 停止位
      updateFieldWithAlternatives(
        'stopBits',
        displayChannel.stopBits.toString()
      )

      // 校验位 - 同时更新checkout(字符串格式)和Parity(数字格式)字段
      // 1. 更新checkout字段（字符串格式，首字母大写如"None"）
      const parityString =
        displayChannel.parity.charAt(0).toUpperCase() +
        displayChannel.parity.slice(1).toLowerCase()
      updateProtocolConfig('checkout', parityString)
      updateProtocolConfig('Checkout', parityString)
      updateProtocolConfig('CHECKOUT', parityString)

      // 2. 更新Parity字段（数字格式）
      let parityNumber = 0 // 默认为None
      switch (displayChannel.parity.toLowerCase()) {
        case 'odd':
          parityNumber = 1
          break
        case 'even':
          parityNumber = 2
          break
        case 'mark':
          parityNumber = 3
          break
        case 'space':
          parityNumber = 4
          break
      }
      updateProtocolConfig('parity', parityNumber)
      updateProtocolConfig('Parity', parityNumber)
      updateProtocolConfig('PARITY', parityNumber)

      // 设置默认值
      updateFieldWithAlternatives('flowControl', 'none')
      updateFieldWithAlternatives('timeout', '3000')

      // 通知用户参数已自动填充
      toast({
        title: '串口参数已自动填充',
        description: `已选择串口通道: ${displayChannel.name}`,
        duration: 2000,
      })
    }
  }

  // 获取协议列表
  const loadProtocols = async () => {
    setLoadingProtocols(true)
    try {
      const response = await DriverService.getProtocols()
      if (response.code === 200 && response.data) {
        setProtocols(response.data)

        // 对协议进行分组
        const grouped = groupProtocols(response.data)
        setGroupedProtocols(grouped)

        // 如果没有默认协议，且有协议列表，则设置第一个为默认
        if (response.data.length > 0 && (!protocol || protocol === '')) {
          setProtocol(response.data[0].name as ProtocolType)
        }
      } else {
        toast({
          title: '获取协议列表失败',
          description: response.msg,
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载协议列表',
        variant: 'destructive',
      })
    } finally {
      setLoadingProtocols(false)
    }
  }

  // 获取协议详情
  const loadProtocolDetail = async (protocolName: string) => {
    if (!protocolName) return

    setLoadingProtocolDetail(true)
    try {
      const response = await DriverService.getProtocolDetail(protocolName)
      if (response.code === 200 && response.data) {
        setProtocolDetail(response.data)

        // 生成初始配置对象，根据协议详情中的connectionConfig
        const initialConfig: ProtocolConfig = {}
        if (
          response.data.connectionConfig &&
          Array.isArray(response.data.connectionConfig)
        ) {
          // 先处理非下拉选择类型的配置
          response.data.connectionConfig.forEach((config) => {
            if (config.identifier) {
              initialConfig[config.identifier] = config.value || ''
            }
          })

          // 然后单独处理下拉选择类型的配置，确保有默认值
          response.data.connectionConfig.forEach((config) => {
            if (
              config.identifier &&
              (config.type?.toLowerCase() === 'select' ||
                config.type?.toLowerCase() === 'enum') &&
              (!initialConfig[config.identifier] ||
                initialConfig[config.identifier] === '')
            ) {
              const options = parseEnumOptions(config.enumInfo)
              // 尝试获取第一个选项作为默认值
              const firstGroupName = Object.keys(options)[0]
              if (firstGroupName) {
                const groupOptions =
                  options[firstGroupName as keyof typeof options]
                if (Array.isArray(groupOptions) && groupOptions.length > 0) {
                  initialConfig[config.identifier] = groupOptions[0].value
                }
              }
            }
          })
        }

        // 如果是串口设备，加载串口通道
        if (response.data.isNet === false) {
          // 加载串口通道
          loadSerialChannels()
        }

        setProtocolConfig(initialConfig)
      } else {
        toast({
          title: '获取协议详情失败',
          description: response.msg,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error(`加载协议 ${protocolName} 详情失败:`, error)
      toast({
        title: '加载失败',
        description: `无法加载协议 ${protocolName} 详情`,
        variant: 'destructive',
      })
    } finally {
      setLoadingProtocolDetail(false)
    }
  }

  // 生成随机ID
  const generateId = () => {
    const prefix = 'DEV'
    const randomPart = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0')
    setDeviceId(`${prefix}_${randomPart}`)
  }

  // 提交表单
  const handleSubmit = async () => {
    // 表单验证
    if (!deviceId.trim()) {
      toast({
        title: '验证失败',
        description: '唯一码不能为空',
        variant: 'destructive',
      })
      // 确保基本信息区段展开
      setSectionsState((prev) => ({
        ...prev,
        basicInfo: true,
        connectionConfig: false,
        advancedSettings: false,
      }))
      return
    }

    if (!deviceName.trim()) {
      toast({
        title: '验证失败',
        description: '设备名称不能为空',
        variant: 'destructive',
      })
      // 确保基本信息区段展开
      setSectionsState((prev) => ({
        ...prev,
        basicInfo: true,
        connectionConfig: false,
        advancedSettings: false,
      }))
      return
    }

    // 协议特定验证
    if (
      protocol === 'ModbusTCP' ||
      protocol === 'OPC UA' ||
      protocol === 'HTTP' ||
      protocol === 'BACnet' ||
      protocol === 'Siemens S7' ||
      protocol === 'Mitsubishi MC' ||
      protocol === 'IEC 61850'
    ) {
      const config = protocolConfig as ModbusTCPConfig
      if (!config.ipAddress) {
        toast({
          title: '验证失败',
          description: 'IP地址不能为空',
          variant: 'destructive',
        })
        // 确保连接配置区段展开
        setSectionsState((prev) => ({
          ...prev,
          basicInfo: false,
          connectionConfig: true,
          advancedSettings: false,
        }))
        return
      }
    }

    if (protocolDetail?.isNet === false) {
      const config = protocolConfig as SerialConfig
      if (!config.serialChannelId) {
        toast({
          title: '验证失败',
          description: '请选择串口通道',
          variant: 'destructive',
        })
        // 确保连接配置区段展开
        setSectionsState((prev) => ({
          ...prev,
          basicInfo: false,
          connectionConfig: true,
          advancedSettings: false,
        }))
        return
      }
    }

    setIsSubmitting(true)

    try {
      if (editMode) {
        // 编辑模式：更新设备
        // 按照API需要的格式构建数据
        const driver: DeviceDriver = {
          driverName: protocol, // 使用driverName而不是name
        }

        const deviceInfo: DeviceInfo = {
          // 这些字段是DeviceInfo接口中定义的
          waitTime: Number(retryInterval) || 0,
          reConnTime: Number(reconnectTimeout) || 30000,
          storeHistoryData: saveHistory,
          minPeriod: Number(collectInterval) || 0, // 添加采集间隔字段
        }

        const deviceInput: DeviceUpdateInput = {
          id: Number(deviceToEdit?.id || deviceId || 0),
          identifier: deviceId,
          name: deviceName,
          description: description,
          deviceConfig: protocolConfig,
          driver: driver,
          deviceInfo: deviceInfo,
          location: location,
          // 移除enable属性，因为接口中不存在
        }

        // 直接调用API，避免DeviceService内部再次转换
        const [error, response] = await feature(
          getAPI(DeviceApi).update(deviceInput as DeviceUpdateInput)
        )

        if (error) {
          console.error('更新设备失败:', error)
          // 提取API返回的详细错误信息
          const errorMessage =
            (error as any).response?.data?.errors ||
            (error as any).response?.data?.message ||
            error.message ||
            '无法更新设备，请稍后重试'
          toast({
            title: '更新失败',
            description: errorMessage,
            variant: 'destructive',
          })
        } else {
          toast({
            title: '更新成功',
            description: `设备 ${deviceName} 已成功更新`,
          })

          // 回调
          if (onDeviceUpdated) {
            const updatedDevice: ExtendedDevice = {
              id: Number(deviceToEdit?.id || deviceId || 0),
              name: deviceName,
              protocol: protocol,
              location: location || '',
              status: 'offline', // 状态保持不变
              lastSeen: '从未',
              ipAddress:
                protocol === 'ModbusTCP'
                  ? (protocolConfig as ModbusTCPConfig).ipAddress
                  : undefined,
              firmware: 'v1.0.0',
              description: description,
              enabled: enabled,
              allowWrite: allowWrite,
              identifier: deviceId, // 确保唯一码字段也被设置
            }
            onDeviceUpdated(updatedDevice)
          }

          // 返回到设备列表页面
          window.history.back()
        }
      } else {
        // 新增模式：创建设备
        // 按照API需要的格式构建数据
        const driver: DeviceDriver = {
          driverName: protocol, // 使用driverName而不是name
        }

        const deviceInfo: DeviceInfo = {
          // 这些字段是DeviceInfo接口中定义的
          waitTime: Number(retryInterval) || 0,
          reConnTime: Number(reconnectTimeout) || 30000,
          storeHistoryData: saveHistory,
          minPeriod: Number(collectInterval) || 1000, // 添加采集间隔字段
        }

        const deviceInput: DeviceAddInput = {
          identifier: deviceId,
          name: deviceName,
          description: description,
          deviceConfig: protocolConfig,
          driver: driver,
          deviceInfo: deviceInfo,
          location: location,
          // 不包含未定义的属性
        }

        // 直接调用API，避免DeviceService内部再次转换
        const [error, response] = await feature<
          AxiosResponse<RESTfulResultInt64>
        >(getAPI(DeviceApi).add(deviceInput as DeviceAddInput))

        if (error) {
          console.error('添加设备失败:', error)
          // 提取API返回的详细错误信息
          const errorMessage =
            (error as any).response?.data?.errors ||
            (error as any).response?.data?.message ||
            error.message ||
            '无法添加设备，请稍后重试'
          toast({
            title: '添加失败',
            description: errorMessage,
            variant: 'destructive',
          })
        } else {
          toast({
            title: '添加成功',
            description: `设备 ${deviceName} 已成功添加`,
          })

          // 回调
          if (onDeviceAdded) {
            const newDevice: ExtendedDevice = {
              id: Number(response.data.data || deviceId || 0),
              name: deviceName,
              protocol: protocol,
              location: location || '',
              status: 'offline', // 新添加的设备初始状态为离线
              lastSeen: '从未',
              ipAddress:
                protocol === 'ModbusTCP'
                  ? (protocolConfig as ModbusTCPConfig).ipAddress
                  : undefined,
              firmware: 'v1.0.0',
              description: description,
              enabled: enabled,
              allowWrite: allowWrite,
              identifier: deviceId,
            }
            onDeviceAdded(newDevice)
          }

          // 返回到设备列表页面
          window.history.back()
        }
      }
    } catch (error) {
      console.error(editMode ? '更新设备失败:' : '添加设备失败:', error)
      // 提取API返回的详细错误信息
      const errorMessage =
        (error as any).response?.data?.errors ||
        (error as any).response?.data?.message ||
        (error as any).message ||
        (editMode ? '无法更新设备，请稍后重试' : '无法添加设备，请稍后重试')
      toast({
        title: editMode ? '更新失败' : '添加失败',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 渲染协议配置表单
  const renderProtocolConfigForm = () => {
    if (loadingProtocolDetail) {
      return (
        <div className="flex justify-center items-center py-8">
          <div className="flex flex-col items-center">
            <Loader2 className="animate-spin h-8 w-8 text-primary" />
            <p className="mt-3 text-gray-500">加载协议配置...</p>
          </div>
        </div>
      )
    }

    if (!protocolDetail || !protocolDetail.connectionConfig) {
      return (
        <div className="p-6 text-center">
          <p className="text-gray-500">请选择协议类型或协议配置不可用</p>
        </div>
      )
    }

    // 按组分类配置项
    const configGroups: { [key: string]: any[] } = {}
    protocolDetail.connectionConfig.forEach((config: any) => {
      const groupName = config.configGroupName || '基础配置'
      if (!configGroups[groupName]) {
        configGroups[groupName] = []
      }
      configGroups[groupName].push(config)
    })

    // 检查是否是串口设备（isNet为false表示串口设备）
    if (protocolDetail.isNet === false) {
      const serialChannelConfig = {
        identifier: 'serialChannelId',
        configName: '串口通道',
        description: '选择已配置的串口通道',
        type: 'select',
        required: true,
        order: -1, // 确保排在最前面
        configGroupName: '连接配置',
      }

      // 检查是否已有串口通道配置项
      let hasSerialChannelItem = false
      Object.values(configGroups).forEach((group) => {
        if (group.some((item) => item.identifier === 'serialChannelId')) {
          hasSerialChannelItem = true
        }
      })

      // 如果没有，添加串口通道选择项
      if (!hasSerialChannelItem) {
        const groupName = '连接配置'
        if (!configGroups[groupName]) {
          configGroups[groupName] = []
        }
        configGroups[groupName].unshift(serialChannelConfig)
      }
    }

    // 对每组内的配置项按order属性排序，并将布尔开关类型放在最后
    Object.keys(configGroups).forEach((groupName) => {
      configGroups[groupName].sort((a, b) => {
        // 如果a是布尔类型而b不是，a应该排在后面
        const aIsBool =
          a.type?.toLowerCase() === 'boolean' ||
          a.type?.toLowerCase() === 'switch'
        const bIsBool =
          b.type?.toLowerCase() === 'boolean' ||
          b.type?.toLowerCase() === 'switch'

        if (aIsBool && !bIsBool) {
          return 1 // a排在后面
        }
        if (!aIsBool && bIsBool) {
          return -1 // b排在后面
        }

        // 如果两者类型相同，则按order排序
        const orderA = a.order !== undefined ? a.order : 9999
        const orderB = b.order !== undefined ? b.order : 9999
        return orderA - orderB
      })
    })

    return (
      <div className="space-y-8">
        {Object.entries(configGroups).map(([groupName, configs]) => (
          <div key={groupName} className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700 border-b pb-1">
              {groupName}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {configs.map((config) => renderConfigItem(config))}
            </div>
          </div>
        ))}
      </div>
    )
  }

  // 渲染单个配置项
  const renderConfigItem = (config: any) => {
    // 仅serialChannelId在SerialChannelSelector中处理，因此这里隐藏
    if (
      protocolDetail?.isNet === false &&
      config.identifier === 'serialChannelId'
    ) {
      return null
    }

    // 如果配置项有显示条件表达式，可以在这里处理
    if (config.displayExpress && !evalDisplayCondition(config.displayExpress)) {
      return null
    }

    const configId = config.identifier || ''
    const configValue =
      protocolConfig[configId] !== undefined
        ? protocolConfig[configId]
        : config.value || config.defaultValue
    const isRequired = config.required === true

    // 获取当前选中的通道ID
    const selectedChannelId = protocolConfig['serialChannelId']?.toString()

    // 检查是否是串口相关字段（非serialChannelId），这些字段应该是只读的
    // 创建辅助函数来检查标识符，不区分大小写
    const isSerialFieldId = (id: string): boolean => {
      if (!id) return false

      // 将所有可能的串口相关字段标识符转为小写进行比较
      const serialFieldIds = [
        'port',
        'portname',
        'serialport',
        'serial',
        'comport',
        'baudrate',
        'baud',
        'databits',
        'bits',
        'stopbits',
        'stop',
        'parity',
        'checkout', // 明确包含'checkout'和'parity'
        'flowcontrol',
        'flow',
        'timeout',
      ]

      // 明确返回布尔值
      const result = serialFieldIds.includes(id.toLowerCase())
      return result
    }

    // 对于串口设备，串口号字段应该是只读的
    // 添加详细调试信息以诊断问题
    const condition1 = protocolDetail?.isNet === false
    const condition2 = isSerialFieldId(config.identifier)
    const condition3 = !!protocolConfig['serialChannelId']

    const isSerialRelatedField = condition1 && condition2 && condition3

    // 为串口相关字段添加特殊标记
    const isAutoFilledField =
      isSerialRelatedField && protocolConfig[configId] !== undefined

    switch (config.type?.toLowerCase()) {
      case 'boolean':
      case 'switch':
        return (
          <div key={configId} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label
                  htmlFor={configId}
                  className={`text-sm font-medium ${
                    isRequired ? 'required' : ''
                  }`}>
                  {config.configName}
                  {isAutoFilledField && (
                    <span className="ml-2 text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full">
                      自动填充
                    </span>
                  )}
                </Label>
                {config.description && (
                  <TooltipProvider delayDuration={300} skipDelayDuration={300}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent
                        className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                        side="top"
                        align="start">
                        <p>{config.description}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
              <Switch
                id={configId}
                checked={!!configValue}
                onCheckedChange={(checked) =>
                  updateProtocolConfig(configId, checked)
                }
                disabled={isSerialRelatedField}
              />
            </div>
          </div>
        )

      case 'select':
      case 'enum':
        const options = parseEnumOptions(config.enumInfo)
        return (
          <div key={configId} className="space-y-2">
            <div className="flex items-center">
              <Label
                htmlFor={configId}
                className={`text-sm font-medium ${
                  isRequired ? 'required' : ''
                }`}>
                {config.configName}
                {isAutoFilledField && (
                  <span className="ml-2 text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full">
                    自动填充
                  </span>
                )}
              </Label>
              {config.description && (
                <TooltipProvider delayDuration={300} skipDelayDuration={300}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                    </TooltipTrigger>
                    <TooltipContent
                      className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                      side="top"
                      align="start">
                      <p>{config.description}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <Select
              value={configValue?.toString() || ''}
              onValueChange={(value) => {
                // 特殊处理serialChannelId字段，当它变化时自动填充相关字段
                if (configId === 'serialChannelId') {
                  handleSerialChannelChange(value)
                } else {
                  updateProtocolConfig(configId, value)
                }
              }}
              disabled={isSerialRelatedField}>
              <SelectTrigger
                id={configId}
                className={`w-full border-gray-300 focus:border-black focus:ring-1 focus:ring-black ${
                  isSerialRelatedField
                    ? 'bg-gray-50 cursor-not-allowed text-gray-600'
                    : ''
                }`}>
                <SelectValue placeholder={`请选择${config.configName}`} />
              </SelectTrigger>
              <SelectContent
                className="bg-white w-full"
                position="popper"
                sideOffset={5}
                align="start">
                {configId === 'serialChannelId' ? (
                  // 特殊渲染串口通道选择
                  <SelectGroup>
                    <SelectLabel className="font-bold text-sm py-1.5 border-b border-gray-100 bg-gray-50 w-full block overflow-hidden text-ellipsis">
                      可用串口通道
                    </SelectLabel>
                    {serialChannels.length > 0 ? (
                      serialChannels.map((channel) => (
                        <SelectItem
                          key={channel.id}
                          value={channel.id?.toString() || ''}
                          disabled={
                            channel.deviceCount !== undefined &&
                            channel.deviceCount > 0 &&
                            channel.id?.toString() !== selectedChannelId
                          }
                          className={
                            channel.deviceCount !== undefined &&
                            channel.deviceCount > 0 &&
                            channel.id?.toString() !== selectedChannelId
                              ? 'opacity-50'
                              : ''
                          }>
                          <div className="flex flex-col py-1">
                            {renderChannelItem(channel)}
                          </div>
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="empty" disabled>
                        无可用串口通道，请先添加串口通道
                      </SelectItem>
                    )}
                  </SelectGroup>
                ) : (
                  // 正常渲染其他下拉选项
                  Object.entries(options).map(([groupName, groupOptions]) => (
                    <SelectGroup key={groupName}>
                      {groupName !== 'default' && groupName !== '默认' && (
                        <SelectLabel className="font-bold text-sm py-1.5 border-b border-gray-100 bg-gray-50 w-full block overflow-hidden text-ellipsis">
                          {groupName}
                        </SelectLabel>
                      )}
                      {Array.isArray(groupOptions) ? (
                        groupOptions.map(
                          (option: {
                            value: string
                            label: string
                            description?: string
                          }) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex flex-col w-full">
                                <span className="truncate">{option.label}</span>
                                {option.description && (
                                  <span className="text-xs text-gray-500 truncate max-w-[270px]">
                                    {option.description}
                                  </span>
                                )}
                              </div>
                            </SelectItem>
                          )
                        )
                      ) : (
                        <SelectItem value="empty">无选项</SelectItem>
                      )}
                    </SelectGroup>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        )

      case 'number':
        return (
          <div key={configId} className="space-y-2">
            <div className="flex items-center">
              <Label
                htmlFor={configId}
                className={`text-sm font-medium ${
                  isRequired ? 'required' : ''
                }`}>
                {config.configName}
                {isAutoFilledField && (
                  <span className="ml-2 text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full">
                    自动填充
                  </span>
                )}
              </Label>
              {config.description && (
                <TooltipProvider delayDuration={300} skipDelayDuration={300}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                    </TooltipTrigger>
                    <TooltipContent
                      className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                      side="top"
                      align="start">
                      <p>{config.description}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <Input
              id={configId}
              type="number"
              value={configValue?.toString() || ''}
              onChange={(e) => {
                // 对数字类型进行转换
                const numValue =
                  e.target.value === '' ? '' : Number(e.target.value)
                updateProtocolConfig(configId, numValue)
              }}
              min={config.min}
              max={config.max}
              step={config.step || 1}
              placeholder={config.description || `请输入${config.configName}`}
              readOnly={isSerialRelatedField}
              className={`border-gray-300 focus:border-black focus:ring-1 focus:ring-black ${
                isSerialRelatedField
                  ? 'bg-gray-50 cursor-not-allowed text-gray-600'
                  : ''
              }`}
            />
          </div>
        )

      case 'password':
        return (
          <div key={configId} className="space-y-2">
            <div className="flex items-center">
              <Label
                htmlFor={configId}
                className={`text-sm font-medium ${
                  isRequired ? 'required' : ''
                }`}>
                {config.configName}
                {isAutoFilledField && (
                  <span className="ml-2 text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full">
                    自动填充
                  </span>
                )}
              </Label>
              {config.description && (
                <TooltipProvider delayDuration={300} skipDelayDuration={300}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                    </TooltipTrigger>
                    <TooltipContent
                      className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                      side="top"
                      align="start">
                      <p>{config.description}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <Input
              id={configId}
              type="password"
              value={configValue?.toString() || ''}
              onChange={(e) => updateProtocolConfig(configId, e.target.value)}
              placeholder={config.description || `请输入${config.configName}`}
              readOnly={isSerialRelatedField}
              className={`border-gray-300 focus:border-black focus:ring-1 focus:ring-black ${
                isSerialRelatedField
                  ? 'bg-gray-50 cursor-not-allowed text-gray-600'
                  : ''
              }`}
            />
          </div>
        )

      // 默认为文本输入
      default:
        const inputType =
          config.type?.toLowerCase() === 'text'
            ? 'text'
            : config.type?.toLowerCase() === 'email'
            ? 'email'
            : config.type?.toLowerCase() === 'tel'
            ? 'tel'
            : 'text'

        return (
          <div key={configId} className="space-y-2">
            <div className="flex items-center">
              <Label
                htmlFor={configId}
                className={`text-sm font-medium ${
                  isRequired ? 'required' : ''
                }`}>
                {config.configName}
                {isAutoFilledField && (
                  <span className="ml-2 text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full">
                    自动填充
                  </span>
                )}
              </Label>
              {config.description && (
                <TooltipProvider delayDuration={300} skipDelayDuration={300}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                    </TooltipTrigger>
                    <TooltipContent
                      className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                      side="top"
                      align="start">
                      <p>{config.description}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <Input
              id={configId}
              type={inputType}
              value={configValue?.toString() || ''}
              onChange={(e) => updateProtocolConfig(configId, e.target.value)}
              placeholder={config.description || `请输入${config.configName}`}
              pattern={config.pattern}
              maxLength={config.maxLength}
              readOnly={isSerialRelatedField}
              className={`border-gray-300 focus:border-black focus:ring-1 focus:ring-black ${
                isSerialRelatedField
                  ? 'bg-gray-50 cursor-not-allowed text-gray-600'
                  : ''
              }`}
            />
          </div>
        )
    }
  }

  // 解析枚举选项
  const parseEnumOptions = (enumInfo: string | null | undefined) => {
    if (!enumInfo) return { 默认: [] }

    try {
      const parsed = JSON.parse(enumInfo)

      // 定义分组结构
      const groupedOptions: {
        [key: string]: Array<{
          value: string
          label: string
          description?: string
        }>
      } = {}

      // 确保返回值是数组
      if (Array.isArray(parsed)) {
        // 检查是否已经是分组格式的数组
        const isGroupedArray =
          parsed.length > 0 &&
          typeof parsed[0] === 'object' &&
          parsed[0] !== null &&
          'options' in parsed[0] &&
          'name' in parsed[0]

        if (isGroupedArray) {
          // 如果已经是分组格式，直接使用
          parsed.forEach((group) => {
            const groupName = String(group.name || '默认')
            if (!groupedOptions[groupName]) {
              groupedOptions[groupName] = []
            }

            if (Array.isArray(group.options)) {
              group.options.forEach((item: any) => {
                if (typeof item === 'object' && item !== null) {
                  const option = {
                    value: String(item.value ?? item.id ?? item.key ?? ''),
                    label: String(
                      item.label ?? item.name ?? item.title ?? item.value ?? ''
                    ),
                    description: item.description || undefined,
                  }
                  groupedOptions[groupName].push(option)
                } else {
                  // 简单类型
                  groupedOptions[groupName].push({
                    value: String(item),
                    label: String(item),
                  })
                }
              })
            }
          })
        } else {
          // 普通数组，按厂商分组
          parsed.forEach((item) => {
            if (typeof item === 'object' && item !== null) {
              // 如果项本身是对象，确保它有value和label属性
              const objItem = item as Record<string, any>
              const option = {
                value: String(objItem.value ?? objItem.id ?? objItem.key ?? ''),
                label: String(
                  objItem.label ??
                    objItem.name ??
                    objItem.title ??
                    objItem.value ??
                    ''
                ),
                description: objItem.description || undefined,
              }

              // 获取厂商信息，没有则放入默认组
              const manufacturer = String(
                objItem.manufacturer ??
                  objItem.vendor ??
                  objItem.group ??
                  '默认'
              )
              if (!groupedOptions[manufacturer]) {
                groupedOptions[manufacturer] = []
              }
              groupedOptions[manufacturer].push(option)
            } else {
              // 如果项是简单类型，则将其作为value和label，放入默认组
              const defaultOption = { value: String(item), label: String(item) }
              if (!groupedOptions['默认']) {
                groupedOptions['默认'] = []
              }
              groupedOptions['默认'].push(defaultOption)
            }
          })
        }
      } else if (typeof parsed === 'object' && parsed !== null) {
        // 检查是否是已分组格式的对象 {groupName1: [...options], groupName2: [...options]}
        let isGroupedObject = false
        for (const key in parsed) {
          if (Array.isArray(parsed[key])) {
            isGroupedObject = true
            break
          }
        }

        if (isGroupedObject) {
          // 已经是分组对象，直接使用
          for (const groupName in parsed) {
            if (Array.isArray(parsed[groupName])) {
              groupedOptions[groupName] = parsed[groupName].map((item: any) => {
                if (typeof item === 'object' && item !== null) {
                  return {
                    value: String(item.value ?? item.id ?? item.key ?? ''),
                    label: String(
                      item.label ?? item.name ?? item.title ?? item.value ?? ''
                    ),
                    description: item.description || undefined,
                  }
                } else {
                  return { value: String(item), label: String(item) }
                }
              })
            }
          }
        } else {
          // 如果是普通键值对对象 (如 {"ABCD":0,"BADC":1})
          Object.entries(parsed).forEach(([key, value]) => {
            let option
            let manufacturer = '默认'

            // 修改这里：使用值作为选项的值，键作为标签
            if (typeof value === 'object' && value !== null) {
              // 如果值是对象，尝试提取有意义的属性
              const objValue = value as Record<string, any>
              option = {
                value: String(objValue.value ?? key),
                label: String(
                  objValue.label ?? objValue.name ?? objValue.title ?? key
                ),
                description: objValue.description || undefined,
              }
              // 提取厂商信息
              manufacturer = String(
                objValue.manufacturer ??
                  objValue.vendor ??
                  objValue.group ??
                  '默认'
              )
            } else {
              option = {
                value: String(value), // 使用值作为值，而不是键名
                label: key, // 使用键名作为标签
              }
            }

            // 将选项加入相应的分组
            if (!groupedOptions[manufacturer]) {
              groupedOptions[manufacturer] = []
            }
            groupedOptions[manufacturer].push(option)
          })
        }
      } else {
        // 其他情况返回单个选项的数组，放入默认组
        groupedOptions['默认'] = [
          { value: String(parsed), label: String(parsed) },
        ]
      }

      // 如果没有任何分组，返回空对象
      if (Object.keys(groupedOptions).length === 0) {
        return { 默认: [] }
      }

      return groupedOptions
    } catch (error) {
      // 如果不是JSON格式，尝试解析简单的逗号分隔字符串
      try {
        const options = enumInfo
          .split(',')
          .map((item) => {
            if (!item.trim()) return { value: '', label: '' }
            const parts = item.split(':')
            const value = parts[0]?.trim() || ''
            const label = parts[1]?.trim() || value
            return { value, label }
          })
          .filter((item) => item.value !== '') // 过滤掉无效项

        return { 默认: options }
      } catch (e) {
        console.error('解析枚举选项错误:', e)
        return { 默认: [] } // 出错时返回空对象
      }
    }
  }

  // 评估显示条件表达式
  const evalDisplayCondition = (expression: string): boolean => {
    if (!expression) return true

    try {
      // 创建一个安全的表达式评估环境
      const context = { config: protocolConfig }
      const evalFunction = new Function(
        'context',
        `with(context) { return ${expression}; }`
      )
      return evalFunction(context)
    } catch (error) {
      console.error('表达式评估错误:', error)
      return true // 出错时默认显示
    }
  }

  /**
   * 将协议列表按制造商分组
   * @param protocols 协议列表
   * @returns 分组后的协议对象
   */
  const groupProtocols = (protocols: ProtocolInfo[]) => {
    const groupedProtocols: {
      [key: string]: ProtocolInfo[]
    } = {}

    protocols.forEach((protocol) => {
      // 使用manufacturer字段进行分组
      const groupName = protocol.manufacturer || '其他'

      // 如果该分组不存在，创建一个空数组
      if (!groupedProtocols[groupName]) {
        groupedProtocols[groupName] = []
      }

      // 将协议添加到相应分组
      groupedProtocols[groupName].push(protocol)
    })

    return groupedProtocols
  }

  // 串口通道选择器组件
  const SerialChannelSelector = () => {
    // 只有串口设备才显示这个组件
    if (protocolDetail?.isNet !== false) {
      return null
    }

    // 获取当前选中的通道
    const selectedChannelId = protocolConfig['serialChannelId']?.toString()
    const selectedChannel = selectedChannelId
      ? serialChannels.find(
          (channel) => channel.id === Number(selectedChannelId)
        )
      : undefined

    // 串口通道管理对话框
    const [showSerialChannelDialog, setShowSerialChannelDialog] =
      useState(false)

    // 添加/编辑串口通道对话框
    const [showAddEditDialog, setShowAddEditDialog] = useState(false)
    const [editingChannel, setEditingChannel] =
      useState<SerialChannelForm | null>(null)
    const [isEditing, setIsEditing] = useState(false)

    // 删除确认对话框
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
    const [channelToDelete, setChannelToDelete] = useState<{
      id: number
      name: string
      port: string
      baudRate: number
      dataBits: number
      stopBits: number
      parity: string
    } | null>(null)

    // 表单状态
    const [formData, setFormData] = useState<SerialChannelForm>({
      channelName: '',
      serial: '',
      baudRate: 9600,
      dataBits: 8,
      stop: '1',
      checkout: 'none',
    })

    const [isSubmitting, setIsSubmitting] = useState(false)

    // 处理串口通道管理
    const handleManageSerialChannels = () => {
      setShowSerialChannelDialog(true)
    }

    // 打开添加串口通道对话框
    const handleAddChannel = () => {
      setIsEditing(false)
      setFormData({
        channelName: '',
        serial: '',
        baudRate: 9600,
        dataBits: 8,
        stop: '1',
        checkout: 'none',
      })
      setShowAddEditDialog(true)
      setShowSerialChannelDialog(false)
    }

    // 打开编辑串口通道对话框
    const handleEditChannel = (channel: EdgeChannel) => {
      const displayChannel = convertChannelToDisplay(channel)

      setIsEditing(true)
      setFormData({
        id: channel.id,
        channelName: displayChannel.name,
        serial: displayChannel.port,
        baudRate: displayChannel.baudRate,
        dataBits: displayChannel.dataBits,
        stop:
          displayChannel.stopBits === 1
            ? 'One'
            : displayChannel.stopBits === 2
            ? 'Two'
            : 'OnePointFive',
        checkout:
          displayChannel.parity === 'none'
            ? 'None'
            : displayChannel.parity === 'odd'
            ? 'Odd'
            : displayChannel.parity === 'even'
            ? 'Even'
            : displayChannel.parity === 'mark'
            ? 'Mark'
            : 'Space',
      })
      setShowAddEditDialog(true)
      setShowSerialChannelDialog(false)
    }

    // 打开删除确认对话框
    const handleDeleteChannel = (channel: EdgeChannel) => {
      const displayChannel = convertChannelToDisplay(channel)

      setChannelToDelete({
        id: channel.id || 0,
        name: displayChannel.name,
        port: displayChannel.port,
        baudRate: displayChannel.baudRate,
        dataBits: displayChannel.dataBits,
        stopBits: displayChannel.stopBits,
        parity: displayChannel.parity,
      })
      setShowDeleteConfirm(true)
    }

    // 表单字段变更处理
    const handleFormChange = (field: keyof SerialChannelForm, value: any) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }))
    }

    // 提交添加或编辑表单
    const handleSubmitChannel = async () => {
      try {
        setIsSubmitting(true)

        if (isEditing && formData.id) {
          // 编辑现有通道
          const channelData = mapFormToUpdateInput(formData)

          const [error] = await feature(getAPI(ChannelApi).update(channelData))

          if (error) {
            throw error
          }

          toast({
            title: '成功',
            description: '串口通道已更新',
          })
        } else {
          // 添加新通道
          const channelData = mapFormToAddInput(formData)

          const [error] = await feature(getAPI(ChannelApi).add(channelData))

          if (error) {
            throw error
          }

          toast({
            title: '成功',
            description: '串口通道已添加',
          })
        }

        // 重新加载串口通道列表
        await loadSerialChannels()

        // 关闭对话框
        setShowAddEditDialog(false)
        setShowSerialChannelDialog(true)
      } catch (err: any) {
        toast({
          title: '错误',
          description: err.message || '操作失败，请重试',
          variant: 'destructive',
        })
      } finally {
        setIsSubmitting(false)
      }
    }

    // 确认删除通道
    const confirmDeleteChannel = async () => {
      if (!channelToDelete) return

      try {
        setIsSubmitting(true)

        const deleteData: BaseIdInputListInt64 = {
          id: [Number(channelToDelete.id)],
        }

        const [error] = await feature(getAPI(ChannelApi).deletes(deleteData))

        if (error) {
          throw error
        }

        toast({
          title: '成功',
          description: '串口通道已删除',
        })

        // 重新加载串口通道列表
        await loadSerialChannels()

        // 关闭对话框
        setShowDeleteConfirm(false)
      } catch (err: any) {
        toast({
          title: '错误',
          description: err.message || '删除失败，请重试',
          variant: 'destructive',
        })
      } finally {
        setIsSubmitting(false)
      }
    }

    // 选择添加新通道时的处理
    const handleSelectChange = (value: string) => {
      if (value === 'add-new') {
        handleAddChannel()
      } else {
        handleSerialChannelChange(value)
      }
    }

    return (
      <div className="mb-6 pb-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Label
              htmlFor="serialChannelSelector"
              className="required text-sm font-medium">
              串口通道
            </Label>
            <TooltipProvider delayDuration={300} skipDelayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent
                  className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                  side="top"
                  align="start">
                  <p>选择已配置的串口通道，通道参数将自动填充</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="p-1 rounded-full hover:bg-gray-100"
              onClick={() => loadSerialChannels()}
              title="刷新串口通道列表">
              <RefreshCw className="h-4 w-4 text-gray-500" />
            </button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleManageSerialChannels}
              className="text-xs px-2 py-1 h-auto border-gray-300 hover:bg-gray-50">
              管理串口通道
            </Button>
          </div>
        </div>
        <Select
          value={selectedChannelId || ''}
          onValueChange={handleSelectChange}>
          <SelectTrigger
            id="serialChannelSelector"
            className="w-full border-gray-300 focus:border-black focus:ring-1 focus:ring-black">
            <SelectValue placeholder="选择串口通道" />
          </SelectTrigger>
          <SelectContent
            className="bg-white w-full"
            position="popper"
            sideOffset={5}
            align="start">
            {loadingChannels ? (
              <div className="p-2 flex items-center justify-center">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>加载串口通道...</span>
              </div>
            ) : serialChannels.length > 0 ? (
              <>
                {serialChannels.map((channel) => (
                  <SelectItem
                    key={channel.id}
                    value={channel.id?.toString() || ''}
                    disabled={
                      channel.deviceCount !== undefined &&
                      channel.deviceCount > 0 &&
                      channel.id?.toString() !== selectedChannelId
                    }
                    className={
                      channel.deviceCount !== undefined &&
                      channel.deviceCount > 0 &&
                      channel.id?.toString() !== selectedChannelId
                        ? 'opacity-50'
                        : ''
                    }>
                    <div className="flex flex-col py-1">
                      {renderChannelItem(channel)}
                    </div>
                  </SelectItem>
                ))}
                <SelectItem value="add-new" className="border-t mt-1 pt-1">
                  <div className="flex items-center text-blue-600">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1">
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M12 8v8"></path>
                      <path d="M8 12h8"></path>
                    </svg>
                    <span>添加新串口通道...</span>
                  </div>
                </SelectItem>
              </>
            ) : (
              <div className="p-4 text-center bg-gray-50 rounded-md">
                <p className="text-gray-500">无可用串口通道</p>
                <Button
                  onClick={handleAddChannel}
                  variant="outline"
                  size="sm"
                  className="mt-2 text-xs">
                  添加串口通道
                </Button>
              </div>
            )}
          </SelectContent>
        </Select>

        {/* 串口通道管理对话框 */}
        <Dialog
          open={showSerialChannelDialog}
          onOpenChange={setShowSerialChannelDialog}>
          <DialogContent className="sm:max-w-3xl">
            <DialogHeader>
              <DialogTitle>串口通道管理</DialogTitle>
              <DialogDescription>
                管理系统中的串口通道，可以添加、编辑或删除串口通道
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              {/* 串口通道列表 */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-medium">已配置的串口通道</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddChannel}
                    className="text-xs">
                    添加通道
                  </Button>
                </div>

                <div className="border rounded-md divide-y">
                  {serialChannels.length > 0 ? (
                    serialChannels.map((channel) => {
                      const displayChannel = convertChannelToDisplay(channel)
                      return (
                        <div
                          key={displayChannel.id}
                          className="p-3 flex justify-between items-start">
                          <div>
                            <div className="font-medium flex items-center">
                              {displayChannel.name}
                              {displayChannel.isInUse && (
                                <span className="ml-2 text-xs px-1.5 py-0.5 bg-amber-100 text-amber-800 rounded-full">
                                  使用中
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-gray-500 mt-0.5">
                              {displayChannel.port}, {displayChannel.baudRate}
                              波特, {displayChannel.dataBits}数据位,{' '}
                              {displayChannel.stopBits}停止位,{' '}
                              {displayChannel.parity === 'none'
                                ? '无校验'
                                : displayChannel.parity}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleEditChannel(channel)}>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round">
                                <path d="M12 20h9"></path>
                                <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                              </svg>
                              <span className="sr-only">编辑</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-500"
                              disabled={displayChannel.isInUse}
                              onClick={() => handleDeleteChannel(channel)}>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round">
                                <path d="M3 6h18"></path>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                              </svg>
                              <span className="sr-only">删除</span>
                            </Button>
                          </div>
                        </div>
                      )
                    })
                  ) : (
                    <div className="p-8 text-center text-gray-500">
                      <p>尚未配置任何串口通道</p>
                      <p className="text-sm mt-1">点击"添加通道"按钮开始添加</p>
                    </div>
                  )}
                </div>
              </div>

              {/* 串口自动检测提示 */}
              <div className="bg-blue-50 border border-blue-100 rounded-md p-3 text-sm text-blue-800 flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2 mt-0.5">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="16" x2="12" y2="12"></line>
                  <line x1="12" y1="8" x2="12.01" y2="8"></line>
                </svg>
                <div>
                  <p className="font-medium">
                    系统会自动检测连接到设备的可用串口
                  </p>
                  <p className="mt-1">
                    请确保串口设备已正确连接，并且驱动程序已正确安装。如果找不到串口，请尝试重新插入设备或重启系统。
                  </p>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowSerialChannelDialog(false)}>
                关闭
              </Button>
              <Button
                onClick={() => {
                  loadSerialChannels()
                  setShowSerialChannelDialog(false)
                }}>
                刷新并关闭
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 添加/编辑串口通道对话框 */}
        <Dialog
          open={showAddEditDialog}
          onOpenChange={(open) => {
            if (!open) {
              setShowAddEditDialog(false)
              setShowSerialChannelDialog(true)
            }
          }}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {isEditing ? '编辑串口通道' : '添加串口通道'}
              </DialogTitle>
              <DialogDescription>
                {isEditing ? '修改串口通道配置信息' : '添加新的串口通道配置'}
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="channelName" className="text-right">
                  通道名称
                </Label>
                <Input
                  id="channelName"
                  value={formData.channelName}
                  onChange={(e) =>
                    handleFormChange('channelName', e.target.value)
                  }
                  className="col-span-3"
                  placeholder="如 COM1"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="portName" className="text-right">
                  端口名
                </Label>
                <Input
                  id="portName"
                  value={formData.serial}
                  onChange={(e) => handleFormChange('serial', e.target.value)}
                  className="col-span-3"
                  placeholder="如 COM1 或 /dev/ttyS0"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="baudRate" className="text-right">
                  波特率
                </Label>
                <Select
                  value={formData.baudRate.toString()}
                  onValueChange={(value) =>
                    handleFormChange('baudRate', Number(value))
                  }>
                  <SelectTrigger id="baudRate" className="col-span-3">
                    <SelectValue placeholder="选择波特率" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1200">1200</SelectItem>
                    <SelectItem value="2400">2400</SelectItem>
                    <SelectItem value="4800">4800</SelectItem>
                    <SelectItem value="9600">9600</SelectItem>
                    <SelectItem value="19200">19200</SelectItem>
                    <SelectItem value="38400">38400</SelectItem>
                    <SelectItem value="57600">57600</SelectItem>
                    <SelectItem value="115200">115200</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="dataBits" className="text-right">
                  数据位
                </Label>
                <Select
                  value={formData.dataBits.toString()}
                  onValueChange={(value) =>
                    handleFormChange('dataBits', Number(value))
                  }>
                  <SelectTrigger id="dataBits" className="col-span-3">
                    <SelectValue placeholder="选择数据位" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7</SelectItem>
                    <SelectItem value="8">8</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="stopBits" className="text-right">
                  停止位
                </Label>
                <Select
                  value={formData.stop}
                  onValueChange={(value) => handleFormChange('stop', value)}>
                  <SelectTrigger id="stopBits" className="col-span-3">
                    <SelectValue placeholder="选择停止位" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1</SelectItem>
                    <SelectItem value="2">2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="parity" className="text-right">
                  校验位
                </Label>
                <Select
                  value={formData.checkout}
                  onValueChange={(value) =>
                    handleFormChange('checkout', value)
                  }>
                  <SelectTrigger id="parity" className="col-span-3">
                    <SelectValue placeholder="选择校验位" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">无校验(None)</SelectItem>
                    <SelectItem value="odd">奇校验(Odd)</SelectItem>
                    <SelectItem value="even">偶校验(Even)</SelectItem>
                    <SelectItem value="mark">标记校验(Mark)</SelectItem>
                    <SelectItem value="space">空格校验(Space)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowAddEditDialog(false)
                  setShowSerialChannelDialog(true)
                }}
                disabled={isSubmitting}>
                取消
              </Button>
              <Button onClick={handleSubmitChannel} disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    正在保存...
                  </>
                ) : (
                  '保存'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 删除确认对话框 */}
        <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="text-red-600">删除串口通道</DialogTitle>
              <DialogDescription>
                此操作无法撤销。确定要删除此串口通道吗？
              </DialogDescription>
            </DialogHeader>

            {channelToDelete && (
              <div className="py-4">
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="font-medium">{channelToDelete.name}</p>
                  <p className="text-sm text-gray-500 mt-1">
                    {channelToDelete.port}, {channelToDelete.baudRate}波特,{' '}
                    {channelToDelete.dataBits}数据位,
                    {channelToDelete.stopBits}停止位,{' '}
                    {channelToDelete.parity === 'none'
                      ? '无校验'
                      : channelToDelete.parity}
                  </p>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isSubmitting}>
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDeleteChannel}
                disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    正在删除...
                  </>
                ) : (
                  '确认删除'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    )
  }

  // 协议搜索过滤函数
  const filterProtocolsBySearchTerm = (searchTerm: string) => {
    if (!groupedProtocols) return

    const filtered: { [key: string]: ProtocolInfo[] } = {}

    Object.entries(groupedProtocols).forEach(([groupName, protocols]) => {
      const matchedProtocols = protocols.filter((p) => {
        const text = `${p.label} ${p.name} ${p.description || ''} ${
          p.version || ''
        }`.toLowerCase()
        return text.includes(searchTerm.toLowerCase())
      })

      if (matchedProtocols.length > 0) {
        filtered[groupName] = matchedProtocols
      }
    })

    setFilteredProtocols(filtered)
  }

  // 初始化过滤后的协议列表
  useEffect(() => {
    setFilteredProtocols(groupedProtocols)
  }, [groupedProtocols])

  // 常用协议存储键
  const FREQUENT_PROTOCOLS_KEY = 'fusiontrack_frequent_protocols'

  // 获取常用协议
  const getFrequentProtocols = (): { [key: string]: number } => {
    try {
      const stored = localStorage.getItem(FREQUENT_PROTOCOLS_KEY)
      return stored ? JSON.parse(stored) : {}
    } catch (e) {
      return {}
    }
  }

  // 更新常用协议
  const updateFrequentProtocol = (protocolName: string) => {
    try {
      const frequentProtocols = getFrequentProtocols()
      frequentProtocols[protocolName] =
        (frequentProtocols[protocolName] || 0) + 1
      localStorage.setItem(
        FREQUENT_PROTOCOLS_KEY,
        JSON.stringify(frequentProtocols)
      )
    } catch (e) {}
  }

  // 获取常用协议列表
  const getFrequentProtocolsList = (
    allProtocols: ProtocolInfo[]
  ): ProtocolInfo[] => {
    const frequentProtocols = getFrequentProtocols()

    // 按使用频率排序协议
    return allProtocols
      .filter((p) => frequentProtocols[p.name])
      .sort(
        (a, b) =>
          (frequentProtocols[b.name] || 0) - (frequentProtocols[a.name] || 0)
      )
      .slice(0, 8) // 限制最多显示8个常用协议
  }

  // 重构协议选择器组件
  const ProtocolSelector = () => {
    const [isOpen, setIsOpen] = useState(false)
    const [searchTerm, setSearchTerm] = useState('')
    const [activeTab, setActiveTab] = useState('all') // 默认显示全部
    const searchInputRef = useRef<HTMLInputElement>(null)
    const protocolDialogContentRef = useRef<HTMLDivElement>(null)
    const [frequentProtocols, setFrequentProtocols] = useState<ProtocolInfo[]>(
      []
    )

    // 处理协议选择
    const handleProtocolSelect = (selectedProtocol: ProtocolType) => {
      setProtocol(selectedProtocol)
      setIsOpen(false)
      setSearchTerm('')
      setFilteredProtocols(groupedProtocols)
      updateFrequentProtocol(selectedProtocol) // 更新常用协议
    }

    // 处理搜索输入
    const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value
      setSearchTerm(value)
      filterProtocolsBySearchTerm(value)
    }

    // 处理输入框键盘事件
    const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // 阻止输入框内的键盘事件冒泡到对话框
      e.stopPropagation()
    }

    // 对话框打开时加载常用协议和添加键盘事件监听
    useEffect(() => {
      if (isOpen) {
        // 聚焦搜索框
        setTimeout(() => {
          searchInputRef.current?.focus()
        }, 100)

        // 加载常用协议
        setFrequentProtocols(getFrequentProtocolsList(protocols))

        // 添加键盘事件监听器，防止对话框关闭
        const handleKeyDown = (e: Event) => {
          // 阻止键盘事件冒泡，防止对话框关闭
          e.stopPropagation()
        }

        // 选择对话框内容元素
        const dialogContentElement = protocolDialogContentRef.current
        if (dialogContentElement) {
          dialogContentElement.addEventListener('keydown', handleKeyDown, true)
        }

        return () => {
          // 清理事件监听器
          if (dialogContentElement) {
            dialogContentElement.removeEventListener(
              'keydown',
              handleKeyDown,
              true
            )
          }
        }
      }
    }, [isOpen, protocols])

    // 获取要显示的协议分组
    const getDisplayProtocols = () => {
      if (searchTerm) {
        return filteredProtocols // 搜索时显示过滤结果
      }

      // 根据当前标签页返回相应分组
      if (activeTab === 'frequent') {
        return { 常用协议: frequentProtocols }
      }

      if (activeTab !== 'all') {
        // 显示特定制造商的协议
        const specificGroup = filteredProtocols[activeTab]
        return specificGroup ? { [activeTab]: specificGroup } : {}
      }

      return filteredProtocols // 显示全部
    }

    // 获取制造商标签列表
    const getManufacturerTabs = () => {
      const tabs = [
        { id: 'all', label: '全部' },
        { id: 'frequent', label: '常用', count: frequentProtocols.length },
      ]

      // 添加各制造商标签
      Object.entries(groupedProtocols).forEach(([groupName, protocols]) => {
        tabs.push({
          id: groupName,
          label: groupName,
          count: protocols.length,
        })
      })

      return tabs
    }

    // 高亮搜索结果文本
    const highlightText = (text: string, query: string) => {
      if (!query) return text

      const index = text.toLowerCase().indexOf(query.toLowerCase())
      if (index === -1) return text

      const before = text.slice(0, index)
      const match = text.slice(index, index + query.length)
      const after = text.slice(index + query.length)

      return (
        <>
          {before}
          <span className="bg-yellow-100 text-yellow-800">{match}</span>
          {after}
        </>
      )
    }

    return (
      <div className="space-y-2">
        <div className="flex items-center">
          <Label htmlFor="protocol" className="required">
            选择协议
          </Label>
          <TooltipProvider delayDuration={300} skipDelayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
              </TooltipTrigger>
              <TooltipContent
                className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                side="top"
                align="start">
                <p>设备使用的通信协议类型</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* 当前选择的协议显示 */}
        <Button
          type="button"
          variant="outline"
          className="w-full justify-between border-gray-300 hover:bg-gray-50 transition-colors required-field"
          disabled={editMode || loadingProtocols}
          onClick={() => setIsOpen(true)}>
          {loadingProtocols ? (
            <span className="flex items-center">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              加载协议列表...
            </span>
          ) : protocol ? (
            <span>
              {protocols.find((p) => p.name === protocol)?.label || protocol}
            </span>
          ) : (
            <span className="text-muted-foreground">选择协议</span>
          )}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="ml-2 h-4 w-4 shrink-0">
            <path d="m6 9 6 6 6-6" />
          </svg>
        </Button>

        {/* 重构的协议选择对话框 */}
        <Dialog
          open={isOpen}
          onOpenChange={(open) => {
            // 仅处理关闭事件，防止在输入框内按键时意外关闭
            if (!open) {
              setIsOpen(false)
              setSearchTerm('')
              setFilteredProtocols(groupedProtocols)
            }
          }}>
          <DialogContent
            ref={protocolDialogContentRef}
            className="sm:max-w-3xl max-h-[90vh]"
            onPointerDownOutside={(e) => {
              // 防止点击外部关闭对话框
              e.preventDefault()
            }}
            onEscapeKeyDown={(e) => {
              // 防止按ESC键关闭对话框
              e.preventDefault()
            }}
            onInteractOutside={(e) => {
              // 防止任何外部交互关闭对话框
              e.preventDefault()
            }}>
            <DialogHeader>
              <DialogTitle>选择协议</DialogTitle>
              <DialogDescription>选择设备使用的通信协议类型</DialogDescription>
            </DialogHeader>

            <div className="py-4">
              {/* 搜索框 */}
              <div className="mb-4 relative">
                <Input
                  ref={searchInputRef}
                  placeholder="搜索协议..."
                  className="w-full pl-9"
                  value={searchTerm}
                  onChange={handleSearchInput}
                  onKeyDown={handleSearchKeyDown}
                  autoComplete="off"
                />
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.3-4.3"></path>
                </svg>
              </div>

              {/* 分组标签页 */}
              {!searchTerm && (
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2 mb-2 overflow-x-auto pb-2">
                    {getManufacturerTabs().map((tab) => (
                      <Button
                        key={tab.id}
                        variant={activeTab === tab.id ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setActiveTab(tab.id)}
                        className={`rounded-full text-xs px-3 py-1 h-auto ${
                          activeTab === tab.id
                            ? 'bg-black text-white hover:bg-gray-800'
                            : 'border-gray-200'
                        }`}>
                        {tab.label}
                        {tab.count !== undefined && (
                          <span
                            className={`ml-1 px-1.5 py-0.5 rounded-full text-[10px] ${
                              activeTab === tab.id
                                ? 'bg-white text-black'
                                : 'bg-gray-100 text-gray-700'
                            }`}>
                            {tab.count}
                          </span>
                        )}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* 协议列表 - 新的卡片式布局 */}
              <div className="max-h-[60vh] overflow-y-auto pr-2">
                {Object.keys(getDisplayProtocols()).length > 0 ? (
                  Object.entries(getDisplayProtocols()).map(
                    ([groupName, protocols]) => (
                      <div key={groupName} className="mb-6">
                        {/* 分组标题 */}
                        <h3 className="font-medium text-sm mb-3 text-gray-700 border-b pb-1 sticky top-0 bg-white z-10 backdrop-blur-sm bg-opacity-90">
                          {groupName}
                        </h3>

                        {/* 协议卡片网格 */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                          {protocols.map((p) => (
                            <div
                              key={p.name}
                              className={`p-3 rounded-md cursor-pointer hover:bg-gray-50 transition-colors border ${
                                protocol === p.name
                                  ? 'bg-blue-50 border-blue-200 shadow-sm'
                                  : 'border-gray-200'
                              }`}
                              onClick={() =>
                                handleProtocolSelect(p.name as ProtocolType)
                              }>
                              <div className="font-medium">
                                {searchTerm
                                  ? highlightText(p.label, searchTerm)
                                  : p.label}{' '}
                                {p.version && `(${p.version})`}
                              </div>
                              {p.description && (
                                <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                                  {searchTerm
                                    ? highlightText(p.description, searchTerm)
                                    : p.description}
                                </div>
                              )}
                              <div className="mt-2 flex items-center justify-between">
                                <span className="text-xs text-gray-400">
                                  {p.name}
                                </span>
                                {getFrequentProtocols()[p.name] && (
                                  <span className="text-xs bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded-full">
                                    常用
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )
                  )
                ) : (
                  <div className="p-8 text-center text-sm text-gray-500">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mx-auto mb-3 text-gray-400">
                      <circle cx="11" cy="11" r="8"></circle>
                      <path d="m21 21-4.3-4.3"></path>
                    </svg>
                    <p>没有找到匹配的协议</p>
                  </div>
                )}
              </div>
            </div>

            <DialogFooter className="sm:justify-between">
              <Button variant="outline" onClick={() => setIsOpen(false)}>
                取消
              </Button>
              <Button
                disabled={!protocol}
                onClick={() => setIsOpen(false)}
                className="bg-black text-white hover:bg-gray-800">
                确认选择
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    )
  }

  // 将SerialChannel映射到EdgeChannelAddInput
  const mapFormToAddInput = (form: SerialChannelForm): EdgeChannelAddInput => {
    return {
      channelName: form.channelName,
      enable: true,
      serial: form.serial,
      baudRate: form.baudRate,
      dataBits: form.dataBits,
      stop: mapStopBitsToEnum(Number(form.stop)), // 映射为StopBits枚举
      checkout: mapParityToEnum(form.checkout), // 映射为Parity枚举
      threadCount: 1,
    }
  }

  // 将SerialChannel映射到EdgeChannel
  const mapFormToUpdateInput = (form: SerialChannelForm): EdgeChannel => {
    return {
      id: Number(form.id),
      channelName: form.channelName,
      enable: true,
      serial: form.serial,
      baudRate: form.baudRate,
      dataBits: form.dataBits,
      stop: mapStopBitsToEnum(Number(form.stop)), // 映射为StopBits枚举
      checkout: mapParityToEnum(form.checkout), // 映射为Parity枚举
      threadCount: 1,
    } as EdgeChannel
  }

  // 将数字形式的stopBits映射为API的StopBits枚举值
  const mapStopBitsToEnum = (stopBits: number): StopBits => {
    switch (stopBits) {
      case 1:
        return StopBits.NUMBER_0 // One
      case 2:
        return StopBits.NUMBER_1 // Two
      case 1.5:
        return StopBits.NUMBER_2 // OnePointFive
      default:
        return StopBits.NUMBER_0 // 默认为One
    }
  }

  // 将字符串形式的校验位映射到API的Parity枚举值
  const mapParityToEnum = (parity: string): Parity => {
    switch (parity) {
      case 'none':
        return Parity.NUMBER_0 // None
      case 'odd':
        return Parity.NUMBER_1 // Odd
      case 'even':
        return Parity.NUMBER_2 // Even
      case 'mark':
        return Parity.NUMBER_3 // Mark
      case 'space':
        return Parity.NUMBER_4 // Space
      default:
        return Parity.NUMBER_0 // 默认为None
    }
  }

  // 处理串口通道选择器视图中显示的内容
  const renderChannelItem = (channel: EdgeChannel) => {
    if (!channel) return null
    const displayChannel = convertChannelToDisplay(channel)

    return (
      <>
        <span className="truncate">{displayChannel.name}</span>
        <span className="text-xs text-gray-500 truncate max-w-[270px]">
          {`${displayChannel.port}, ${displayChannel.baudRate}波特, ${
            displayChannel.dataBits
          }位数据位, ${displayChannel.stopBits}位停止位, ${
            displayChannel.parity === 'none' ? '无校验' : displayChannel.parity
          }`}
          {displayChannel.isInUse && ' - 已占用'}
        </span>
      </>
    )
  }

  // 协议变更时加载协议详情
  useEffect(() => {
    if (protocol) {
      loadProtocolDetail(protocol)
    }
  }, [protocol])

  // 组件初始化时加载协议列表
  useEffect(() => {
    loadProtocols()

    if (editMode && deviceToEdit) {
      // 编辑模式：加载设备数据
      setDeviceId(deviceToEdit.identifier || deviceToEdit.id?.toString() || '') // 添加默认值和回退到id
      setDeviceName(deviceToEdit.name)
      setDescription(deviceToEdit.description || '')
      setLocation(deviceToEdit.location || '')
      setEnabled(deviceToEdit.enabled !== false)
      setProtocol(deviceToEdit.protocol)
      setAllowWrite(deviceToEdit.allowWrite === true)

      // 加载协议详情（实际配置数据会在loadProtocolDetail中设置）
      if (deviceToEdit.protocol) {
        loadProtocolDetail(deviceToEdit.protocol)
      }

      // 设置高级选项（在实际应用中，这些数据应该从API获取）
      setCollectInterval('1000')
      setRetryInterval('0')
      setReconnectTimeout('30000')
      setReportMode('immediate')
      setSaveHistory(true)

      // 确保基本信息区段展开
      setSectionsState((prev) => ({ ...prev, basicInfo: true }))
    } else {
      // 新增模式：重置表单
      setDeviceId('')
      setDeviceName('')
      setDescription('')
      setLocation('')
      setEnabled(true)

      // 默认选择ModbusTCP协议（或第一个可用协议）
      setProtocol('ModbusTcp')
      setProtocolConfig({})
      setCollectInterval('1000')
      setRetryInterval('0')
      setReconnectTimeout('30000')
      setReportMode('immediate')
      setSaveHistory(true)
      setAllowWrite(false)
      // 确保基本信息区段展开
      setSectionsState((prev) => ({ ...prev, basicInfo: true }))

      // 生成随机ID
      generateId()
    }
  }, [editMode, deviceToEdit])

  // 更新协议配置
  const updateProtocolConfig = (key: string, value: any) => {
    setProtocolConfig((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  // 页面模式：直接渲染内容
  return (
    <div className="w-full">
      {/* 添加CSS样式定义必填项的标记 */}
      <style>{`
        .required:after {
          content: ' *';
          color: #f43f5e;
          font-weight: bold;
        }

        /* 输入验证样式增强 */
        input:required:invalid,
        select:required:invalid {
          border-color: #f43f5e;
        }

        /* 必填项焦点样式 */
        .required-field:focus-within {
          border-color: #2563eb;
          box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
        }
      `}</style>

      <div className="flex-1 overflow-auto">
        {/* 操作指引卡片 */}
        <div className="mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start gap-4">
            <div className="bg-blue-100 rounded-full p-2 mt-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-blue-600">
                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                <path d="M12 16v-4" />
                <path d="M12 8h.01" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-medium text-blue-700">
                {editMode ? '如何编辑设备' : '如何添加设备'}
              </h3>
              <p className="text-sm text-blue-600 mb-2">
                {editMode
                  ? '请按照以下步骤编辑您的设备设置：'
                  : '请按照以下步骤添加您的设备：'}
              </p>
              <ol className="list-decimal pl-5 text-sm text-blue-700 space-y-1">
                <li>
                  {editMode ? '检查' : '填写'}
                  <strong>基本信息</strong>
                  {editMode ? '（唯一码无法修改）' : '（唯一码、名称等必填项）'}
                </li>
                <li>
                  {editMode ? '确认' : '选择'}合适的<strong>通信协议</strong>
                  {editMode ? '是否正确' : '并配置连接参数'}
                </li>
                <li>配置高级设置（可选）</li>
                <li>
                  点击<strong>{editMode ? '保存设备' : '添加设备'}</strong>
                  按钮提交
                </li>
              </ol>
              <p className="text-sm text-blue-600 mt-2 font-medium">
                注意：带有 <span className="text-red-500">*</span>{' '}
                标记的为必填项
              </p>
            </div>
          </div>
        </div>

        {/* 基本信息区段 */}
        <div className="mb-8">
          <div
            className="flex items-center justify-between border-b pb-2 mb-4 cursor-pointer"
            onClick={() => toggleSection('basicInfo')}>
            <h3 className="text-lg font-medium">基本信息</h3>
            <Button variant="ghost" size="sm" className="p-0 h-auto">
              {sectionsState.basicInfo ? (
                <ChevronUp className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )}
            </Button>
          </div>

          {sectionsState.basicInfo && (
            <div className="space-y-6 p-4">
              {/* 基本信息表单内容 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label
                      htmlFor="deviceId"
                      className="required text-sm font-medium">
                      唯一码
                    </Label>
                    <TooltipProvider
                      delayDuration={300}
                      skipDelayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                        </TooltipTrigger>
                        <TooltipContent
                          className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                          side="top"
                          align="start">
                          <p>设备的唯一标识符，系统内不可重复</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="flex gap-2">
                    <Input
                      id="deviceId"
                      value={deviceId}
                      onChange={(e) => setDeviceId(e.target.value)}
                      placeholder="请输入唯一码"
                      disabled={editMode}
                      className="border-gray-300 focus:border-black focus:ring-1 focus:ring-black required-field"
                      required
                    />
                    {!editMode && (
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={generateId}
                        type="button"
                        className="border-gray-300 hover:bg-gray-50"
                        title="生成随机ID">
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  {!deviceId && (
                    <p className="text-xs text-gray-500 mt-1">
                      示例：DEV-1234、SENSOR-TEMP-01
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label
                      htmlFor="deviceName"
                      className="required text-sm font-medium">
                      设备名称
                    </Label>
                    <TooltipProvider
                      delayDuration={300}
                      skipDelayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                        </TooltipTrigger>
                        <TooltipContent
                          className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                          side="top"
                          align="start">
                          <p>设备的显示名称，便于识别</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Input
                    id="deviceName"
                    value={deviceName}
                    onChange={(e) => setDeviceName(e.target.value)}
                    placeholder="请输入设备名称"
                    className="border-gray-300 focus:border-black focus:ring-1 focus:ring-black required-field"
                    required
                  />
                  {!deviceName && (
                    <p className="text-xs text-gray-500 mt-1">
                      示例：车间温度传感器、1号流水线PLC
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="location" className="text-sm font-medium">
                    位置
                  </Label>
                  <TooltipProvider delayDuration={300} skipDelayDuration={300}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                      </TooltipTrigger>
                      <TooltipContent
                        className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                        side="top"
                        align="start">
                        <p>设备的物理位置信息</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="location"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="请输入设备位置"
                  className="border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="enabled" className="text-sm font-medium">
                      启用设备
                    </Label>
                    <TooltipProvider
                      delayDuration={300}
                      skipDelayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent
                          className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                          side="top"
                          align="start">
                          <p>设置设备是否启用，禁用后将不再采集数据</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Switch
                    id="enabled"
                    checked={enabled}
                    onCheckedChange={setEnabled}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <ProtocolSelector />
              </div>
            </div>
          )}
        </div>

        {/* 连接配置区段 */}
        <div className="mb-8">
          <div
            className="flex items-center justify-between border-b pb-2 mb-4 cursor-pointer"
            onClick={() => toggleSection('connectionConfig')}>
            <h3 className="text-lg font-medium">连接配置</h3>
            <Button variant="ghost" size="sm" className="p-0 h-auto">
              {sectionsState.connectionConfig ? (
                <ChevronUp className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )}
            </Button>
          </div>

          {sectionsState.connectionConfig && (
            <div className="p-4">
              <div className="border rounded-lg">
                <div className="p-4">
                  {/* 添加连接说明 */}
                  <div className="mb-6 bg-gray-50 p-4 rounded-md border border-gray-200">
                    <div className="flex items-start gap-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-gray-600 mt-0.5">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="16" x2="12" y2="12"></line>
                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                      </svg>
                      <div>
                        <h4 className="font-medium text-gray-700 mb-1">
                          连接配置说明
                        </h4>
                        <p className="text-sm text-gray-600">
                          配置连接参数时，请参考设备厂商提供的通信规范。不同协议类型具有不同的连接参数。
                        </p>
                        <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3 text-xs text-gray-500">
                          <div>
                            <span className="font-medium block">
                              网络设备需提供：
                            </span>
                            <ul className="list-disc pl-5 mt-1 space-y-0.5">
                              <li>IP地址和端口</li>
                              <li>站号/单元号</li>
                              <li>超时和心跳设置</li>
                            </ul>
                          </div>
                          <div>
                            <span className="font-medium block">
                              串口设备需提供：
                            </span>
                            <ul className="list-disc pl-5 mt-1 space-y-0.5">
                              <li>串口通道</li>
                              <li>波特率、数据位、校验位</li>
                              <li>站号和超时设置</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <SerialChannelSelector />
                  {renderProtocolConfigForm()}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 高级设置区段 */}
        <div className="mb-8">
          <div
            className="flex items-center justify-between border-b pb-2 mb-4 cursor-pointer"
            onClick={() => toggleSection('advancedSettings')}>
            <h3 className="text-lg font-medium">高级设置</h3>
            <Button variant="ghost" size="sm" className="p-0 h-auto">
              {sectionsState.advancedSettings ? (
                <ChevronUp className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )}
            </Button>
          </div>

          {sectionsState.advancedSettings && (
            <div className="space-y-6 p-4">
              {/* 添加高级设置提示 */}
              <div className="bg-amber-50 p-4 rounded-md border border-amber-100 mb-4">
                <div className="flex items-start gap-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-amber-600 mt-0.5">
                    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                    <line x1="12" y1="9" x2="12" y2="13"></line>
                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                  </svg>
                  <div>
                    <h4 className="font-medium text-amber-800 mb-1">
                      高级配置说明
                    </h4>
                    <p className="text-sm text-amber-700">
                      这些设置为可选项，大多数情况下可以使用默认值。如果您不确定如何设置，请保留默认值。
                    </p>
                  </div>
                </div>
              </div>

              {/* 高级设置表单内容 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label
                      htmlFor="collectInterval"
                      className="text-sm font-medium">
                      采集间隔 (ms)
                    </Label>
                    <TooltipProvider
                      delayDuration={300}
                      skipDelayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                        </TooltipTrigger>
                        <TooltipContent
                          className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                          side="top"
                          align="start">
                          <p>数据采集的时间间隔，单位为毫秒</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Input
                    id="collectInterval"
                    type="number"
                    value={collectInterval}
                    onChange={(e) => setCollectInterval(e.target.value)}
                    placeholder="采集间隔"
                    min={20}
                    className="border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label
                      htmlFor="retryInterval"
                      className="text-sm font-medium">
                      重连延迟采集时间 (ms)
                    </Label>
                    <TooltipProvider
                      delayDuration={300}
                      skipDelayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                        </TooltipTrigger>
                        <TooltipContent
                          className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                          side="top"
                          align="start">
                          <p>
                            设备连接成功后，延迟后再进行数据采集，单位为毫秒
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Input
                    id="retryInterval"
                    type="number"
                    value={retryInterval}
                    onChange={(e) => setRetryInterval(e.target.value)}
                    placeholder="重试间隔"
                    min={0}
                    className="border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label
                      htmlFor="reconnectTimeout"
                      className="text-sm font-medium">
                      重连超时 (ms)
                    </Label>
                    <TooltipProvider
                      delayDuration={300}
                      skipDelayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                        </TooltipTrigger>
                        <TooltipContent
                          className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                          side="top"
                          align="start">
                          <p>设备重连的超时时间，单位为毫秒</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Input
                    id="reconnectTimeout"
                    type="number"
                    value={reconnectTimeout}
                    onChange={(e) => setReconnectTimeout(e.target.value)}
                    placeholder="重连超时"
                    min={1000}
                    className="border-gray-300 focus:border-black focus:ring-1 focus:ring-black"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label htmlFor="reportMode" className="text-sm font-medium">
                      上报方式
                    </Label>
                    <TooltipProvider
                      delayDuration={300}
                      skipDelayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                        </TooltipTrigger>
                        <TooltipContent
                          className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                          side="top"
                          align="start">
                          <p>设备数据上报到平台的方式</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Select value={reportMode} onValueChange={setReportMode}>
                    <SelectTrigger className="border-gray-300 focus:border-black focus:ring-1 focus:ring-black">
                      <SelectValue placeholder="选择上报方式" />
                    </SelectTrigger>
                    <SelectContent>
                      {reportModes.map((mode) => (
                        <SelectItem key={mode.value} value={mode.value}>
                          <div className="flex flex-col">
                            <span>{mode.label}</span>
                            <span className="text-xs text-gray-500">
                              {mode.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Label
                      htmlFor="saveHistory"
                      className="text-sm font-medium">
                      保存历史数据
                    </Label>
                    <TooltipProvider
                      delayDuration={300}
                      skipDelayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent
                          className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                          side="top"
                          align="start">
                          <p>是否保存设备的历史数据</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Switch
                    id="saveHistory"
                    checked={saveHistory}
                    onCheckedChange={setSaveHistory}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="allowWrite" className="text-sm font-medium">
                      允许写入
                    </Label>
                    <TooltipProvider
                      delayDuration={300}
                      skipDelayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent
                          className="max-w-[220px] bg-white bg-opacity-100 text-sm break-words"
                          side="top"
                          align="start">
                          <p>是否允许向设备写入数据</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Switch
                    id="allowWrite"
                    checked={allowWrite}
                    onCheckedChange={setAllowWrite}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between mt-6 pt-4 border-t">
        <div className="text-xs text-gray-500 flex items-center">
          <span className="required mr-1"></span> 表示必填字段
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => {
              // 返回到设备列表页面
              window.history.back()
            }}
            disabled={isSubmitting}>
            取消
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || loadingProtocolDetail}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 px-6">
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {editMode ? '保存中...' : '添加中...'}
              </>
            ) : editMode ? (
              '保存设备'
            ) : (
              '添加设备'
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
