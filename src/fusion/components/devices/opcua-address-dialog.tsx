import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Loader2,
  Search,
  ChevronRight,
  Server,
  Database,
  File,
  RefreshCw,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { DynamicAddressBrowser } from './dynamic-address-browser'
import { BrowsableNode } from '@/lib/api/device-api'

interface OpcUaAddressDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  deviceId: number
  onAddressSelected: (nodeId: string, browseName: string) => void
}

export function OpcUaAddressDialog({
  open,
  onOpenChange,
  deviceId,
  onAddressSelected,
}: OpcUaAddressDialogProps) {
  const [selectedAddress, setSelectedAddress] = useState('')

  const handleNodeSelect = (node: BrowsableNode) => {
    setSelectedAddress(node.nodeId)
  }

  const handleConfirm = () => {
    if (selectedAddress) {
      onAddressSelected(selectedAddress, selectedAddress)
      onOpenChange(false)
      toast({
        title: '地址已选择',
        description: `已选择地址: ${selectedAddress}`
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>OPC UA 地址浏览器</DialogTitle>
          <DialogDescription>
            浏览和选择OPC UA节点地址
          </DialogDescription>
        </DialogHeader>

        <DynamicAddressBrowser
          deviceId={deviceId}
          value={selectedAddress}
          onChange={setSelectedAddress}
          onSelect={handleNodeSelect}
        />

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleConfirm} disabled={!selectedAddress}>
            确认选择
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
