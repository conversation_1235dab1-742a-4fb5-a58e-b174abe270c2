import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Settings, HelpCircle, CheckCircle, XCircle } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { DeviceService, AddressBrowseConfig, BrowsableNode } from '@/lib/api/device-api'
import { DynamicAddressBrowser } from './dynamic-address-browser'

interface SmartAddressInputProps {
  deviceId: number
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  required?: boolean
}

export function SmartAddressInput({
  deviceId,
  value = '',
  onChange,
  placeholder = '请输入地址',
  className,
  disabled = false,
  required = false
}: SmartAddressInputProps) {
  const [config, setConfig] = useState<AddressBrowseConfig | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const [validationResult, setValidationResult] = useState<boolean | null>(null)
  const [inputValue, setInputValue] = useState(value)

  // 加载地址浏览配置
  useEffect(() => {
    loadConfig()
  }, [deviceId])

  // 同步外部值变化
  useEffect(() => {
    setInputValue(value)
    if (value && config?.validationPattern) {
      validateAddress(value)
    }
  }, [value, config])

  const loadConfig = async () => {
    try {
      const response = await DeviceService.getAddressBrowseConfig(deviceId)
      if (response.code === 200 && response.data) {
        setConfig(response.data)
      }
    } catch (error) {
      console.error('加载地址浏览配置失败:', error)
    }
  }

  const validateAddress = async (address: string) => {
    if (!address || !config) {
      setValidationResult(null)
      return
    }

    // 客户端验证
    if (config.validationPattern) {
      const regex = new RegExp(config.validationPattern)
      if (!regex.test(address)) {
        setValidationResult(false)
        return
      }
    }

    // 服务端验证（如果支持）
    if (config.supportsDynamicBrowsing) {
      try {
        setIsValidating(true)
        const response = await DeviceService.validateNode(deviceId, address)
        setValidationResult(response.data || false)
      } catch (error) {
        console.error('验证地址失败:', error)
        setValidationResult(false)
      } finally {
        setIsValidating(false)
      }
    } else {
      setValidationResult(true)
    }
  }

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue)
    onChange?.(newValue)
    
    // 延迟验证
    if (newValue) {
      const timer = setTimeout(() => {
        validateAddress(newValue)
      }, 500)
      return () => clearTimeout(timer)
    } else {
      setValidationResult(null)
    }
  }

  const handleNodeSelect = (node: BrowsableNode) => {
    setInputValue(node.nodeId)
    onChange?.(node.nodeId)
    setIsDialogOpen(false)
    setValidationResult(true)
    
    toast({
      title: '地址已选择',
      description: `已选择节点: ${node.displayName} (${node.nodeId})`
    })
  }

  const getValidationIcon = () => {
    if (isValidating) {
      return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" />
    }
    if (validationResult === true) {
      return <CheckCircle className="h-4 w-4 text-green-600" />
    }
    if (validationResult === false) {
      return <XCircle className="h-4 w-4 text-red-600" />
    }
    return null
  }

  const getInputType = () => {
    if (!config) return 'text'
    return config.inputType.toLowerCase()
  }

  const shouldShowBrowser = () => {
    return config?.supportsDynamicBrowsing && (
      config.inputType === 'Helper' || 
      config.inputType === 'Hybrid'
    )
  }

  return (
    <div className={className}>
      <div className="space-y-2">
        {/* 地址输入框 */}
        <div className="relative">
          <Input
            value={inputValue}
            onChange={(e) => handleInputChange(e.target.value)}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            className={`pr-20 ${
              validationResult === false ? 'border-red-500' : 
              validationResult === true ? 'border-green-500' : ''
            }`}
          />
          
          {/* 右侧按钮组 */}
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center space-x-1">
            {/* 验证状态图标 */}
            {inputValue && getValidationIcon()}
            
            {/* 地址浏览器按钮 */}
            {shouldShowBrowser() && (
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    disabled={disabled}
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[80vh]">
                  <DialogHeader>
                    <DialogTitle>地址浏览器</DialogTitle>
                    <DialogDescription>
                      浏览和选择设备地址
                    </DialogDescription>
                  </DialogHeader>
                  <DynamicAddressBrowser
                    deviceId={deviceId}
                    value={inputValue}
                    onChange={handleInputChange}
                    onSelect={handleNodeSelect}
                  />
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>

        {/* 地址信息和帮助 */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-2">
            {config && (
              <>
                <Badge variant="outline" className="text-xs">
                  {config.inputType}
                </Badge>
                {config.supportsDynamicBrowsing && (
                  <Badge variant="outline" className="text-xs">
                    动态浏览
                  </Badge>
                )}
                {config.requiresConnection && (
                  <Badge variant="outline" className="text-xs">
                    需要连接
                  </Badge>
                )}
              </>
            )}
          </div>
          
          {config?.formatDescription && (
            <div className="flex items-center space-x-1">
              <HelpCircle className="h-3 w-3" />
              <span title={config.formatDescription}>格式帮助</span>
            </div>
          )}
        </div>

        {/* 验证错误信息 */}
        {validationResult === false && (
          <div className="text-xs text-red-600">
            地址格式无效或节点不存在
          </div>
        )}

        {/* 格式说明 */}
        {config?.formatDescription && inputValue === '' && (
          <div className="text-xs text-muted-foreground">
            {config.formatDescription}
          </div>
        )}
      </div>
    </div>
  )
}

// 便捷的表单字段组件
interface SmartAddressFieldProps extends SmartAddressInputProps {
  label?: string
  description?: string
  error?: string
}

export function SmartAddressField({
  label,
  description,
  error,
  ...inputProps
}: SmartAddressFieldProps) {
  return (
    <div className="space-y-2">
      {label && (
        <Label htmlFor={`address-${inputProps.deviceId}`}>
          {label}
          {inputProps.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <SmartAddressInput
        {...inputProps}
        className="w-full"
      />
      
      {description && (
        <div className="text-xs text-muted-foreground">
          {description}
        </div>
      )}
      
      {error && (
        <div className="text-xs text-red-600">
          {error}
        </div>
      )}
    </div>
  )
}
