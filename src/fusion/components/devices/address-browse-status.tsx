import React, { useState, useEffect } from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Loader2, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw, 
  Wifi, 
  WifiOff,
  Clock,
  TrendingUp,
  Activity
} from 'lucide-react'
import { addressBrowsePerformanceMonitor } from '@/lib/api/address-browse-performance'

interface AddressBrowseStatusProps {
  deviceId: number
  isConnected?: boolean
  onRetry?: () => void
  onClearCache?: () => void
  className?: string
}

export function AddressBrowseStatus({
  deviceId,
  isConnected = false,
  onRetry,
  onClearCache,
  className
}: AddressBrowseStatusProps) {
  const [performanceStats, setPerformanceStats] = useState<any>(null)
  const [errorStats, setErrorStats] = useState<any>(null)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    const updateStats = () => {
      setPerformanceStats(addressBrowsePerformanceMonitor.getPerformanceStats())
      setErrorStats(addressBrowsePerformanceMonitor.getErrorStats())
    }

    updateStats()
    const interval = setInterval(updateStats, 5000) // 每5秒更新一次

    return () => clearInterval(interval)
  }, [])

  const getConnectionStatus = () => {
    if (isConnected) {
      return {
        icon: <Wifi className="h-4 w-4 text-green-600" />,
        text: '已连接',
        variant: 'default' as const
      }
    } else {
      return {
        icon: <WifiOff className="h-4 w-4 text-red-600" />,
        text: '未连接',
        variant: 'destructive' as const
      }
    }
  }

  const getPerformanceLevel = () => {
    if (!performanceStats || performanceStats.totalOperations === 0) {
      return { level: 'unknown', color: 'gray', text: '未知' }
    }

    const avgDuration = performanceStats.averageDuration
    const successRate = performanceStats.successRate

    if (successRate >= 95 && avgDuration < 500) {
      return { level: 'excellent', color: 'green', text: '优秀' }
    } else if (successRate >= 90 && avgDuration < 1000) {
      return { level: 'good', color: 'blue', text: '良好' }
    } else if (successRate >= 80 && avgDuration < 2000) {
      return { level: 'fair', color: 'yellow', text: '一般' }
    } else {
      return { level: 'poor', color: 'red', text: '较差' }
    }
  }

  const connectionStatus = getConnectionStatus()
  const performanceLevel = getPerformanceLevel()

  return (
    <div className={className}>
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">连接状态</CardTitle>
            <div className="flex items-center space-x-2">
              {connectionStatus.icon}
              <Badge variant={connectionStatus.variant}>
                {connectionStatus.text}
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* 性能概览 */}
          {performanceStats && performanceStats.totalOperations > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>性能等级</span>
                <Badge 
                  variant="outline" 
                  className={`text-${performanceLevel.color}-600 border-${performanceLevel.color}-200`}
                >
                  <Activity className="h-3 w-3 mr-1" />
                  {performanceLevel.text}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <div className="text-muted-foreground">成功率</div>
                  <div className="font-medium">
                    {performanceStats.successRate.toFixed(1)}%
                  </div>
                </div>
                <div>
                  <div className="text-muted-foreground">平均响应</div>
                  <div className="font-medium">
                    {performanceStats.averageDuration.toFixed(0)}ms
                  </div>
                </div>
                <div>
                  <div className="text-muted-foreground">缓存命中</div>
                  <div className="font-medium">
                    {performanceStats.cacheHitRate.toFixed(1)}%
                  </div>
                </div>
                <div>
                  <div className="text-muted-foreground">总操作数</div>
                  <div className="font-medium">
                    {performanceStats.totalOperations}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 错误提示 */}
          {errorStats && errorStats.totalErrors > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                检测到 {errorStats.totalErrors} 个错误
                {errorStats.recentErrors.length > 0 && (
                  <span className="block text-xs mt-1">
                    最近错误: {errorStats.recentErrors[0].error.message}
                  </span>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* 操作按钮 */}
          <div className="flex space-x-2">
            {onRetry && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                disabled={!isConnected}
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                重试
              </Button>
            )}
            
            {onClearCache && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearCache}
              >
                清除缓存
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? '隐藏' : '详情'}
            </Button>
          </div>

          {/* 详细信息 */}
          {showDetails && performanceStats && (
            <div className="border-t pt-4 space-y-3">
              <div className="text-sm font-medium">性能详情</div>
              
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span>慢操作数量:</span>
                  <span>{performanceStats.slowOperations}</span>
                </div>
                
                {errorStats && Object.keys(errorStats.errorsByType).length > 0 && (
                  <div>
                    <div className="font-medium mb-1">错误分布:</div>
                    {Object.entries(errorStats.errorsByType).map(([type, count]) => (
                      <div key={type} className="flex justify-between pl-2">
                        <span>{type}:</span>
                        <span>{count as number}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// 全局加载状态组件
interface LoadingOverlayProps {
  isLoading: boolean
  message?: string
  progress?: number
}

export function LoadingOverlay({ isLoading, message = '加载中...', progress }: LoadingOverlayProps) {
  if (!isLoading) return null

  return (
    <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto" />
        <div className="text-sm text-muted-foreground">{message}</div>
        {progress !== undefined && (
          <div className="w-48">
            <Progress value={progress} className="h-2" />
            <div className="text-xs text-muted-foreground mt-1">
              {progress.toFixed(0)}%
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// 错误边界组件
interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  onError?: (error: Error, errorInfo: any) => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class AddressBrowseErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('地址浏览组件错误:', error, errorInfo)
    this.props.onError?.(error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            地址浏览组件发生错误，请刷新页面重试
            {this.state.error && (
              <details className="mt-2">
                <summary className="cursor-pointer">错误详情</summary>
                <pre className="text-xs mt-1 whitespace-pre-wrap">
                  {this.state.error.message}
                </pre>
              </details>
            )}
          </AlertDescription>
        </Alert>
      )
    }

    return this.props.children
  }
}
