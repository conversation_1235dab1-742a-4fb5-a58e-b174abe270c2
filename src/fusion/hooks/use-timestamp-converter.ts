/**
 * 时间戳转换工具核心逻辑Hook
 *
 * 提供时间戳与日期时间相互转换的功能
 * 支持秒级和毫秒级时间戳
 * 支持获取当前时间戳
 */

import { useState, useCallback } from 'react'

export interface TimestampConverterState {
  timestamp: string
  dateTime: string
  dateInput: string
  error: string | null
}

export interface TimestampResult {
  seconds: number
  milliseconds: number
}

export function useTimestampConverter() {
  const [timestamp, setTimestamp] = useState('')
  const [dateTime, setDateTime] = useState('')
  const [dateInput, setDateInput] = useState('')
  const [error, setError] = useState<string | null>(null)

  // 时间戳转日期时间
  const convertTimestampToDate = useCallback(() => {
    try {
      if (!timestamp.trim()) {
        setError('请输入时间戳')
        return
      }

      // 判断是秒还是毫秒时间戳
      const ts =
        timestamp.length < 13 ? parseInt(timestamp) * 1000 : parseInt(timestamp)

      if (isNaN(ts)) {
        setError('无效的时间戳格式')
        return
      }

      const date = new Date(ts)

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        setError('无效的时间戳值')
        return
      }

      setDateTime(date.toISOString())
      setError(null)
    } catch (e) {
      setError(`转换错误: ${(e as Error).message}`)
    }
  }, [timestamp])

  // 日期时间转时间戳
  const convertDateToTimestamp = useCallback((): TimestampResult | null => {
    try {
      if (!dateInput) {
        return null
      }

      const date = new Date(dateInput)

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        setError('无效的日期格式')
        return null
      }

      setError(null)
      return {
        seconds: Math.floor(date.getTime() / 1000),
        milliseconds: date.getTime(),
      }
    } catch (e) {
      setError(`转换错误: ${(e as Error).message}`)
      return null
    }
  }, [dateInput])

  // 获取当前时间戳
  const getCurrentTimestamp = useCallback((): TimestampResult => {
    const now = new Date()
    return {
      seconds: Math.floor(now.getTime() / 1000),
      milliseconds: now.getTime(),
    }
  }, [])

  // 格式化日期时间
  const formatDateTime = useCallback((date: Date): string => {
    return date.toISOString().replace('T', ' ').substring(0, 19)
  }, [])

  return {
    // 状态
    timestamp,
    setTimestamp,
    dateTime,
    setDateTime,
    dateInput,
    setDateInput,
    error,

    // 方法
    convertTimestampToDate,
    convertDateToTimestamp,
    getCurrentTimestamp,
    formatDateTime,
  }
}
