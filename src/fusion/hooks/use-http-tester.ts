/**
 * HTTP测试工具核心逻辑Hook
 *
 * 提供HTTP请求发送、响应处理和历史记录功能
 * 支持多种HTTP方法、请求头和请求体
 */

import { useState, useCallback, useEffect } from 'react'

export type HttpMethod =
  | 'GET'
  | 'POST'
  | 'PUT'
  | 'DELETE'
  | 'PATCH'
  | 'HEAD'
  | 'OPTIONS'

export interface HttpHeader {
  key: string
  value: string
  enabled: boolean
}

export interface HttpParam {
  key: string
  value: string
  enabled: boolean
}

export interface HttpRequestHistory {
  id: string
  method: HttpMethod
  url: string
  timestamp: Date
  responseTime?: number
  status?: number
  success: boolean
}

export interface HttpResponse {
  status?: number
  statusText?: string
  headers?: Record<string, string>
  data?: any
  error?: string
  time?: number
}

export function useHttpTester() {
  const [method, setMethod] = useState<HttpMethod>('GET')
  const [url, setUrl] = useState('')
  const [headers, setHeaders] = useState<HttpHeader[]>([
    { key: 'Content-Type', value: 'application/json', enabled: true },
  ])
  const [params, setParams] = useState<HttpParam[]>([
    { key: '', value: '', enabled: true },
  ])
  const [body, setBody] = useState('')
  const [response, setResponse] = useState<HttpResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [history, setHistory] = useState<HttpRequestHistory[]>([])
  const [error, setError] = useState<string | null>(null)

  // 从本地存储加载历史记录
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem('http-tester-history')
      if (savedHistory) {
        const parsedHistory = JSON.parse(savedHistory)
        // 将字符串日期转换回Date对象
        const processedHistory = parsedHistory.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp),
        }))
        setHistory(processedHistory)
      }
    } catch (e) {
      console.error('Failed to load HTTP request history:', e)
    }
  }, [])

  // 保存历史记录到本地存储
  const saveHistory = useCallback((newHistory: HttpRequestHistory[]) => {
    try {
      localStorage.setItem('http-tester-history', JSON.stringify(newHistory))
    } catch (e) {
      console.error('Failed to save HTTP request history:', e)
    }
  }, [])

  // 添加请求头
  const addHeader = useCallback(() => {
    setHeaders([...headers, { key: '', value: '', enabled: true }])
  }, [headers])

  // 删除请求头
  const removeHeader = useCallback(
    (index: number) => {
      const newHeaders = [...headers]
      newHeaders.splice(index, 1)
      setHeaders(newHeaders)
    },
    [headers]
  )

  // 更新请求头
  const updateHeader = useCallback(
    (index: number, key: string, value: string, enabled: boolean) => {
      const newHeaders = [...headers]
      newHeaders[index] = { key, value, enabled }
      setHeaders(newHeaders)
    },
    [headers]
  )

  // 添加请求参数
  const addParam = useCallback(() => {
    setParams([...params, { key: '', value: '', enabled: true }])
  }, [params])

  // 删除请求参数
  const removeParam = useCallback(
    (index: number) => {
      const newParams = [...params]
      newParams.splice(index, 1)
      setParams(newParams)
    },
    [params]
  )

  // 更新请求参数
  const updateParam = useCallback(
    (index: number, key: string, value: string, enabled: boolean) => {
      const newParams = [...params]
      newParams[index] = { key, value, enabled }
      setParams(newParams)
    },
    [params]
  )

  // 构建URL（包含查询参数）
  const buildUrl = useCallback(() => {
    let finalUrl = url
    const enabledParams = params.filter((p) => p.enabled && p.key)

    if (enabledParams.length > 0) {
      const queryString = enabledParams
        .map(
          (p) => `${encodeURIComponent(p.key)}=${encodeURIComponent(p.value)}`
        )
        .join('&')

      finalUrl += (url.includes('?') ? '&' : '?') + queryString
    }

    return finalUrl
  }, [url, params])

  // 发送HTTP请求
  const sendRequest = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    setResponse(null)

    const finalUrl = buildUrl()
    const enabledHeaders = headers
      .filter((h) => h.enabled && h.key)
      .reduce((obj, h) => ({ ...obj, [h.key]: h.value }), {})

    const requestId = Date.now().toString()
    const requestStart = performance.now()

    try {
      const requestOptions: RequestInit = {
        method,
        headers: enabledHeaders as HeadersInit,
        credentials: 'include',
      }

      // 添加请求体（对于非GET/HEAD请求）
      if (method !== 'GET' && method !== 'HEAD' && body) {
        requestOptions.body = body
      }

      const res = await fetch(finalUrl, requestOptions)
      const requestEnd = performance.now()
      const responseTime = Math.round(requestEnd - requestStart)

      // 解析响应头
      const responseHeaders: Record<string, string> = {}
      res.headers.forEach((value, key) => {
        responseHeaders[key] = value
      })

      // 尝试解析响应体
      let responseData
      const contentType = res.headers.get('content-type')

      if (contentType?.includes('application/json')) {
        responseData = await res.json()
      } else if (contentType?.includes('text/')) {
        responseData = await res.text()
      } else {
        responseData = await res.text()
      }

      // 更新响应状态
      setResponse({
        status: res.status,
        statusText: res.statusText,
        headers: responseHeaders,
        data: responseData,
        time: responseTime,
      })

      // 添加到历史记录
      const historyItem: HttpRequestHistory = {
        id: requestId,
        method,
        url: finalUrl,
        timestamp: new Date(),
        responseTime,
        status: res.status,
        success: res.ok,
      }

      const newHistory = [historyItem, ...history].slice(0, 50) // 限制历史记录数量
      setHistory(newHistory)
      saveHistory(newHistory)
    } catch (e) {
      const requestEnd = performance.now()
      const responseTime = Math.round(requestEnd - requestStart)

      setError(`请求失败: ${(e as Error).message}`)
      setResponse({
        error: (e as Error).message,
        time: responseTime,
      })

      // 添加失败的请求到历史记录
      const historyItem: HttpRequestHistory = {
        id: requestId,
        method,
        url: finalUrl,
        timestamp: new Date(),
        responseTime,
        success: false,
      }

      const newHistory = [historyItem, ...history].slice(0, 50)
      setHistory(newHistory)
      saveHistory(newHistory)
    } finally {
      setIsLoading(false)
    }
  }, [method, url, headers, params, body, history, buildUrl, saveHistory])

  // 从历史记录中加载请求
  const loadFromHistory = useCallback((historyItem: HttpRequestHistory) => {
    setMethod(historyItem.method)
    setUrl(historyItem.url)
    // 注意：这里不会恢复headers和body，因为历史记录中没有保存这些信息
  }, [])

  // 清除历史记录
  const clearHistory = useCallback(() => {
    setHistory([])
    saveHistory([])
  }, [saveHistory])

  // 重置表单
  const resetForm = useCallback(() => {
    setMethod('GET')
    setUrl('')
    setHeaders([
      { key: 'Content-Type', value: 'application/json', enabled: true },
    ])
    setParams([{ key: '', value: '', enabled: true }])
    setBody('')
    setResponse(null)
    setError(null)
  }, [])

  return {
    // 状态
    method,
    setMethod,
    url,
    setUrl,
    headers,
    setHeaders,
    params,
    setParams,
    body,
    setBody,
    response,
    isLoading,
    history,
    error,

    // 方法
    addHeader,
    removeHeader,
    updateHeader,
    addParam,
    removeParam,
    updateParam,
    buildUrl,
    sendRequest,
    loadFromHistory,
    clearHistory,
    resetForm,
  }
}
