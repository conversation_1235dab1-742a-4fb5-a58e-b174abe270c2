import { useState, useEffect, useCallback } from 'react'
import { 
  userManagement<PERSON><PERSON>, 
  SysUser, 
  UserQueryParams, 
  UserInput,
  PagedResponse 
} from '@/lib/api/user-management-api'
import { toast } from '@/hooks/use-toast'

export interface UseUserManagementReturn {
  // 数据状态
  users: SysUser[]
  total: number
  page: number
  pageSize: number
  
  // 加载状态
  loading: boolean
  createLoading: boolean
  updateLoading: boolean
  deleteLoading: boolean
  
  // 操作方法
  loadUsers: (params?: UserQueryParams) => Promise<void>
  createUser: (user: UserInput) => Promise<boolean>
  updateUser: (id: number, user: UserInput) => Promise<boolean>
  deleteUser: (id: number) => Promise<boolean>
  toggleUserStatus: (id: number, status: boolean) => Promise<boolean>
  resetUserPassword: (id: number, newPassword: string) => Promise<boolean>
  
  // 分页方法
  setPage: (page: number) => void
  setPageSize: (pageSize: number) => void
  
  // 搜索方法
  searchUsers: (searchTerm: string) => Promise<void>
  
  // 刷新方法
  refresh: () => Promise<void>
}

export function useUserManagement(initialParams?: UserQueryParams): UseUserManagementReturn {
  const [users, setUsers] = useState<SysUser[]>([])
  const [total, setTotal] = useState(0)
  const [page, setPageState] = useState(initialParams?.page || 1)
  const [pageSize, setPageSizeState] = useState(initialParams?.pageSize || 10)
  const [currentParams, setCurrentParams] = useState<UserQueryParams>(initialParams || {})

  // 加载用户列表
  const loadUsers = useCallback(async (params?: UserQueryParams) => {
    try {
      const queryParams = { ...currentParams, ...params, page, pageSize }
      const response = await userManagementApi.getUserList(queryParams)
      
      if (response.data.succeeded) {
        const data = response.data.data as PagedResponse<SysUser>
        setUsers(data.items)
        setTotal(data.total)
        setCurrentParams(queryParams)
      } else {
        toast({
          title: '加载失败',
          description: response.data.errors || '获取用户列表失败',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('加载用户列表失败:', error)
      toast({
        title: '加载失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    }
  }, [currentParams, page, pageSize])

  // 创建用户
  const createUser = useCallback(async (user: UserInput): Promise<boolean> => {
    try {
      const response = await userManagementApi.createUser(user)
      
      if (response.data.succeeded) {
        toast({
          title: '创建成功',
          description: `用户 "${user.name}" 已创建`,
        })
        await loadUsers() // 刷新列表
        return true
      } else {
        toast({
          title: '创建失败',
          description: response.data.errors || '创建用户失败',
          variant: 'destructive',
        })
        return false
      }
    } catch (error) {
      console.error('创建用户失败:', error)
      toast({
        title: '创建失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
      return false
    }
  }, [loadUsers])

  // 更新用户
  const updateUser = useCallback(async (id: number, user: UserInput): Promise<boolean> => {
    try {
      const response = await userManagementApi.updateUser(id, user)
      
      if (response.data.succeeded) {
        toast({
          title: '更新成功',
          description: `用户 "${user.name}" 已更新`,
        })
        await loadUsers() // 刷新列表
        return true
      } else {
        toast({
          title: '更新失败',
          description: response.data.errors || '更新用户失败',
          variant: 'destructive',
        })
        return false
      }
    } catch (error) {
      console.error('更新用户失败:', error)
      toast({
        title: '更新失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
      return false
    }
  }, [loadUsers])

  // 删除用户
  const deleteUser = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await userManagementApi.deleteUser(id)
      
      if (response.data.succeeded) {
        toast({
          title: '删除成功',
          description: '用户已删除',
        })
        await loadUsers() // 刷新列表
        return true
      } else {
        toast({
          title: '删除失败',
          description: response.data.errors || '删除用户失败',
          variant: 'destructive',
        })
        return false
      }
    } catch (error) {
      console.error('删除用户失败:', error)
      toast({
        title: '删除失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
      return false
    }
  }, [loadUsers])

  // 切换用户状态
  const toggleUserStatus = useCallback(async (id: number, status: boolean): Promise<boolean> => {
    try {
      const response = await userManagementApi.toggleUserStatus(id, status)
      
      if (response.data.succeeded) {
        toast({
          title: status ? '用户已启用' : '用户已禁用',
          description: '用户状态已更新',
        })
        await loadUsers() // 刷新列表
        return true
      } else {
        toast({
          title: '状态更新失败',
          description: response.data.errors || '切换用户状态失败',
          variant: 'destructive',
        })
        return false
      }
    } catch (error) {
      console.error('切换用户状态失败:', error)
      toast({
        title: '状态更新失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
      return false
    }
  }, [loadUsers])

  // 重置用户密码
  const resetUserPassword = useCallback(async (id: number, newPassword: string): Promise<boolean> => {
    try {
      const response = await userManagementApi.resetUserPassword(id, newPassword)
      
      if (response.data.succeeded) {
        toast({
          title: '密码重置成功',
          description: '用户密码已重置',
        })
        return true
      } else {
        toast({
          title: '密码重置失败',
          description: response.data.errors || '重置用户密码失败',
          variant: 'destructive',
        })
        return false
      }
    } catch (error) {
      console.error('重置用户密码失败:', error)
      toast({
        title: '密码重置失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
      return false
    }
  }, [])

  // 设置页码
  const setPage = useCallback((newPage: number) => {
    setPageState(newPage)
  }, [])

  // 设置页大小
  const setPageSize = useCallback((newPageSize: number) => {
    setPageSizeState(newPageSize)
    setPageState(1) // 重置到第一页
  }, [])

  // 搜索用户
  const searchUsers = useCallback(async (searchTerm: string) => {
    const searchParams: UserQueryParams = {
      page: 1,
      pageSize,
    }

    if (searchTerm.trim()) {
      // 如果搜索词包含@符号，按账号搜索，否则按姓名搜索
      if (searchTerm.includes('@') || /^[a-zA-Z0-9_]+$/.test(searchTerm)) {
        searchParams.account = searchTerm
      } else {
        searchParams.name = searchTerm
      }
    }

    setPageState(1)
    await loadUsers(searchParams)
  }, [pageSize, loadUsers])

  // 刷新数据
  const refresh = useCallback(async () => {
    await loadUsers(currentParams)
  }, [loadUsers, currentParams])

  // 初始加载
  useEffect(() => {
    loadUsers()
  }, [page, pageSize])

  return {
    // 数据状态
    users,
    total,
    page,
    pageSize,
    
    // 加载状态
    loading: userManagementApi.isUserListLoading(),
    createLoading: userManagementApi.isUserCreateLoading(),
    updateLoading: userManagementApi.isUserUpdateLoading(),
    deleteLoading: userManagementApi.isUserDeleteLoading(),
    
    // 操作方法
    loadUsers,
    createUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    resetUserPassword,
    
    // 分页方法
    setPage,
    setPageSize,
    
    // 搜索方法
    searchUsers,
    
    // 刷新方法
    refresh,
  }
}
