#!/usr/bin/env node

/**
 * 迁移执行脚本
 * 用于开始实际的页面迁移工作
 */

const fs = require('fs')
const path = require('path')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log (message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 检查文件是否存在
function fileExists (filePath) {
  return fs.existsSync(filePath)
}

// 读取文件内容
function readFile (filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8')
  } catch (error) {
    log(`读取文件失败 ${filePath}: ${error.message}`, 'red')
    return null
  }
}

// 写入文件
function writeFile (filePath, content) {
  try {
    const dir = path.dirname(filePath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
    fs.writeFileSync(filePath, content, 'utf8')
    return true
  } catch (error) {
    log(`写入文件失败 ${filePath}: ${error.message}`, 'red')
    return false
  }
}

// 转换Next.js页面内容为React组件
function transformPageContent (content, pageName) {
  let transformed = content

  // 移除  指令
  transformed = transformed.replace(/["']use client["']\s*\n?/g, '')

  // 替换 Next.js 导入
  transformed = transformed.replace(/import\s+Link\s+from\s+['"]next\/link['"]/g,
    'import { Link } from "react-router-dom"')

  transformed = transformed.replace(/import\s*{\s*([^}]+)\s*}\s*from\s+['"]next\/navigation['"]/g,
    (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim())
      const reactRouterImports = []

      if (importList.includes('useRouter')) {
        reactRouterImports.push('useNavigate')
      }
      if (importList.includes('usePathname')) {
        reactRouterImports.push('useLocation')
      }
      if (importList.includes('useSearchParams')) {
        reactRouterImports.push('useLocation')
      }
      if (importList.includes('useParams')) {
        reactRouterImports.push('useParams')
      }

      return reactRouterImports.length > 0
        ? `import { ${reactRouterImports.join(', ')} } from "react-router-dom"`
        : ''
    })

  transformed = transformed.replace(/import\s+Image\s+from\s+['"]next\/image['"]/g,
    '// TODO: Replace with regular img tag or custom Image component')

  // 替换 Next.js 钩子使用
  transformed = transformed.replace(/const\s+router\s*=\s*useRouter\(\)/g,
    'const navigate = useNavigate()')

  transformed = transformed.replace(/const\s+pathname\s*=\s*usePathname\(\)/g,
    'const location = useLocation()\n  const pathname = location.pathname')

  transformed = transformed.replace(/const\s+searchParams\s*=\s*useSearchParams\(\)/g,
    'const location = useLocation()\n  const searchParams = new URLSearchParams(location.search)')

  // 替换路由方法调用
  transformed = transformed.replace(/router\.push\(/g, 'navigate(')
  transformed = transformed.replace(/router\.replace\(/g, 'navigate(')
  transformed = transformed.replace(/router\.back\(\)/g, 'navigate(-1)')

  // 替换 Link 组件属性
  transformed = transformed.replace(/<Link\s+href=/g, '<Link to=')

  // 替换 Image 组件 (简单替换为 img)
  transformed = transformed.replace(/<Image\s+([^>]*)\s*\/>/g, (match, attrs) => {
    // 简单的属性转换
    const imgAttrs = attrs
      .replace(/src=/g, 'src=')
      .replace(/alt=/g, 'alt=')
      .replace(/width=\{?\d+\}?/g, '')
      .replace(/height=\{?\d+\}?/g, '')
      .replace(/className=/g, 'className=')
    return `<img ${imgAttrs} />`
  })

  // 添加迁移注释
  const migrationComment = `/**
 * 迁移自: app/${pageName}
 * 迁移时间: ${new Date().toISOString()}
 * 
 * 迁移说明:
 * - 已移除  指令
 * - 已替换 Next.js 路由为 React Router
 * - 已替换 Next.js 组件为标准 React 组件
 */

`

  transformed = migrationComment + transformed

  return transformed
}

// 迁移单个页面
function migratePage (pageName, pageConfig) {
  log(`\n🔄 开始迁移页面: ${pageName}`, 'cyan')

  const sourcePath = pageConfig.sourcePath
  const targetPath = pageConfig.targetPath

  // 检查源文件是否存在
  if (!fileExists(sourcePath)) {
    log(`❌ 源文件不存在: ${sourcePath}`, 'red')
    return false
  }

  // 读取源文件内容
  const sourceContent = readFile(sourcePath)
  if (!sourceContent) {
    return false
  }

  log(`📖 读取源文件: ${sourcePath}`, 'blue')

  // 转换内容
  const transformedContent = transformPageContent(sourceContent, pageConfig.sourcePath)

  // 写入目标文件
  if (writeFile(targetPath, transformedContent)) {
    log(`✅ 已创建目标文件: ${targetPath}`, 'green')
  } else {
    return false
  }

  // 更新路由配置 (如果需要)
  // TODO: 实现路由配置更新

  log(`✅ 页面 ${pageName} 迁移完成`, 'green')
  return true
}

// 批量迁移页面
function migratePages (priority = 'high') {
  log(`\n🚀 开始批量迁移 ${priority} 优先级页面`, 'bright')

  // 读取状态配置
  const statusFile = './migration-status.json'
  if (!fileExists(statusFile)) {
    log('❌ 找不到迁移状态文件', 'red')
    return
  }

  const status = JSON.parse(readFile(statusFile))
  const pages = status.pages.items

  // 筛选指定优先级的待处理页面
  const targetPages = Object.entries(pages).filter(([_, page]) =>
    page.priority === priority && page.status === 'pending'
  )

  if (targetPages.length === 0) {
    log(`没有找到 ${priority} 优先级的待处理页面`, 'yellow')
    return
  }

  log(`找到 ${targetPages.length} 个 ${priority} 优先级页面:`, 'cyan')
  targetPages.forEach(([name, page]) => {
    log(`  - ${name} (${page.route})`, 'blue')
  })

  // 确认是否继续
  log(`\n是否继续迁移这些页面? (y/N)`, 'yellow')

  // 在实际使用中，这里应该等待用户输入
  // 为了演示，我们直接继续
  const shouldContinue = true

  if (!shouldContinue) {
    log('迁移已取消', 'yellow')
    return
  }

  // 执行迁移
  let successCount = 0
  let failCount = 0

  for (const [pageName, pageConfig] of targetPages) {
    try {
      if (migratePage(pageName, pageConfig)) {
        successCount++

        // 更新状态为进行中
        // TODO: 调用进度更新脚本
        log(`📝 更新页面状态: ${pageName} -> inProgress`, 'blue')
      } else {
        failCount++
      }
    } catch (error) {
      log(`❌ 迁移页面 ${pageName} 时出错: ${error.message}`, 'red')
      failCount++
    }
  }

  // 输出结果
  log(`\n📊 迁移结果:`, 'bright')
  log(`  ✅ 成功: ${successCount}`, 'green')
  log(`  ❌ 失败: ${failCount}`, 'red')
  log(`  📈 成功率: ${Math.round(successCount / (successCount + failCount) * 100)}%`, 'cyan')

  if (successCount > 0) {
    log(`\n🎯 下一步建议:`, 'bright')
    log(`  1. 运行 npm run dev 测试迁移的页面`, 'cyan')
    log(`  2. 检查页面功能是否正常`, 'cyan')
    log(`  3. 使用 node update-progress.js update <页面名> completed 标记完成`, 'cyan')
    log(`  4. 继续迁移下一批页面`, 'cyan')
  }
}

// 显示帮助信息
function showHelp () {
  log('\n🔧 迁移执行脚本使用说明', 'bright')
  log('================================', 'bright')
  log('')
  log('命令:', 'cyan')
  log('  node start-migration.js migrate [priority]  - 批量迁移指定优先级的页面', 'blue')
  log('  node start-migration.js page <name>         - 迁移单个页面', 'blue')
  log('  node start-migration.js list [priority]     - 列出待迁移页面', 'blue')
  log('  node start-migration.js help                - 显示帮助信息', 'blue')
  log('')
  log('优先级:', 'cyan')
  log('  high    - 高优先级 (dashboard, login, examples 等)', 'yellow')
  log('  medium  - 中优先级 (monitoring, analytics 等)', 'yellow')
  log('  low     - 低优先级 (settings, help 等)', 'yellow')
  log('')
  log('示例:', 'cyan')
  log('  node start-migration.js migrate high       - 迁移所有高优先级页面', 'green')
  log('  node start-migration.js page dashboard     - 只迁移dashboard页面', 'green')
  log('  node start-migration.js list medium        - 列出中优先级页面', 'green')
}

// 列出待迁移页面
function listPages (priority = null) {
  const statusFile = './migration-status.json'
  if (!fileExists(statusFile)) {
    log('❌ 找不到迁移状态文件', 'red')
    return
  }

  const status = JSON.parse(readFile(statusFile))
  const pages = status.pages.items

  const filteredPages = Object.entries(pages).filter(([_, page]) =>
    (!priority || page.priority === priority) && page.status === 'pending'
  )

  if (filteredPages.length === 0) {
    log(`没有找到${priority ? ` ${priority} 优先级的` : ''}待迁移页面`, 'yellow')
    return
  }

  log(`\n📋 ${priority ? `${priority} 优先级` : '所有'}待迁移页面 (${filteredPages.length} 个):`, 'bright')

  filteredPages.forEach(([name, page]) => {
    const priorityIcon = page.priority === 'high' ? '🔴' :
      page.priority === 'medium' ? '🟡' : '🟢'
    log(`  ${priorityIcon} ${name}`, 'cyan')
    log(`     路由: ${page.route}`, 'blue')
    log(`     源文件: ${page.sourcePath}`, 'blue')
    log(`     目标: ${page.targetPath}`, 'blue')
    log(`     工作量: ${page.estimatedEffort}`, 'blue')
    if (page.dependencies && page.dependencies.length > 0) {
      log(`     依赖: ${page.dependencies.join(', ')}`, 'yellow')
    }
    log('')
  })
}

// 命令行处理
const command = process.argv[2]
const arg1 = process.argv[3]

switch (command) {
  case 'migrate':
    migratePages(arg1 || 'high')
    break

  case 'page':
    if (!arg1) {
      log('请指定页面名称', 'red')
      break
    }
    // TODO: 实现单页面迁移
    log(`迁移单个页面: ${arg1}`, 'cyan')
    break

  case 'list':
    listPages(arg1)
    break

  case 'help':
    showHelp()
    break

  default:
    showHelp()
    break
}
