<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <SatelliteResourceLanguages>en-US</SatelliteResourceLanguages>
        <PublishReadyToRunComposite>true</PublishReadyToRunComposite>
        <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
        <UserSecretsId>ad9369d1-f29b-4f8f-a7df-8b4d7aa0726b</UserSecretsId>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <GenerateSatelliteAssembliesForCore>true</GenerateSatelliteAssembliesForCore>
        <Version>5.2.1</Version>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="logs\**"/>
        <Compile Remove="publish\**"/>
        <Content Remove="logs\**"/>
        <Content Remove="publish\**"/>
        <EmbeddedResource Remove="logs\**"/>
        <EmbeddedResource Remove="publish\**"/>
        <None Remove="logs\**"/>
        <None Remove="publish\**"/>
        <None Update="ScriptJs\db.js">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="ScriptJs\http.js">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Templates\复合物实例导入模版.xlsx">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Templates\物模型导入模板.xlsx">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Templates\网关实例导入模版.xlsx">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Templates\设备实例导入模版.xlsx">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Dockerfile">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="wait-for-it.sh">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Templates\DeviceVariableImport.xlsx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Templates\DeviceVariableSimpleImport.xlsx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Templates\EdgeComputingPolicyImport.xlsx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Templates\EdgeComputingScriptImport.xlsx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="RSA.Private">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Content Update="wwwroot\Avatar\**" CopyToPublishDirectory="Never"/>
        <Content Update="wwwroot\logs\**" CopyToPublishDirectory="Never"/>
        <Content Update="wwwroot\upload\**" CopyToPublishDirectory="Never"/>
    </ItemGroup>

    <ItemGroup>
        <None Remove="sensitive-words.txt"/>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="sensitive-words.txt">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\IotPlatform.Web.Core\IotPlatform.Web.Core.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="wwwroot\"/>
    </ItemGroup>

    <ItemGroup>
        <_ContentIncludedByDefault Remove="wwwroot\Upload\Avatar\32606239252549.jpg"/>
    </ItemGroup>

</Project>
