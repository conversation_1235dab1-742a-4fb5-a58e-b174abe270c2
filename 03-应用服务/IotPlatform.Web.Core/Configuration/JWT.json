{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",
  "JWTSettings": {
    "ValidateIssuerSigningKey": true,
    // 是否验证密钥，bool 类型，默认true
    "IssuerSigningKey": "0a3852e18a1e9b06a9c4f2d3b5f9d7e8b2e0c8d4f6a2d3b5f9d7e8b2e0c8d4f",
    // 密钥，string 类型，必须是复杂密钥，长度大于16
    "ValidateIssuer": true,
    // 是否验证签发方，bool 类型，默认true
    "ValidIssuer": "HangZhou.FengHui",
    // 签发方，string 类型
    "ValidateAudience": true,
    // 是否验证签收方，bool 类型，默认true
    "ValidAudience": "HangZhou.FengHui",
    // 签收方，string 类型
    "ValidateLifetime": true,
    // 是否验证过期时间，bool 类型，默认true，建议true
    "ClockSkew": 5,
    // 过期时间容错值，long 类型，单位秒，默认5秒
    "Algorithm": "HS256"
    // 加密算法，string 类型，默认 HS256
  }
}