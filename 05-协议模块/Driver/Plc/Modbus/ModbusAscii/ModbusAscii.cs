using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Modbus;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.Core;
using HslCommunication.LogNet;

namespace ModbusAscii;

[DriverSupported("ModbusAscii")]
[DriverInfo("ModbusAscii", "V1.0.0", "Modbus驱动")]
public class ModbusAscii : ModBusSerialBase, IDriver
{
    public ModbusAscii(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                Driver = new HslCommunication.ModBus.ModbusAscii(Station);
                Driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                if (ByteTransform == BoolEnum.True)
                    Driver.ByteTransform = new ByteTransformBase();
                if (StringReverse == BoolEnum.True)
                    Driver.IsStringReverse = true;
                Driver.DataFormat = DataFormat;
                Driver.LogNet = new LogNetSingle("");
                var tcpNet = (HslCommunication.ModBus.ModbusAscii) Driver;
                tcpNet.LogNet.BeforeSaveToFile += async (object sender, HslEventArgs e) => { await SocketSend(e); };
            }

            OperateResult = Driver.Open();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
}