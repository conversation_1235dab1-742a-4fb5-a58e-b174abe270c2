using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Modbus;
using DriversInterface;
using HslCommunication.Core;
using HslCommunication.LogNet;

namespace ModbusUdpNet;

[DriverSupported("ModbusUdpNet")]
[DriverInfo("ModbusUdpNet", "V1.0.0", "Modbus驱动")]
public class ModbusUdpNet : ModBusNetworkBase, IDriver
{
    public ModbusUdpNet(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                Driver = new HslCommunication.ModBus.ModbusUdpNet(IpAddress, Port, Station)
                {
                    ReceiveTimeout = Timeout
                };
                if (ByteTransform == BoolEnum.True)
                    Driver.ByteTransform = new ByteTransformBase();
                Driver.DataFormat = DataFormat;
                Driver.LogNet = new LogNetSingle("");
                var tcpNet = (HslCommunication.ModBus.ModbusUdpNet) Driver;
                tcpNet.LogNet.BeforeSaveToFile += async (object sender, HslEventArgs e) => { await SocketSend(e); };
            }

            OperateResult = Driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
}