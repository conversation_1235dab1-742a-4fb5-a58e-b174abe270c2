using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Melsec;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Melsec;

namespace McA1E;

[DriverSupported("McA1E")]
[DriverInfo("McA1E", "V1.1.0", "三菱(Melsec)")]
public class McA1E : MelsecNetworkDeviceBase, IDriver
{
    public McA1E(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    //private MelsecA1ENet Driver;
    public override bool IsConnected => Driver != null && (Driver.GetPipeSocket().Socket?.Connected ?? false);

    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                Driver = new MelsecA1ENet(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                Driver.LogNet = new LogNetSingle("");
                var tcpNet = (MelsecA1ENet) Driver;
                tcpNet.LogNet.BeforeSaveToFile += async (_, e) => { await SocketSend(e); };
            }

            OperateResult = Driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
}