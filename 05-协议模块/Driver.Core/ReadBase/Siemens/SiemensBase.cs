using Common.Enums;
using Common.Extension;
using HslCommunication.Profinet.Siemens;
using System.Text;

namespace Driver.Core.ReadBase.Siemens;

/// <summary>
///     西门子协议
/// </summary>
public class SiemensBase : BasePlcProtocolCollector
{
    // private SiemensS7Net Driver;

    /// <summary>
    ///     批量读取失败的地址
    /// </summary>
    private List<DriverAddressIoArgModel> paramList = new();

    /// <summary>
    /// 单次查询最大字节数，0表示不限制
    /// </summary>
    [ConfigParameter("最大查询长度", GroupName = "高级配置", Remark = "单次查询最大字节数，0表示不限制")]
    public int MaxQueryLength { get; set; } = 128;

    /// <summary>
    /// 地址间隔超过此值时分为不同组
    /// </summary>
    [ConfigParameter("报文间隔", GroupName = "高级配置", Remark = "地址间隔超过此值时分为不同组")]
    public int PacketInterval { get; set; } = 20;

    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", description: "用于解析Siemens的标准地址", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        if (registerAddress.IsNull()) return "Read";

        if (registerAddress.StartsWith("AI"))
            return "AI";
        if (registerAddress.StartsWith("AQ"))
            return "AQ";
        if (registerAddress.StartsWith("T"))
            return "T";
        if (registerAddress.StartsWith("C"))
            return "C";
        if (registerAddress.StartsWith("I"))
            return "I";
        if (registerAddress.StartsWith("Q"))
            return "Q";
        if (registerAddress.StartsWith("M"))
            return "M";
        if (registerAddress.StartsWith("DB") || registerAddress.StartsWith("D"))
            return "DB";
        if (registerAddress.StartsWith("V"))
            return "V";

        return "Read";
    }

    #region Methods

    /// <summary>
    ///     I0:输入寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("I0", description: "位地址示例:I1.6", name: "I:输入寄存器")]
    public async Task<DriverReturnValueModel> I(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Q0:输出寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Q0", description: "位地址示例:Q1.6", name: "Q:输出寄存器")]
    public async Task<DriverReturnValueModel> Q(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     M0:内部寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M0", description: "位地址示例:M1.6", name: "M:内部寄存器")]
    public async Task<DriverReturnValueModel> M(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     DB1.0:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("DB1.0", description: "位地址示例:DB1.0.1", name: "DB:数据寄存器")]
    public async Task<DriverReturnValueModel> DB(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     V0:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("V0", description: "等同于DB1.0", name: "V:数据寄存器")]
    public async Task<DriverReturnValueModel> V(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T0:定时器寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T0", description: "smart200测试通过", name: "T:定时器寄存器")]
    public async Task<DriverReturnValueModel> T(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     C0:计数器寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C0", description: "smart200测试通过", name: "C:计数器寄存器")]
    public async Task<DriverReturnValueModel> C(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     AI0:智能输入寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("AI0", description: "仅支持字单位", name: "AI:智能输入寄存器")]
    public async Task<DriverReturnValueModel> AI(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }


    /// <summary>
    ///     AQ0:智能输出寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("AQ0", description: "仅支持字单位", name: "AQ:智能输出寄存器")]
    public async Task<DriverReturnValueModel> AQ(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion Methods

    #region 地址初始化

    /// <summary>
    ///     地址分组,只初始化一次
    /// </summary>
    /// <param name="groupVal"></param>
    [Method("Init", description: "地址初始化", name: "Init")]
    public bool InitGroup(List<IGrouping<string, DriverAddressIoArgModel>> groupVal)
    {
        foreach (var item in groupVal)
        {
            paramList.AddRange(item);
        }
        return true;
    }

    #endregion

    #region 新批量读取方法

    /// <summary>
    /// 解析地址，提取DB块号和偏移量
    /// </summary>
    /// <param name="address">地址字符串，如"DB2.8"</param>
    /// <returns>包含DB块号和偏移量的元组</returns>
    private (string DbBlock, int Offset, int BitIndex) ParseAddress(string address)
    {
        // 默认值
        string dbBlock = "";
        int offset = 0;
        int bitIndex = -1;

        try
        {
            // 解析地址
            if (address.StartsWith("DB"))
            {
                var parts = address.Split('.');
                if (parts.Length >= 2)
                {
                    dbBlock = parts[0]; // 如 "DB2"
                    if (int.TryParse(parts[1], out offset))
                    {
                        // 如果有位索引（如DB2.8.0）
                        if (parts.Length >= 3 && int.TryParse(parts[2], out bitIndex))
                        {
                            // 位索引已解析
                        }
                    }
                }
            }
            else if (address.StartsWith("V"))
            {
                // V地址实际上是DB1的别名
                dbBlock = "DB1";
                var parts = address.Split('.');
                if (parts.Length >= 1 && parts[0].Length > 1)
                {
                    // 提取V后面的数字作为偏移量
                    if (int.TryParse(parts[0].Substring(1), out offset))
                    {
                        // 如果有位索引
                        if (parts.Length >= 2 && int.TryParse(parts[1], out bitIndex))
                        {
                            // 位索引已解析
                        }
                    }
                }
            }
            else if (address.StartsWith("I") || address.StartsWith("Q") || address.StartsWith("M"))
            {
                // I、Q、M类型地址
                dbBlock = address.Substring(0, 1);
                var parts = address.Split('.');
                if (parts.Length >= 1 && parts[0].Length > 1)
                {
                    // 提取I/Q/M后面的数字作为偏移量
                    if (int.TryParse(parts[0].Substring(1), out offset))
                    {
                        // 如果有位索引
                        if (parts.Length >= 2 && int.TryParse(parts[1], out bitIndex))
                        {
                            // 位索引已解析
                        }
                    }
                }
            }
            else if (address.StartsWith("T") || address.StartsWith("C"))
            {
                // T、C类型地址
                dbBlock = address.Substring(0, 1);
                var parts = address.Split('.');
                if (parts.Length >= 1 && parts[0].Length > 1)
                {
                    // 提取T/C后面的数字作为偏移量
                    if (int.TryParse(parts[0].Substring(1), out offset))
                    {
                        // 如果有位索引
                        if (parts.Length >= 2 && int.TryParse(parts[1], out bitIndex))
                        {
                            // 位索引已解析
                        }
                    }
                }
            }

            // 调试输出
            // OnOutputReceived($"【地址解析】{address} => 块:{dbBlock}, 偏移:{offset}, 位索引:{bitIndex}");
        }
        catch (Exception ex)
        {
            OnOutputReceived($"【地址解析错误】{address}: {ex.Message}");
        }

        return (dbBlock, offset, bitIndex);
    }

    /// <summary>
    /// 根据地址连续性和最大查询长度对参数进行分组
    /// </summary>
    /// <param name="parameters">参数列表</param>
    /// <param name="getDataTypeLength">获取数据类型长度的函数</param>
    /// <returns>分组后的参数列表</returns>
    private List<List<DriverAddressIoArgModel>> GroupParameters(
        List<DriverAddressIoArgModel> parameters,
        Func<DriverAddressIoArgModel, ushort> getDataTypeLength)
    {
        var result = new List<List<DriverAddressIoArgModel>>();
        if (parameters.Count == 0) return result;

        // 按DB块和偏移量排序
        var sortedParams = parameters
            .Select(p => new
            {
                Param = p,
                Address = ParseAddress(p.Address),
                Length = getDataTypeLength(p),
                // 直接使用字节长度，不再转换为字长度
                ByteLength = getDataTypeLength(p)
            })
            .OrderBy(p => p.Address.DbBlock)
            .ThenBy(p => p.Address.Offset)
            .ToList();

        // 输出排序后的参数列表，便于调试
        // OnOutputReceived($"【排序参数】共 {sortedParams.Count} 个参数，按DB块和偏移量排序");
        // for (int i = 0; i < Math.Min(sortedParams.Count, 5); i++) // 只显示前5个，避免日志过多
        // {
        //     var item = sortedParams[i];
        //     OnOutputReceived($"【排序参数】#{i + 1}: {item.Param.Address} => 块:{item.Address.DbBlock}, 偏移:{item.Address.Offset}, 字节长度:{item.Length}");
        // }

        // 当前分组
        var currentGroup = new List<DriverAddressIoArgModel>();
        string currentDbBlock = sortedParams[0].Address.DbBlock;

        // 使用字节偏移量进行计算（直接使用PLC地址中的偏移量）
        int currentEndByteOffset = sortedParams[0].Address.Offset; // 当前分组的起始字节偏移量
        int groupStartByteOffset = sortedParams[0].Address.Offset; // 当前分组的起始字节偏移量
        int groupEndByteOffset = currentEndByteOffset; // 当前分组的结束字节偏移量

        // 计算第一个参数的结束偏移量
        int firstItemByteLength = sortedParams[0].Length; // 直接使用字节长度
        groupEndByteOffset = currentEndByteOffset + firstItemByteLength;

        foreach (var item in sortedParams)
        {
            // 当前项的字节偏移量
            int itemByteOffset = item.Address.Offset; // 字节偏移量

            // 当前项的字节长度
            int itemByteLength = item.Length; // 直接使用字节长度

            // 当前项的结束字节偏移量
            int itemEndByteOffset = itemByteOffset + itemByteLength;

            // 如果DB块不同，则创建新分组
            if (item.Address.DbBlock != currentDbBlock)
            {
                if (currentGroup.Count > 0)
                {
                    int groupByteLength = groupEndByteOffset - groupStartByteOffset; // 分组字节长度
                    OnOutputReceived($"【分组信息】新建分组(DB块不同): {currentDbBlock}.{groupStartByteOffset}:{groupByteLength}, 字节范围: {groupStartByteOffset}-{groupEndByteOffset}");
                    result.Add(currentGroup);
                    currentGroup = new List<DriverAddressIoArgModel>();
                }
                currentDbBlock = item.Address.DbBlock;
                groupStartByteOffset = itemByteOffset;
                groupEndByteOffset = itemEndByteOffset;
                currentGroup.Add(item.Param);
                continue;
            }

            // 如果当前项是第一个项，直接添加
            if (currentGroup.Count == 0)
            {
                currentGroup.Add(item.Param);
                groupStartByteOffset = itemByteOffset;
                groupEndByteOffset = itemEndByteOffset;
                continue;
            }

            // 如果字节偏移量间隔过大，则创建新分组
            // 判断当前项的起始偏移量与当前分组结束偏移量的间隔是否超过PacketInterval
            if (itemByteOffset - groupEndByteOffset > PacketInterval)
            {
                int groupByteLength = groupEndByteOffset - groupStartByteOffset; // 分组字节长度
                OnOutputReceived($"【分组信息】新建分组(间隔过大): {currentDbBlock}.{groupStartByteOffset}:{groupByteLength}, 字节间隔: {itemByteOffset - groupEndByteOffset}");
                result.Add(currentGroup);
                currentGroup = new List<DriverAddressIoArgModel>();
                groupStartByteOffset = itemByteOffset;
                groupEndByteOffset = itemEndByteOffset;
                currentGroup.Add(item.Param);
                continue;
            }

            // 如果当前分组长度加上新项的长度超过最大查询长度，则创建新分组
            if (MaxQueryLength > 0 && (itemEndByteOffset - groupStartByteOffset) > MaxQueryLength && currentGroup.Count > 0)
            {
                int groupByteLength = groupEndByteOffset - groupStartByteOffset; // 分组字节长度
                OnOutputReceived($"【分组信息】新建分组(长度超限): {currentDbBlock}.{groupStartByteOffset}:{groupByteLength}, 当前字节长度: {groupEndByteOffset - groupStartByteOffset}, 新增字节长度: {itemByteLength}");
                result.Add(currentGroup);
                currentGroup = new List<DriverAddressIoArgModel>();
                groupStartByteOffset = itemByteOffset;
                groupEndByteOffset = itemEndByteOffset;
                currentGroup.Add(item.Param);
                continue;
            }

            // 如果以上条件都不满足，则将当前项添加到当前分组
            currentGroup.Add(item.Param);

            // 更新当前分组的结束字节偏移量
            if (itemEndByteOffset > groupEndByteOffset)
            {
                groupEndByteOffset = itemEndByteOffset;
            }
        }

        // 添加最后一个分组
        if (currentGroup.Count > 0)
        {
            int groupByteLength = groupEndByteOffset - groupStartByteOffset; // 分组字节长度
            OnOutputReceived($"【分组信息】添加最后分组: {currentDbBlock}.{groupStartByteOffset}:{groupByteLength}, 字节范围: {groupStartByteOffset}-{groupEndByteOffset}");
            result.Add(currentGroup);
        }

        // 输出分组信息
        OnOutputReceived($"【分组结果】共分为 {result.Count} 组");
        for (int i = 0; i < result.Count; i++)
        {
            var group = result[i];
            if (group.Count == 0) continue;

            var firstAddr = ParseAddress(group[0].Address);
            var lastAddr = ParseAddress(group[group.Count - 1].Address);

            // 计算分组的起始和结束字节偏移量
            int firstByteOffset = firstAddr.Offset;
            int lastItemByteLength = getDataTypeLength(group[group.Count - 1]); // 直接使用字节长度
            int lastByteOffset = lastAddr.Offset;
            int lastByteEndOffset = lastByteOffset + lastItemByteLength;
            int totalByteLength = lastByteEndOffset - firstByteOffset;

            OnOutputReceived($"【分组详情】组 #{i + 1}: {firstAddr.DbBlock}.{firstAddr.Offset}:{totalByteLength} 包含 {group.Count} 个地址, 字节范围: {firstByteOffset}-{lastByteEndOffset}");

            // 输出组内的前几个地址，便于调试
            int maxDisplayItems = Math.Min(group.Count, 3); // 只显示前3个，避免日志过多
            for (int j = 0; j < maxDisplayItems; j++)
            {
                var param = group[j];
                var addr = ParseAddress(param.Address);
                int byteOffset = addr.Offset;
                int byteLength = getDataTypeLength(param); // 直接使用字节长度
                OnOutputReceived($"【分组详情】组 #{i + 1} 地址 #{j + 1}: {param.Address}, 类型: {param.DataType}, 字节偏移: {byteOffset}, 字节长度: {byteLength}");
            }

            // 如果有更多地址，显示省略信息
            if (group.Count > maxDisplayItems)
            {
                OnOutputReceived($"【分组详情】组 #{i + 1} 还有 {group.Count - maxDisplayItems} 个地址未显示");
            }
        }

        return result;
    }

    /// <summary>
    /// 获取数据类型的字节长度
    /// </summary>
    /// <param name="param">参数</param>
    /// <returns>字节长度</returns>
    private ushort GetDataTypeLength(DriverAddressIoArgModel param)
    {
        return param.DataType switch
        {
            DataTypeEnum.Bool or DataTypeEnum.Bit => 1,
            DataTypeEnum.Int32 or DataTypeEnum.Uint32 or DataTypeEnum.Float => 4,
            DataTypeEnum.Double or DataTypeEnum.Int64 or DataTypeEnum.Uint64 => 8,
            DataTypeEnum.Int16 or DataTypeEnum.Uint16 => 2,
            DataTypeEnum.String => (ushort)(param.Length == 0 ? 10 : param.Length),
            _ => 2
        };
    }

    /// <summary>
    /// 解析数据值
    /// </summary>
    /// <param name="param">参数</param>
    /// <param name="content">读取的内容</param>
    /// <param name="offset">偏移量</param>
    /// <param name="length">长度</param>
    /// <returns>解析后的值</returns>
    private object ParseValue(DriverAddressIoArgModel param, byte[] content, int offset, int length)
    {
        // 根据数据类型解析值
        if (param.DataType == DataTypeEnum.Bool || param.DataType == DataTypeEnum.Bit)
        {
            // 布尔类型特殊处理
            if (param.Address.Contains("."))
            {
                string[] addressParts = param.Address.Split('.');
                if (addressParts.Length >= 3)
                {
                    // 获取位索引
                    int bitIndex = int.Parse(addressParts[addressParts.Length - 1]);
                    byte byteValue = content[offset];
                    bool value = ((byteValue >> bitIndex) & 0x01) == 0x01;
                    return param.DataType == DataTypeEnum.Bit ? (value ? 1 : 0) : value;
                }
                else
                {
                    byte byteValue = content[offset];
                    bool value = (byteValue & 0x01) == 0x01;
                    return param.DataType == DataTypeEnum.Bit ? (value ? 1 : 0) : value;
                }
            }
            else
            {
                bool value = Driver.ByteTransform.TransBool(content, offset);
                return param.DataType == DataTypeEnum.Bit ? (value ? 1 : 0) : value;
            }
        }
        else
        {
            // 其他类型
            return param.DataType switch
            {
                DataTypeEnum.Int32 => Driver.ByteTransform.TransInt32(content, offset),
                DataTypeEnum.Uint32 => Driver.ByteTransform.TransUInt32(content, offset),
                DataTypeEnum.Float => Driver.ByteTransform.TransSingle(content, offset),
                DataTypeEnum.Double => Driver.ByteTransform.TransDouble(content, offset),
                DataTypeEnum.Int64 => Driver.ByteTransform.TransInt64(content, offset),
                DataTypeEnum.Uint64 => Driver.ByteTransform.TransUInt64(content, offset),
                DataTypeEnum.Int16 => Driver.ByteTransform.TransInt16(content, offset),
                DataTypeEnum.Uint16 => Driver.ByteTransform.TransUInt16(content, offset),
                DataTypeEnum.String => Encoding.ASCII.GetString(content, offset, length),
                _ => Driver.ByteTransform.TransInt16(content, offset)
            };
        }
    }

    // public SiemensS7Net Driver;

    /// <summary>
    /// 读取参数，统一处理所有类型
    /// </summary>
    /// <param name="parameters">参数列表</param>
    /// <returns>读取结果列表</returns>
    private async Task<List<DriverReturnValueModel>> ReadParameters(List<DriverAddressIoArgModel> parameters)
    {
        var output = new List<DriverReturnValueModel>();
        if (parameters.Count == 0) return output;

        // 对参数进行分组
        var groups = GroupParameters(parameters, GetDataTypeLength);
        OnOutputReceived($"【批量读取】参数共分为 {groups.Count} 组");

        // 处理每个分组
        for (int groupIndex = 0; groupIndex < groups.Count; groupIndex++)
        {
            var group = groups[groupIndex];
            if (group.Count == 0) continue;

            try
            {
                // 获取分组的第一个地址作为起始地址
                var firstParam = group[0];
                var firstAddr = ParseAddress(firstParam.Address);

                // 获取分组的最后一个地址和其数据长度
                var lastParam = group[group.Count - 1];
                var lastAddr = ParseAddress(lastParam.Address);
                var lastLength = GetDataTypeLength(lastParam);

                // 计算总的字节长度
                int totalLength = (lastAddr.Offset - firstAddr.Offset) + lastLength;

                // 构建起始地址 - 对于bool类型地址，需要从字节边界开始
                string startAddress;
                if (firstParam.DataType == DataTypeEnum.Bool || firstParam.DataType == DataTypeEnum.Bit)
                {
                    // bool类型地址需要从字节边界开始读取
                    if (firstAddr.BitIndex >= 0)
                    {
                        // 有位索引的地址，去掉位索引部分，从字节边界开始
                        startAddress = $"{firstAddr.DbBlock}.{firstAddr.Offset}";
                    }
                    else
                    {
                        // 没有位索引的地址，直接使用
                        startAddress = firstParam.Address;
                    }
                }
                else
                {
                    // 非bool类型地址，直接使用原地址
                    startAddress = firstParam.Address;
                }

                OnOutputReceived($"【批量读取】处理分组 #{groupIndex + 1}，起始地址: {startAddress}，总长度: {totalLength}字节，包含 {group.Count} 个地址");

                // 添加调试信息，显示原始地址和调整后的地址
                if (firstParam.DataType == DataTypeEnum.Bool || firstParam.DataType == DataTypeEnum.Bit)
                {
                    OnOutputReceived($"【地址调整】原始地址: {firstParam.Address} -> 调整后起始地址: {startAddress} (bool类型地址已调整到字节边界)");
                }

                // 执行单次读取
                var readStopwatch = System.Diagnostics.Stopwatch.StartNew();
                var read = await Driver.ReadAsync(startAddress, (ushort)totalLength);
                readStopwatch.Stop();

                OnOutputReceived($"【性能统计】分组 #{groupIndex + 1} 读取耗时: {readStopwatch.ElapsedMilliseconds}ms, 成功状态: {read.IsSuccess}");

                if (!read.IsSuccess)
                {
                    // 读取失败，所有地址均标记为失败
                    OnOutputReceived($"【读取失败】分组 #{groupIndex + 1} 失败原因: {read.Message}");
                    output.AddRange(group.Select(param => new DriverReturnValueModel
                    {
                        Id = param.Id,
                        VariableStatus = VariableStatusTypeEnum.Error,
                        Message = read.Message,
                        DataType = param.DataType
                    }));
                }
                else
                {
                    // 读取成功，解析数据
                    OnOutputReceived($"【读取成功】分组 #{groupIndex + 1} 读取到 {read.Content?.Length ?? 0} 字节数据");

                    // 处理每个参数
                    foreach (var param in group)
                    {
                        try
                        {
                            // 计算该参数相对于起始地址的偏移量
                            var addr = ParseAddress(param.Address);
                            int relativeOffset = addr.Offset - firstAddr.Offset;

                            // 获取参数的字节长度
                            var length = GetDataTypeLength(param);

                            // 确保偏移量不超出读取的数据范围
                            if (relativeOffset < 0 || relativeOffset + length > read.Content.Length)
                            {
                                OnOutputReceived($"【解析数据】偏移量超出范围: 地址={param.Address}, 相对偏移={relativeOffset}, 长度={length}, 数据长度={read.Content.Length}");
                                output.Add(new DriverReturnValueModel
                                {
                                    Id = param.Id,
                                    VariableStatus = VariableStatusTypeEnum.MethodError,
                                    Message = "偏移量超出范围",
                                    DataType = param.DataType,
                                    Value = null
                                });
                                continue;
                            }

                            // 解析数据
                            object value = ParseValue(param, read.Content, relativeOffset, length);

                            output.Add(new DriverReturnValueModel
                            {
                                Id = param.Id,
                                VariableStatus = VariableStatusTypeEnum.Good,
                                Message = read.Message,
                                DataType = param.DataType,
                                Value = value
                            });
                        }
                        catch (Exception ex)
                        {
                            OnOutputReceived($"【解析数据】解析异常: 地址={param.Address}, 错误={ex.Message}");
                            output.Add(new DriverReturnValueModel
                            {
                                Id = param.Id,
                                VariableStatus = VariableStatusTypeEnum.MethodError,
                                Message = $"解析异常: {ex.Message}",
                                DataType = param.DataType,
                                Value = null
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 批量读取过程中发生异常
                OnOutputReceived($"【批量读取异常】分组 #{groupIndex + 1} 异常: {ex.Message}");
                output.AddRange(group.Select(param => new DriverReturnValueModel
                {
                    Id = param.Id,
                    VariableStatus = VariableStatusTypeEnum.Error,
                    Message = $"读取异常: {ex.Message}",
                    DataType = param.DataType
                }));
            }
        }

        return output;
    }

    /// <summary>
    /// 新的批量读取方法，根据地址连续性和最大查询长度进行分组
    /// </summary>
    /// <returns>读取结果列表</returns>
    [Method("Batch", description: "批量读取", name: "BatchRead")]
    public async Task<List<DriverReturnValueModel>> BatchRead()
    {
        // 结果输出列表
        var output = new List<DriverReturnValueModel>();
        if (paramList == null || paramList.Count == 0)
        {
            OnOutputReceived("【批量读取】参数列表为空，无需读取");
            return output;
        }

        OnOutputReceived($"【批量读取】开始批量读取，共 {paramList.Count} 个地址，最大查询长度: {MaxQueryLength}，报文间隔: {PacketInterval}");

        try
        {
            // 统一处理所有参数
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var results = await ReadParameters(paramList);
            stopwatch.Stop();

            // 统计成功和失败的数量
            int successCount = results.Count(r => r.VariableStatus == VariableStatusTypeEnum.Good);
            int errorCount = results.Count - successCount;

            OnOutputReceived($"【性能统计】ReadParameters 总耗时: {stopwatch.ElapsedMilliseconds}ms, 成功: {successCount}, 失败: {errorCount}");
            output.AddRange(results);
        }
        catch (Exception ex)
        {
            OnOutputReceived($"【批量读取异常】总体异常: {ex.Message}");
            // 所有参数都标记为失败
            output.AddRange(paramList.Select(param => new DriverReturnValueModel
            {
                Id = param.Id,
                VariableStatus = VariableStatusTypeEnum.Error,
                Message = $"批量读取异常: {ex.Message}",
                DataType = param.DataType
            }));
        }

        return output;
    }

    /// <summary>
    /// 输出调试信息的方法
    /// </summary>
    /// <param name="message">调试信息</param>
    protected virtual void OnOutputReceived(string message)
    {
        // 使用现有的设备控制台输出方法
        _ = DriverInfo.Socket.DeviceConsole($"【Siemens】 {message}", DriverInfo.DeviceId);
    }

    #endregion 新批量读取方法
}

/// <summary>
///     西门子网口协议基类
/// </summary>
public class SiemensNetworkDeviceBase : SiemensBase
{
    #region 配置参数

    //
    // [ConfigParameter("批量读取", GroupName = "高级配置")]
    // public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("PLC类型")] public SiemensPLCS TypeRelationName { get; set; } = SiemensPLCS.S200Smart;
    [ConfigParameter("端口号")] public int Port { get; set; } = 102;

    #region S200

    [ConfigParameter("LocalTSAP", Display = false, DisplayExpress = "{\"TypeRelationName\": [\"6\"]}")]
    public string LocalTsap { get; set; } = "4D57";

    [ConfigParameter("DestTSAP", Display = false, DisplayExpress = "{\"TypeRelationName\": [\"6\"]}")]
    public string DestTsap { get; set; } = "4D57";

    #endregion S200

    #endregion
}

/// <summary>
///     西门子串口协议基类
/// </summary>
public class SiemensSerialDeviceBase : SiemensBase
{
    public override void Close()
    {
        Driver?.Close();
    }

    public override void Dispose()
    {
        Driver?.Dispose();
    }

    #region 配置参数

    // [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    // public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("串口号", Remark = "")]
    public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS1;

    [ConfigParameter("波特率")] public BaudRateEnum BaudRate { get; set; } = BaudRateEnum.Bps9600;
    [ConfigParameter("数据位")] public int DataBits { get; set; } = 8;

    [ConfigParameter("停止位")] public StopBits Stop { get; set; } = StopBits.Two;
    [ConfigParameter("校验位")] public Parity Checkout { get; set; } = Parity.Even;
    [ConfigParameter("站号")] public byte Station { get; set; } = 2;

    #endregion
}