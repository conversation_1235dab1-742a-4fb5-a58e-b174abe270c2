using System;
using System.Runtime.InteropServices;
using System.Text;
using System.Diagnostics;
using System.IO;

// ===== 数据结构定义 =====

//RSA公钥格式(兼容1024,2048)
[StructLayout(LayoutKind.Sequential)]
public struct RSA_PUBLIC_KEY
{
    public uint bits;                   // length in bits of modulus
    public uint modulus;				  // modulus
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
    public byte[] exponent;       // public exponent
}

//RSA私钥格式(兼容1024,2048)
[StructLayout(LayoutKind.Sequential)]
public struct RSA_PRIVATE_KEY
{
    public uint bits;                   // length in bits of modulus
    public uint modulus;				  // modulus
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
    public byte[] publicExponent;       // public exponent
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
    public byte[] exponent;       // public exponent
}

//外部ECCSM2公钥格式 ECC(支持bits为192或256)和SM2的(bits为固定值0x8100)公钥格式
[StructLayout(LayoutKind.Sequential)]
public struct ECCSM2_PUBLIC_KEY
{
    public uint bits;                   // length in bits of modulus
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public uint[] XCoordinate;       // 曲线上点的X坐标
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public uint[] YCoordinate;       // 曲线上点的Y坐标
}

//外部ECCSM2私钥格式 ECC(支持bits为192或256)和SM2的(bits为固定值0x8100)私钥格式
[StructLayout(LayoutKind.Sequential)]
public struct ECCSM2_PRIVATE_KEY
{
    public uint bits;                   // length in bits of modulus
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public uint[] PrivateKey;           // 私钥
}

//加密锁信息
[StructLayout(LayoutKind.Sequential)]
public struct DONGLE_INFO
{
    public ushort m_Ver;               //COS版本,比如:0x0201,表示2.01版
    public ushort m_Type;              //产品类型: 0xFF表示标准版, 0x00为时钟锁,0x01为带时钟的U盘锁,0x02为标准U盘锁
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public byte[] m_BirthDay;       //出厂日期
    public uint m_Agent;             //代理商编号,比如:默认的0xFFFFFFFF
    public uint m_PID;               //产品ID
    public uint m_UserID;            //用户ID
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public byte[] m_HID;            //8字节的硬件ID
    public uint m_IsMother;          //母锁标志: 0x01表示是母锁, 0x00表示不是母锁
    public uint m_DevType;           //设备类型(PROTOCOL_HID或者PROTOCOL_CCID)
}

//数据文件授权结构
[StructLayout(LayoutKind.Sequential)]
public struct DATA_LIC
{
    public ushort m_Read_Priv;     //读权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    public ushort m_Write_Priv;    //写权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
}

//私钥文件授权结构
[StructLayout(LayoutKind.Sequential)]
public struct PRIKEY_LIC
{
    public uint m_Count;        //可调次数: 0xFFFFFFFF表示不限制, 递减到0表示已不可调用
    public byte m_Priv;         //调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    public byte m_IsDecOnRAM;   //是否是在内存中递减: 1为在内存中递减，0为在FLASH中递减
    public byte m_IsReset;      //用户态调用后是否自动回到匿名态: TRUE为调后回到匿名态 (开发商态不受此限制)
    public byte m_Reserve;      //保留,用于4字节对齐
}

//对称加密算法(SM4/TDES)密钥文件授权结构
[StructLayout(LayoutKind.Sequential)]
public struct KEY_LIC
{
    public uint m_Priv_Enc;   //加密时的调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
}

//可执行文件授权结构
[StructLayout(LayoutKind.Sequential)]
public struct EXE_LIC
{
    public ushort m_Priv_Exe;   //运行的权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
}

//数据文件属性数据结构
[StructLayout(LayoutKind.Sequential)]
public struct DATA_FILE_ATTR
{
    public uint m_Size;      //数据文件长度，该值最大为4096
    public DATA_LIC m_Lic;       //授权
}

//ECCSM2/RSA私钥文件属性数据结构
[StructLayout(LayoutKind.Sequential)]
public struct PRIKEY_FILE_ATTR
{
    public ushort m_Type;       //数据类型:ECCSM2私钥 或 RSA私钥
    public ushort m_Size;       //数据长度:RSA该值为1024或2048, ECC该值为192或256, SM2该值为0x8100
    public PRIKEY_LIC m_Lic;        //授权
}

//对称加密算法(SM4/TDES)密钥文件属性数据结构
[StructLayout(LayoutKind.Sequential)]
public struct KEY_FILE_ATTR
{
    public uint m_Size;       //密钥数据长度=16
    public KEY_LIC m_Lic;        //授权
}

//可执行文件属性数据结构
[StructLayout(LayoutKind.Sequential)]
public struct EXE_FILE_ATTR
{
    public EXE_LIC m_Lic;        //授权
    public ushort m_Len;        //文件长度
}

//获取私钥文件列表时返回的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct PRIKEY_FILE_LIST
{
    public ushort m_FILEID;  //文件ID
    public ushort m_Reserve; //保留,用于4字节对齐
    public PRIKEY_FILE_ATTR m_attr;    //文件属性
}

//获取SM4及TDES密钥文件列表时返回的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct KEY_FILE_LIST
{
    public ushort m_FILEID;  //文件ID
    public ushort m_Reserve; //保留,用于4字节对齐
    public KEY_FILE_ATTR m_attr;    //文件属性
}

//获取数据文件列表时返回的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct DATA_FILE_LIST
{
    public ushort m_FILEID;  //文件ID
    public ushort m_Reserve; //保留,用于4字节对齐
    public DATA_FILE_ATTR m_attr;    //文件属性
}

//获取可执行文件列表时返回的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct EXE_FILE_LIST
{
    public ushort m_FILEID;    //文件ID
    public EXE_FILE_ATTR m_attr;
    public ushort m_Reserve;  //保留,用于4字节对齐
}

//下载和列可执行文件时填充的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct EXE_FILE_INFO
{
    public ushort m_dwSize;           //可执行文件大小
    public ushort m_wFileID;          //可执行文件ID
    public byte m_Priv;             //调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    public byte[] m_pData;            //可执行文件数据
}

//需要发给空锁的初始化数据
[StructLayout(LayoutKind.Sequential)]
public struct SON_DATA
{
    public int m_SeedLen;                 //种子码长度
    [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
    public string m_SeedForPID;	       //产生产品ID和开发商密码的种子码 (最长250个字节)
    [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 18)]
    public string m_UserPIN;         //用户密码(16个字符的0终止字符串)
    public sbyte m_UserTryCount;            //用户密码允许的最大错误重试次数
    public sbyte m_AdminTryCount;           //开发商密码允许的最大错误重试次数
    public int m_UserID_Start;            //起始用户ID
}

//母锁数据
[StructLayout(LayoutKind.Sequential)]
public struct MOTHER_DATA
{
    public SON_DATA m_Son;                  //子锁初始化数据
    public int m_Count;                //可产生子锁初始化数据的次数 (-1表示不限制次数, 递减到0时会受限)
}

// ===== 主程序类 =====

public class Program
{
    // ===== 跨平台DLL名称定义 =====

    /// <summary>
    /// 根据运行环境动态获取DLL名称
    /// </summary>
    /// <returns>对应平台的DLL文件名</returns>
    private static string GetDllName()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return "Dongle_d.dll";
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            // 检查是否为ARM64架构
            if (RuntimeInformation.ProcessArchitecture == Architecture.Arm64)
            {
                return "libRockeyARM.so.0.3";
            }
            else if (RuntimeInformation.ProcessArchitecture == Architecture.X64)
            {
                return "libDongle_d_x64.so";
            }
            else if (RuntimeInformation.ProcessArchitecture == Architecture.X86)
            {
                return "libDongle_d_x86.so";
            }
            else
            {
                // 默认使用x64版本
                return "libDongle_d_x64.so";
            }
        }
        else
        {
            // 未知平台，默认使用Windows版本
            return "Dongle_d.dll";
        }
    }

    /// <summary>
    /// 获取当前运行平台的描述信息（使用.NET 8现代API）
    /// </summary>
    /// <returns>平台描述字符串</returns>
    private static string GetPlatformDescription()
    {
        return RuntimeInformation.OSDescription;
    }

    /// <summary>
    /// 获取处理器架构信息（使用.NET 8现代API）
    /// </summary>
    /// <returns>架构描述字符串</returns>
    private static string GetProcessorArchitecture()
    {
        return RuntimeInformation.ProcessArchitecture.ToString();
    }

    // ===== API 函数声明 =====
    // 使用条件编译来处理不同平台的DLL名称

#if WINDOWS || NET48 || NET472 || NET471 || NET47 || NET462 || NET461 || NET46 || NET452 || NET451 || NET45 || NET40 || NET35 || NET20
    const string DLL_NAME = "Dongle_d.dll";
#elif LINUX_X64
    const string DLL_NAME = "libDongle_d_x64.so";
#elif LINUX_ARM64
    const string DLL_NAME = "libDongle_d_arm64.so";
#elif OSX_X64
    const string DLL_NAME = "libDongle_d_x64.dylib";
#elif OSX_ARM64
    const string DLL_NAME = "libDongle_d_arm64.dylib";
#else
    // 默认使用Windows版本，运行时会根据平台动态选择
    const string DLL_NAME = "Dongle_d.dll";
#endif

[DllImport(DLL_NAME)]
static extern uint Dongle_Enum(ref DONGLE_INFO pDongleInfo, out ushort pCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_Open(ref uint phDongle, int nIndex);

[DllImport(DLL_NAME)]
static extern uint Dongle_Close(uint hDongle);

[DllImport(DLL_NAME)]
static extern uint Dongle_VerifyPIN(uint hDongle, uint nFlags, byte[] pPIN, out int pRemainCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_CreateFile(uint hDongle, uint nFileType, ushort wFileID, uint pFileAttr);

[DllImport(DLL_NAME)]
static extern uint Dongle_WriteFile(uint hDongle, uint nFileType, ushort wFileID, short wOffset, byte[] buffer, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_ReadFile(uint hDongle, short wFileID, short wOffset, byte[] buffer, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_ListFile(uint hDongle, uint nFileType, DATA_FILE_LIST[] pFileList, ref int pDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_DeleteFile(uint hDongle, uint nFileType, short wFileID);

[DllImport(DLL_NAME)]
static extern uint Dongle_DownloadExeFile(uint hDongle, EXE_FILE_INFO[] pExeFileInfo, int nCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_RunExeFile(uint hDongle, short wFileID, byte[] pInOutData, short wInOutDataLen, ref int nMainRet);

[DllImport(DLL_NAME)]
static extern uint Dongle_WriteShareMemory(uint hDongle, byte[] pData, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_ReadShareMemory(uint hDongle, byte[] pData);

[DllImport(DLL_NAME)]
static extern uint Dongle_WriteData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_ReadData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_LEDControl(uint hDongle, uint nFlag);

[DllImport(DLL_NAME)]
static extern uint Dongle_SwitchProtocol(uint hDongle, uint nFlag);

[DllImport(DLL_NAME)]
static extern uint Dongle_GetUTCTime(uint hDongle, ref uint pdwUTCTime);

[DllImport(DLL_NAME)]
static extern uint Dongle_SetDeadline(uint hDongle, uint dwTime);

[DllImport(DLL_NAME)]
static extern uint Dongle_GenUniqueKey(uint hDongle, int nSeedLen, byte[] pSeed, byte[] pPIDstr, byte[] pAdminPINstr);

[DllImport(DLL_NAME)]
static extern uint Dongle_ResetState(uint hDongle);

[DllImport(DLL_NAME)]
static extern uint Dongle_ChangePIN(uint hDongle, uint nFlags, byte[] pOldPIN, byte[] pNewPIN, int nTryCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_RFS(uint hDongle);

[DllImport(DLL_NAME)]
static extern uint Dongle_SetUserID(uint hDongle, uint dwUserID);

[DllImport(DLL_NAME)]
static extern uint Dongle_ResetUserPIN(uint hDongle, byte[] pAdminPIN);

[DllImport(DLL_NAME)]
static extern uint Dongle_RsaGenPubPriKey(uint hDongle, ushort wPriFileID, ref RSA_PUBLIC_KEY pPubBakup, ref RSA_PRIVATE_KEY pPriBakup);

[DllImport(DLL_NAME)]
static extern uint Dongle_RsaPri(uint hDongle, ushort wPriFileID, uint nFlag, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_RsaPub(uint hDongle, uint nFlag, ref RSA_PUBLIC_KEY pPubKey, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_TDES(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_SM4(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_HASH(uint hDongle, uint nFlag, byte[] pInData, uint nDataLen, byte[] pHash);

[DllImport(DLL_NAME)]
static extern uint Dongle_LimitSeedCount(uint hDongle, int nCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_Seed(uint hDongle, byte[] pSeed, uint nSeedLen, byte[] pOutData);

[DllImport(DLL_NAME)]
static extern uint Dongle_EccGenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY vPubBakup, ref ECCSM2_PRIVATE_KEY vPriBakup);

[DllImport(DLL_NAME)]
static extern uint Dongle_EccSign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

[DllImport(DLL_NAME)]
static extern uint Dongle_EccVerify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

[DllImport(DLL_NAME)]
static extern uint Dongle_SM2GenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY pPubBakup, ref ECCSM2_PRIVATE_KEY pPriBakup);

[DllImport(DLL_NAME)]
static extern uint Dongle_SM2Sign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

[DllImport(DLL_NAME)]
static extern uint Dongle_SM2Verify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

// ===== 错误处理函数 =====

static string GetErrorDescription(uint errorCode)
{
    switch (errorCode)
    {
        case 0x00000000: return "成功";
        case 0x00000001: return "无效的参数";
        case 0x00000002: return "内存不足";
        case 0x00000003: return "设备未找到";
        case 0x00000004: return "设备已打开";
        case 0x00000005: return "设备未打开";
        case 0x00000006: return "通信错误";
        case 0x00000007: return "操作超时";
        case 0x00000008: return "权限不足";
        case 0x00000009: return "文件不存在";
        case 0x0000000A: return "文件已存在";
        case 0x0000000B: return "空间不足";
        case 0x0000000C: return "PIN码错误";
        case 0x0000000D: return "PIN码被锁定";
        case 0x0000000E: return "操作被拒绝";
        case 0x0000000F: return "数据校验错误";

        // 厂商特定错误代码
        case 0xF0000001: return "设备通信失败";
        case 0xF0000002: return "设备句柄无效或设备已断开连接";
        case 0xF0000003: return "设备忙碌或被其他程序占用";
        case 0xF0000004: return "设备驱动程序错误";
        case 0xF0000005: return "设备固件错误";

        default: return string.Format("未知错误代码: 0x{0:X8}", errorCode);
    }
}

// ===== 主程序入口 =====

    public static void Main(string[] args)
    {
        Console.OutputEncoding = Encoding.UTF8;
        Console.WriteLine("=== 加密锁测试控制台程序 ===");
        Console.WriteLine("程序启动时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"));
        Console.WriteLine("当前运行平台: " + GetPlatformDescription());
        Console.WriteLine("当前处理器架构: " + GetProcessorArchitecture());
        Console.WriteLine("编译时DLL文件: " + DLL_NAME);
        Console.WriteLine("运行时推荐DLL: " + GetDllName());

        // 检查编译时DLL和运行时推荐DLL是否一致
        if (DLL_NAME != GetDllName())
        {
            Console.WriteLine("⚠️  警告: 编译时DLL与运行时推荐DLL不一致!");
            Console.WriteLine("   建议使用对应平台的编译版本以获得最佳性能。");
        }
        else
        {
            Console.WriteLine("✅ DLL文件匹配当前运行环境");
        }
        Console.WriteLine();

        Console.WriteLine("开始执行测试（严格按照WinForm代码顺序）...");
        Console.WriteLine();

        // 严格按照您的WinForm Test_Click方法的顺序执行
        TestClick();

        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }

    static void TestClick()
    {
        uint ret = 0;
        ushort pCount = 0;
        DONGLE_INFO pDongleInfo = new DONGLE_INFO();
        uint hDongle = 0;

        // 第一次枚举
        ret = Dongle_Enum(ref pDongleInfo, out pCount);
        if (ret != 0)
        {
            Console.WriteLine("Enum Dongle Failed! Return value:" + ret.ToString("X"));
            return;
        }
        else
        {
            Console.WriteLine("Enum Dongle Success!Count: " + pCount);
        }

        // 第二次枚举
        ret = Dongle_Enum(ref pDongleInfo, out pCount);
        if (ret != 0)
        {
            Console.WriteLine("GetInfo Dongle Failed! Return value:" + ret.ToString("X"));
            return;
        }
        else
        {
            Console.WriteLine("GetInfo Dongle Success!");
        }

        // 显示设备信息
        for (int k = 0; k < pCount; k++)
        {
            Console.WriteLine("\n*********Dongle ARM INFO*******\n");
            Console.WriteLine("The index: " + k);
            Console.WriteLine("Agent ID: " + pDongleInfo.m_Agent.ToString("X"));
            Console.WriteLine("Dev Type: " + pDongleInfo.m_DevType);
            Console.Write("HID: ");

            for (int i = 0; i < 8; i++)
            {
                Console.Write(pDongleInfo.m_HID[i].ToString("X") + "  ");
            }
            Console.WriteLine();

            Console.WriteLine("Brith day: 20" + pDongleInfo.m_BirthDay[0].ToString("X") + "-" + pDongleInfo.m_BirthDay[1].ToString("X") + "-" + pDongleInfo.m_BirthDay[2].ToString("X") + "  " + pDongleInfo.m_BirthDay[3].ToString("X") + ":" + pDongleInfo.m_BirthDay[4].ToString("X") + ":" + pDongleInfo.m_BirthDay[5].ToString("X"));
            Console.WriteLine("Is Mother Dongle: " + pDongleInfo.m_IsMother);
            Console.WriteLine("PID: " + pDongleInfo.m_PID.ToString("X"));
            Console.WriteLine("Product Type: " + pDongleInfo.m_Type.ToString("X"));
            Console.WriteLine("UID: " + pDongleInfo.m_UserID.ToString("X"));
        }

        // 打开设备
        ret = Dongle_Open(ref hDongle, 0);
        if (ret != 0)
        {
            Console.WriteLine("Open Dongle Failed! Return value:" + ret.ToString("X"));
            return;
        }
        else
        {
            Console.WriteLine("Open Dongle Success!");
        }

        // 关闭设备
        ret = Dongle_Close(hDongle);
        if (ret != 0)
        {
            Console.WriteLine("Close Dongle Failed! Return value:" + ret.ToString("X"));
            return;
        }
        else
        {
            Console.WriteLine("Close Dongle Success!");
        }
    }
}

