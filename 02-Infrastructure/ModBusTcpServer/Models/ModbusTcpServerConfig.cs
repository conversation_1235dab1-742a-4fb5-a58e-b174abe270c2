using EdgeGateway.Service.Abstractions;
using EdgeGateway.Service.Abstractions.Models;

namespace ModBusTcpServer.Models;

/// <summary>
///     ModbusTcp服务端配置
/// </summary>
public class ModbusTcpServerConfig : IServiceConfig
{
    /// <summary>
    /// 服务Id
    /// </summary>
    public string ServiceId => "modbus-tcp-server";
    /// <summary>
    /// 版本号
    /// </summary>
    public string Version => "1.0.0";

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = false;

    /// <summary>
    ///     服务端口号
    /// </summary>
    public int Port { get; set; } = 502;

    /// <summary>
    ///     站号
    /// </summary>
    public byte Station { get; set; } = 1;

    /// <summary>
    ///     是否启用写入功能
    /// </summary>
    public bool EnableWrite { get; set; } = true;

    /// <summary>
    ///     是否启用IPv6
    /// </summary>
    public bool EnableIPv6 { get; set; } = false;

    /// <summary>
    ///     是否启用站号数据隔离
    /// </summary>
    public bool StationDataIsolation { get; set; } = false;

    /// <summary>
    ///     是否使用RTU over TCP模式
    /// </summary>
    public bool UseModbusRtuOverTcp { get; set; } = false;

    /// <summary>
    ///     字符串是否反转
    /// </summary>
    public bool IsStringReverse { get; set; } = false;

    /// <summary>
    ///     是否启用写掩码功能
    /// </summary>
    public bool EnableWriteMaskCode { get; set; } = true;

    /// <summary>
    ///     数据格式
    /// </summary>
    public string DataFormat { get; set; } = "ABCD";

    /// <summary>
    ///     活动超时时间（分钟）
    /// </summary>
    public int ActiveTimeoutMinutes { get; set; } = 60;

    /// <summary>
    ///     是否打印实时日志
    /// </summary>
    public bool PrintRealTimeLog { get; set; } = false;

    /// <summary>
    ///     获取默认配置
    /// </summary>
    /// <returns>默认配置</returns>
    public Dictionary<string, string> GetDefaultConfig()
    {
        return new Dictionary<string, string>
        {
            ["ServiceId"] = ServiceId,
            ["Version"] = Version,
            ["IsEnabled"] = IsEnabled.ToString().ToLower(),
            ["Port"] = Port.ToString(),
            ["Station"] = Station.ToString(),
            ["EnableWrite"] = EnableWrite.ToString().ToLower(),
            ["EnableIPv6"] = EnableIPv6.ToString().ToLower(),
            ["StationDataIsolation"] = StationDataIsolation.ToString().ToLower(),
            ["UseModbusRtuOverTcp"] = UseModbusRtuOverTcp.ToString().ToLower(),
            ["IsStringReverse"] = IsStringReverse.ToString().ToLower(),
            ["EnableWriteMaskCode"] = EnableWriteMaskCode.ToString().ToLower(),
            ["DataFormat"] = DataFormat,
            ["ActiveTimeoutMinutes"] = ActiveTimeoutMinutes.ToString(),
            ["PrintRealTimeLog"] = PrintRealTimeLog.ToString().ToLower()
        };
    }

    /// <summary>
    ///     获取配置描述
    /// </summary>
    /// <returns>配置描述</returns>
    public ServiceConfigDescription GetConfigDescription()
    {
        // 获取配置描述
        return new ServiceConfigDescription
        {
            // 设置服务ID
            ServiceId = ServiceId,
            // 设置配置项
            Items = new List<ConfigItemDescription>
            {
                // 设置端口号
                new()
                {
                    Name = nameof(Port),
                    Description = "服务端口号",
                    Type = "number",
                    DefaultValue = 502,
                    Required = true,
                    ValidationRules = new List<ValidationRule>
                    {
                        new()
                        {
                            Type = "range",
                            Parameters = new object[] { 1, 65535 },
                            Message = "端口号必须在1-65535之间"
                        }
                    },
                    Order = 1,
                    ExtendDescription = "ModbusTcp服务端口号"
                },
                new()
                {
                    Name = nameof(Station),
                    Description = "站号",
                    Type = "number",
                    DefaultValue = 1,
                    Required = true,
                    ValidationRules = new List<ValidationRule>
                    {
                        new()
                        {
                            Type = "range",
                            Parameters = new object[] { 0, 255 },
                            Message = "站号必须在0-255之间"
                        }
                    },
                    Order = 2
                },
                new()
                {
                    Name = nameof(DataFormat),
                    Description = "数据格式",
                    Type = "enum",
                    DefaultValue = "ABCD",
                    Required = true,
                    Options = new List<string> { "ABCD", "BADC", "CDAB", "DCBA" },
                    Order = 3,
                    ExtendDescription = "数据格式,用来对数据进行排序，默认ABCD"
                },
                new()
                {
                    Name = nameof(ActiveTimeoutMinutes),
                    Description = "活动超时时间（分钟）",
                    Type = "number",
                    DefaultValue = 60,
                    Required = true,
                    ValidationRules = new List<ValidationRule>
                    {
                        new() { Type = "range", Parameters = new object[] { 1, 65535 }, Message = "活动超时时间必须在1-65535之间" }
                    },
                    Order = 4,
                    ExtendDescription = "活动超时时间，默认60分钟，超时后会断开连接"
                },
                new()
                {
                    Name = nameof(PrintRealTimeLog),
                    Description = "是否打印实时日志",
                    Type = "boolean",
                    DefaultValue = false,
                    Required = false,
                    Order = 5,
                    ExtendDescription = "是否打印实时日志，开启在实时日志中会打印ModbusTcp服务端的数据接收日志"
                },
                new()
                {
                    Name = nameof(EnableWriteMaskCode),
                    Description = "是否启用写掩码功能",
                    Type = "boolean",
                    DefaultValue = true,
                    Required = false,
                    Order = 6,
                    ExtendDescription = "是否启用写掩码功能，开启后在写入数据时会使用掩码码"
                },
                new()
                {
                    Name = nameof(EnableWrite),
                    Description = "是否启用写入功能",
                    Type = "boolean",
                    DefaultValue = true,
                    Required = false,
                    Order = 7,
                    ExtendDescription = "是否启用写入功能，开启后可以写入数据"
                },
                new()
                {
                    Name = nameof(EnableIPv6),
                    Description = "是否启用IPv6",
                    Type = "boolean",
                    DefaultValue = false,
                    Required = false,
                    Order = 8,
                    ExtendDescription = "是否启用IPv6，开启后可以启用IPv6"
                },
                new()
                {
                    Name = nameof(StationDataIsolation),
                    Description = "是否启用站号数据隔离",
                    Type = "boolean",
                    DefaultValue = false,
                    Required = false,
                    Order = 9,
                    ExtendDescription = "是否启用站号数据隔离，开启后可以启用站号数据隔离"
                },
                new()
                {
                    Name = nameof(UseModbusRtuOverTcp),
                    Description = "是否使用RTU over TCP模式",
                    Type = "boolean",
                    DefaultValue = false,
                    Required = false,
                    Order = 10,
                    ExtendDescription = "是否使用RTU over TCP模式，开启后可以启用RTU over TCP模式"
                },
                new()
                {
                    Name = nameof(IsStringReverse),
                    Description = "是否反转字符串",
                    Type = "boolean",
                    DefaultValue = false,
                    Required = false,
                    Order = 11,
                    ExtendDescription = "是否反转字符串，开启后可以反转字符串"
                },

            }
        };
    }

    /// <summary>
    ///     验证配置
    /// </summary>
    /// <returns>是否有效，错误信息</returns>
    public (bool IsValid, string? ErrorMessage) Validate()
    {
        // 验证端口号
        if (Port < 1 || Port > 65535)
            return (false, "端口号必须在1-65535之间");

        // 验证站号
        if (Station < 0 || Station > 255)
            return (false, "站号必须在0-255之间");

        // 验证数据格式
        if (!new[] { "ABCD", "BADC", "CDAB", "DCBA" }.Contains(DataFormat))
            return (false, "无效的数据格式");

        // 验证活动超时时间
        if (ActiveTimeoutMinutes <= 0)
            return (false, "活动超时时间必须大于0");

        return (true, null);
    }
}