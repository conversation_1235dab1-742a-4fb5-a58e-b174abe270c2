using EdgeGateway.Service.Configuration;
using Furion.DynamicApiController;
using Furion.SpecificationDocument;
using Microsoft.AspNetCore.Mvc;

namespace EdgeGateway.Service.Services;

/// <summary>
///     服务管理控制器
/// </summary>
[ApiDescriptionSettings("系统设置")]
[Route("/api/service/")]
public class ServiceManagerService : ITransient, IDynamicApiController
{
    /// <summary>
    ///     服务配置管理器
    /// </summary>
    private readonly ServiceConfiguration _serviceConfig;

    /// <summary>
    ///     服务管理器
    /// </summary>
    private readonly ServiceManager _serviceManager;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="serviceConfig">服务配置管理器</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="serviceMonitor">服务监控器</param>
    public ServiceManagerService(
        ServiceConfiguration serviceConfig,
        ServiceManager serviceManager)
    {
        _serviceConfig = serviceConfig;
        _serviceManager = serviceManager;
    }

    /// <summary>
    ///     获取所有服务的配置
    /// </summary>
    [HttpGet("services")]
    [OperationId(nameof(GetAllServices))]
    public async Task<Dictionary<string, dynamic>> GetAllServices()
    {
        // 获取所有服务配置描述
        var descriptions = _serviceConfig.GetAllServiceDescriptions();
        // 获取所有服务状态
        var statuses = _serviceManager.GetServicesStatus();
        var result = new Dictionary<string, object>();
        // 遍历所有配置描述,获取实际配置值
        foreach (var service in descriptions)
        {
            // 获取服务实际配置值
            var config = await _serviceConfig.GetServiceConfig(service.Key);
            bool isRunning;
            // 获取服务连接状态
            if (statuses.TryGetValue(service.Key, out var status))
            {
                isRunning = status;
            }
            else
            {
                isRunning = false;
            }

            result.Add(service.Key, new
            {
                config,
                description = service.Value.Items,
                isRunning
            });
        }

        return result;
    }

    /// <summary>
    ///     获取指定服务的配置
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    [HttpGet("services/{serviceName}/config")]
    [OperationId(nameof(GetServiceConfig))]
    public async Task<object> GetServiceConfig(string serviceName)
    {
        return await _serviceConfig.GetServiceConfig(serviceName);
    }

    /// <summary>
    ///     更新服务配置
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="config">配置内容</param>
    [HttpPut("services/{serviceName}/save")]
    [OperationId(nameof(UpdateServiceConfig))]
    public async Task UpdateServiceConfig(string serviceName, [FromBody] object config)
    {
        await _serviceConfig.UpdateServiceConfig(serviceName, config);
    }

    /// <summary>
    ///     启用或禁用服务
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="enable">true为启用，false为禁用</param>
    [HttpPut("services/{serviceName}/status")]
    [OperationId(nameof(SetServiceStatus))]
    public async Task<string> SetServiceStatus(string serviceName, [FromQuery] bool enable)
    {
        await _serviceConfig.SetServiceEnabled(serviceName, enable);
        if (enable)
        {
            await _serviceConfig.StartService(serviceName);
            return $"服务 {serviceName} 已启用";
        }
        await _serviceConfig.StopService(serviceName);

        return $"服务 {serviceName} 已禁用";
    }
}