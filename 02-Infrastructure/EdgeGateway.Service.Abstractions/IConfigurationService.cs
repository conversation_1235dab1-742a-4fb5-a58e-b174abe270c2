namespace EdgeGateway.Service.Abstractions;

/// <summary>
/// 配置服务接口
/// </summary>
public interface IConfigurationService
{
  /// <summary>
  /// 获取所有的服务配置
  /// </summary>
  /// <returns></returns>
  Task<Dictionary<string, Dictionary<string, string>>> GetAllServicesAsync();
  /// <summary>
  /// 获取所有启用的服务配置
  /// </summary>
  /// <returns></returns>
  Task<Dictionary<string, Dictionary<string, string>>> GetEnabledServicesAsync();
  
  /// <summary>
  /// 获取服务配置
  /// </summary>
  Task<Dictionary<string, string>> GetServiceConfigAsync(string serviceName);
  
  /// <summary>
  /// 获取类型化配置
  /// </summary>
  Task<T> GetTypedConfigAsync<T>(T serviceConfig) where T : IServiceConfig;

  /// <summary>
  /// 更新服务配置
  /// </summary>
  Task UpdateServiceConfigAsync(string serviceName, Dictionary<string, string> config);

  
}