# PM2 for Windows 一键安装工具

这是一个帮助您在 Windows 系统上快速安装和配置 PM2 进程管理器的工具包。

## 简介

PM2 是一个流行的 Node.js 应用程序进程管理器，它可以帮助您保持应用程序在线，并管理日志、监控和集群。本工具可以帮助 Windows 用户快速完成 PM2 及相关服务的安装和配置。

## 功能特点

- 自动检查 Node.js 环境
- 全局安装 PM2
- 配置 PM2 作为 Windows 服务，实现开机自启动
- 提供基本的 PM2 使用指南
- 友好的用户界面和完整的错误处理

## 系统要求

- Windows 7/8/10/11
- Node.js (v10.0.0 或更高版本)
- npm (通常随 Node.js 一起安装)
- 管理员权限

## 使用方法

### 方法一（推荐）：使用批处理文件

1. 右键点击`Install-PM2.bat`文件
2. 选择"以管理员身份运行"
3. 按照屏幕提示完成安装

### 方法二：直接使用 PowerShell 脚本

1. 右键点击 PowerShell，选择"以管理员身份运行"
2. 执行以下命令导航到脚本所在目录：
   ```
   cd 脚本所在路径
   ```
3. 执行以下命令允许运行本地脚本：
   ```
   Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
   ```
4. 运行安装脚本：
   ```
   .\Install-PM2.ps1
   ```

## 安装后使用

安装完成后，您可以使用以下命令来管理您的应用：

- 启动应用：`pm2 start app.js --name 应用名称`
- 查看所有应用：`pm2 list`
- 停止应用：`pm2 stop 应用名称/id`
- 重启应用：`pm2 restart 应用名称/id`
- 删除应用：`pm2 delete 应用名称/id`
- 查看日志：`pm2 logs`
- 保存当前应用列表(用于开机自启)：`pm2 save`

## 常见问题

**Q: 脚本执行时报错"无法加载文件，因为在此系统上禁止运行脚本"**

A: 这是因为 PowerShell 默认的执行策略限制。请以管理员身份打开 PowerShell，并运行命令：

```
Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
```

然后再次尝试运行脚本。

**Q: 安装后，PM2 命令不被识别**

A: 请尝试重新打开命令提示符或 PowerShell。如果问题依然存在，请检查环境变量 PATH 中是否包含 npm 全局安装路径。

**Q: PM2 服务无法自启动**

A: 请确保以管理员身份运行了安装脚本，并且在安装过程中没有出现错误。您也可以通过 Windows 服务管理器检查 PM2 服务的状态。

## 更多资源

- [PM2 官方文档](https://pm2.keymetrics.io/docs/usage/quick-start/)
- [Node.js 官方网站](https://nodejs.org/)

## 许可

MIT
