namespace EdgeGateway.Core;

/// <summary>
///     账号类型枚举
/// </summary>
[Description("账号类型枚举")]
public enum AccountTypeEnum
{
    /// <summary>
    ///     超级管理员
    /// </summary>
    [Description("超级管理员")] SuperAdmin = 999,

    /// <summary>
    ///     系统管理员
    /// </summary>
    [Description("系统管理员")] SysAdmin = 888,

    /// <summary>
    ///     普通账号
    /// </summary>
    [Description("普通账号")] NormalUser = 777
}

/// <summary>
///     菜单类型枚举
/// </summary>
[Description("菜单类型枚举")]
public enum MenuTypeEnum
{
    /// <summary>
    ///     模块
    /// </summary>
    [Description("模块")] Module = 1,

    /// <summary>
    ///     菜单
    /// </summary>
    [Description("菜单")] Menu = 2
}