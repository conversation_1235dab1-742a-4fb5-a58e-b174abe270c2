namespace EdgeGateway.Core.Attribute;

/// <summary>
/// 权限验证特性
/// </summary>
[SuppressSniffer]
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class PermissionAttribute : System.Attribute
{
    /// <summary>
    /// 权限代码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 权限名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 是否忽略权限验证
    /// </summary>
    public bool Ignore { get; set; } = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="code">权限代码</param>
    /// <param name="name">权限名称</param>
    public PermissionAttribute(string code, string? name = null)
    {
        Code = code;
        Name = name;
    }

    /// <summary>
    /// 忽略权限验证的构造函数
    /// </summary>
    /// <param name="ignore">是否忽略</param>
    public PermissionAttribute(bool ignore = true)
    {
        Ignore = ignore;
        Code = string.Empty;
    }
}

/// <summary>
/// 菜单权限验证特性
/// </summary>
[SuppressSniffer]
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class MenuPermissionAttribute : PermissionAttribute
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="menuCode">菜单代码</param>
    /// <param name="name">权限名称</param>
    public MenuPermissionAttribute(string menuCode, string? name = null) : base(menuCode, name)
    {
    }
}

/// <summary>
/// 模块权限验证特性
/// </summary>
[SuppressSniffer]
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class ModulePermissionAttribute : PermissionAttribute
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="moduleCode">模块代码</param>
    /// <param name="name">权限名称</param>
    public ModulePermissionAttribute(string moduleCode, string? name = null) : base(moduleCode, name)
    {
    }
}
