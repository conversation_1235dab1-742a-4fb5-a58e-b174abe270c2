namespace EdgeGateway.Core.Service;

/// <summary>
/// 权限验证服务接口
/// </summary>
public interface IPermissionService
{
    /// <summary>
    /// 检查用户是否有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionCode">权限代码</param>
    /// <returns>是否有权限</returns>
    Task<bool> HasPermissionAsync(long userId, string permissionCode);

    /// <summary>
    /// 获取用户权限列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>权限代码列表</returns>
    Task<List<string>> GetUserPermissionsAsync(long userId);

    /// <summary>
    /// 清除用户权限缓存
    /// </summary>
    /// <param name="userId">用户ID</param>
    void ClearUserPermissionCache(long userId);

    /// <summary>
    /// 清除所有权限缓存
    /// </summary>
    void ClearAllPermissionCache();
}
