using System;
using System.IO;
using System.Threading.Tasks;
using EdgeGateway.Core.Configuration;
using EdgeGateway.Core.Const;
using EdgeGateway.Core.Attribute;
using EdgeGateway.Core.Service;
using Furion;
using Furion.Authorization;
using Furion.DataEncryption;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Reflection;

namespace EdgeGateway.Web.Core.Handlers;
public class JwtHandler : AppAuthorizeHandler
{
    /// <summary>
    /// 
    /// </summary>
    private readonly IniConfiguration _config;
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="config"></param>
    public JwtHandler(IniConfiguration config)
    {
        _config = config;
    }

    /// <summary>
    ///     自动刷新Token
    /// </summary>
    /// <param name="context"></param>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    public override async Task HandleAsync(AuthorizationHandlerContext context, DefaultHttpContext httpContext)
    {
        // 生成Token令牌
        var tokenExpire = _config.GetValue<int>("system_System", ConfigConst.SysTokenExpire);
        var refreshTokenExpire = _config.GetValue<int>("system_System", ConfigConst.SysRefreshTokenExpire);
        if (JWTEncryption.AutoRefreshToken(context, context.GetCurrentHttpContext(), tokenExpire,refreshTokenExpire))
        {
            await AuthorizeHandleAsync(context);
        }
        else
        {
            context.Fail(); // 授权失败
            var currentHttpContext = context.GetCurrentHttpContext();
            if (currentHttpContext == null)
                return;
            currentHttpContext.SignoutToSwagger();
        }
    }
    /// <summary>
    ///     授权判断逻辑,授权通过返回 true,否则返回 false
    /// </summary>
    /// <param name="context"></param>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    public override async Task<bool> PipelineAsync(AuthorizationHandlerContext context, DefaultHttpContext httpContext)
    {
        // 此处已经自动验证 Jwt Token的有效性了,无需手动验证
        return await CheckAuthorizeAsync(httpContext);
    }
    /// <summary>
    ///     检查权限
    /// </summary>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    private static async Task<bool> CheckAuthorizeAsync(DefaultHttpContext httpContext)
    {
        try
        {
            // 获取当前请求的控制器和动作信息
            var endpoint = httpContext.GetEndpoint();
            if (endpoint == null)
                return true;

            var controllerActionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
            if (controllerActionDescriptor == null)
                return true;

            // 检查是否有忽略权限验证的特性
            var ignorePermission = controllerActionDescriptor.MethodInfo.GetCustomAttribute<PermissionAttribute>()?.Ignore == true ||
                                   controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<PermissionAttribute>()?.Ignore == true;

            if (ignorePermission)
                return true;

            // 检查是否有AllowAnonymous特性
            var allowAnonymous = controllerActionDescriptor.MethodInfo.GetCustomAttribute<AllowAnonymousAttribute>() != null ||
                                 controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<AllowAnonymousAttribute>() != null;

            if (allowAnonymous)
                return true;

            // 获取权限特性
            var permissionAttr = controllerActionDescriptor.MethodInfo.GetCustomAttribute<PermissionAttribute>() ??
                                 controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<PermissionAttribute>();

            // 如果没有权限特性，默认允许访问（向后兼容）
            if (permissionAttr == null || string.IsNullOrWhiteSpace(permissionAttr.Code))
                return true;

            // 获取当前用户ID
            var userIdClaim = httpContext.User.FindFirst(ClaimConst.UserId)?.Value;
            if (!long.TryParse(userIdClaim, out var userId) || userId <= 0)
                return false;

            // 获取权限服务并检查权限
            var permissionService = httpContext.RequestServices.GetService<IPermissionService>();
            if (permissionService == null)
                return true; // 如果权限服务未注册，默认允许访问

            return await permissionService.HasPermissionAsync(userId, permissionAttr.Code);
        }
        catch (Exception ex)
        {
            // 记录异常但不阻止访问（向后兼容）
            var logger = httpContext.RequestServices.GetService<ILogger<JwtHandler>>();
            logger?.LogError(ex, "权限检查时发生异常");
            return true;
        }
    }
}