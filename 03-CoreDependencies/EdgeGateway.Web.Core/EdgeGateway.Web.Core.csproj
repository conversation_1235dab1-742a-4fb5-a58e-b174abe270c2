<Project Sdk="Microsoft.NET.Sdk">


    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <NoWarn>1701;1702;1591</NoWarn>
        <DocumentationFile>EdgeGateway.Web.Core.xml</DocumentationFile>
    </PropertyGroup>


    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>


    <ItemGroup>
        <None Remove="EdgeGateway.Web.Core.xml"/>
        <None Remove="EdgeSnoop.Web.Core.xml" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\01-Business\00-System\EdgeGateway.Base\EdgeGateway.Base.csproj" />
        <ProjectReference Include="..\..\01-Business\01-Device\EdgeGateway.Device\EdgeGateway.Device.csproj" />
        <ProjectReference Include="..\..\01-Business\04-Forwarding\EdgeGateway.Forwarding\EdgeGateway.Forwarding.csproj" />
        <ProjectReference Include="..\..\01-Business\05-AI\EdgeGateway.AI\EdgeGateway.AI.csproj" />
        <ProjectReference Include="..\..\01-Business\08-Notification\EdgeGateway.Notification\EdgeGateway.Notification.csproj" />
        <ProjectReference Include="..\..\02-Infrastructure\EdgeGateway.Service\EdgeGateway.Service.csproj" />
        <ProjectReference Include="..\..\04-CommonTools\EdgeGateway.Preferences\EdgeGateway.Preferences.csproj" />
    </ItemGroup>

</Project>
